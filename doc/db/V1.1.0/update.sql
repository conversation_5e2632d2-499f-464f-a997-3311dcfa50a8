

/* Alter table in target */
ALTER TABLE `tbiz_activity`
	ADD COLUMN `limit` int(11)   NULL COMMENT '活动购买上限' after `notice`,
	ADD COLUMN `buy_start_time` datetime   NULL COMMENT '售票开始时间' after `address_location`,
	ADD COLUMN `buy_end_time` datetime   NULL COMMENT '售票结束时间' after `buy_start_time`;


/* Alter table in target */
ALTER TABLE `tbiz_activity_ticket_line`
	ADD COLUMN `delete_flag` varchar(4)  COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标识 0:正常1:已删除' after `order_no`;


/* Alter table in target */
ALTER TABLE `tbiz_fresh_news`
	ADD COLUMN `news_type` varchar(4)  COLLATE utf8mb4_general_ci NULL COMMENT '类型dict={\"0\":\"新鲜事\",\"1\":\"往期回顾\"}' after `type`;


/* Alter table in target */
ALTER TABLE `tbiz_goods`
	CHANGE `pic` `pic` varchar(2048)  COLLATE utf8mb4_general_ci NULL COMMENT '商品图片' after `price`;


/* Alter table in target */
ALTER TABLE `tsys_user`
	ADD COLUMN `nickname_change_flag` varchar(4)  COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '昵称修改标识dict={\"0\":\"未修改\",\"1\":\"已修改\"}' after `nickname`,
	ADD COLUMN `reservation_status` varchar(4)  COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '预约状态 dict={\"0\":\"正常\",\"1\":\"限制\"}' after `channel_grade`,
	ADD COLUMN `reservation_expired_number` int(11)   NULL DEFAULT 0 COMMENT '预约过期次数' after `reservation_status`,
	ADD COLUMN `reservation_unseal_time` datetime   NULL COMMENT '预约限制解封时间' after `reservation_expired_number`;

/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;

CREATE TABLE `tbiz_daily_income_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `activity_amount` decimal(18,4) DEFAULT '0.0000' COMMENT '活动收益',
  `goods_amount` decimal(18,4) DEFAULT '0.0000' COMMENT '商品收益',
  `order_count` int(11) DEFAULT NULL COMMENT '订单数',
  `income_date` date DEFAULT NULL COMMENT '收益日期',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index` (`income_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='每日收益';