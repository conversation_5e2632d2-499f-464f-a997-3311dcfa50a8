/*
 Navicat Premium Data Transfer

 Source Server         : 研发数据库
 Source Server Type    : MySQL
 Source Server Version : 50633 (5.6.33)
 Source Host           : *************:3307
 Source Schema         : ml_gallery

 Target Server Type    : MySQL
 Target Server Version : 50633 (5.6.33)
 File Encoding         : 65001

 Date: 13/02/2025 12:40:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tbiz_activity
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_activity`;
CREATE TABLE `tbiz_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `name` varchar(255) DEFAULT NULL COMMENT '活动名称',
  `title` varchar(255) DEFAULT NULL COMMENT '活动标题',
  `location` varchar(4) DEFAULT NULL COMMENT '位置dict={"0":"普通","1":"热门"}',
  `pic` varchar(255) DEFAULT NULL COMMENT '活动封面',
  `address` varchar(255) DEFAULT NULL COMMENT '活动地址',
  `price` decimal(18,2) DEFAULT NULL COMMENT '售价',
  `visitor_information` text COMMENT '入馆须知',
  `notice` text COMMENT '注意事项',
  `minimum_buy_number` int(11) DEFAULT NULL COMMENT '最小购买数量',
  `maximum_buy_number` int(11) DEFAULT NULL COMMENT '最大购买数量',
  `day_limit` int(11) DEFAULT NULL COMMENT '每日预约上限',
  `longitude` varchar(255) DEFAULT NULL COMMENT '经度',
  `latitude` varchar(255) DEFAULT NULL COMMENT '纬度',
  `start_time` date DEFAULT NULL COMMENT '活动开始时间',
  `end_time` date DEFAULT NULL COMMENT '活动结束时间',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待上架","1":"未开始","2":"售票中","3":"已结束","4":"已下架"}',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) NOT NULL DEFAULT '0' COMMENT '顺序',
  `province` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '省份名称',
  `city` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '城市名称',
  `county` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '区/县',
  `address_location` varchar(255) DEFAULT NULL COMMENT '活动地名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_activity_order
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_activity_order`;
CREATE TABLE `tbiz_activity_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `order_number` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '订单号',
  `serial_number` varchar(255) DEFAULT NULL COMMENT '流水号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动id',
  `ticket_line_id` bigint(20) DEFAULT NULL COMMENT '票档id',
  `ticket_line_name` varchar(255) DEFAULT NULL COMMENT '票档名称',
  `pic` varchar(255) DEFAULT NULL COMMENT '活动图片',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `contact` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `price` decimal(18,2) DEFAULT NULL COMMENT '实付单价',
  `number` int(11) DEFAULT NULL COMMENT '数量',
  `is_modify` varchar(4) DEFAULT '0' COMMENT '是否修改 0:未修改 1:已经修改',
  `date` date DEFAULT NULL COMMENT '预约日期',
  `old_date` date DEFAULT NULL COMMENT '修改前日期',
  `code_pic` varchar(255) DEFAULT NULL COMMENT '二维码',
  `pay_type` varchar(4) CHARACTER SET utf8 DEFAULT '0' COMMENT '支付类型 dict={"0":"微信"}',
  `status` varchar(4) DEFAULT NULL COMMENT '状态 dict={"0":"预约成功,未使用","1":"已使用","2":"已过期","3":"取消中","4":"已取消"}',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `cancle_datetime` datetime DEFAULT NULL COMMENT '取消时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=904899168005529695 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_activity_order_statistics
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_activity_order_statistics`;
CREATE TABLE `tbiz_activity_order_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动序号',
  `order_date` date NOT NULL COMMENT '预约日期',
  `total_tickets` int(11) DEFAULT '0' COMMENT '预约门票总数(包含未支付完成的)',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index` (`activity_id`,`order_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='活动预约统计';

-- ----------------------------
-- Table structure for tbiz_activity_ticket_line
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_activity_ticket_line`;
CREATE TABLE `tbiz_activity_ticket_line` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动序号',
  `name` varchar(255) DEFAULT NULL COMMENT '票档名称',
  `price` decimal(18,2) DEFAULT NULL COMMENT '价格',
  `number` int(11) DEFAULT '0' COMMENT '总量',
  `inventory` int(11) DEFAULT '0' COMMENT '库存',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待上架","1":"上架中","2":"已下架"}',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) NOT NULL DEFAULT '0' COMMENT '顺序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_big_activity_order
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_big_activity_order`;
CREATE TABLE `tbiz_big_activity_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单id',
  `serial_number` varchar(64) DEFAULT NULL COMMENT '流水号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动id',
  `ticket_line_id` bigint(20) DEFAULT NULL COMMENT '票档id',
  `ticket_line_name` varchar(255) DEFAULT NULL COMMENT '票档名称',
  `pic` varchar(255) DEFAULT NULL COMMENT '活动图片',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `contact` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `total_price` decimal(18,2) DEFAULT NULL COMMENT '总价',
  `number` int(11) DEFAULT NULL COMMENT '数量',
  `pay_type` varchar(4) CHARACTER SET utf8 DEFAULT '0' COMMENT '支付类型 dict={"0":"微信"}',
  `status` varchar(4) DEFAULT NULL COMMENT '状态 dict={"0":"待支付","1":"支付失败","2":"支付成功"}',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_datetime` datetime DEFAULT NULL COMMENT '支付时间',
  `date` date DEFAULT NULL COMMENT '预约日期',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `index` (`serial_number`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='预约单大订单';

-- ----------------------------
-- Table structure for tbiz_fresh_news
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_fresh_news`;
CREATE TABLE `tbiz_fresh_news` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `pic` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `type` varchar(4) DEFAULT NULL COMMENT '类型dict={"0":"跳转到外部","1":"跳转本系统"}',
  `content` longtext COMMENT '内容',
  `content_type` varchar(4) NOT NULL DEFAULT '0' COMMENT '内容类型 0:富文本,1:图片',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待上架","1":"上架中","2":"已下架"}',
  `up_datetime` datetime DEFAULT NULL COMMENT '发布时间',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) NOT NULL DEFAULT '0' COMMENT '顺序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods`;
CREATE TABLE `tbiz_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `type_id` bigint(20) DEFAULT NULL COMMENT '类型序号',
  `name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `pic` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `pics` varchar(2048) DEFAULT NULL COMMENT '商品图片',
  `content` longtext COMMENT '商品详情',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待上架","1":"上架中","2":"已下架"}',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) NOT NULL DEFAULT '0' COMMENT '顺序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods_category
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods_category`;
CREATE TABLE `tbiz_goods_category` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `type` varchar(4) DEFAULT NULL COMMENT '类型0:大类 1:小类',
  `parent_id` varchar(255) DEFAULT NULL COMMENT '上级类型',
  `name` varchar(32) NOT NULL COMMENT '名称',
  `status` varchar(32) NOT NULL COMMENT '状态 dict={"0":"待上架","1":"上架中","2":"已下架"}',
  `updater` bigint(32) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(32) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) DEFAULT NULL COMMENT 'UI序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods_norms
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods_norms`;
CREATE TABLE `tbiz_goods_norms` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `good_id` bigint(20) DEFAULT NULL COMMENT '商品序号',
  `name` varchar(255) DEFAULT NULL COMMENT '规格名称',
  `price` decimal(18,2) DEFAULT NULL COMMENT '价格',
  `number` int(11) DEFAULT '0' COMMENT '总量',
  `inventory` int(11) DEFAULT '0' COMMENT '库存',
  `pic` varchar(255) DEFAULT NULL COMMENT '图片',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待上架","1":"上架中","2":"已下架"}',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `order_no` int(11) NOT NULL DEFAULT '0' COMMENT '顺序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods_order
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods_order`;
CREATE TABLE `tbiz_goods_order` (
  `id` bigint(20) NOT NULL COMMENT '序号',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `type` varchar(4) DEFAULT NULL COMMENT '类型dict={"0":"普通订单","1":"购物车订单"}',
  `order_number` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '订单号',
  `courier_number` varchar(255) DEFAULT NULL COMMENT '快递单号',
  `number` int(11) DEFAULT NULL COMMENT '购买数量',
  `total_price` decimal(18,2) DEFAULT NULL COMMENT '总价',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"待付款","1":"待发货","2":"待收货","3":"已完成","4":"已取消"}',
  `receive_way` varchar(255) DEFAULT NULL COMMENT '收货方式dict={"0":"到付","1":"包邮"}',
  `pay_type` varchar(4) CHARACTER SET utf8 DEFAULT '0' COMMENT '支付类型 dict={"0":"微信"}',
  `address` varchar(255) DEFAULT NULL COMMENT '收货地址',
  `user_name` varchar(255) DEFAULT NULL COMMENT '收货人名称',
  `user_mobile` varchar(255) DEFAULT NULL COMMENT '收货人手机号',
  `company` varchar(255) DEFAULT NULL COMMENT '快递公司',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_datetime` datetime DEFAULT NULL COMMENT '支付时间',
  `cancle_datetime` datetime DEFAULT NULL COMMENT '取消时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(255) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `finish_datetime` datetime DEFAULT NULL COMMENT '确认时间',
  `auto_receive_datetime` datetime DEFAULT NULL COMMENT '自动收货时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods_order_detail`;
CREATE TABLE `tbiz_goods_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单序号',
  `order_number` varchar(255) DEFAULT NULL COMMENT '订单编号',
  `goods_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `norms_id` bigint(20) DEFAULT NULL COMMENT '规格id',
  `number` int(11) DEFAULT NULL COMMENT '购买数量',
  `name` varchar(255) DEFAULT NULL COMMENT '商品名称',
  `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `pic` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `norms_name` varchar(255) DEFAULT NULL COMMENT '规格名称',
  `content` longtext COMMENT '商品详情',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=904963235223183426 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tbiz_goods_trolley
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_goods_trolley`;
CREATE TABLE `tbiz_goods_trolley` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单序号',
  `goods_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `norms_id` bigint(20) DEFAULT NULL COMMENT '规格id',
  `number` int(11) DEFAULT NULL COMMENT '数量',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"已购买","1":"未购买","2":"已删除"}',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='购物车';

-- ----------------------------
-- Table structure for tbiz_verification_record
-- ----------------------------
DROP TABLE IF EXISTS `tbiz_verification_record`;
CREATE TABLE `tbiz_verification_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动id',
  `ticket_line_id` bigint(20) DEFAULT NULL COMMENT '票档id',
  `activity_order_id` bigint(20) DEFAULT NULL COMMENT '预约单编号',
  `date` datetime DEFAULT NULL COMMENT '预约日期',
  `status` varchar(4) DEFAULT NULL COMMENT '状态dict={"0":"已核销"}',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `code` varchar(255) DEFAULT NULL COMMENT '入场码',
  `pic` varchar(255) DEFAULT NULL COMMENT '活动图片',
  `ticket_line_name` varchar(255) DEFAULT NULL COMMENT '票档名称',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `contract` varchar(255) DEFAULT NULL COMMENT '联系方式',
  `creater` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `creater_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tstd_account
-- ----------------------------
DROP TABLE IF EXISTS `tstd_account`;
CREATE TABLE `tstd_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
  `account_number` varchar(32) NOT NULL COMMENT '账户编号',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `currency` varchar(32) NOT NULL COMMENT '币种',
  `type` varchar(4) NOT NULL COMMENT '类别',
  `status` varchar(2) NOT NULL COMMENT '状态（1正常 2程序冻结 3人工冻结）',
  `amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总资产',
  `available_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `lock_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '锁仓金额',
  `create_datetime` datetime NOT NULL COMMENT '创建时间',
  `last_order` varchar(32) DEFAULT NULL COMMENT '最近一次变动对应的流水编号',
  `cloud_account_number` varchar(64) DEFAULT NULL COMMENT '云钱包账户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index7` (`user_id`,`currency`),
  UNIQUE KEY `account_number_UNIQUE` (`account_number`),
  KEY `index3` (`type`),
  KEY `index4` (`user_id`),
  KEY `index5` (`currency`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='账户';

-- ----------------------------
-- Table structure for tstd_address
-- ----------------------------
DROP TABLE IF EXISTS `tstd_address`;
CREATE TABLE `tstd_address` (
  `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户uid（用户类型对应的id）',
  `name` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `is_default` char(3) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '是否默认地址1是，0否,一个用户只能有一个默认地址',
  `province` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '省份名称',
  `province_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '省份ID对应area表中的id',
  `city` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '城市名称',
  `city_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID对应area表中的id',
  `county` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '区/县',
  `county_id` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID对应area表中的id',
  `address` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '详细地址',
  `phone` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '联系方式',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for tstd_area
-- ----------------------------
DROP TABLE IF EXISTS `tstd_area`;
CREATE TABLE `tstd_area` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '索引ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '地区名称',
  `pid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '地区父ID',
  `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `deep` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '地区深度，从1开始',
  `short_name` varchar(50) NOT NULL DEFAULT '',
  `enabled` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用（1：是；0：否）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `area_parent_id` (`pid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='地区表';

-- ----------------------------
-- Table structure for tstd_charge
-- ----------------------------
DROP TABLE IF EXISTS `tstd_charge`;
CREATE TABLE `tstd_charge` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `account_number` varchar(32) NOT NULL COMMENT '账户编号',
  `account_type` varchar(4) NOT NULL COMMENT '账户类型',
  `user_kind` varchar(32) NOT NULL COMMENT '用户类型（C端用户 CLINIC诊所用户）',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `amount` decimal(18,8) NOT NULL DEFAULT '0.********' COMMENT '充值金额',
  `currency` varchar(8) NOT NULL COMMENT '币种',
  `biz_type` varchar(32) NOT NULL COMMENT '关联业务类型',
  `biz_note` varchar(255) NOT NULL COMMENT '关联业务备注',
  `biz_no` varchar(255) DEFAULT NULL COMMENT '关联订单号',
  `bill_flag` varchar(4) DEFAULT '0' COMMENT '是否开票（0否 1是）',
  `status` varchar(4) NOT NULL COMMENT '状态（1待支付 2支付失败 3支付成功）',
  `apply_user` bigint(20) NOT NULL COMMENT '申请人',
  `apply_note` varchar(255) DEFAULT NULL COMMENT '申请说明',
  `apply_datetime` datetime NOT NULL COMMENT '申请时间',
  `channel_type` varchar(32) NOT NULL COMMENT '支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）',
  `channel_bank` varchar(255) DEFAULT NULL COMMENT '渠道银行',
  `channel_account_info` varchar(255) DEFAULT NULL COMMENT '支付渠道账号信息',
  `channel_account_number` varchar(255) DEFAULT NULL COMMENT '支付渠道账号',
  `channel_order` varchar(255) DEFAULT NULL COMMENT '支付渠道单号',
  `pay_group` varchar(32) DEFAULT NULL COMMENT '订单分组组号（信息代表）',
  `pay_user` varchar(32) DEFAULT NULL COMMENT '支付回录人',
  `pay_note` varchar(255) DEFAULT NULL COMMENT '支付回录说明',
  `pay_datetime` datetime DEFAULT NULL COMMENT '支付回录时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充值订单';

-- ----------------------------
-- Table structure for tstd_jour
-- ----------------------------
DROP TABLE IF EXISTS `tstd_jour`;
CREATE TABLE `tstd_jour` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `type` varchar(32) NOT NULL COMMENT '流水类型（1余额流水 2冻结流水）',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `account_number` varchar(32) NOT NULL COMMENT '账户编号',
  `account_type` varchar(4) NOT NULL COMMENT '账户类型',
  `currency` varchar(32) DEFAULT NULL COMMENT '币种',
  `biz_category` varchar(32) NOT NULL COMMENT '业务大类',
  `biz_category_note` varchar(32) NOT NULL COMMENT '业务大类',
  `biz_type` varchar(255) NOT NULL COMMENT '业务小类',
  `biz_note` varchar(255) NOT NULL COMMENT '业务小类说明',
  `ref_no` varchar(255) NOT NULL COMMENT '系统内部参考订单号',
  `ref_user_id` bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
  `trans_amount` decimal(18,8) NOT NULL COMMENT '变动金额',
  `pre_amount` decimal(18,8) DEFAULT NULL COMMENT '变动前金额',
  `post_amount` decimal(18,8) DEFAULT NULL COMMENT '变动后金额',
  `prev_jour_code` varchar(32) DEFAULT NULL COMMENT '上一条流水编号',
  `status` varchar(4) DEFAULT NULL COMMENT '状态 1待对账 3已对账且账已平 4账不平待调账审批 5已对账且账不平 6无需对账 11待入账 12已入账 13入账失败 14待出账 15出账成功 16出账失败',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_datetime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `index3` (`biz_type`),
  KEY `index4` (`user_id`),
  KEY `index5` (`status`),
  KEY `index6` (`account_number`),
  KEY `index7` (`ref_no`),
  KEY `index2` (`currency`),
  KEY `index8` (`account_number`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='账户流水';

-- ----------------------------
-- Table structure for tstd_lock
-- ----------------------------
DROP TABLE IF EXISTS `tstd_lock`;
CREATE TABLE `tstd_lock` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `biz_type` varchar(4) NOT NULL COMMENT '类型',
  `ref_code` varchar(32) NOT NULL COMMENT '关联编号',
  `create_datetime` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index2` (`biz_type`,`ref_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务锁';

-- ----------------------------
-- Table structure for tstd_pay_record
-- ----------------------------
DROP TABLE IF EXISTS `tstd_pay_record`;
CREATE TABLE `tstd_pay_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `pay_type` varchar(64) NOT NULL COMMENT '支付渠道 wechat、alipy等',
  `pay_method` varchar(64) NOT NULL COMMENT '支付方式 app、h5、web等',
  `amount` decimal(18,2) NOT NULL COMMENT '支付金额',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `callback_time` datetime DEFAULT NULL COMMENT '回调时间',
  `status` varchar(8) NOT NULL DEFAULT '0' COMMENT '状态dict={"0":"待回调","1":"支付成功","2":"支付失败"}',
  `biz_type` varchar(8) NOT NULL COMMENT '业务类型dict={"0":"充值","1":"一口价订单支付"}',
  `biz_code` bigint(20) NOT NULL COMMENT '关联业务编号',
  `biz_status` varchar(8) NOT NULL DEFAULT '0' COMMENT '状态dict={"0":"待处理","1":"已处理"}',
  `request` varchar(1024) DEFAULT NULL,
  `response` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=902432917316444161 DEFAULT CHARSET=utf8 COMMENT='支付记录';

-- ----------------------------
-- Table structure for tstd_sms
-- ----------------------------
DROP TABLE IF EXISTS `tstd_sms`;
CREATE TABLE `tstd_sms` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `target` varchar(32) NOT NULL DEFAULT 'C' COMMENT '针对人群',
  `type` varchar(4) NOT NULL DEFAULT '2' COMMENT '消息类型dict={"1":"系统公告","2":"我的消息"}',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `content` longtext COMMENT '内容',
  `content_type` varchar(4) CHARACTER SET utf8mb4 NOT NULL DEFAULT '0' COMMENT '内容类型 0:富文本,1:图片',
  `synopsis` text COMMENT '简介',
  `status` varchar(4) DEFAULT '0' COMMENT '状态dict={"0":"草稿","1":"已发送","2":"已撤回"}',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `creator_name` varchar(32) DEFAULT NULL COMMENT '创建人名称',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `updater` varchar(32) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(32) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `ref_type` varchar(32) DEFAULT NULL COMMENT '关联类型',
  `ref_no` varchar(32) DEFAULT NULL COMMENT '关联编号',
  `is_read` varchar(8) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8 COMMENT='系统公告';

-- ----------------------------
-- Table structure for tstd_sms_read
-- ----------------------------
DROP TABLE IF EXISTS `tstd_sms_read`;
CREATE TABLE `tstd_sms_read` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `sms_code` bigint(20) DEFAULT NULL COMMENT '消息编号',
  `receive_way` varchar(4) DEFAULT NULL COMMENT '接受方式(站内消息，APP推送,短信)',
  `status` varchar(4) DEFAULT NULL COMMENT '状态 0-未阅读 1-已阅读 2-已删除',
  `create_datetime` datetime DEFAULT NULL COMMENT '推送时间',
  `read_datetime` datetime DEFAULT NULL COMMENT '阅读时间',
  `delete_datetime` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='公告阅读记录';

-- ----------------------------
-- Table structure for tstd_user_action_log
-- ----------------------------
DROP TABLE IF EXISTS `tstd_user_action_log`;
CREATE TABLE `tstd_user_action_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `type` varchar(4) DEFAULT NULL COMMENT '类型 dict={"0":"微信解绑","1":"绑定微信"}',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户序号',
  `old_data` varchar(255) DEFAULT NULL COMMENT '原数据',
  `new_data` varchar(255) DEFAULT NULL COMMENT '新数据',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户业务操作记录';

-- ----------------------------
-- Table structure for tstd_user_node_level
-- ----------------------------
DROP TABLE IF EXISTS `tstd_user_node_level`;
CREATE TABLE `tstd_user_node_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `way` varchar(32) NOT NULL COMMENT '设置方式 dict={"0":"程序自动","1":"人工手动"}',
  `node_level_auto` int(11) DEFAULT '0' COMMENT '节点等级（自动）',
  `node_level_manual` int(11) DEFAULT '0' COMMENT '节点等级（手动）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index2` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='节点用户';

-- ----------------------------
-- Table structure for tstd_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `tstd_withdraw`;
CREATE TABLE `tstd_withdraw` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `account_number` varchar(32) NOT NULL COMMENT '账户编号',
  `account_type` varchar(4) NOT NULL COMMENT '类别（B端账号，C端账号，平台账号）',
  `currency` varchar(32) NOT NULL COMMENT '币种',
  `biz_type` varchar(32) NOT NULL COMMENT '业务类型（withdraw取现 transfer 内部划转）',
  `amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '取现金额',
  `fee` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `balance_amount` decimal(18,2) NOT NULL COMMENT '提现时账户余额',
  `bill_flag` varchar(4) DEFAULT '0' COMMENT '是否开票（0否 1是）',
  `channel_type` varchar(32) NOT NULL COMMENT '支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）',
  `channel_bank` varchar(32) DEFAULT NULL COMMENT '渠道银行',
  `channel_account_info` varchar(255) DEFAULT NULL COMMENT '支付渠道账号信息',
  `channel_account_number` varchar(255) DEFAULT NULL COMMENT '支付渠道账号',
  `channel_order` varchar(255) DEFAULT NULL COMMENT '支付渠道单号',
  `status` varchar(4) NOT NULL COMMENT '状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）',
  `apply_user` bigint(20) NOT NULL COMMENT '申请人',
  `apply_user_kind` varchar(32) NOT NULL COMMENT '用户类型（C端用户 CLINIC诊所用户）',
  `apply_note` varchar(255) DEFAULT NULL COMMENT '申请说明',
  `apply_datetime` datetime NOT NULL COMMENT '申请时间',
  `approve_user` bigint(20) DEFAULT NULL COMMENT '审批人',
  `approve_note` varchar(255) DEFAULT NULL COMMENT '审批说明',
  `approve_datetime` datetime DEFAULT NULL COMMENT '审批时间',
  `pay_user` varchar(255) DEFAULT NULL COMMENT '支付回录人',
  `pay_note` varchar(255) DEFAULT NULL COMMENT '支付回录说明',
  `pay_fee` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '支付渠道手续费',
  `pay_datetime` datetime DEFAULT NULL COMMENT '支付回录时间',
  PRIMARY KEY (`id`),
  KEY `index2` (`status`),
  KEY `index3` (`currency`),
  KEY `index4` (`apply_datetime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='取现订单';

-- ----------------------------
-- Table structure for tstd_withdraw_rule
-- ----------------------------
DROP TABLE IF EXISTS `tstd_withdraw_rule`;
CREATE TABLE `tstd_withdraw_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kind` varchar(32) NOT NULL COMMENT '针对对象',
  `type` varchar(32) NOT NULL COMMENT '类型（转账transfer 取现withdraw）',
  `name` varchar(32) NOT NULL COMMENT '规则名称',
  `symbol` varchar(32) NOT NULL COMMENT '币种',
  `withdraw_min` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '最小取现金额',
  `withdraw_max` decimal(18,2) DEFAULT NULL COMMENT '单笔最大提币量',
  `withdraw_step` decimal(18,2) DEFAULT NULL COMMENT '提币步长',
  `withdraw_limit` decimal(18,2) DEFAULT NULL COMMENT '每人每日提现额度',
  `withdraw_fee_take_location` varchar(8) NOT NULL DEFAULT '0' COMMENT '取现手续费扣除位置: 0取现金额中 1余额中',
  `withdraw_fee_type` varchar(8) NOT NULL DEFAULT '0' COMMENT '手续费类型 0=绝对值 1=百分比',
  `withdraw_fee` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '取现手续费',
  `approve_flag` varchar(32) NOT NULL DEFAULT '1' COMMENT '是否需要审核 0否 1是',
  `withdraw_rule` varchar(255) DEFAULT NULL COMMENT '提币规则',
  `withdraw_min1` decimal(18,2) DEFAULT NULL COMMENT '第一次提现最低金额',
  `withdraw_min2` decimal(18,2) DEFAULT NULL COMMENT '第二次提现最低金额',
  `withdraw_min3` decimal(18,2) DEFAULT NULL COMMENT '第三次开始提现最低金额',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index2` (`kind`,`type`,`symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='取现规则';

-- ----------------------------
-- Table structure for tsys_action
-- ----------------------------
DROP TABLE IF EXISTS `tsys_action`;
CREATE TABLE `tsys_action` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `type` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '类型',
  `name` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '名称',
  `code` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '操作编码',
  `url` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '拦截URL',
  `input` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '入参',
  `output` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '出参',
  `status` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '状态',
  `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='系统接口';

-- ----------------------------
-- Table structure for tsys_article
-- ----------------------------
DROP TABLE IF EXISTS `tsys_article`;
CREATE TABLE `tsys_article` (
  `id` bigint(32) NOT NULL COMMENT '编号',
  `type_id` bigint(32) NOT NULL COMMENT '类型编号',
  `type` varchar(32) NOT NULL COMMENT '文章类型',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图',
  `pic` text COMMENT '缩略图',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '副标题',
  `content_type` varchar(4) NOT NULL DEFAULT '0' COMMENT '内容类型 0:富文本,1:图片',
  `content` text COMMENT '内容',
  `status` varchar(32) NOT NULL COMMENT '状态（0下架 1上架）',
  `order_no` int(11) DEFAULT NULL COMMENT 'UI序号',
  `updater` bigint(32) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(32) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `readNumber` bigint(32) unsigned DEFAULT '0' COMMENT '浏览次数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='文章';

-- ----------------------------
-- Table structure for tsys_article_type
-- ----------------------------
DROP TABLE IF EXISTS `tsys_article_type`;
CREATE TABLE `tsys_article_type` (
  `id` bigint(20) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `status` varchar(32) NOT NULL COMMENT '状态（0下架 1上架）',
  `order_no` int(11) DEFAULT NULL COMMENT 'UI序号',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(32) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` text COMMENT '备注',
  `location` varchar(5) NOT NULL COMMENT '位置',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文章类型';

-- ----------------------------
-- Table structure for tsys_cnavigate
-- ----------------------------
DROP TABLE IF EXISTS `tsys_cnavigate`;
CREATE TABLE `tsys_cnavigate` (
  `id` bigint(20) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '名称',
  `type` varchar(32) DEFAULT NULL COMMENT '类型',
  `url` varchar(255) DEFAULT NULL COMMENT '访问Url',
  `pic` varchar(255) NOT NULL COMMENT '图片',
  `status` varchar(4) NOT NULL COMMENT '状态(1 已发布,0待发布,2已下架)',
  `location` varchar(32) DEFAULT NULL COMMENT '位置',
  `action` varchar(32) DEFAULT NULL COMMENT '动作（0不能点击 1跳转至外链 2跳转本系统）',
  `group_name` varchar(32) DEFAULT NULL COMMENT '分组',
  `order_no` int(11) DEFAULT NULL COMMENT '相对位置编号',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父编号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `content` text COMMENT '存放内容 动作 不能点击 就非必填，点击 就需要填',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='导航';

-- ----------------------------
-- Table structure for tsys_config
-- ----------------------------
DROP TABLE IF EXISTS `tsys_config`;
CREATE TABLE `tsys_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) COLLATE utf8_bin NOT NULL,
  `key` varchar(255) COLLATE utf8_bin NOT NULL,
  `value` longtext COLLATE utf8_bin NOT NULL,
  `updater` varchar(255) COLLATE utf8_bin NOT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `show_type` varchar(32) COLLATE utf8_bin DEFAULT NULL,
  `is_show` varchar(2) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='系统参数';

-- ----------------------------
-- Table structure for tsys_cuser
-- ----------------------------
DROP TABLE IF EXISTS `tsys_cuser`;
CREATE TABLE `tsys_cuser` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `willing_value` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '愿力值',
  `member_flag` varchar(32) DEFAULT NULL COMMENT '会员身份',
  `vip_flag` varchar(255) DEFAULT NULL COMMENT '套餐身份',
  `level` varchar(8) DEFAULT NULL COMMENT '用户等级',
  `openid` varchar(32) DEFAULT NULL COMMENT '微信小程序openid',
  `agent_status` varchar(8) DEFAULT NULL COMMENT '合作状态dict={"0":"合伙中","1":"已解除"}',
  `agent_join_datetime` datetime DEFAULT NULL COMMENT '加盟时间',
  `agent_quit_datetime` datetime DEFAULT NULL COMMENT '解除时间',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
  `recovery_time` timestamp NULL DEFAULT NULL COMMENT '恢复时间',
  `negation_comment_decrease_status` varchar(4) DEFAULT '0' COMMENT '差评扣减状态 1-开启 0-关闭',
  `authentication_status` varchar(4) NOT NULL DEFAULT '0' COMMENT '实名认证状态 dict={"-1":"认证不通过","0":"未认证","1":"认证中","2":"认证通过"}',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `USERID` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='C端用户';

-- ----------------------------
-- Table structure for tsys_dict
-- ----------------------------
DROP TABLE IF EXISTS `tsys_dict`;
CREATE TABLE `tsys_dict` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) DEFAULT NULL COMMENT '类型（0父类 1子类）',
  `parent_key` varchar(255) DEFAULT NULL COMMENT '父亲key',
  `key` varchar(255) DEFAULT NULL COMMENT 'key',
  `value` varchar(255) DEFAULT NULL COMMENT 'value',
  `group_no` int(11) DEFAULT NULL COMMENT '组号',
  `order_no` int(11) DEFAULT NULL COMMENT '序号',
  `updater` varchar(32) DEFAULT NULL COMMENT '最近修改人',
  `update_datetime` datetime DEFAULT NULL COMMENT '最近修改人',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据字典';

-- ----------------------------
-- Table structure for tsys_group
-- ----------------------------
DROP TABLE IF EXISTS `tsys_group`;
CREATE TABLE `tsys_group` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父编号',
  `kind` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '类型',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '名称',
  `order_no` int(11) DEFAULT NULL COMMENT '展示顺序',
  `creater` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '创建人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户组';

-- ----------------------------
-- Table structure for tsys_group_role
-- ----------------------------
DROP TABLE IF EXISTS `tsys_group_role`;
CREATE TABLE `tsys_group_role` (
  `group_id` bigint(20) NOT NULL COMMENT '用户组编号',
  `role_id` bigint(20) NOT NULL COMMENT '角色编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户组角色关联表';

-- ----------------------------
-- Table structure for tsys_language_resource
-- ----------------------------
DROP TABLE IF EXISTS `tsys_language_resource`;
CREATE TABLE `tsys_language_resource` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table` varchar(255) NOT NULL COMMENT '表名',
  `ref_id` varchar(255) NOT NULL COMMENT '记录编号',
  `column` varchar(255) NOT NULL COMMENT '列名',
  `zn_data` text COMMENT '中文数据',
  `en_data` text COMMENT '英文数据',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index2` (`table`,`column`,`ref_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='语言资源';

-- ----------------------------
-- Table structure for tsys_menu
-- ----------------------------
DROP TABLE IF EXISTS `tsys_menu`;
CREATE TABLE `tsys_menu` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父编号',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '类型（菜单/按钮）',
  `kind` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '端',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '名称',
  `logo` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'logo',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'url',
  `order_no` bigint(20) DEFAULT NULL COMMENT '序号',
  `location` varchar(8) DEFAULT '0' COMMENT '展示位置',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='菜单';

-- ----------------------------
-- Table structure for tsys_menu_action
-- ----------------------------
DROP TABLE IF EXISTS `tsys_menu_action`;
CREATE TABLE `tsys_menu_action` (
  `menu_id` bigint(20) NOT NULL COMMENT '菜单编号',
  `action_id` bigint(20) NOT NULL COMMENT '资源编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='菜单资源关联表';

-- ----------------------------
-- Table structure for tsys_notifier
-- ----------------------------
DROP TABLE IF EXISTS `tsys_notifier`;
CREATE TABLE `tsys_notifier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(8) NOT NULL COMMENT '类型: 0提币通知人',
  `start_date` tinyint(3) unsigned NOT NULL COMMENT '开始时间0-23',
  `end_date` tinyint(3) unsigned NOT NULL COMMENT '结束时间0-23',
  `name` varchar(32) NOT NULL COMMENT '姓名',
  `phone` varchar(32) DEFAULT NULL COMMENT '手机号码',
  `email` text COMMENT '邮箱',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='通知人';

-- ----------------------------
-- Table structure for tsys_permission_role
-- ----------------------------
DROP TABLE IF EXISTS `tsys_permission_role`;
CREATE TABLE `tsys_permission_role` (
  `role_id` bigint(20) NOT NULL COMMENT '角色编号',
  `resource_id` bigint(20) NOT NULL COMMENT '资源编号',
  `resource_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '资源类型',
  UNIQUE KEY `index1` (`role_id`,`resource_id`,`resource_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色权限关联表';

-- ----------------------------
-- Table structure for tsys_role
-- ----------------------------
DROP TABLE IF EXISTS `tsys_role`;
CREATE TABLE `tsys_role` (
  `id` bigint(20) NOT NULL,
  `name` char(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `kind` char(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `creator` char(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `updater` char(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `remark` char(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `company_id` bigint(20) DEFAULT NULL,
  `alias` char(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `is_default` char(8) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色';

-- ----------------------------
-- Table structure for tsys_user
-- ----------------------------
DROP TABLE IF EXISTS `tsys_user`;
CREATE TABLE `tsys_user` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `kind` varchar(32) NOT NULL COMMENT '类型',
  `login_name` varchar(32) NOT NULL COMMENT '登录名称',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mobile` varchar(32) DEFAULT NULL COMMENT '手机号',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
  `photo` varchar(1024) DEFAULT NULL COMMENT '头像',
  `sex` varchar(8) DEFAULT NULL COMMENT '性别',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `id_kind` varchar(4) DEFAULT NULL COMMENT '证件类型 dict={"1":"身份证","2":"护照"}',
  `id_no` varchar(64) DEFAULT NULL COMMENT '证件号码',
  `real_name` varchar(32) DEFAULT NULL COMMENT '真实名称',
  `province` varchar(32) DEFAULT NULL COMMENT '省',
  `city` varchar(32) DEFAULT NULL COMMENT '市',
  `area` varchar(32) DEFAULT NULL COMMENT '区',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `willing_value` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '愿力值余额',
  `login_pwd` varchar(255) NOT NULL COMMENT '登录密码',
  `login_pwd_strength` varchar(4) NOT NULL COMMENT '登录密码强度',
  `salt` varchar(6) DEFAULT NULL COMMENT '干扰码',
  `trade_pwd` varchar(255) DEFAULT NULL COMMENT '支付密码',
  `trade_pwd_strength` varchar(4) DEFAULT NULL COMMENT '支付密码强度',
  `status` varchar(32) NOT NULL COMMENT '状态',
  `register_datetime` datetime NOT NULL COMMENT '创建时间',
  `register_ip` varchar(32) DEFAULT NULL COMMENT '注册IP',
  `invite_no` int(11) NOT NULL AUTO_INCREMENT COMMENT '邀请码',
  `user_referee` bigint(20) DEFAULT NULL COMMENT '推荐人',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `last_login_datetime` datetime DEFAULT NULL COMMENT '最后登录时间',
  `updater` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updater_name` varchar(32) DEFAULT NULL COMMENT '更新人名称',
  `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
  `company_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '公司编号',
  `channel_id` bigint(20) DEFAULT NULL,
  `channel_grade` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invite_no_UNIQUE` (`invite_no`),
  UNIQUE KEY `index1` (`kind`,`mobile`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户';

-- ----------------------------
-- Table structure for tsys_user_group
-- ----------------------------
DROP TABLE IF EXISTS `tsys_user_group`;
CREATE TABLE `tsys_user_group` (
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `group_id` bigint(20) NOT NULL COMMENT '用户组编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户用户组管理表';

-- ----------------------------
-- Table structure for tsys_user_log
-- ----------------------------
DROP TABLE IF EXISTS `tsys_user_log`;
CREATE TABLE `tsys_user_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
  `user_id` varchar(32) NOT NULL COMMENT '用户编号',
  `user_name` varchar(64) NOT NULL COMMENT '用户名称',
  `content` varchar(255) NOT NULL COMMENT '内容',
  `type` varchar(32) NOT NULL COMMENT '分类',
  `type_note` varchar(32) NOT NULL COMMENT '类型说明',
  `ip` varchar(255) DEFAULT NULL COMMENT 'ip',
  `client` varchar(7) DEFAULT NULL COMMENT '客户端（1Web 2Android 3IOS）',
  `location` varchar(255) DEFAULT NULL COMMENT '操作时定位',
  `create_datetime` datetime DEFAULT NULL COMMENT '创建时间时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='用户日志';

-- ----------------------------
-- Table structure for tsys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `tsys_user_role`;
CREATE TABLE `tsys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户编号',
  `role_id` bigint(20) NOT NULL COMMENT '角色编号',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户角色关联表';

SET FOREIGN_KEY_CHECKS = 1;
