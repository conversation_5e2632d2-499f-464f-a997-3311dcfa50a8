54#!/usr/bin/expect

set project_name "czzmsg-core"
set jar_name "czzmsg-core-1.0.0-SNAPSHOT.jar"

set host_ip "************"
set host_pwd "hO8aT6CCCqQ2"

set remote_path "/mnt/www/czzmsg"

######## 1.传输文件至服务器根目录 ########
spawn scp ../../target/${jar_name} root@${host_ip}:~/
expect "*password:"
send "${host_pwd}\r"
set timeout 300
send "exit\r"
expect eof

######## 2.登录服务器 ########
spawn ssh root@${host_ip}
expect "*password:"
send "${host_pwd}\r"

######## 4.杀掉这个tomcat相关进程 ########
expect "*#"
#send "ps -efww|grep -w ${jar_name}|grep -v grep|cut -c 9-15|xargs kill -9\r"
send "ps -efww | grep -w ${jar_name} | grep -v grep | awk '{print \$2}' | xargs kill -9\r"


######## 5.删除原来的部署包 ########
expect "*#"
send "rm -rf ${remote_path}/${jar_name}\r"

######## 5.移动新包至tomcat ########
expect "*#"
send "mv ~/${jar_name} ${remote_path}/\r"

######## 6.重启tomcat ########
expect "*#"
send "nohup java -jar ${remote_path}/${jar_name} >> ${remote_path}/logs/`date +%Y-%m-%d`.log 2>&1 &\r"

expect "*#"
send "exit\r"








