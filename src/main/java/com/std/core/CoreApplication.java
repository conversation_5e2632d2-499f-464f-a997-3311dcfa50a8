package com.std.core;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiSort;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/5/17 7:00 下午
 */
@SpringBootApplication(exclude = {FreeMarkerAutoConfiguration.class})
@EnableAsync
@EnableEurekaClient
@EnableFeignClients
@EnableScheduling
@RestController
@Api(value = "系统版本", tags = "0、系统版本")
@ApiSort(0)
@MapperScan("com.std.core.mapper")
@ComponentScan(basePackages = {"com.std.core", "com.std.common"})
public class CoreApplication {

    private static Logger logger = LoggerFactory.getLogger(CoreApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(CoreApplication.class, args);
    }

    @RequestMapping(value = "/", method = RequestMethod.GET)
    public void apiVersion(HttpServletResponse response) throws IOException {
        logger.info("Successful Deployment");
        PrintWriter writer = response.getWriter();
        writer.append("Version:1.1.0 \n");
        writer.append("Description:czzmsg-core 20250716 \n");
        writer.flush();
    }
}
