package com.std.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:40
 */
@Data
@Component
public class AliOSSConfig {

    @Value("${oss.endpoint}")
    private String endpoint;
    @Value("${oss.accessKeyId}")
    private String accessKeyId;
    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;
    @Value("${oss.roleArn}")
    private String roleArn;
    @Value("${oss.bucket}")
    private String bucket;

    @Value("${oss.bucket.endpoint}")
    private String bucketEndpoint;

    @Value("${oss.bucket.ossEndpoint}")
    private String bucketOssEndpoint;

    @Value("${oss.bucket.filePath}")
    private String bucketFilePath;


}
