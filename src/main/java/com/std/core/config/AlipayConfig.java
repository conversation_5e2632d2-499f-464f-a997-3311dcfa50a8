package com.std.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:40
 */
@Data
@Component
public class AlipayConfig {

    @Value("${alipay.appid}")
    private String appid;

    @Value("${alipay.privatekey}")
    private String privatekey;

    @Value("${alipay.publickey}")
    private String publickey;

    @Value("${alipay.notifyurl}")
    private String notifyurl;

    @Value("${alipay.returnurl}")
    private String returnurl;

    @Value("${alipay.signtype}")
    private String signtype;

    @Value("${alipay.gateway}")
    private String gateway;

    @Value("${alipay.format}")
    private String format;

    @Value("${alipay.charset}")
    private String charset;

    @Value("${alipay.providerid}")
    private String providerid;
}
