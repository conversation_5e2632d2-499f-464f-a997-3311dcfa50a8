package com.std.core.config;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.JWTUtil;
import com.std.common.utils.JsonUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.pojo.domain.OperatorLog;
import com.std.core.util.RedisUtil;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> zhoudong
 */
@Slf4j
@Service
public class PiceaInterceptor implements HandlerInterceptor {


    private static RedisUtil redisService;

    @Autowired
    public void setRedisService(RedisUtil redisService) {
        PiceaInterceptor.redisService = redisService;
    }
//
//    private static IOperatorLogService operatorLogService;
//
//    @Autowired
//    public void setOperatorLogService(IOperatorLogService operatorLogService) {
//        PiceaInterceptor.operatorLogService = operatorLogService;
//    }

//    private static IOperationLogService operationLogService;
//
//    @Autowired
//    public void setOperationLogService(IOperationLogService operationLogService) {
//        PiceaInterceptor.operationLogService = operationLogService;
//    }

    /**
     * 在DispatcherServlet之前执行
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {


        // 检查Content-Type是否表明请求体是文本类型，如果是文件上传则不处理
        String contentType = request.getContentType();
        if (contentType != null && (contentType.startsWith("text/") || contentType.startsWith("application/json"))) {
            // 读取原始请求体
            String body = "";
            try {
                InputStream originalStream = request.getInputStream();
                body = new String(StreamUtils.copyToByteArray(originalStream), StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("拦截器异常：{}", e);
            }

            // 判断emoji
            if (containsEmoji(body)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"输入文字含有非法字符");
            }
        }


        String ip = getRemoteHost(request);
        String token = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(token)) {
            insertOperatorLog(request, ip, token);
        }
//        else if (request.getRequestURI().contains("permission_none/sms_code")){
//
//            try {
//                //获取请求body
//                byte[] bodyBytes = StreamUtils.copyToByteArray(request.getInputStream());
//                String body = new String(bodyBytes, request.getCharacterEncoding());
//                log.info("短信调用：{}",body);
//            }catch (Exception e){
//                // 报错异常暂不处理
//            }
//        }

//        if (handler instanceof HandlerMethod) {
//
//            HandlerMethod hm = (HandlerMethod) handler;
//            AccessLimit accessLimit = hm.getMethodAnnotation(AccessLimit.class);
//            if (accessLimit == null) {
//                return true;
//            }
//            int seconds = accessLimit.seconds();
//            int maxCount = accessLimit.maxCount();
//
//            // uuid规则判断
////            String uuid = request.getHeader("uuid");
////            if (StringUtils.isBlank(uuid)) {
////                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请求未携带uuid参数");
////            }
////            if (!checkUUID(uuid)) {
////                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请求uuid不合规");
////            }
////            //判断uuid请求的次数
////            String md5Uuid = MD5Util.md5(uuid);
////            Object count = redisService.get(md5Uuid);
////            if (count == null) {
////                redisService.set(md5Uuid, 1, seconds);
////            } else if (Integer.parseInt(count.toString()) < maxCount) {
////                redisService.incr(md5Uuid, 1);
////            } else {
////                // 否则直接异常
////                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请求太过频繁，请稍后再试哦!");
////            }
//        }

        return true;
    }

    private void insertOperatorLog(HttpServletRequest request, String ip, String token) {
        try {
            //请求用户
            Long userInfo = JWTUtil.getUserInfo(token);


            // 检查 Content-Type 是否为 multipart/form-data
            String contentType = request.getContentType();
            boolean isMultipart = contentType != null && contentType.toLowerCase().startsWith("multipart/");

            OperatorLog data = new OperatorLog();
            data.setUserId(userInfo);
            data.setOperateTime(new Date());
            data.setOperateTs(data.getOperateTime().getTime());
            data.setIp(ip);
            String client = request.getHeader("client");
            data.setUserAgent(client);
//            String uuid = request.getHeader("uuid");
//            if (StringUtils.isNotBlank(uuid)) {
//                data.setUuid(uuid);
//            }
            data.setUrl(request.getRequestURI());
            // 只在 Content-Type 不是 multipart/form-data 时记录请求体
            if (!isMultipart) {
                //获取请求body
                byte[] bodyBytes = StreamUtils.copyToByteArray(request.getInputStream());
                String body = new String(bodyBytes, request.getCharacterEncoding());
                // 过滤emoji
                body = EmojiParser.removeAllEmojis(body);
                data.setPost(body);

            } else {
                data.setPost("[Multipart Form Data]");
            }
            log.info("请求[{}]", JsonUtil.getJson(data));

//            operatorLogService.create(data);


        } catch (Exception e) {
            e.printStackTrace();
            log.error("接口日志调用失败");
        }
    }

    /**
     * 获取IP地址
     */
    private String getRemoteHost(ServletRequest request) {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        String ip = httpServletRequest.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = httpServletRequest.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = httpServletRequest.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        /*
         * if (ip.indexOf(",") >= 0) // 存在逗号，有多个IP，取最后一个 { ip =
         * ip.substring(ip.indexOf(",") + 1); }
         */
        if (ip.indexOf(",") >= 0) // 存在逗号，有多个IP，取前面一个
        {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
    }

    /**
     * 解析UUID规则是否匹配
     *
     * <AUTHOR>
     */
    private static boolean checkUUID(String uuid) {
        String keywordDict = "!abcEQ@#$%&*";
        int posArray[] = {2, 8, 13, 19, 20, 25, 29, 31};
        for (int i = 0; i < posArray.length; i++) {
            int pos = posArray[i];
            if (!keywordDict.contains(String.valueOf(uuid.charAt(pos)))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 在controller执行之后的DispatcherServlet之后执行
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView)
            throws Exception {
//        System.out.println("在controller执行之后的DispatcherServlet之后执行");
    }

    /**
     * 在页面渲染完成返回给客户端之前执行
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//        System.out.println("在页面渲染完成返回给客户端之前执行");
    }


    // 判断字符串中是否包含Emoji字符
    public static boolean containsEmoji(String input) {
        // Emoji字符的编码范围
        Pattern pattern = Pattern.compile("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]");
        Matcher matcher = pattern.matcher(input);
        return matcher.find();
    }

    public static void main(String[] args) {
        String str1 = "Hello World! 😊";
        String str2 = "Hello World!";
        String a = "最好的矿泉水\uD83E\uDEE5";
        System.out.println(containsEmoji(str1)); // 输出：true
        System.out.println(containsEmoji(str2)); // 输出：false
        System.out.println(containsEmoji(a)); // 输出：true

    }

}
