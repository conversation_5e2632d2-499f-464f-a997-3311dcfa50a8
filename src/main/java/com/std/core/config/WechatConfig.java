package com.std.core.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:40
 */
@Data
@Component
public class WechatConfig {

    @Value("${wechat.backurl}")
    private String backurl;

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.appSecret}")
    private String appSecret;

    @Value("${wechat.merchant.id}")
    private String merchantId;

    //    @Value("${wechat.merchant.name}")
    //写在配置文件会出现乱码问题
    private String merchantName = "西溪蔡志忠美术馆";

    @Value("${wechat.merchant.privatekey}")
    private String merchantPrivateKey;

    @Value("${wechat.refundurl}")
    private String refundUrl;

    @Value("${system.code}")
    private String systemCode;


}
