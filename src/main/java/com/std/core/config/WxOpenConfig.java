package com.std.core.config;

import lombok.Data;
import me.chanjar.weixin.mp.api.WxMpInMemoryConfigStorage;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create
 */
@Configuration
@Data
public class WxOpenConfig {
    /**
     * appid
     */
    @Value("${wx.open.config.appid}")
    private String appid;

    /**
     * secret
     */
    @Value("${wx.open.config.secret}")
    private String secret;

    /**
     * token
     */
    @Value("${wx.open.config.token}")
    private String token;


    @Value("${wx.open.wechat.merchant.id}")
    private String merchantId;

    @Value("${wx.open.wechat.merchant.v2.privatekey}")
    private String merchantPrivateKey;

    @Value("${wx.open.wechat.templateId}")
    private String templateId;

    @Value("${wx.open.wechat.activity.templateId}")
    private String activityTemplateId;



    @Bean
    public WxMpService wxMpService() {
        WxMpService service = new WxMpServiceImpl();
        WxMpInMemoryConfigStorage configStorage = new WxMpInMemoryConfigStorage();
        configStorage.setAppId(appid);
        configStorage.setSecret(secret);
        configStorage.setToken(token);
        service.setWxMpConfigStorage(configStorage);
        return service;
    }

}
