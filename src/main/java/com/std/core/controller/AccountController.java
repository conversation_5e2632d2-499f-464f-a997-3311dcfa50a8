package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EBoolean;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EJourBizTypeUser.AssetBiz;
import com.std.core.enums.ESystemAccount;
import com.std.core.enums.ESystemAccount.BIZ;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.AccountAmountSumListRes;
import com.std.core.service.IAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 账户Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@ApiVersion(1)
@RestController
@Api(value = "账户管理", tags = "10、账户管理", position = 11)
@RequestMapping("{version}/account")
@Slf4j
public class AccountController extends BaseController {

    @Resource
    private IAccountService accountService;

    @ApiOperation(value = "oss:手动加减账户金额")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/change_account")
    public Result changeAccount(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AccountManualChangeReq request,
                                @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);
        accountService.changeAccount(operator, request, ip, AssetBiz.ManualChange);

        return new Result();
    }

    @ApiOperation(value = "oss:手动加减空投账户金额")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/change_corp_account")
    public Result changeCorpAccount(@RequestHeader(value = "Authorization") String token,
                                    @RequestBody @Valid AccountManualCorpChangeReq request,
                                    @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);
        accountService.changeCorpAccount(operator, request, ip);

        return new Result();
    }

    @ApiOperation(value = "查询账户对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<Account> detail(@RequestHeader(value = "Authorization") String token,
                                  @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(accountService.getAccountById(id));
    }

    @ApiOperation(value = "front:根据编号查询账户对象", position = 60)
    @PostMapping("/detailByAccountNumber")
    public Result<Account> detail(@RequestHeader(value = "Authorization") String token,
                                  @RequestBody @Valid AccountDetailByNumberReq request) {
        User operator = getUserByToken(token);

        return new Result<>(accountService.getAccount(request.getAccountNumber()));
    }

    @ApiOperation(value = "front:根据用户查询账户对象", position = 61)
    @PostMapping("/detailByUser")
    public Result<Account> detail(@RequestHeader(value = "Authorization") String token,
                                  @RequestBody @Valid AccountDetailByUserReq request) {
        User operator = getUserByToken(token);

        return new Result<>(accountService.getAccountFront(operator.getId(), request.getCurrency(), request.getWithdrawAmountFlag()));
    }

    @ApiOperation(value = "oss-根据用户信息查询账户对象", position = 61)
    @PostMapping("/ossFindUserAccount")
    public Result<Account> ossFindUserAccount(@RequestHeader(value = "Authorization") String token,
                                              @RequestBody @Valid AccountDetailByOssReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(accountService.getAccount(request.getUserId(), request.getCurrency()));
    }

    @ApiOperation(value = "分页条件查询账户", position = 70)
    @PostMapping(value = "/page")
    public Result<PageInfo<Account>> page(@RequestHeader(value = "Authorization") String token,
                                          @RequestBody @Valid AccountPageReq request) {
        User operator = getUserByToken(token);

        if (StringUtils.isBlank(request.getSort())) {
            request.setSort("accountNumber");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Account.class));

        return PageUtil.pageResult(accountService.page(request));
    }

//    @ApiOperation(value = "分页条件查询账户", position = 70)
//    @PostMapping(value = "/yao_page")
//    public Result<PageInfo<Account>> yaoPage(@RequestHeader(value = "Authorization") String token,
//                                             @RequestBody @Valid AccountPageReq request) {
//        User operator = getUserByToken(token);
//
//        if (StringUtils.isBlank(request.getSort())) {
//            request.setSort("accountNumber");
//        }
//
//        List<String> currencyList = new ArrayList<>();
//        currencyList.add(ECurrency.YINYAO.getCode());
//        currencyList.add(ECurrency.YANGYAO.getCode());
//        currencyList.add(ECurrency.QIAN.getCode());
//        currencyList.add(ECurrency.KUN.getCode());
//        currencyList.add(ECurrency.XUN.getCode());
//        currencyList.add(ECurrency.KAN.getCode());
//        currencyList.add(ECurrency.GEN.getCode());
//        currencyList.add(ECurrency.ZHEN.getCode());
//        currencyList.add(ECurrency.LI.getCode());
//        currencyList.add(ECurrency.DUI.getCode());
//        request.setCurrencyList(currencyList);
//
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//                SqlUtil.parseSort(request.getSort(), Account.class));
//
//        return PageUtil.pageResult(accountService.page(request));
//    }

//    @ApiOperation(value = "分页条件查询账户", position = 70)
//    @PostMapping(value = "/shell_page")
//    public Result<PageInfo<Account>> shellPage(@RequestHeader(value = "Authorization") String token,
//                                          @RequestBody @Valid AccountPageReq request) {
//        User operator = getUserByToken(token);
//
//        if (StringUtils.isBlank(request.getSort())) {
//            request.setSort("accountNumber");
//        }
//
//        request.setCurrency(ECurrency.SHELL.getCode());
//
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//                SqlUtil.parseSort(request.getSort(), Account.class));
//
//        return PageUtil.pageResult(accountService.page(request));
//    }

    @ApiOperation(value = "列表条件查询账户", position = 80)
    @PostMapping(value = "/list")
    public Result<List<Account>> list(@RequestHeader(value = "Authorization") String token,
                                      @RequestBody @Valid AccountListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        List<Account> accountList = accountService.list(request);
        List<Account> accounts = new ArrayList<>();

        return new Result<>(accounts);
    }

    @ApiOperation(value = "查询空投账户", position = 80)
    @PostMapping(value = "/detail_drop_account")
    public Result<Account> detailDropAccount(@RequestHeader(value = "Authorization") String token,
                                             @RequestBody @Valid AccountDropDetailReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        Account account = new Account();
        // 查找用户对应账户


        return new Result<>(account);
    }

    @ApiOperation(value = "front:列表条件查询账户", position = 90)
    @PostMapping(value = "/list_front")
    public Result<List<Account>> listFront(@RequestHeader(value = "Authorization") String token,
                                           @RequestBody @Valid AccountListFrontReq request) {
        User operator = getUserByToken(token);
        List<Account> accountList = new ArrayList<>();



        return new Result<>(accountList);
    }

    @ApiOperation(value = "oss:查询账户金额汇总", position = 90)
    @PostMapping(value = "/amount_sum_list")
    public Result<List<AccountAmountSumListRes>> amountSumList(@RequestHeader(value = "Authorization") String token,
                                                               @RequestBody @Valid AccountAmountSumListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(accountService.amountSumList(request));
    }

//    @ApiOperation(value = "front:积分排行榜", position = 90)
//    @PostMapping(value = "/public/integral_list")
//    public Result<AccountIntegralRankListRes> integralList(@RequestHeader(value = "Authorization", required = false) String token) {
//        User operator = null;
//
//        if (StringUtils.isNotBlank(token)) {
//            operator = getUserByToken(token);
//        }
//        return new Result<>(accountService.integralList(operator));
//    }

//    @ApiOperation(value = "扣除金额", position = 90)
//    @PostMapping(value = "/public/deduct")
//    public Result deductAmount(@RequestHeader(value = "Authorization", required = false) String token) {
//        List<AccountDeductRes> list = accountService.selectAccountDeductList();
//        for (AccountDeductRes accountDeductRes : list) {
//            try {
//                accountService.doDeductAccount(accountDeductRes);
//            } catch (Exception e) {
////                log.error(e.getMessage());
//                log.error(e.getMessage() + " " + accountDeductRes.getId() + " " + accountDeductRes.getMobile());
//            }
//        }
//        return new Result();
//    }


//    @ApiOperation(value = "front:我的原粟账户", position = 90)
//    @PostMapping(value = "/my_meta_millet_account")
//    public Result<AccountDiamondExchangeYaoRes> myMetaMilletAccount(@RequestHeader(value = "Authorization") String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//
//        return new Result<>(accountService.myMetaMilletAccount(operator));
//    }
//
//    @ApiOperation(value = "front:钻石兑换爻", position = 90)
//    @PostMapping(value = "/diamond_exchange_yao")
//    public Result diamondExchangeYao(@RequestHeader(value = "Authorization") String token,
//                                     @RequestBody @Valid AccountDiamondExchangeYaoReq request) {
//        User operator = getUserByToken(token, EUserKind.C);
//        accountService.diamondExchangeYao(operator, request);
//        return new Result();
//    }
//
//
//    @ApiOperation(value = "front:查询我的爻账户", position = 90)
//    @PostMapping(value = "/detail_yao_account")
//    public Result<AccountDetailYaoAccountRes> detailYaoAccount(@RequestHeader(value = "Authorization") String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//
//        return new Result<>(accountService.detailYaoAccount(operator));
//    }
//
//
//    @ApiOperation(value = "分页查询原粟账户", position = 70)
//    @PostMapping(value = "/page_yao_millet_account_oss")
//    public Result<PageInfo<AccountYaoMilletAccountOssPageRes>> pageYaoMilletAccountOss(@RequestHeader(value = "Authorization") String token,
//                                                                                       @RequestBody @Valid AccountYaoMilletAccountOssPageReq request) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//                SqlUtil.parseSort(request.getSort(), Account.class));
//
//        return PageUtil.pageResult(accountService.pageYaoMilletAccountOss(request));
//    }
}