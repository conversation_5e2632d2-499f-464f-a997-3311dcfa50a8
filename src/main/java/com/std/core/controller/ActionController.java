package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.request.ActionCreateReq;
import com.std.core.pojo.request.ActionListReq;
import com.std.core.pojo.request.ActionModifyReq;
import com.std.core.pojo.request.ActionPageReq;
import com.std.core.service.IActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import java.util.List;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 15:48
 */
@ApiVersion(1)
@RestController
@Api(value = "资源管理", tags = "5、资源管理")
@ApiSort(50)
@RequestMapping(value = "{version}/action")
public class ActionController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(ActionController.class);

    @Autowired
    private IActionService actionService;

    @ApiOperation(value = "创建资源")
    @PostMapping(value = "/create")
    public Result create(@RequestBody @Valid ActionCreateReq request) {
        actionService.create(request);

        return new Result();
    }

    @ApiOperation(value = "修改资源")
    @PostMapping(value = "/modify")
    public Result modify(@RequestBody @Valid ActionModifyReq request) {
        actionService.modify(request);

        return new Result();
    }

    @ApiOperation(value = "删除资源")
    @PostMapping(value = "/remove/{id}")
    public Result remove(@PathVariable("id") @Valid Long id) {
        actionService.remove(id);
        return new Result();
    }

    @ApiOperation(value = "详细查资源")
    @PostMapping(value = "/detail/{id}")
    public Result<Action> detail(@PathVariable("id") @Valid Long id) {
        return new Result<>(actionService.detail(id));
    }

    @ApiOperation(value = "分页查资源")
    @PostMapping(value = "/page")
    public Result<PageInfo<Action>> page(@RequestBody @Valid ActionPageReq request) {
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Action.class));

        List<Action> list = actionService.page(request);
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "列表查资源")
    @PostMapping(value = "/list")
    public Result<List<Action>> page(@RequestBody @Valid ActionListReq request) {
        return new Result<>(actionService.list(request));
    }
}
