package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.IActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.bouncycastle.cert.ocsp.Req;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@ApiVersion(1)
@RestController
@Api(value = "活动管理", tags = "活动管理")
@RequestMapping("{version}/activity")
public class ActivityController extends BaseController {

    @Resource
    private IActivityService activityService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增活动', NULL, '/core/v1/activity/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增活动")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityCreateReq request) {
        User operator = getUserByToken(token);
        activityService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除活动', NULL, '/core/v1/activity/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除活动")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        activityService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改活动', NULL, '/core/v1/activity/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改活动")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityModifyReq request) {
        User operator = getUserByToken(token);
        activityService.modify(request, operator);

        return new Result();
    }
    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改活动', NULL, '/core/v1/activity/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "活动上下架")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        activityService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询活动', NULL, '/core/v1/activity/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询活动")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<Activity> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询活动', NULL, '/core/v1/activity/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询活动")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Activity>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(activityService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询活动', NULL, '/core/v1/activity/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询活动")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<Activity>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询活动', NULL, '/core/v1/activity/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询活动")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_front/{id}")
    public Result<ActivityDetailRes> detailFront( @PathVariable("id") @Valid Long id) {

        return new Result<>(activityService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询活动', NULL, '/core/v1/activity/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询活动")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<ActivityPageRes>> pageFront( @RequestBody @Valid ActivityPageFrontReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), Activity.class));

        return PageUtil.pageResult(activityService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询活动', NULL, '/core/v1/activity/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询活动")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front")
    public Result<List<ActivityListRes>> listFront( @RequestBody @Valid ActivityListFrontReq request) {

        return new Result<>(activityService.listFront(request));
    }
    //    List<OrderTimeRes> orderTime(Long id);
   @ApiOperation(value = "前端列表条件查询活动")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/order_time/{id}")
    public Result<List<OrderTimeRes>> orderTime(@PathVariable("id") @Valid Long id) {

        return new Result<>(activityService.orderTime(id));
    }

    @ApiOperation(value = "活动排序")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/sort")
    public Result orderTime(@RequestBody @Valid SortReq request) {
        activityService.sort(request);
        return new Result();
    }

    @ApiOperation(value = "前端详情查询活动")
    @ApiOperationSupport(order = 70)
    @PostMapping("/can_order")
    public Result<CanOrderRes> canOrder(@RequestHeader(value = "Authorization") String token,@PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityService.canOrder(id,operator));
    }
}