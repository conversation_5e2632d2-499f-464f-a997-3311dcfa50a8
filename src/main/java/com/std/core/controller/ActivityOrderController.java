package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EErrorCode;
import com.std.core.pojo.domain.ActivityOrder;
import com.std.core.pojo.domain.ActivityTicketLine;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.IActivityOrderService;
import com.std.core.service.IActivityTicketLineService;
import com.std.core.util.DateUtil;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动预约单Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@ApiVersion(1)
@RestController
@Api(value = "活动预约单管理", tags = "活动预约单管理")
@RequestMapping("{version}/activity_order")
public class ActivityOrderController extends BaseController {

    @Resource
    private IActivityOrderService activityOrderService;
    @Resource
    private IActivityTicketLineService activityTicketLineService;


    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增活动预约单', NULL, '/core/v1/activity_order/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增活动预约单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<OrderPayRes> create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderCreateReq request) {
        User operator = getUserByToken(token);

        ActivityTicketLine activityTicketLine = activityTicketLineService.detail(request.getTicketLineId());
        // 预约时间
        String orderDate = null;
        try {
            orderDate = DateUtil.dateToStr(DateUtil.strToDate(request.getDate(), DateUtil.DATA_TIME_PATTERN_9), DateUtil.DATA_TIME_PATTERN_9);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择正确的预约时间");
        }
        if (StringUtils.isBlank(orderDate)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择正确的预约时间");
        }
        String lockId = "activity_order_create:" + orderDate + "-" + activityTicketLine.getActivityId();
        Long startTime = System.currentTimeMillis();

        Long time = startTime + metaLockTimeout;
        boolean lockAcquired = false;

        while (!(lockAcquired = redisLock.lock(lockId, String.valueOf(time)))
                && System.currentTimeMillis() - startTime <= metaLockTimeout) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买失败，请联系客服");
            }
        }
        // 如果未成功获取锁，抛出异常
        if (!lockAcquired) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "网络超时，请稍后重试");
        }
        try {
            return new Result<>(activityOrderService.create(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除活动预约单', NULL, '/core/v1/activity_order/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除活动预约单")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        activityOrderService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改活动预约单', NULL, '/core/v1/activity_order/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改活动预约单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderModifyReq request) {
        User operator = getUserByToken(token);
        activityOrderService.modify(request, operator);

        return new Result();
    }


    @ApiOperation(value = "修改活动预约日期")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_date")
    public Result modifyDate(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderModifyDateReq request) {
        User operator = getUserByToken(token);

        ActivityOrder activityOrder = activityOrderService.detail(request.getId());
        // 预约时间
        String orderDate = null;
        try {
            orderDate = DateUtil.dateToStr(DateUtil.strToDate(request.getDate(), DateUtil.DATA_TIME_PATTERN_9), DateUtil.DATA_TIME_PATTERN_9);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择正确的预约时间");
        }
        String lockId = "activity_order_create:" + orderDate + "-" + activityOrder.getActivityId();
        Long startTime = System.currentTimeMillis();

        Long time = startTime + metaLockTimeout;
        boolean lockAcquired = false;

        while (!(lockAcquired = redisLock.lock(lockId, String.valueOf(time)))
                && System.currentTimeMillis() - startTime <= metaLockTimeout) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "修改失败，请联系客服");
            }
        }
        // 如果未成功获取锁，抛出异常
        if (!lockAcquired) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "网络超时，请稍后重试");
        }
        try {
            activityOrderService.modifyDate(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }


        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询活动预约单', NULL, '/core/v1/activity_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询活动预约单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ActivityOrder> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.detail(id));
    }

    @ApiOperation(value = "取消预约单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/cancleOrder/{id}")
    public Result cancleOrder(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        String lockId = "cancleOrder:"+id;
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"操作进行中");
        }
        try {
            activityOrderService.cancleOrder(id, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }


        return new Result();
    }
    @ApiOperation(value = "取消预约单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/cancleOrderByOss/{id}")
    public Result cancleOrderByOss(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        String lockId = "cancleOrder:"+id;
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"操作进行中");
        }
        try {
            activityOrderService.cancleOrderByOss(id, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询活动预约单', NULL, '/core/v1/activity_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询活动预约单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ActivityOrder>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ActivityOrder.class));

        return PageUtil.pageResult(activityOrderService.page(request));
    }



    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询活动预约单', NULL, '/core/v1/activity_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询活动预约单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ActivityOrder>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.list(request));
    }    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询活动预约单', NULL, '/core/v1/activity_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "收入报表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/income/report")
    public Result<List<IncomeItemRes>> income(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IncomeGroupReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.income(request));
    }

    @ApiOperation(value = "收入报表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/income/total")
    public Result<IncomeRes> incomeTotal(@RequestHeader(value = "Authorization") String token ,@RequestBody @Valid IncomeGroupReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.incomeTotal(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询活动预约单', NULL, '/core/v1/activity_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询活动预约单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ActivityOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询活动预约单', NULL, '/core/v1/activity_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询活动预约单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ActivityOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ActivityOrder.class));

        return PageUtil.pageResult(activityOrderService.pageFront(request));
    }

    @ApiOperation(value = "我的预约单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/my_order")
    public Result<PageInfo<MyOrderRes>> myOrder(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MyOrderReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ActivityOrder.class));

        return PageUtil.pageResult(activityOrderService.myPageFront(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询活动预约单', NULL, '/core/v1/activity_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询活动预约单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ActivityOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderService.listFront(request));
    }

}