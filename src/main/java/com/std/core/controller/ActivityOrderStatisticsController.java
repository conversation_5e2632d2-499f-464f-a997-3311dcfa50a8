package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ActivityOrderStatistics;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ActivityOrderStatisticsCreateReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListFrontReq;
import com.std.core.pojo.request.ActivityOrderStatisticsModifyReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageFrontReq;
import com.std.core.pojo.response.ActivityOrderStatisticsDetailRes;
import com.std.core.pojo.response.ActivityOrderStatisticsListRes;
import com.std.core.pojo.response.ActivityOrderStatisticsPageRes;
import com.std.core.service.IActivityOrderStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动预约统计Controller
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
@ApiVersion(1)
@RestController
@Api(value = "活动预约统计管理", tags = "活动预约统计管理")
@RequestMapping("{version}/activity_order_statistics")
public class ActivityOrderStatisticsController extends BaseController {

    @Resource
    private IActivityOrderStatisticsService activityOrderStatisticsService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增活动预约统计', NULL, '/core/v1/activity_order_statistics/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增活动预约统计")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsCreateReq request) {
        User operator = getUserByToken(token);
        activityOrderStatisticsService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除活动预约统计', NULL, '/core/v1/activity_order_statistics/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除活动预约统计")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        activityOrderStatisticsService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改活动预约统计', NULL, '/core/v1/activity_order_statistics/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改活动预约统计")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsModifyReq request) {
        User operator = getUserByToken(token);
        activityOrderStatisticsService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询活动预约统计', NULL, '/core/v1/activity_order_statistics/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询活动预约统计")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ActivityOrderStatistics> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderStatisticsService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询活动预约统计', NULL, '/core/v1/activity_order_statistics/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询活动预约统计")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ActivityOrderStatistics>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ActivityOrderStatistics.class));

        return PageUtil.pageResult(activityOrderStatisticsService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询活动预约统计', NULL, '/core/v1/activity_order_statistics/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询活动预约统计")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ActivityOrderStatistics>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderStatisticsService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询活动预约统计', NULL, '/core/v1/activity_order_statistics/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询活动预约统计")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ActivityOrderStatisticsDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderStatisticsService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询活动预约统计', NULL, '/core/v1/activity_order_statistics/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询活动预约统计")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ActivityOrderStatisticsPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ActivityOrderStatistics.class));

        return PageUtil.pageResult(activityOrderStatisticsService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询活动预约统计', NULL, '/core/v1/activity_order_statistics/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询活动预约统计")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ActivityOrderStatisticsListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityOrderStatisticsListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityOrderStatisticsService.listFront(request));
    }

}