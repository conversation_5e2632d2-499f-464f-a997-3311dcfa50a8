package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ActivityTicketLine;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.ActivityTicketLineDetailRes;
import com.std.core.pojo.response.ActivityTicketLineListRes;
import com.std.core.pojo.response.ActivityTicketLinePageRes;
import com.std.core.service.IActivityTicketLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 票档Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
@ApiVersion(1)
@RestController
@Api(value = "票档管理", tags = "票档管理")
@RequestMapping("{version}/activity_ticket_line")
public class ActivityTicketLineController extends BaseController {

    @Resource
    private IActivityTicketLineService activityTicketLineService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增票档', NULL, '/core/v1/activity_ticket_line/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增票档")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLineCreateReq request) {
        User operator = getUserByToken(token);
        activityTicketLineService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除票档', NULL, '/core/v1/activity_ticket_line/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除票档")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        activityTicketLineService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改票档', NULL, '/core/v1/activity_ticket_line/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改票档")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLineModifyReq request) {
        User operator = getUserByToken(token);
        activityTicketLineService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "增加票券库存")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/add_stock")
    public Result addStock(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLineAddStockReq request) {
        User operator = getUserByToken(token);
        activityTicketLineService.addStock(request, operator);

        return new Result();
    }
    @ApiOperation(value = "票档上下架")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        activityTicketLineService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询票档', NULL, '/core/v1/activity_ticket_line/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询票档")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ActivityTicketLine> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityTicketLineService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询票档', NULL, '/core/v1/activity_ticket_line/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询票档")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ActivityTicketLine>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLinePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ActivityTicketLine.class));

        return PageUtil.pageResult(activityTicketLineService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询票档', NULL, '/core/v1/activity_ticket_line/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询票档")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ActivityTicketLine>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLineListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityTicketLineService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询票档', NULL, '/core/v1/activity_ticket_line/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询票档")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ActivityTicketLineDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityTicketLineService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询票档', NULL, '/core/v1/activity_ticket_line/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询票档")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ActivityTicketLinePageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityTicketLinePageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ActivityTicketLine.class));

        return PageUtil.pageResult(activityTicketLineService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询票档', NULL, '/core/v1/activity_ticket_line/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询票档")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front")
    public Result<List<ActivityTicketLineListRes>> listFront( @RequestBody @Valid ActivityTicketLineListFrontReq request) {

        return new Result<>(activityTicketLineService.listFront(request));
    }

}