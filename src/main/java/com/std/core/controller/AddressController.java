package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Address;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AddressCreateReq;
import com.std.core.pojo.request.AddressModifyReq;
import com.std.core.pojo.request.AddressPageFrontReq;
import com.std.core.pojo.request.AddressPageReq;
import com.std.core.pojo.response.AddressDetailRes;
import com.std.core.pojo.response.AddressPageRes;
import com.std.core.service.IAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 收货地址Controller
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@ApiVersion(1)
@RestController
@Api(value = "收货地址管理", tags = "收货地址管理")
@RequestMapping("{version}/address")
public class AddressController extends BaseController {

    @Resource
    private IAddressService addressService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增收货地址', NULL, '/core/v1/address/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "H5-新增收货地址")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
                         @RequestBody @Valid AddressCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        addressService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除收货地址', NULL, '/core/v1/address/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "H5-删除收货地址")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        addressService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改收货地址', NULL, '/core/v1/address/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "H5-修改收货地址/设置默认地址")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AddressModifyReq request) {
        User operator = getUserByToken(token);
        addressService.modify(request, operator);

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询收货地址', NULL, '/core/v1/address/detail/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "查询收货地址")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/detail/{id}")
//    public Result<Address> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(addressService.detail(id));
//    }
//
    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询收货地址', NULL, '/core/v1/address/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询收货地址")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Address>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AddressPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), Address.class));

        return PageUtil.pageResult(addressService.page(request));
    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询收货地址', NULL, '/core/v1/address/list', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "列表条件查询收货地址")
//    @ApiOperationSupport(order = 60)
//    @PostMapping(value = "/list")
//    public Result<List<Address>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AddressListReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(addressService.list(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询收货地址', NULL, '/core/v1/address/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "H5-前端详情查询收货地址")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<AddressDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
                                                @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(addressService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询收货地址', NULL, '/core/v1/address/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "H5-前端分页条件查询收货地址")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<AddressPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
                                                      @RequestBody @Valid AddressPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(addressService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询收货地址', NULL, '/core/v1/address/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询收货地址")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<AddressListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AddressListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(addressService.listFront(request));
//    }

}