package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Area;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AreaListReq;
import com.std.core.pojo.request.AreaPageReq;
import com.std.core.service.IAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 地区表Controller
 *
 * <AUTHOR> z<PERSON><PERSON>
 * @since : 2020-08-10 17:06
 */
@ApiVersion(1)
@RestController
@Api(value = "地区表管理", tags = "地区表管理")
@RequestMapping("{version}/area")
public class AreaController extends BaseController {

    @Resource
    private IAreaService areaService;

    @ApiOperation(value = "获取所有省份")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/allProvince")
    public Result
            <List<Area>> allProvince(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);

        return new Result<>(areaService.getAllProvince());
    }

    @ApiOperation(value = "根据Pid获取所有市或者区")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/allCityOrCounty")
    public Result
            <List<Area>> allCityOrCounty(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        return new Result<>(areaService.allCityOrCounty(id));
    }


    @ApiOperation(value = "分页条件查询地区表")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Area>> page(@RequestHeader(value = "Authorization") String token,
                                       @RequestBody @Valid AreaPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Area.class));

        return PageUtil.pageResult(areaService.page(request));
    }

    @ApiOperation(value = "列表条件查询地区表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<Area>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid AreaListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(areaService.list(request));
    }

}