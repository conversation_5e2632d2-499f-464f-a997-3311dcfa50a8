package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Article;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IArticleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 文章Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@ApiVersion(1)
@RestController
@Api(value = "文章管理", tags = "文章管理", position = 3)
@RequestMapping("{version}/article")
public class ArticleController extends BaseController {

    @Resource
    private IArticleService articleService;

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增文章对象", position = 10)
    @PostMapping(value = "/create")
    public Result<Long> create(@RequestHeader(value = "Authorization") String token,
                               @RequestBody @Valid ArticleCreateReq request) {
        User operator = getUserByToken(token);
        return new Result<>(articleService.create(request, operator));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "删除文章对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        articleService.remove(id);

        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "修改文章对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ArticleModifyReq request) {
        User operator = getUserByToken(token);
        articleService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架文章", position = 40)
    @PostMapping(value = "/batch_up_down")
    public Result upDown(@RequestHeader(value = "Authorization") String token,
                         @RequestBody @Valid ArticleBatchUpDownReq request) {
        User operator = getUserByToken(token);
        articleService.upDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询文章对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<Article> detail(@RequestHeader(value = "Authorization") String token,
                                  @ModelAttribute("language") String language,
                                  @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(articleService.detail(id, language));
    }

    @ApiOperation(value = "APP-PC端-查询文章对象", position = 50)
    @PostMapping("/public/pcDetail/{id}")
    public Result<Article> pcDetail(
            @ModelAttribute("language") String language,
            @PathVariable("id") @Valid Long id) {

        return new Result<>(articleService.detail(id, language));
    }

    @ApiOperation(value = "分页条件查询文章", position = 60)
    @PostMapping(value = "/page")
    public Result<PageInfo<Article>> page(@RequestHeader(value = "Authorization") String token,
                                          @ModelAttribute("language") String language,
                                          @RequestBody @Valid ArticlePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Article.class));
        request.setLanguage(language);
        return PageUtil.pageResult(articleService.page(request));
    }

    @ApiOperation(value = "app学院分页条件查询文章列表", position = 70)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<Article>> pageFront(
            @ModelAttribute("language") String language,
            @RequestBody @Valid ArticlePageFrontReq request) {
       
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        request.setLanguage(language);
        return PageUtil.pageResult(articleService.page(request));
    }

    @ApiOperation(value = "app学院热门文章", position = 70)
    @PostMapping(value = "/public/hot_article")
    public Result<List<Article>> hotArticle() {

        return new Result<>(articleService.hotArticle());
    }


    @ApiOperation(value = "列表条件查询文章", position = 80)
    @PostMapping(value = "/public/list")
    public Result<List<Article>> list(
                                      @ModelAttribute("language") String language,
                                      @RequestBody @Valid ArticleListReq request) {
//        User operator = getUserByToken(token);
//        request.setLanguage(language);
        return new Result<>(articleService.list(request));
    }

}