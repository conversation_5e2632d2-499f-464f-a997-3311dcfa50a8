package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ArticleType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IArticleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 文章类型Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@ApiVersion(1)
@RestController
@Api(value = "文章类型管理（组别管理）", tags = "文章类型管理", position = 2)
@RequestMapping("{version}/article_type")
public class ArticleTypeController extends BaseController {

    @Resource
    private IArticleTypeService articleTypeService;

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增文章类型对象", position = 10)
    @PostMapping(value = "/create")
    public Result<Long> create(@RequestHeader(value = "Authorization") String token,
                               @RequestBody @Valid ArticleTypeCreateReq request) {
        User operator = getUserByToken(token);
        return new Result<>(articleTypeService.create(request, operator));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "删除文章类型对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        articleTypeService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改文章类型对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ArticleTypeModifyReq request) {
        User operator = getUserByToken(token);
        articleTypeService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架文章类型对象", position = 40)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token,
                              @RequestBody @Valid ArticleTypeBatchUpDownReq request) {
        User operator = getUserByToken(token);
        articleTypeService.upDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询文章类型对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<ArticleType> detail(@RequestHeader(value = "Authorization") String token,
                                      @ModelAttribute("language") String language,
                                      @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(articleTypeService.detail(id, language));
    }

    @ApiOperation(value = "分页条件查询文章类型", position = 60)
    @PostMapping(value = "/page")
    public Result<PageInfo<ArticleType>> page(@RequestHeader(value = "Authorization") String token,
                                              @ModelAttribute("language") String language,
                                              @RequestBody @Valid ArticleTypePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ArticleType.class));
        request.setLanguage(language);
        return PageUtil.pageResult(articleTypeService.page(request));
    }

    @ApiOperation(value = "列表条件查询文章类型", position = 70)
    @PostMapping(value = "/list")
    public Result<List<ArticleType>> list(@RequestHeader(value = "Authorization") String token,
                                          @ModelAttribute("language") String language,
                                          @RequestBody @Valid ArticleTypeListReq request) {
        User operator = getUserByToken(token);
        request.setLanguage(language);
        return new Result<>(articleTypeService.list(request));
    }


//    @ApiOperation(value = "app学院列表条件查询文章类型", position = 70)
//    @PostMapping(value = "/list_detail")
//    public Result<List<ArticleType>> listDetail(
//            @RequestHeader(value = "Authorization") String token,
//            @ModelAttribute("language") String language,
//            @RequestBody @Valid ArticleTypeListReq request) {
//        User operator = getUserByToken(token);
//        request.setLanguage(language);
//        return new Result<>(articleTypeService.listDetail(request));
//    }

}