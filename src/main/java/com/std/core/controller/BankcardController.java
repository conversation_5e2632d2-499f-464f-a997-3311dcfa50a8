package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Bankcard;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.BanksListRes;
import com.std.core.service.IBankcardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 银行卡Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@ApiVersion(1)
@RestController
@Api(value = "银行卡管理", tags = "8、银行卡管理", position = 9)
@RequestMapping("{version}/bankcard")
public class BankcardController extends BaseController {

    @Resource
    private IBankcardService bankcardService;

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增银行卡对象", position = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BankcardCreateReq request) {
        User operator = getUserByToken(token);
        bankcardService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除银行卡对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        bankcardService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改银行卡对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BankcardModifyReq request) {
        User operator = getUserByToken(token);
        bankcardService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "生效/失效银行卡", position = 40)
    @PostMapping(value = "/up_down/{id}")
    public Result upDown(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        bankcardService.upDown(id, operator);

        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "设置为默认", position = 41)
    @PostMapping(value = "/set_default")
    public Result setDefault(@RequestHeader(value = "Authorization") String token,
                             @RequestBody @Valid BankcardSetDefaultReq request) {
        User operator = getUserByToken(token);
        Bankcard bankcard = bankcardService.detail(request.getId());
        bankcardService.refreshDefault(bankcard.getType(), bankcard.getUserId(),
                EBoolean.NO.getCode());
        bankcardService.refreshDefault(request.getId(), EBoolean.YES.getCode(), operator);
        return new Result();
    }

    @ApiOperation(value = "查询银行卡对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<Bankcard> detail(@RequestHeader(value = "Authorization") String token,
                                   @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(bankcardService.detail(id));
    }

    @ApiOperation(value = "分页条件查询银行卡", position = 60)
    @PostMapping(value = "/page")
    public Result<PageInfo<Bankcard>> page(@RequestHeader(value = "Authorization") String token,
                                           @RequestBody @Valid BankcardPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Bankcard.class));

        return PageUtil.pageResult(bankcardService.page(request));
    }

    @ApiOperation(value = "前端分页条件查询银行卡", position = 61)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<Bankcard>> pageFront(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BankcardPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Bankcard.class));

        return PageUtil.pageResult(bankcardService.page(request, operator));
    }

    @ApiOperation(value = "列表条件查询银行卡", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Bankcard>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BankcardListReq request) {
        User operator = getUserByToken(token);
        
        return new Result<>(bankcardService.list(request));
    }

    @ApiOperation(value = "商家端-查询商家的所有银行卡", position = 80)
    @PostMapping(value = "/seller_list")
    public Result<List<BanksListRes>> sellerList(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        BankcardListReq bankcardListReq=new BankcardListReq();
        bankcardListReq.setUserId(operator.getId());
        return new Result<>(bankcardService.sellerList(bankcardListReq));
    }
}