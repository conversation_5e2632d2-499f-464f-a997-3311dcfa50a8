package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.BigActivityOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.BigActivityOrderCreateReq;
import com.std.core.pojo.request.BigActivityOrderListReq;
import com.std.core.pojo.request.BigActivityOrderListFrontReq;
import com.std.core.pojo.request.BigActivityOrderModifyReq;
import com.std.core.pojo.request.BigActivityOrderPageReq;
import com.std.core.pojo.request.BigActivityOrderPageFrontReq;
import com.std.core.pojo.response.BigActivityOrderDetailRes;
import com.std.core.pojo.response.BigActivityOrderListRes;
import com.std.core.pojo.response.BigActivityOrderPageRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.service.IBigActivityOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预约单大订单Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
@ApiVersion(1)
@RestController
@Api(value = "预约单大订单管理", tags = "预约单大订单管理")
@RequestMapping("{version}/big_activity_order")
public class BigActivityOrderController extends BaseController {

    @Resource
    private IBigActivityOrderService bigActivityOrderService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增预约单大订单', NULL, '/core/v1/big_activity_order/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增预约单大订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<OrderPayRes> create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderCreateReq request) {
        User operator = getUserByToken(token);
        bigActivityOrderService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除预约单大订单', NULL, '/core/v1/big_activity_order/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除预约单大订单")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        bigActivityOrderService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改预约单大订单', NULL, '/core/v1/big_activity_order/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改预约单大订单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderModifyReq request) {
        User operator = getUserByToken(token);
        bigActivityOrderService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询预约单大订单', NULL, '/core/v1/big_activity_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询预约单大订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<BigActivityOrder> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(bigActivityOrderService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询预约单大订单', NULL, '/core/v1/big_activity_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询预约单大订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<BigActivityOrder>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), BigActivityOrder.class));

        return PageUtil.pageResult(bigActivityOrderService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询预约单大订单', NULL, '/core/v1/big_activity_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询预约单大订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<BigActivityOrder>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(bigActivityOrderService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询预约单大订单', NULL, '/core/v1/big_activity_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询预约单大订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<BigActivityOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(bigActivityOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询预约单大订单', NULL, '/core/v1/big_activity_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询预约单大订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<BigActivityOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), BigActivityOrder.class));

        return PageUtil.pageResult(bigActivityOrderService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询预约单大订单', NULL, '/core/v1/big_activity_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询预约单大订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<BigActivityOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BigActivityOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(bigActivityOrderService.listFront(request));
    }

    @ApiOperation(value = "前端列表条件查询预约单大订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front/{id}")
    public Result<List<BigActivityOrderListRes>> listFront( @PathVariable("id") @Valid Long id) {
        bigActivityOrderService.serveDoCallback(id.toString());
        return new Result();
    }

}