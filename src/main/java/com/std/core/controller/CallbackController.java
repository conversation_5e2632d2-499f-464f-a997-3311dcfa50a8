package com.std.core.controller;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EErrorCode;
import com.std.core.service.IAlipayService;
import com.std.core.service.IChargeService;
import com.std.core.service.IWechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.charset.Charset;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> silver
 * @since : 2020-04-13 14:40
 */
@Slf4j
@RestController
@Api(value = "第三方回调管理", tags = "19、第三方回调管理", position = 20)
@RequestMapping("callback")
public class CallbackController extends BaseController {

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private IChargeService chargeService;


    private static final String CHARSET = "utf-8";

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "支付宝回调", position = 10)
    @PostMapping(value = "/public/alipay")
    public synchronized void doCallbackAlipayAPP(HttpServletRequest request,
            HttpServletResponse response) {
        try {
            log.info("******************支付宝回调开始******************");

            // 获取支付宝回调的参数
            PrintWriter out = response.getWriter();
            InputStream inStream = request.getInputStream();
            String result = getReqResult(out, inStream);
            alipayService.doCallback(result);

            // 通知支付宝我已收到请求，不用再继续回调我了
            out.print("success");
            log.info("******************支付宝回调结束******************");
        } catch (Exception e) {
            log.error("支付宝回调异常,原因：" + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "微信回调", position = 20)
    @PostMapping(value = "/public/wechat")
    public synchronized ResponseEntity<?> doCallbackWechat(HttpServletRequest request,
                                                           HttpServletResponse response) {

        response.setContentType("text/xml");
        response.setCharacterEncoding("UTF-8");
        try {
            log.info("******************微信公众号回调开始******************");

            // 获取回调参数
//            PrintWriter out = response.getWriter();
            PrintWriter out = null;
            InputStream inStream = request.getInputStream();
            String result = getReqResult(out, inStream);

            log.info("**** 公众号支付回调结果 ****：");
            log.info(result);

            // 解析回调结果并通知业务biz
            wechatService.doCallback(result);

            // 业务特殊处理
//            log.info("orderCode:" + chargeRes.getOrderCode() + ",payCode:" + chargeRes
//                    .getWechatOrderNo());
//            doPayOrder(chargeRes.getBizType(), chargeRes.getOrderCode(), chargeRes.getIsSuccess());

            // 通知微信服务器(我已收到请求，不用再继续回调我了)
            String noticeStr = setXML("SUCCESS", "OK");
            response.getWriter().write(noticeStr);

//            out.print(new ByteArrayInputStream(
//                    noticeStr.getBytes(Charset.forName("UTF-8"))));

            log.info("******************微信公众号回调结束******************");


//            return  ResponseEntity.status(HttpStatus.OK);
            return ResponseEntity.status(HttpStatus.OK).build();
        } catch (Exception e) {
            throw new BizException(EErrorCode.CORE00021, "微信支付回调异常，原因：" + e.getMessage());
        }
    }

    @ApiOperation(value = "微信授权code", position = 21)
    @GetMapping(value = "/wechat_code")
    public synchronized void wechatCode(HttpServletRequest request,
            HttpServletResponse response) {
        try {

            String code = request.getParameter("code");
            PrintWriter out = response.getWriter();
            out.print(code);

            log.info("**** 微信授权code ****：" + code);
        } catch (Exception e) {
            log.info("******************获取微信授权code失败,原因：" + e.getMessage());
        }
    }

    private String getReqResult(PrintWriter out, InputStream inStream)
            throws IOException {
        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outSteam.write(buffer, 0, len);
        }
        outSteam.close();
        inStream.close();
        return new String(outSteam.toByteArray(), CHARSET);
    }

    private String setXML(String returnCode, String returnMsg) {
        return "<xml><return_code><![CDATA[" + returnCode
                + "]]></return_code><return_msg><![CDATA[" + returnMsg
                + "]]></return_msg></xml>";
    }

}
