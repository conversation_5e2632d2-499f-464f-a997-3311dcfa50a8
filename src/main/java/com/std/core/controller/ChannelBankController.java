package com.std.core.controller;

import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.github.pagehelper.PageHelper;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ChannelBank;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelBankBatchUpDownReq;
import com.std.core.pojo.request.ChannelBankCreateReq;
import com.std.core.pojo.request.ChannelBankListReq;
import com.std.core.pojo.request.ChannelBankModifyReq;
import com.std.core.pojo.request.ChannelBankPageFrontReq;
import com.std.core.pojo.request.ChannelBankPageReq;
import com.std.core.service.IChannelBankService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渠道银行Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@ApiVersion(1)
@RestController
@Api(value = "渠道银行管理", tags = "4、渠道银行管理", position = 5)
@RequestMapping("{version}/channel_bank")
public class ChannelBankController extends BaseController {

    @Resource
    private IChannelBankService channelBankService;

    @ApiOperation(value = "新增渠道银行对象", position = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelBankCreateReq request) {
        User operator = getUserByToken(token);
        channelBankService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除渠道银行对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        channelBankService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改渠道银行对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelBankModifyReq request) {
        User operator = getUserByToken(token);
        channelBankService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架渠道银行", position = 40)
    @PostMapping(value = "/batch_up_down")
    public Result upDown(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelBankBatchUpDownReq request) {
        User operator = getUserByToken(token);
        channelBankService.upDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询渠道银行对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<ChannelBank> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(channelBankService.detail(id));
    }

    @ApiOperation(value = "分页条件查询渠道银行", position = 60)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChannelBank>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelBankPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ChannelBank.class));

        return PageUtil.pageResult(channelBankService.page(request));
    }

    @ApiOperation(value = "前端分页条件查询渠道银行", position = 70)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChannelBank>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelBankPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ChannelBank.class));

        return PageUtil.pageResult(channelBankService.pageFront(request));
    }

    @ApiOperation(value = "列表条件查询渠道银行", position = 80)
    @PostMapping(value = "/list")
    public Result<List<ChannelBank>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelBankListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(channelBankService.list(request));
    }

}