package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Cnavigate;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.ICnavigateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 导航Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@ApiVersion(1)
@RestController
@Api(value = "导航管理", tags = "3、导航管理", position = 4)
@RequestMapping("{version}/cnavigate")
public class CnavigateController extends BaseController {

    @Resource
    private ICnavigateService cnavigateService;

    @ApiOperation(value = "新增导航对象", position = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CnavigateCreateReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);

        if (null != request.getParentId()) {
            cnavigateService.detail(request.getParentId());
        }

        cnavigateService.create(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "删除导航对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        cnavigateService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改导航对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CnavigateModifyReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cnavigateService.modify(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "批量上下架导航", position = 40)
    @PostMapping(value = "/batch_up_down")
    public Result upDown(@RequestHeader(value = "Authorization") String token,
                         @RequestBody @Valid CNavigateBatchUpDownReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        cnavigateService.upDown(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "查询导航对象", position = 50)
    @PostMapping("/detail/{id}")
    public Result<Cnavigate> detail(@RequestHeader(value = "Authorization") String token,
                                    @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(cnavigateService.detail(id));
    }

    @ApiOperation(value = "分页条件查询导航", position = 60)
    @PostMapping(value = "/page")
    public Result<PageInfo<Cnavigate>> page(@RequestHeader(value = "Authorization") String token,
                                            @RequestBody @Valid CnavigatePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Cnavigate.class));

        return PageUtil.pageResult(cnavigateService.page(request));
    }

    @ApiOperation(value = "列表条件查询导航", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Cnavigate>> list(@RequestHeader(value = "Authorization") String token,
                                        @RequestBody @Valid CnavigateListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(cnavigateService.list(request));
    }

    @ApiOperation(value = "前端列表条件查询导航", position = 80)
    @PostMapping(value = "/public/list_front")
    public Result<List<Cnavigate>> listFront(@RequestBody @Valid CnavigateListFrontReq request) {
        return new Result<>(cnavigateService.list(request));
    }

    @ApiOperation(value = "前端列表查询快捷路口", position = 90)
    @PostMapping(value = "/public/quick_entry_list")
    public Result<List<Cnavigate>> quickEntryList() {
        return new Result<>(cnavigateService.quickEntryList());
    }
}