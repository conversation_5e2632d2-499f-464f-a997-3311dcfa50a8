package com.std.core.controller;

import com.std.common.base.Result;
import com.std.common.service.ISmsOutService;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.response.AliOSSRes;
import com.std.core.service.ICommonService;
import com.std.core.service.IWechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 */
@ApiVersion(1)
@RestController
@Api(value = "通用接口", tags = "8、通用接口")
@ApiSort(80)
@RequestMapping(value = "{version}/common")
public class CommonController extends BaseController {

    @Resource
    private ICommonService commonService;

    @ApiOperation(value = "获取七牛token")
    @PostMapping(value = "/get_qiniu_token")
    public Result<String> create() {
        return new Result<>(commonService.getQiniutoken());
    }

    @ApiOperation(value = "获取阿里token")
    @PostMapping(value = "/public/get_ali_token")
    public Result<AliOSSRes> getAliOssToken() {

        return new Result<>(commonService.getAliOssToken());
    }

}
