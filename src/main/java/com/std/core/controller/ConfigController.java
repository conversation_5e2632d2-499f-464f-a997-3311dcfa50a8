package com.std.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IConfigService;
import com.std.core.util.SysProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 15:48
 */
@ApiVersion(1)
@RestController
@Api(value = "系统配置管理", tags = "6、系统配置管理")
@ApiSort(60)
@RequestMapping(value = "{version}/config")
public class ConfigController extends BaseController {

    @Resource
    private IConfigService configService;

    @Resource
    private SysProperties sysProperties;

    @ApiOperation(value = "创建系统配置")
    @PostMapping(value = "/create")
    public Result create(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid ConfigCreateReq request) {
        User operateUser = getUserByToken(token);

        configService.create(request, operateUser);

        return new Result();
    }

    @ApiOperation(value = "修改资源配置")
    @PostMapping(value = "/modify")
    public Result modify(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid ConfigModifyReq request, @ModelAttribute("ip") String ip) {
        User operateUser = getUserByToken(token, EUserKind.SYS);

        configService.modify(request, operateUser, ip);

        return new Result();
    }

    @ApiOperation(value = "详细查系统配置")
    @PostMapping(value = "/detail/{id}")
    public Result<Config> detail(@PathVariable("id") @Valid Long id) {
        return new Result<>(configService.detail(id));
    }

    @ApiOperation(value = "分页查系统配置")
    @PostMapping(value = "/page")
    public Result<PageInfo<Config>> page(@RequestBody @Valid ConfigPageReq request) {
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Config.class));

        List<Config> list = configService.page(request);
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "列表查系统配置")
    @PostMapping(value = "/list")
    public Result<List<Config>> page(@RequestBody @Valid ConfigListReq request) {
        return new Result<>(configService.list(request));
    }

    @ApiOperation(value = "图片服务器配置参数获取")
    @PostMapping(value = "/public/pic_config")
    public Result<JSONObject> picConfig() {
        List<Config> configList = configService.list("pic");
        JSONObject data = new JSONObject();
        for (Config config : configList) {
            data.put(config.getKey(), config.getValue());
        }
        return new Result<>(data);
    }

    @ApiOperation(value = "根据参数类型查系统配置")
    @PostMapping(value = "public/list")
    public Result<Map<String, String>> detail(@RequestBody @Valid ConfigPublicDetailReq request) {
        return new Result<>(configService.listByType(request.getType(), request.getKey()));
    }

    @ApiOperation(value = "app根据参数类型查系统配置")
    @PostMapping(value = "/public/aList")
    public Result<List<Config>> aDetail(@RequestBody @Valid ConfigPublicDetailReq request) {
        return new Result<>(configService.aListByType(request.getType(), request.getKey()));
    }
}
