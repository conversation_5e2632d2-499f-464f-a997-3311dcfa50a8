package com.std.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.PhoneUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.*;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.MailUtils;
import com.std.core.websocket.WebSocketServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * C端用户Controller
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@ApiVersion(1)
@RestController
@Api(value = "C端用户管理", tags = "C端用户管理")
@RequestMapping("{version}/cuser")
@Slf4j
public class CuserController extends BaseController {

    @Resource
    private ICuserService cuserService;

    @Resource
    private WebSocketServer webSocketServer;

    @Resource
    private IUserService userService;

    @Resource
    private IUserLogService userLogService;


    Map<String, String> map = new HashMap<String, String>();

    @ApiOperation(value = "C端用户注册")
    @ApiOperationSupport(order = 1)
    @PostMapping("/public/register")
    public Result<JSONObject> register(@RequestBody @Valid UserRegisterReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(cuserService.register(request, ip));
    }

    @ApiOperation(value = "oss-C端用户注册(代注册)")
    @ApiOperationSupport(order = 15)
    @PostMapping("/register_instead")
    public Result<JSONObject> registerInstead(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRegisterOssReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.registerInstead(request, ip));
    }

    @ApiOperation(value = "C端用户邮箱注册")
    @ApiOperationSupport(order = 1)
    @PostMapping("/public/email_register")
    public Result emailRegister(@RequestBody @Valid User userEmail, @ModelAttribute("ip") String ip) throws Exception {

        String uuid = UUID.randomUUID().toString().replace("-", "");
        map.put(userEmail.getEmail(), uuid);
        String emailTitle = "橙链科技验证码";

//        SendmailUtil.sendEmail(userEmail.getEmail(),emailTitle,uuid);

        MailUtils mailUtils = new MailUtils();
        mailUtils.sendMail(userEmail.getEmail(), "600", emailTitle, uuid);

        return new Result<>();
    }

    @ApiOperation(value = "C端用户登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/login")
    public Result<JSONObject> cLogin(
            @RequestBody @Valid UserLoginReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(cuserService.login(request, ip));
    }


    @ApiOperation(value = "C端用户登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/login/c")
    public Result<JSONObject> login(
            @RequestBody @Valid CUserLoginReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(cuserService.login(request, ip));
    }

    @ApiOperation(value = "C端个人详细信息查询")
    @ApiOperationSupport(order = 3)
    @PostMapping("/my")
    public Result<CuserRes> detailByToken(@RequestHeader(value = "Authorization") @Valid String token) {
        User user = getUserByToken(token);
        if (StringUtils.isNotBlank(user.getTradePwdStrength())) {
            user.setTradePwdFlag(EBoolean.YES.getCode());
        } else {
            user.setTradePwdFlag(EBoolean.NO.getCode());
        }

        CuserRes res = new CuserRes();
        BeanUtils.copyProperties(user, res);

//        User condition = new User();
//        condition.setUserReferee(user.getId());
//        user.setReferUserCount(userService.selectCount(condition));

        return new Result<>(res);
    }

    @ApiOperation(value = "查询个人愿力值")
    @ApiOperationSupport(order = 55)
    @PostMapping("/detail_willing")
    public Result<Cuser> detailWilling(@RequestHeader(value = "Authorization") @Valid String token) {
        User operator = getUserByToken(token);
        return new Result<>(cuserService.detailByUser(operator.getId()));
    }

    @ApiOperation(value = "设置等级")
    @ApiOperationSupport(order = 4)
    @PostMapping("/set_up_level")
    public Result setUpLevel(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserLevelReq request,
                             @ModelAttribute("ip") String ip) {

        User operator = getUserByToken(token);
        Cuser detail = cuserService.detail(request.getId());

        CuserModifyReq cuserModifyReq = new CuserModifyReq();
        cuserModifyReq.setId(request.getId());
        cuserModifyReq.setLevel(request.getLevel());
        cuserService.modify(cuserModifyReq, operator);

        String oldLevelName = ECuserLevel.getCuserLevel(detail.getLevel()).getValue();
        String levelName = ECuserLevel.getCuserLevel(request.getLevel()).getValue();
        String content = "等级更新：" + oldLevelName + "改成" + levelName;
        userLogService.create(operator, EUserLogType.CUSER.getCode(), EUserLogType.CUSER.getValue(), ip, content);
        return new Result<>();
    }

    @ApiOperation(value = "设置状态")
    @ApiOperationSupport(order = 5)
    @PostMapping("/set_up_status")
    public Result setUpstatus(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserStatusReq request,
                              @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);

        cuserService.modifyStatus(request, operator, ip);
        return new Result<>();
    }


    @ApiOperation(value = "加盟成为渠道商")
    @ApiOperationSupport(order = 6)
    @PostMapping("/join_agent")
    public Result joinAgent(@RequestHeader(value = "Authorization") String token,
                            @RequestBody @Valid CuserJoinAgentReq request) {
        User user = getUserByToken(token, EUserKind.SYS);
        cuserService.joinAgent(request);
        return new Result();
    }

    @ApiOperation(value = "渠道商解除合作/恢复")
    @ApiOperationSupport(order = 6)
    @PostMapping("/remove_agent")
    public Result removeAgent(@RequestHeader(value = "Authorization") String token,
                              @RequestBody @Valid CuserRemoveAgentReq request) {
        User user = getUserByToken(token, EUserKind.SYS);
        cuserService.removeAgent(request);
        return new Result();
    }

    @ApiOperation(value = "修改渠道商配置")
    @ApiOperationSupport(order = 7)
    @PostMapping("/agent/set_rate")
    public Result agentSetRate(@RequestHeader(value = "Authorization") String token,
                               @RequestBody @Valid CuserAgentSetRateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.agentSetRate(request, operator);
        return new Result();
    }

    @ApiOperation(value = "渠道商登录")
    @ApiOperationSupport(order = 8)
    @PostMapping("/public/agent/login")
    public Result<JSONObject> agentLogin(
            @RequestBody @Valid UserLoginReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(cuserService.agentLogin(request, ip));
    }

    @ApiOperation(value = "分页条件查询C端用户（OSS）")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/page")
    public Result<PageInfo<Cuser>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));

        return PageUtil.pageResult(cuserService.page(request));
    }

    @ApiOperation(value = "下拉框模糊查询")
    @ApiOperationSupport(order = 24)
    @PostMapping(value = "/vague_deatil")
    public Result<List<Cuser>> vagueDeatil(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.vagueDeatil(request));
    }

    @ApiOperation(value = "C端用户详情（OSS）")
    @ApiOperationSupport(order = 25)
    @PostMapping(value = "/detail/{id}")
    public Result<Cuser> detail(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        return new Result<>(cuserService.detail(id));
    }


    @ApiOperation(value = "前端用户退出")
    @ApiOperationSupport(order = 30)
    @GetMapping(value = "/logOut")
    public Result<Object> logOut(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);
        webSocketServer.onClose();
        cuserService.delCuserRedis(operator.getId());
        //todo token删除
        return new Result<>();
    }


    private UserRes initResult(List<Long> effectUsers, User user) {
        UserRes res = new UserRes();
        res.setLoginName(PhoneUtil.hideMobile(user.getLoginName()));
        res.setEffectFlag(EBoolean.NO.getCode());
        if (effectUsers.contains(user.getId())) {
            res.setEffectFlag(EBoolean.YES.getCode());
        }
        res.setRegisterDatetime(user.getRegisterDatetime());
        return res;
    }

    @ApiOperation(value = "分页条件查询渠道商用户（OSS）")
    @ApiOperationSupport(order = 21)
    @PostMapping(value = "/agent/page")
    public Result<PageInfo<Cuser>> agentPage(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserAgentPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));

        return PageUtil.pageResult(cuserService.agentPage(request));
    }

    @ApiOperation(value = "详情查询渠道商用户（OSS）")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/agent/detail/{id}")
    public Result<Cuser> agentDetail(
            @RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        return new Result<>(cuserService.agentDetail(id));
    }

    @ApiOperation(value = "平台分页社区查询")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/team_page")
    public Result<PageInfo<TeamUserRes>> teamPage(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));
        return PageUtil.pageResult(cuserService.teamPage(request));
    }

    @ApiOperation(value = "平台社区查询-下级人员")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/team_page_next")
    public Result<List<TeamUserRes>> teamPageNext(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserListReq2 request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        CuserPageReq request2 = new CuserPageReq();
        request2.setUserReferee(request.getUserReferee());

        return new Result<>(cuserService.teamPage(request2));
    }


    @ApiOperation(value = "重置推荐", position = 220)
    @PostMapping(value = "/change_refer_user")
    public Result changeReferUser(@RequestHeader(value = "Authorization") String token,
                                  @RequestBody @Valid UserChangeRefereeReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.changeReferUser(request);

        return new Result();
    }


    @ApiOperation(value = "推荐链查询")
    @ApiOperationSupport(order = 666)
    @PostMapping(value = "/recommend_chain")
    public Result<List<RecommendUserRes>> recommendChain(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserRecommendReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.recommendChain(request));
    }

    @ApiOperation(value = "等级链查询")
    @ApiOperationSupport(order = 667)
    @PostMapping(value = "/grade_chain")
    public Result<List<GradeUserRes>> dradeChain(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserRecommendReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.gradeChain(request));
    }


    @ApiOperation(value = "游离等级链查询")
    @ApiOperationSupport(order = 668)
    @PostMapping(value = "/dissociate_chain")
    public Result<List<DissociateUserRes>> dissociateChain(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.dissociateChain());
    }


    @ApiOperation(value = "C端用户微信App登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/wx_app_login")
    public Result<WechatAppLoginRes> wxAppLogin(
            @RequestBody @Valid UserWxGzhLoginReq request, @ModelAttribute("ip") String ip) {
        log.info("微信登录入参:+{}", request);
        request.setLoginType(EUserLoginType.APP_LOGIN.getCode());
        WechatAppLoginRes wechatAppLoginRes = cuserService.wxAppLogin(request, ip);
        log.info("wechatAppLoginRes:{}", wechatAppLoginRes);
        return new Result<>(wechatAppLoginRes);
    }

    @ApiOperation(value = "c端微信授权登录绑定手机号", position = 5)
    @PostMapping("/public/login/c_wechat_app")
    public Result<WechatAppLoginRes> cWechatAppLogin(
            @RequestBody @Valid UserWechatAppLoginReq request, @ModelAttribute("ip") String ip) {

        WechatAppLoginRes wechatAppLoginRes = cuserService.cWechatAppLogin(request, ip);
        cuserService.delCuserRedis(wechatAppLoginRes.getUserId());
        return new Result<>(wechatAppLoginRes);
    }

    @ApiOperation(value = "绑定微信")
    @ApiOperationSupport(order = 2)
    @PostMapping("/bind_wx")
    public Result<JSONObject> bindWx(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserBindWxGzhLoginReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        cuserService.bindWx(request, operator);
        return new Result();
    }

    @ApiOperation(value = "解除微信绑定")
    @ApiOperationSupport(order = 2)
    @PostMapping("/unbind_wx")
    public Result<JSONObject> unbindWx(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);
        cuserService.unbindWx(operator);
        return new Result();
    }

}
