package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.DailyIncomeSummary;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.DailyIncomeSummaryDetailRes;
import com.std.core.pojo.response.DailyIncomeSummaryListRes;
import com.std.core.pojo.response.DailyIncomeSummaryPageRes;
import com.std.core.service.IDailyIncomeSummaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 每日收益Controller
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@ApiVersion(1)
@RestController
@Api(value = "每日收益管理", tags = "每日收益管理")
@RequestMapping("{version}/daily_income_summary")
public class DailyIncomeSummaryController extends BaseController {

    @Resource
    private IDailyIncomeSummaryService dailyIncomeSummaryService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增每日收益', NULL, '/core/v1/daily_income_summary/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增每日收益")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryCreateReq request) {
//        User operator = getUserByToken(token);
//        dailyIncomeSummaryService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除每日收益', NULL, '/core/v1/daily_income_summary/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除每日收益")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        dailyIncomeSummaryService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改每日收益', NULL, '/core/v1/daily_income_summary/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改每日收益")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryModifyReq request) {
//        User operator = getUserByToken(token);
//        dailyIncomeSummaryService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询每日收益', NULL, '/core/v1/daily_income_summary/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询每日收益")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<DailyIncomeSummary> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(dailyIncomeSummaryService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询每日收益', NULL, '/core/v1/daily_income_summary/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询每日收益")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<DailyIncomeSummary>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), DailyIncomeSummary.class));

        return PageUtil.pageResult(dailyIncomeSummaryService.page(request));
    }

    @ApiOperation(value = "列表条件查询每日收益")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/income/total")
    public Result<DailyIncomeSummaryDetailRes> incomeTotal(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryTotalReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(dailyIncomeSummaryService.incomeTotal(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询每日收益', NULL, '/core/v1/daily_income_summary/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询每日收益")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/income/report")
    public Result<List<DailyIncomeSummaryListRes>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(dailyIncomeSummaryService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询每日收益', NULL, '/core/v1/daily_income_summary/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询每日收益")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<DailyIncomeSummaryDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(dailyIncomeSummaryService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询每日收益', NULL, '/core/v1/daily_income_summary/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询每日收益")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<DailyIncomeSummaryPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), DailyIncomeSummary.class));
//
//        return PageUtil.pageResult(dailyIncomeSummaryService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询每日收益', NULL, '/core/v1/daily_income_summary/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询每日收益")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<DailyIncomeSummaryListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid DailyIncomeSummaryListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(dailyIncomeSummaryService.listFront(request));
//    }

}