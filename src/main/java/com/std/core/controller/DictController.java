package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.DictCreateReq;
import com.std.core.pojo.request.DictListReq;
import com.std.core.pojo.request.DictModifyReq;
import com.std.core.pojo.request.DictPageReq;
import com.std.core.service.IDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 数据字典Controller
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:49
 */
@ApiVersion(1)
@RestController
@Api(value = "数据字典管理", tags = "7、数据字典管理")
@ApiSort(70)
@RequestMapping("{version}/dict")
public class DictController extends BaseController {

    @Resource
    private IDictService dictService;

    @ApiOperation(value = "创建数据字典")
    @PostMapping(value = "/create")
    public Result create(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid DictCreateReq request) {
        User operateUser = getUserByToken(token);

        dictService.create(request, operateUser);

        return new Result();
    }

    @ApiOperation(value = "修改数据字典")
    @PostMapping(value = "/modify")
    public Result modify(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid DictModifyReq request, @ModelAttribute("ip") String ip) {
        User operateUser = getUserByToken(token);

        dictService.modify(request, operateUser, ip);

        return new Result();
    }

    @ApiOperation(value = "删除数据字典")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
        User operator = getUserByToken(token);
        dictService.remove(id, operator);

        return new Result();
    }

    @ApiOperation(value = "查询数据字典对象", position = 40)
    @PostMapping("/detail/{id}")
    public Result<Dict> detail(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
//        User operator = getUserByToken(token);

        return new Result<>(dictService.detail(id));
    }

    @ApiOperation(value = "分页条件查询数据字典", position = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Dict>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid DictPageReq request) {
//        User operator = getUserByToken(token);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Dict.class));

        return PageUtil.pageResult(dictService.page(request));
    }

    @ApiOperation(value = "列表条件查询数据字典", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Dict>> list(@RequestBody @Valid DictListReq request) {

        return new Result<>(dictService.list(request));
    }

    @ApiOperation(value = "列表条件查询数据字典", position = 71)
    @PostMapping(value = "/public/list")
    public Result<List<Dict>> aList(@RequestBody @Valid DictListReq request) {

        return new Result<>(dictService.list(request));
    }
}
