package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.FreshNews;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.FreshNewsDetailRes;
import com.std.core.pojo.response.FreshNewsListRes;
import com.std.core.pojo.response.FreshNewsPageRes;
import com.std.core.service.IFreshNewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新鲜事Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
@ApiVersion(1)
@RestController
@Api(value = "新鲜事管理", tags = "新鲜事管理")
@RequestMapping("{version}/fresh_news")
public class FreshNewsController extends BaseController {

    @Resource
    private IFreshNewsService freshNewsService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增新鲜事', NULL, '/core/v1/fresh_news/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增新鲜事")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FreshNewsCreateReq request) {
        User operator = getUserByToken(token);
        freshNewsService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除新鲜事', NULL, '/core/v1/fresh_news/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除新鲜事")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        freshNewsService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改新鲜事', NULL, '/core/v1/fresh_news/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改新鲜事")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FreshNewsModifyReq request) {
        User operator = getUserByToken(token);
        freshNewsService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        freshNewsService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询新鲜事', NULL, '/core/v1/fresh_news/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询新鲜事")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FreshNews> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(freshNewsService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询新鲜事', NULL, '/core/v1/fresh_news/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询新鲜事")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FreshNews>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FreshNewsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FreshNews.class));

        return PageUtil.pageResult(freshNewsService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询新鲜事', NULL, '/core/v1/fresh_news/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询新鲜事")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FreshNews>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FreshNewsListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(freshNewsService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询新鲜事', NULL, '/core/v1/fresh_news/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询新鲜事")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_front/{id}")
    public Result<FreshNewsDetailRes> detailFront(@PathVariable("id") @Valid Long id) {
        return new Result<>(freshNewsService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询新鲜事', NULL, '/core/v1/fresh_news/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询新鲜事")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<FreshNewsPageRes>> pageFront( @RequestBody @Valid FreshNewsPageFrontReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FreshNews.class));
        return PageUtil.pageResult(freshNewsService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询新鲜事', NULL, '/core/v1/fresh_news/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询新鲜事")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FreshNewsListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FreshNewsListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(freshNewsService.listFront(request));
    }


    @ApiOperation(value = "新鲜事排序")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/sort")
    public Result orderTime(@RequestBody @Valid SortReq request) {
        freshNewsService.sort(request);
        return new Result();
    }
}