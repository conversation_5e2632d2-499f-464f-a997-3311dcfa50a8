package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.GoodsCategory;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsCategoryDetailRes;
import com.std.core.pojo.response.GoodsCategoryListRes;
import com.std.core.pojo.response.GoodsCategoryPageRes;
import com.std.core.service.IGoodsCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品类型Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
@ApiVersion(1)
@RestController
@Api(value = "商品类型管理", tags = "商品类型管理")
@RequestMapping("{version}/goods_category")
public class GoodsCategoryController extends BaseController {

    @Resource
    private IGoodsCategoryService goodsCategoryService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增商品类型', NULL, '/core/v1/goods_category/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增商品类型")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCategoryCreateReq request) {
        User operator = getUserByToken(token);
        goodsCategoryService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除商品类型', NULL, '/core/v1/goods_category/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除商品类型")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsCategoryService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改商品类型', NULL, '/core/v1/goods_category/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改商品类型")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCategoryModifyReq request) {
        User operator = getUserByToken(token);
        goodsCategoryService.modify(request, operator);

        return new Result();
    }
    @ApiOperation(value = "上下架商品类型")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        goodsCategoryService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询商品类型', NULL, '/core/v1/goods_category/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询商品类型")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsCategory> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsCategoryService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询商品类型', NULL, '/core/v1/goods_category/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询商品类型")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsCategory>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCategoryPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsCategory.class));

        return PageUtil.pageResult(goodsCategoryService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询商品类型', NULL, '/core/v1/goods_category/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询商品类型")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsCategory>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCategoryListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsCategoryService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询商品类型', NULL, '/core/v1/goods_category/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询商品类型")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<GoodsCategoryDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsCategoryService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询商品类型', NULL, '/core/v1/goods_category/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询商品类型")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<GoodsCategoryPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCategoryPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsCategory.class));

        return PageUtil.pageResult(goodsCategoryService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询商品类型', NULL, '/core/v1/goods_category/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询商品类型")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front")
    public Result<List<GoodsCategoryListRes>> listFront(@RequestBody @Valid GoodsCategoryListFrontReq request) {
        return new Result<>(goodsCategoryService.listFront(request));
    }



    @ApiOperation(value = "商品排序")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/sort")
    public Result orderTime(@RequestBody @Valid SortReq request) {
        goodsCategoryService.sort(request);
        return new Result();
    }
}