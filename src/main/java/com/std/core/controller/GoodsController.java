package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsDetailRes;
import com.std.core.pojo.response.GoodsListRes;
import com.std.core.pojo.response.GoodsPageRes;
import com.std.core.service.IGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@ApiVersion(1)
@RestController
@Api(value = "商品管理", tags = "商品管理")
@RequestMapping("{version}/goods")
public class GoodsController extends BaseController {

    @Resource
    private IGoodsService goodsService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增商品', NULL, '/core/v1/goods/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增商品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsCreateReq request) {
        User operator = getUserByToken(token);
        goodsService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除商品', NULL, '/core/v1/goods/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除商品")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改商品', NULL, '/core/v1/goods/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改商品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsModifyReq request) {
        User operator = getUserByToken(token);
        goodsService.modify(request, operator);

        return new Result();
    }
    @ApiOperation(value = "上下架商品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        goodsService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询商品', NULL, '/core/v1/goods/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询商品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<Goods> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询商品', NULL, '/core/v1/goods/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询商品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Goods>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), Goods.class));

        return PageUtil.pageResult(goodsService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询商品', NULL, '/core/v1/goods/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询商品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<Goods>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询商品', NULL, '/core/v1/goods/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询商品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_front/{id}")
    public Result<GoodsDetailRes> detailFront(@PathVariable("id") @Valid Long id) {

        return new Result<>(goodsService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询商品', NULL, '/core/v1/goods/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询商品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<GoodsPageRes>> pageFront(@RequestBody @Valid GoodsPageFrontReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(goodsService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询商品', NULL, '/core/v1/goods/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询商品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<GoodsListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsService.listFront(request));
    }


    @ApiOperation(value = "商品排序")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/sort")
    public Result orderTime(@RequestBody @Valid SortReq request) {
        goodsService.sort(request);
        return new Result();
    }
}