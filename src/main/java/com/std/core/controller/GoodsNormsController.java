package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.GoodsNorms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsNormsDetailRes;
import com.std.core.pojo.response.GoodsNormsListRes;
import com.std.core.pojo.response.GoodsNormsPageRes;
import com.std.core.service.IGoodsNormsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品规格Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:59
 */
@ApiVersion(1)
@RestController
@Api(value = "商品规格管理", tags = "商品规格管理")
@RequestMapping("{version}/goods_norms")
public class GoodsNormsController extends BaseController {

    @Resource
    private IGoodsNormsService goodsNormsService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增商品规格', NULL, '/core/v1/goods_norms/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增商品规格")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsCreateReq request) {
        User operator = getUserByToken(token);
        goodsNormsService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除商品规格', NULL, '/core/v1/goods_norms/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除商品规格")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsNormsService.remove(id,operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改商品规格', NULL, '/core/v1/goods_norms/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改商品规格")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsModifyReq request) {
        User operator = getUserByToken(token);
        goodsNormsService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "增加商品库存")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/add_stock")
    public Result addStock(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsAddStockReq request) {
        User operator = getUserByToken(token);
        goodsNormsService.addStock(request, operator);

        return new Result();
    }

    @ApiOperation(value = "上下架商品规格")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BatchUpDownReq request) {
        User operator = getUserByToken(token);
        goodsNormsService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询商品规格', NULL, '/core/v1/goods_norms/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询商品规格")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsNorms> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsNormsService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询商品规格', NULL, '/core/v1/goods_norms/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询商品规格")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsNorms>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsNorms.class));

        return PageUtil.pageResult(goodsNormsService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询商品规格', NULL, '/core/v1/goods_norms/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询商品规格")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsNorms>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsNormsService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询商品规格', NULL, '/core/v1/goods_norms/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询商品规格")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<GoodsNormsDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsNormsService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询商品规格', NULL, '/core/v1/goods_norms/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询商品规格")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<GoodsNormsPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsNormsPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsNorms.class));

        return PageUtil.pageResult(goodsNormsService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询商品规格', NULL, '/core/v1/goods_norms/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询商品规格")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/public/list_front")
    public Result<List<GoodsNormsListRes>> listFront( @RequestBody @Valid GoodsNormsListFrontReq request) {
        return new Result<>(goodsNormsService.listFront(request));
    }

}