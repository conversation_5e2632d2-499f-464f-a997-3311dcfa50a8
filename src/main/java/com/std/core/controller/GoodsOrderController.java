package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EErrorCode;
import com.std.core.pojo.domain.GoodsOrder;
import com.std.core.pojo.domain.GoodsOrderTrollery;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.IGoodsOrderService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品订单Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@ApiVersion(1)
@RestController
@Api(value = "商品订单管理", tags = "商品订单管理")
@RequestMapping("{version}/goods_order")
@Slf4j
public class GoodsOrderController extends BaseController {

    @Resource
    private IGoodsOrderService goodsOrderService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;
    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增商品订单', NULL, '/core/v1/goods_order/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增商品订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<OrderPayRes> create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderCreateReq request) {
        User operator = getUserByToken(token);
        String lockId = "serve_order_create:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"购买进行中，请稍后重试");
        }
        try {
            return new Result<>(goodsOrderService.create(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

    @ApiOperation(value = "新增购物车订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_trolley")
    public Result<OrderPayRes> createTrolley(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderTrolleryCreateReq request) {
        User operator = getUserByToken(token);
        String lockId = "serve_order_create:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"购买进行中，请稍后重试");
        }
        try {
            GoodsOrder goodsOrder = goodsOrderService.createByTrollery(request, operator);
            OrderPayRes res = goodsOrderService.payServeOrder(goodsOrder);
            return new Result<>(res);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

    @ApiOperation(value = "支付购物车订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/pay/{id}")
    public Result<OrderPayRes> pay(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        String lockId = "serve_order_create:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"购买进行中，请稍后重试");
        }
        try {
            GoodsOrder goodsOrder = goodsOrderService.detail(id);
            OrderPayRes res = goodsOrderService.payServeOrder(goodsOrder);
            return new Result<>(res);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除商品订单', NULL, '/core/v1/goods_order/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除商品订单")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        goodsOrderService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改商品订单', NULL, '/core/v1/goods_order/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改商品订单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderModifyReq request) {
        User operator = getUserByToken(token);
        goodsOrderService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "发货")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/send")
    public Result send(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderSendReq request) {
        User operator = getUserByToken(token);
        GoodsOrder goodsOrder = goodsOrderService.send(request, operator);

        try {
            // 发货完成通知微信
            goodsOrderService.sendSuccessNoticeWechat(goodsOrder);
        }catch (Exception e){
            log.error("发货完成，通知微信调用失败:{}",e.getMessage());
        }

        return new Result();
    }

    @ApiOperation(value = "收货")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/receive")
    public Result receive(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderReceiveReq request) {
        User operator = getUserByToken(token);
        goodsOrderService.receive(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询商品订单', NULL, '/core/v1/goods_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询商品订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsOrder> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderService.detail(id));
    }


    @ApiOperation(value = "取消订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/cancelGoodsOrderByOss/{id}")
    //cancelGoodsOrderByOss
    public Result cancelGoodsOrderByOss(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsOrderService.cancelGoodsOrderByOss(id);
        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询商品订单', NULL, '/core/v1/goods_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询商品订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsOrder>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsOrder.class));

        return PageUtil.pageResult(goodsOrderService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询商品订单', NULL, '/core/v1/goods_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询商品订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsOrder>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询商品订单', NULL, '/core/v1/goods_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询商品订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<GoodsOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询商品订单', NULL, '/core/v1/goods_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询商品订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<GoodsOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsOrder.class));

        return PageUtil.pageResult(goodsOrderService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询商品订单', NULL, '/core/v1/goods_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询商品订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<GoodsOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderService.listFront(request));
    }

    //    List<OrderNumberByStatusRes> orderNumberByStatus(User operator);
    @ApiOperation(value = "首页订单数量")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/order_number_by_status")
    public Result<List<OrderNumberByStatusRes>> orderNumberByStatus(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        return new Result<>(goodsOrderService.orderNumberByStatus(operator));
    }

}