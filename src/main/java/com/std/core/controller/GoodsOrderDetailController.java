package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.GoodsOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsOrderDetailCreateReq;
import com.std.core.pojo.request.GoodsOrderDetailListReq;
import com.std.core.pojo.request.GoodsOrderDetailListFrontReq;
import com.std.core.pojo.request.GoodsOrderDetailModifyReq;
import com.std.core.pojo.request.GoodsOrderDetailPageReq;
import com.std.core.pojo.request.GoodsOrderDetailPageFrontReq;
import com.std.core.pojo.response.GoodsOrderDetailDetailRes;
import com.std.core.pojo.response.GoodsOrderDetailListRes;
import com.std.core.pojo.response.GoodsOrderDetailPageRes;
import com.std.core.service.IGoodsOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品订单详情Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:53
 */
@ApiVersion(1)
@RestController
@Api(value = "商品订单详情管理", tags = "商品订单详情管理")
@RequestMapping("{version}/goods_order_detail")
public class GoodsOrderDetailController extends BaseController {

    @Resource
    private IGoodsOrderDetailService goodsOrderDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增商品订单详情', NULL, '/core/v1/goods_order_detail/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增商品订单详情")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailCreateReq request) {
        User operator = getUserByToken(token);
        goodsOrderDetailService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除商品订单详情', NULL, '/core/v1/goods_order_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除商品订单详情")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsOrderDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改商品订单详情', NULL, '/core/v1/goods_order_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改商品订单详情")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailModifyReq request) {
        User operator = getUserByToken(token);
        goodsOrderDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询商品订单详情', NULL, '/core/v1/goods_order_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询商品订单详情")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsOrderDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询商品订单详情', NULL, '/core/v1/goods_order_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询商品订单详情")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsOrderDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsOrderDetail.class));

        return PageUtil.pageResult(goodsOrderDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询商品订单详情', NULL, '/core/v1/goods_order_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询商品订单详情")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsOrderDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询商品订单详情', NULL, '/core/v1/goods_order_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询商品订单详情")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<GoodsOrderDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询商品订单详情', NULL, '/core/v1/goods_order_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询商品订单详情")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<GoodsOrderDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsOrderDetail.class));

        return PageUtil.pageResult(goodsOrderDetailService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询商品订单详情', NULL, '/core/v1/goods_order_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询商品订单详情")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<GoodsOrderDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsOrderDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsOrderDetailService.listFront(request));
    }

}