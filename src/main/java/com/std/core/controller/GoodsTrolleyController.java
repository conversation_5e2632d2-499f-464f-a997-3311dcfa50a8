package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.GoodsTrolley;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsTrolleyDetailRes;
import com.std.core.pojo.response.GoodsTrolleyListRes;
import com.std.core.pojo.response.GoodsTrolleyPageRes;
import com.std.core.service.IGoodsTrolleyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 购物车Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
@ApiVersion(1)
@RestController
@Api(value = "购物车管理", tags = "购物车管理")
@RequestMapping("{version}/goods_trolley")
public class GoodsTrolleyController extends BaseController {

    @Resource
    private IGoodsTrolleyService goodsTrolleyService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增购物车', NULL, '/core/v1/goods_trolley/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增购物车")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyCreateReq request) {
        User operator = getUserByToken(token);
        goodsTrolleyService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除购物车', NULL, '/core/v1/goods_trolley/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除购物车")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove")
    public Result remove(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyRemoveReq request) {
        User operator = getUserByToken(token);
        goodsTrolleyService.remove(request,operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改购物车', NULL, '/core/v1/goods_trolley/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改购物车")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyModifyReq request) {
        User operator = getUserByToken(token);
        goodsTrolleyService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改规格")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/exchange")
    public Result exchange(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyExchangeReq request) {
        User operator = getUserByToken(token);
        goodsTrolleyService.exchange(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询购物车', NULL, '/core/v1/goods_trolley/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询购物车")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsTrolley> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsTrolleyService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询购物车', NULL, '/core/v1/goods_trolley/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询购物车")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsTrolley>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsTrolley.class));

        return PageUtil.pageResult(goodsTrolleyService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询购物车', NULL, '/core/v1/goods_trolley/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询购物车")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsTrolley>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsTrolleyService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询购物车', NULL, '/core/v1/goods_trolley/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询购物车")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<GoodsTrolleyDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsTrolleyService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询购物车', NULL, '/core/v1/goods_trolley/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询购物车")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<GoodsTrolleyPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), GoodsTrolley.class));

        return PageUtil.pageResult(goodsTrolleyService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询购物车', NULL, '/core/v1/goods_trolley/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询购物车")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<GoodsTrolleyListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsTrolleyListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsTrolleyService.listFront(request,operator));
    }

}