package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Group;
import com.std.core.pojo.domain.GroupRole;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AllotRolesReq;
import com.std.core.pojo.request.GroupCreateReq;
import com.std.core.pojo.request.GroupListReq;
import com.std.core.pojo.request.GroupModifyReq;
import com.std.core.pojo.request.GroupPageReq;
import com.std.core.service.IGroupRoleService;
import com.std.core.service.IGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import java.util.List;
import javax.validation.Valid;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-02-17 00:39
 */
@ApiVersion(1)
@RestController
@Api(value = "组管理", tags = "3、组管理")
@ApiSort(30)
@RequestMapping("/{version}/group")
public class GroupController extends BaseController {

    @Autowired
    private IGroupService groupService;

    @Autowired
    private IGroupRoleService groupRoleService;

    @ApiOperation(value = "创建组")
    @PostMapping("/create")
    public Result create(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GroupCreateReq request) {
        User user = getUserByToken(token);
        groupService.create(user, request);
        return new Result();
    }

    @ApiOperation(value = "删除组")
    @PostMapping("/remove/{id}")
    public Result remove(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        getUserByToken(token);
        groupService.remove(id);
        return new Result();
    }

    @ApiOperation(value = "修改组")
    @PostMapping("/modify")
    public Result modify(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GroupModifyReq request) {
        User user = super.getUserByToken(token);
        groupService.modify(user, request);
        return new Result();
    }

    @ApiOperation(value = "分配组角色")
    @PostMapping("/allot_roles")
    public Result allotRoles(@RequestBody @Valid AllotRolesReq request) {
        groupRoleService.allotRoles(request.getGroupId(), request.getRoleIdList());
        return new Result();
    }

    @ApiOperation(value = "详细查组")
    @PostMapping("/detail/{id}")
    public Result<Group> detail(@PathVariable("id") @Valid Long id) {

        Group group = groupService.info(id);
        List<GroupRole> groupRoleList = groupRoleService.list(group.getId());
        group.setGroupRoleList(groupRoleList);

        return new Result<>(group);
    }

    @ApiOperation(value = "分页查组")
    @PostMapping("/page")
    public Result<PageInfo<Group>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GroupPageReq request) {
        User user = getUserByToken(token);

        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Group.class));

        List<Group> list = groupService.page(request, user);

        if (CollectionUtils.isNotEmpty(list)) {
            for (Group group : list) {
                List<GroupRole> groupRoleList = groupRoleService.list(group.getId());
                group.setGroupRoleList(groupRoleList);
            }
        }
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "列表查组")
    @PostMapping("/list")
    public Result<List<Group>> list(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GroupListReq request) {
        User user = getUserByToken(token);

        return new Result<>(groupService.list(request, user));
    }

    @ApiOperation(value = "查询下级组")
    @PostMapping("/sub_owner/list")
    public Result<List<Group>> subOwnerList(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GroupListReq request) {
        User user = getUserByToken(token);

        return new Result<>(groupService.subOwnerList(request, user));
    }

    @ApiOperation(value = "查询其他组")
    @PostMapping("/list_other_group/{id}")
    public Result<List<Group>> listOtherGroup(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User user = getUserByToken(token);

        return new Result<>(groupService.listOtherGroup(id, user));
    }

    @ApiOperation(value = "查询用户所属组")
    @PostMapping("/list_by_token")
    public Result<List<Group>> listByToken(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);

        return new Result<>(groupService.listByToken(user));
    }
}
