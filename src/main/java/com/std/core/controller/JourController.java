package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Jour;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.JourKykPageReq;
import com.std.core.pojo.request.JourListReq;
import com.std.core.pojo.request.JourMyPageReq;
import com.std.core.pojo.request.JourPageReq;
import com.std.core.pojo.response.JourMyPageRes;
import com.std.core.service.IJourService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 账户流水Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@ApiVersion(1)
@RestController
@Api(value = "账户流水管理", tags = "11、账户流水管理", position = 12)
@RequestMapping("{version}/jour")
public class JourController extends BaseController {

    @Resource
    private IJourService jourService;

//    @ApiOperation(value = "app流水详情", position = 40)
//    @PostMapping("/jour_detail")
//    public Result<JourDetailRes> jourDetail(@RequestHeader(value = "Authorization") String token,
//                                            @RequestBody @Valid BaseIdReq idReq) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(jourService.jourDetail(idReq));
//    }


    @ApiOperation(value = "查询账户流水对象", position = 40)
    @PostMapping("/detail/{id}")
    public Result<Jour> detail(@RequestHeader(value = "Authorization") String token,
            @ModelAttribute("language") String language,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(jourService.detail(id, language));
    }


    @ApiOperation(value = "分页条件查询账户流水", position = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Jour>> page(@RequestHeader(value = "Authorization") String token,
            @ModelAttribute("language") String language,
            @RequestBody @Valid JourPageReq request) {
        User operator = getUserByToken(token);
        request.setLanguage(language);
        return PageUtil.pageResult(jourService.page(request, operator));
    }


    @ApiOperation(value = "列表条件查询账户流水", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Jour>> list(@RequestHeader(value = "Authorization") String token,
            @ModelAttribute("language") String language,
            @RequestBody @Valid JourListReq request) {
        User operator = getUserByToken(token);
        request.setLanguage(language);
        return new Result<>(jourService.list(request));
    }


    @ApiOperation(value = "分页查询我的账户流水", position = 80)
    @PostMapping(value = "/my/page")
    public Result<PageInfo<Jour>> userPage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid JourMyPageReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Jour.class));

        return PageUtil.pageResult(jourService.userPage(request, operator));
    }

}