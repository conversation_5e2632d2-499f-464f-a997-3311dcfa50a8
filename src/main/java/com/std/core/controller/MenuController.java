package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.MenuAction;
import com.std.core.pojo.request.AllotActionReq;
import com.std.core.pojo.request.MenuCreateReq;
import com.std.core.pojo.request.MenuListReq;
import com.std.core.pojo.request.MenuModifyReq;
import com.std.core.pojo.request.MenuPageReq;
import com.std.core.service.IMenuActionService;
import com.std.core.service.IMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;

import java.util.HashMap;
import java.util.List;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 22:12
 */
@ApiVersion(1)
@RestController
@Api(value = "菜单管理", tags = "4、菜单管理")
@ApiSort(40)
@RequestMapping("/{version}/menu")
public class MenuController extends BaseController {

    @Autowired
    private IMenuService menuService;

    @Autowired
    private IMenuActionService menuActionService;

    @ApiOperation(value = "创建菜单")
    @PostMapping("/create")
    public Result<HashMap<String, Long>> create(@RequestBody @Valid MenuCreateReq request) {
        Long menuId = menuService.create(request);
        HashMap<String, Long> map = new HashMap<>();
        map.put("id", menuId);
        return new Result<>(map);
    }

    @ApiOperation(value = "删除菜单")
    @PostMapping("/remove/{id}")
    public Result remove(@PathVariable("id") @Valid Long id) {
        menuService.remove(id);
        return new Result();
    }

    @ApiOperation(value = "修改菜单")
    @PostMapping("/modify")
    public Result modify(@RequestBody @Valid MenuModifyReq request) {
        menuService.modify(request);
        return new Result();
    }

    @ApiOperation(value = "分配资源")
    @PostMapping("/allot_action")
    public Result allotAction(@RequestBody @Valid AllotActionReq request) {
        menuActionService.allotMenuAction(request);
        return new Result();
    }

    @ApiOperation(value = "详细查菜单")
    @PostMapping("/detail/{id}")
    public Result<Menu> detail(@PathVariable("id") @Valid Long id) {
        Menu menu = menuService.detail(id);
        menu.setParentMenu(menuService.detail(menu.getParentId()));
        return new Result<>(menu);
    }

    @ApiOperation(value = "分页查菜单")
    @PostMapping("/page")
    public Result<PageInfo<Menu>> page(@RequestBody @Valid MenuPageReq request) {
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Menu.class));
        List<Menu> list = menuService.page(request);
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "列表查菜单")
    @PostMapping("/list")
    public Result<List<Menu>> list(@RequestBody @Valid MenuListReq request) {
        return new Result<>(menuService.list(request));
    }

    @ApiOperation(value = "查询菜单资源")
    @PostMapping("/action_list/{id}")
    public Result<List<MenuAction>> menuActionList(@PathVariable("id") @Valid Long id) {
        return new Result<>(menuActionService.list(id));
    }
}
