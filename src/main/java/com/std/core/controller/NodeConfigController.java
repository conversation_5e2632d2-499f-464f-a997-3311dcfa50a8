package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.NodeConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NodeConfigCreateReq;
import com.std.core.pojo.request.NodeConfigListReq;
import com.std.core.pojo.request.NodeConfigModifyReq;
import com.std.core.pojo.request.NodeConfigPageReq;
import com.std.core.service.INodeConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 星级节点配置Controller
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
@ApiVersion(1)
@RestController
@Api(value = "星级节点配置管理", tags = "星级节点配置管理")
@RequestMapping("{version}/node_config")
public class NodeConfigController extends BaseController {

    @Resource
    private INodeConfigService nodeConfigService;

    @ApiOperation(value = "新增星级节点配置")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NodeConfigCreateReq request) {
        User operator = getUserByToken(token);
        nodeConfigService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除星级节点配置")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        nodeConfigService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改星级节点配置")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NodeConfigModifyReq request) {
        User operator = getUserByToken(token);
        nodeConfigService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询星级节点配置")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<NodeConfig> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(nodeConfigService.detail(id));
    }

    @ApiOperation(value = "分页条件查询星级节点配置")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<NodeConfig>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid NodeConfigPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), NodeConfig.class));

        return PageUtil.pageResult(nodeConfigService.page(request));
    }

    @ApiOperation(value = "列表条件查询星级节点配置")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<NodeConfig>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NodeConfigListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(nodeConfigService.list(request));
    }

}