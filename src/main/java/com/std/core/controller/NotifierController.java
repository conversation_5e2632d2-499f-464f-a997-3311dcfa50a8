package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Notifier;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NotifierCreateReq;
import com.std.core.pojo.request.NotifierListReq;
import com.std.core.pojo.request.NotifierModifyReq;
import com.std.core.pojo.request.NotifierPageReq;
import com.std.core.service.INotifierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通知人Controller
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 15:39
 */
@ApiVersion(1)
@RestController
@Api(value = "通知人管理", tags = "通知人管理")
@RequestMapping("{version}/notifier")
public class NotifierController extends BaseController {

    @Resource
    private INotifierService notifierService;

    @ApiOperation(value = "新增通知人")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NotifierCreateReq request) {
        User operator = getUserByToken(token);
        notifierService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除通知人")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
        User operator = getUserByToken(token);
        notifierService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改通知人")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NotifierModifyReq request) {
        User operator = getUserByToken(token);
        notifierService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询通知人")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<Notifier> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
        User operator = getUserByToken(token);

        return new Result<>(notifierService.detail(id));
    }

    @ApiOperation(value = "分页条件查询通知人")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Notifier>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid NotifierPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Notifier.class));

        return PageUtil.pageResult(notifierService.page(request));
    }

    @ApiOperation(value = "列表条件查询通知人")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<Notifier>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid NotifierListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(notifierService.list(request));
    }

}