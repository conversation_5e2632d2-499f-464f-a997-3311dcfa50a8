package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PayRecordListReq;
import com.std.core.pojo.request.PayRecordModifyReq;
import com.std.core.pojo.request.PayRecordPageReq;
import com.std.core.service.IPayRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付记录Controller
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
@ApiVersion(1)
@RestController
@Api(value = "支付记录管理", tags = "支付记录管理")
@RequestMapping("{version}/pay_record")
public class PayRecordController extends BaseController {

    @Resource
    private IPayRecordService payRecordService;

    @ApiOperation(value = "删除支付记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        payRecordService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改支付记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PayRecordModifyReq request) {
        User operator = getUserByToken(token);
        payRecordService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询支付记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PayRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(payRecordService.detail(id));
    }

    @ApiOperation(value = "分页条件查询支付记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PayRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid PayRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), PayRecord.class));

        return PageUtil.pageResult(payRecordService.page(request));
    }

    @ApiOperation(value = "列表条件查询支付记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<PayRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PayRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(payRecordService.list(request));
    }

}