package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Product;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ProductCreateReq;
import com.std.core.pojo.request.ProductListReq;
import com.std.core.pojo.request.ProductListFrontReq;
import com.std.core.pojo.request.ProductModifyReq;
import com.std.core.pojo.request.ProductPageReq;
import com.std.core.pojo.request.ProductPageFrontReq;
import com.std.core.pojo.response.ProductDetailRes;
import com.std.core.pojo.response.ProductListRes;
import com.std.core.pojo.response.ProductPageRes;
import com.std.core.service.IProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标的Controller
 *
 * <AUTHOR> ycj
 * @since : 2024-11-22 14:39
 */
@ApiVersion(1)
@RestController
@Api(value = "标的管理", tags = "标的管理")
@RequestMapping("{version}/product")
public class ProductController extends BaseController {

    @Resource
    private IProductService productService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增标的', NULL, '/core/v1/product/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增标的")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductCreateReq request) {
        User operator = getUserByToken(token);
        productService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除标的', NULL, '/core/v1/product/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除标的")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        productService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改标的', NULL, '/core/v1/product/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改标的")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductModifyReq request) {
        User operator = getUserByToken(token);
        productService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询标的', NULL, '/core/v1/product/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询标的")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<Product> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(productService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询标的', NULL, '/core/v1/product/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询标的")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Product>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), Product.class));

        return PageUtil.pageResult(productService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询标的', NULL, '/core/v1/product/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询标的")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<Product>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(productService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询标的', NULL, '/core/v1/product/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询标的")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ProductDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(productService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询标的', NULL, '/core/v1/product/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询标的")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ProductPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), Product.class));

        return PageUtil.pageResult(productService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询标的', NULL, '/core/v1/product/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询标的")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ProductListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ProductListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(productService.listFront(request));
    }

}