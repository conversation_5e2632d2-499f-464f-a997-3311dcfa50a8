package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EResourceType;
import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.PermissionRole;
import com.std.core.pojo.domain.Role;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AllotRolePermissionReq;
import com.std.core.pojo.request.RoleCreateReq;
import com.std.core.pojo.request.RoleListReq;
import com.std.core.pojo.request.RoleModifyReq;
import com.std.core.pojo.request.RolePageReq;
import com.std.core.pojo.request.RoleSubOwnerCreateReq;
import com.std.core.pojo.request.RoleSubOwnerListReq;
import com.std.core.pojo.request.RoleSubOwnerPageReq;
import com.std.core.service.IPermissionRoleService;
import com.std.core.service.IRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 22:11
 */
@ApiVersion(1)
@RestController
@Api(value = "角色管理", tags = "2、角色管理")
@ApiSort(20)
@RequestMapping("/{version}/role")
public class RoleController extends BaseController {

    @Autowired
    private IRoleService roleService;

    @Autowired
    private IPermissionRoleService permissionRoleService;

    @ApiOperation(value = "创建角色")
    @PostMapping("/create")
    public Result create(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid RoleCreateReq request) {
        User operateUser = getUserByToken(token);
        roleService.create(request, operateUser);
        return new Result();
    }

    @ApiOperation(value = "创建下级")
    @PostMapping("/sub_owner/create")
    public Result subOwnerCreate(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid RoleSubOwnerCreateReq request) {
        User operateUser = getUserByToken(token);
        roleService.subOwnerCreate(request, operateUser);
        return new Result();
    }

    @ApiOperation(value = "删除角色")
    @PostMapping("/remove/{id}")
    public Result remove(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operateUser = getUserByToken(token);
        roleService.remove(id, operateUser);
        return new Result();
    }

    @ApiOperation(value = "修改角色")
    @PostMapping("/modify")
    public Result modify(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid RoleModifyReq request) {
        User operateUser = getUserByToken(token);

        roleService.modify(request, operateUser);

        return new Result();
    }

    @ApiOperation(value = "分配角色权限")
    @PostMapping("/allot_permission")
    public Result allotRolePermission(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid AllotRolePermissionReq request) {
        User operateUser = getUserByToken(token);
        roleService.allotRolePermission(request, operateUser);
        return new Result();
    }

    @ApiOperation(value = "详细查角色")
    @PostMapping("/detail/{id}")
    public Result<Role> detail(@PathVariable("id") @Valid Long id) {
        return new Result<>(roleService.detail(id));
    }

    @ApiOperation(value = "分页查角色")
    @PostMapping("/page")
    public Result<PageInfo<Role>> page(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid RolePageReq request) {
        User operateUser = getUserByToken(token);

        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Role.class));

        List<Role> list = roleService.page(request, operateUser);
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "分页查下级角色")
    @PostMapping("/sub_owner/page")
    public Result<PageInfo<Role>> subOwnerPage(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid RoleSubOwnerPageReq request) {
        User operateUser = getUserByToken(token);

        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Role.class));

        List<Role> list = roleService.subOwnerPage(request, operateUser);
        return PageUtil.pageResult(list);
    }

    @ApiOperation(value = "列表查角色")
    @PostMapping("/list")
    public Result<List<Role>> list(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid RoleListReq request) {
        User operateUser = getUserByToken(token);
        return new Result<>(roleService.list(request, operateUser));
    }

    @ApiOperation(value = "列表查下级角色")
    @PostMapping("/sub_owner/list")
    public Result<List<Role>> list(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid RoleSubOwnerListReq request) {
        User operateUser = getUserByToken(token);
        return new Result<>(roleService.subOwnerList(request, operateUser));
    }

    @ApiOperation(value = "查询菜单树")
    @PostMapping("/menu_tree/{roleId}")
    public Result<List<Menu>> getMenuTreeByRole(
            @RequestHeader(value = "Authorization") String token,
            @PathVariable("roleId") @Valid Long roleId) {
        User operateUser = getUserByToken(token);

        return new Result<>(roleService.listByRole(roleId));
    }

    @ApiOperation(value = "查询角色权限")
    @PostMapping("/permission_list/{roleId}")
    public Result<List<PermissionRole>> permissionList(
            @RequestHeader(value = "Authorization") String token,
            @PathVariable("roleId") @Valid Long roleId) {
        getUserByToken(token);

        return new Result<>(
                permissionRoleService.list(
                        roleId,
                        EResourceType.CLIENT.getCode(),
                        EResourceType.MENU.getCode(),
                        EResourceType.BUTTON.getCode(),
                        EResourceType.FUNC_MENU.getCode()));
    }
}
