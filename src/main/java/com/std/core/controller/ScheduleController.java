package com.std.core.controller;

import com.std.core.controller.base.BaseController;
import com.std.core.service.IScheduleService;
import com.std.core.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2021/2/24 下午8:17
 */
@Component
@Slf4j
public class ScheduleController extends BaseController {

    @Resource
    private IScheduleService scheduleService;


    /**
     * 【活动】活动预约大订单超时未支付，自动取消
     */
    @XxlJob(value = "DoCancelBigOrderHandler")
    @PostMapping("/doCancelBigOrder")
    public ReturnT<String> doCancelBigOrder(String param) {
//        log.info("活动预约大订单超时未支付，自动取消开始执行");
        try {
            scheduleService.doCancelBigOrder();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "活动预约大订单超时未支付，自动取消执行失败,原因：" + e.getMessage());
        }
//        log.info("活动预约大订单超时未支付，自动取消执行结束");
        return ReturnT.SUCCESS;
    }

    /**
     * 【商品】商品订单超时未支付，自动取消
     */
    @XxlJob(value = "DoGoodsCancelBigOrderHandler")
    @PostMapping("/doGoodsCancelBigOrder")
    public ReturnT<String> doGoodsCancelBigOrder(String param) {
//        log.info("商品订单超时未支付，自动取消开始执行");
        try {
            scheduleService.doGoodsCancelBigOrder();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "商品订单超时未支付，自动取消执行失败,原因：" + e.getMessage());
        }
//        log.info("商品订单超时未支付，自动取消执行结束");
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "DoExpiredActivityHandler")
    @PostMapping("/doExpiredActivity")
    public ReturnT<String> doExpiredActivity(String param) {
        log.info("订单过期更新开始执行");
        try {
            scheduleService.doExpiredActivity();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "订单过期更新失败,原因：" + e.getMessage());
        }
        log.info("订单过期更新执行结束");
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "DoActivityExpiredHandler")
    @PostMapping("/doActivityExpired")
    public ReturnT<String> doActivityExpired(String param) {
        log.info("活动更新开始执行");
        try {
            scheduleService.activityExpired();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "活动更新失败,原因：" + e.getMessage());
        }
        log.info("活动更新执行结束");
        return ReturnT.SUCCESS;
    }


    @XxlJob(value = "DoAutoReceiveHandler")
    @PostMapping("/doAutoReceive")
    public ReturnT<String> doAutoReceive(String param) {
//        log.info("订单自动收货开始执行");
        try {
            scheduleService.doAutoReceive();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "订单自动收货失败,原因：" + e.getMessage());
        }
//        log.info("订单自动收货执行结束");
        return ReturnT.SUCCESS;
    }


    /**
     * 用户预约限制到期
     *
     * @param param
     * @return
     */
    @XxlJob(value = "DoUserReservationRecoveryHandler")
    @PostMapping("/doUserReservationRecovery")
    public ReturnT<String> doUserReservationRecovery(String param) {
        log.info("用户预约限制到期开始执行");
        try {
            scheduleService.doUserReservationRecovery();
        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "用户预约限制到期失败,原因：" + e.getMessage());
        }
        log.info("用户预约限制到期执行结束");
        return ReturnT.SUCCESS;
    }


    /**
     * 每日收益统计
     *
     * @param param
     * @return
     */
    @XxlJob(value = "DoDailyIncomeSummaryHandler")
    @PostMapping("/doDailyIncomeSummary")
    public ReturnT<String> doDailyIncomeSummary(String param) {
        log.info("每日收益统计开始执行");
        log.info( "入参：{}", param);
        try {
            if (StringUtils.isNotBlank(param) && param.contains(",")) {
                String[] split = param.split(",");
                String startDateStr = split[0];
                String endDateStr = split[1];
                Date startDate = DateUtil.strToDate(startDateStr, DateUtil.DATA_TIME_PATTERN_9);
                Date endDate = DateUtil.strToDate(endDateStr, DateUtil.DATA_TIME_PATTERN_9);
                while (startDate.before(endDate)){
                    scheduleService.doDailyIncomeSummary(startDate);
                    startDate = DateUtils.addDays(startDate, 1);
                }
            } else {
                Date date1 = DateUtils.addDays(new Date(), -1);
                String date = DateUtil.dateToStr(date1, DateUtil.DATA_TIME_PATTERN_9);
                Date incomeDate = DateUtil.strToDate(date, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
                if (StringUtils.isNotBlank(param)) {
                    try {
                        incomeDate = DateUtil.strToDate(param, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
                    } catch (Exception e) {
                        log.error("时间转换失败，入参{}", param);
                    }
                }
                scheduleService.doDailyIncomeSummary(incomeDate);
            }


        } catch (Exception e) {
            log.error(DateUtil.dateToStr(new Date(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "每日收益统计失败,原因：" + e.getMessage());
        }
        log.info("每日收益统计执行结束");
        return ReturnT.SUCCESS;
    }
}


