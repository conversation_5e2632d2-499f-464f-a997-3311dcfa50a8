package com.std.core.controller;

import com.std.core.pojo.domain.Shipment;
import com.std.core.service.ShipmentService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("/api/shipment")
@RequiredArgsConstructor
public class ShipmentController {
    private final ShipmentService shipmentService;

    @PostMapping("/upload")
    public ResponseEntity<?> uploadShipment(@RequestBody Shipment shipment) {
        String accessToken = shipmentService.getAccessToken();

        String response = shipmentService.uploadShippingInfo(
                accessToken,
                shipment.getOrderKey().getTransactionId(),
                shipment.getShippingList().get(0).getTrackingNo(),
                shipment.getShippingList().get(0).getExpressCompany(),
                shipment.getShippingList().get(0).getItemDesc(),
                shipment.getShippingList().get(0).getContact().getConsignorContact(),
                shipment.getPayer().getOpenid()
        );

        return ResponseEntity.ok(Collections.singletonMap("message", "发货信息上传成功"));
    }
}

