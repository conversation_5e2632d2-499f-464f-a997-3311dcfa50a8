package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BasePageReq;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.ESmsStatus;
import com.std.core.enums.ESmsType;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.SmsNewsRes;
import com.std.core.service.ISmsReadService;
import com.std.core.service.ISmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统公告Controller
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@ApiVersion(1)
@RestController
@Api(value = "系统公告管理", tags = "系统公告管理")
@RequestMapping("{version}/sms")
public class SmsController extends BaseController {

    @Resource
    private ISmsService smsService;

    @Resource
    private ISmsReadService smsReadService;

    @ApiOperation(value = "新增系统公告")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
                         @RequestBody @Valid SmsCreateReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        smsService.create(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "发布系统公告")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/release_create")
    public Result releaseCreate(@RequestHeader(value = "Authorization") String token,
                                @RequestBody @Valid SmsCreateReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        request.setType(ESmsType.SYSTEM.getCode());
        smsService.create(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "消息群发")
    @ApiOperationSupport(order = 15)
    @PostMapping(value = "/batch_create")
    public Result batchCreate(@RequestHeader(value = "Authorization") String token,
                              @RequestBody @Valid SmsBatchCreateReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        smsService.batchCreate(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "删除系统公告")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token,
                         @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        Sms sms = smsService.detail(id);
        smsReadService.removeBySmsCode(sms.getId());
        smsService.remove(id);
        return new Result();
    }

    @ApiOperation(value = "修改系统公告")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
                         @RequestBody @Valid SmsModifyReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        smsService.modify(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "批量发布", position = 31)
    @PostMapping(value = "/batch_release")
    public Result batchRelease(@RequestHeader(value = "Authorization") String token,
                               @RequestBody @Valid SmsPutReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        smsService.batchRelease(request, operator, ip);

        return new Result();
    }

    @ApiOperation(value = "批量撤销", position = 32)
    @PostMapping(value = "/batch_revoke")
    public Result batchRevoke(@RequestHeader(value = "Authorization") String token,
                              @RequestBody @Valid SmsPutReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        smsService.batchRevoke(request, operator, ip);

        return new Result();
    }

//    @ApiOperation(value = "查询系统公告")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/public/detail/{id}")
//    public Result<Sms> detail(@PathVariable("id") @Valid Long id) {
////        User operator = getUserByToken(token);
//
//        return new Result<>(smsService.detail(id));
//    }

    @ApiOperation(value = "查询系统公告")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<Sms> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(smsService.appDetail(id, operator));
    }

    @ApiOperation(value = "分页条件查询系统公告")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/public/page")
    public Result
            <PageInfo<Sms>> page(@RequestHeader(value = "Authorization") String token,
                                 @RequestBody @Valid SmsPageReq request) {
        User user = null;
        if (StringUtils.isNotBlank(token)) {
            user = getUserByToken(token);
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Sms.class));

        request.setType(ESmsType.SYSTEM.getCode());
        request.setStatus(ESmsStatus.SENDED.getCode());
        request.setTarget(EUserKind.C.getCode());
        return PageUtil.pageResult(smsService.page(request, user));
    }

    @ApiOperation(value = "OSS:分页条件查询”公告管理“")
    @ApiOperationSupport(order = 55)
    @PostMapping(value = "/ossPage")
    public Result
            <PageInfo<Sms>> ossPage(@RequestHeader(value = "Authorization") String token,
                                    @RequestBody @Valid SmsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Sms.class));

        request.setType(ESmsType.SYSTEM.getCode());
        return PageUtil.pageResult(smsService.page(request, operator));
    }

    @ApiOperation(value = "OSS:分页条件查询”消息管理“")
    @ApiOperationSupport(order = 57)
    @PostMapping(value = "/oss_news_page")
    public Result
            <PageInfo<Sms>> ossNewsPage(@RequestHeader(value = "Authorization") String token,
                                        @RequestBody @Valid SmsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Sms.class));

        request.setType(ESmsType.MY.getCode());
        return PageUtil.pageResult(smsService.page(request, operator));
    }

    @ApiOperation(value = "列表条件查询系统公告")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/public/list")
    public Result<List<Sms>> list(@RequestBody @Valid SmsListReq request) {
        return new Result<>(smsService.list(request));
    }

    @ApiOperation(value = "APP未读消息数")
    @ApiOperationSupport(order = 62)
    @PostMapping(value = "/unread_messages_number")
    public Result<Sms> unreadMessagesNumber(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);

        return new Result<>(smsService.unreadMessagesNumber(user));
    }

    @ApiOperation(value = "APP未读公告数")
    @ApiOperationSupport(order = 64)
    @PostMapping(value = "/unread_notice_number")
    public Result<Sms> unreadNoticeNumber(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);

        return new Result<>(smsService.unreadNoticeNumber(user));
    }

    @ApiOperation(value = "一键已读消息")
    @ApiOperationSupport(order = 65)
    @PostMapping(value = "/unified_read_messages")
    public Result unifiedReadMessages(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);
        smsService.unifiedReadMessages(user);
        return new Result<>();
    }

    @ApiOperation(value = "一键已读公告")
    @ApiOperationSupport(order = 66)
    @PostMapping(value = "/unified_read_notice")
    public Result unifiedReadNotice(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);
        smsService.unifiedReadNotice(user);
        return new Result<>();
    }

    @ApiOperation(value = "消息中心")
    @ApiOperationSupport(order = 67)
    @PostMapping(value = "/public/message_center")
    public Result<List<Sms>> messageCenter(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SmsListReq request) {
        User user = getUserByToken(token);

        return new Result<>(smsService.messageCenter(request, user));
    }

    @ApiOperation(value = "消息中心-系统消息")
    @ApiOperationSupport(order = 69)
    @PostMapping(value = "/public/message_center_page")
    public Result<PageInfo<Sms>> messageCenterPage(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BasePageReq req) {

        User user = null;
        if (StringUtils.isNotBlank(token)) {
            user = getUserByToken(token);
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize(),
                SqlUtil.parseSort(req.getSort(), Sms.class));
        return PageUtil.pageResult(smsService.messageCenterPage(req, user));
    }


    @ApiOperation(value = "未读消息")
    @ApiOperationSupport(order = 70)
    @PostMapping(value = "/unread_messages")
    public Result<Sms> unreadMessages(@RequestHeader(value = "Authorization") String token) {
        User user = getUserByToken(token);

        return new Result<>(smsService.unreadMessages(user));
    }

    @ApiOperation(value = "OSS消息管理详情查")
    @ApiOperationSupport(order = 75)
    @PostMapping(value = "/smsNews/{id}")
    public Result<SmsNewsRes> smsNews(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User user = getUserByToken(token);

        return new Result<>(smsService.smsNews(id));
    }

    @ApiOperation(value = "APP站内信")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/user_sms")
    public Result<PageInfo<Sms>> userSms(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BasePageReq req) {
        User user = getUserByToken(token, EUserKind.C);

        PageHelper.startPage(req.getPageNum(), req.getPageSize(),
                SqlUtil.parseSort(req.getSort(), Sms.class));
        return PageUtil.pageResult(smsService.userSms(user));
    }
}