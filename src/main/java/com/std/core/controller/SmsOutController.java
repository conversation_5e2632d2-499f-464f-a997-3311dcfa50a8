package com.std.core.controller;

import com.std.common.base.Result;
import com.std.common.enums.ESmsOutBizType;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.PhoneUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.request.SendSmsCaptchaOutReq;
import com.std.core.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> silver
 * @since : 2020-03-16 11:55
 */
@ApiVersion(1)
@RestController
@Api(value = "短信管理", tags = "9、短信管理")
@ApiSort(99)
@RequestMapping("{version}/sms_out")
public class SmsOutController extends BaseController {

    @Resource
    private ISmsOutService smsOutService;

    @Resource
    private IUserService userService;

    @ApiOperation(value = "发送短信验证码", position = 11)
    @PostMapping("permission_none/sms_code")
    public Result sendSmsCaptchaOut(@RequestBody @Valid SendSmsCaptchaOutReq request) {
        PhoneUtil.checkMobile(request.getMobile());

        if (ESmsOutBizType.C_REG_MOBILE.getCode().equals(request.getBizType())) {
            userService.checkMobileExist(request.getMobile(), EUserKind.C.getCode());
        }

        smsOutService.sendSmsCaptcha(request.getMobile(), request.getBizType(), "");
        return new Result();
    }
}
