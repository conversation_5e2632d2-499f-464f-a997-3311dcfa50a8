package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SmsPageMyReq;
import com.std.core.pojo.response.SmsUnreadCountRes;
import com.std.core.service.ISmsReadService;
import com.std.core.service.ISmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 公告阅读记录Controller
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
@ApiVersion(1)
@RestController
@Api(value = "系统公告阅读", tags = "系统公告阅读")
@RequestMapping("{version}/mySms")
public class SmsReadController extends BaseController {

    @Resource
    private ISmsReadService smsReadService;

    @Resource
    private ISmsService smsService;

    @ApiOperation(value = "我的系统公告未读数量")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/unreadCount")
    public Result getUnreadCount(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        Integer count = smsReadService.getMyUnreadCount(operator);

        return new Result(new SmsUnreadCountRes(count));
    }

    @ApiOperation(value = "我的消息未读数量")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/msg/unreadCount")
    public Result getMsgUnreadCount(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        Integer count = smsReadService.getMsgMyUnreadCount(operator);

        return new Result(new SmsUnreadCountRes(count));
    }

    @ApiOperation(value = "我的系统公告分页条件查询")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/page")
    public Result mySmsPage(@RequestHeader(value = "Authorization") String token,
                            @RequestBody @Valid SmsPageMyReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Sms.class));

        return PageUtil.pageResult(smsReadService.page(request, operator));
    }

    @ApiOperation(value = "我的系统公告详情查询")
    @ApiOperationSupport(order = 30)
    @PostMapping("/detail")
    public Result<Sms> frontDetail(@RequestHeader(value = "Authorization") String token,
                                   @RequestBody @Valid BaseIdReq req) {
        User operator = getUserByToken(token);
        boolean result = smsReadService.isExist(operator.getId(), req.getId());
        if (!result) {
            smsReadService.create(req.getId(), operator);
        }

        return new Result<>(smsService.detail(req.getId()));
    }
}