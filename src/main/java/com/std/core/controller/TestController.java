package com.std.core.controller;


import com.std.common.base.Result;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.RoleCreateReq;
import com.std.core.service.ICommonService;
import com.std.core.service.IScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 挑战Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-12-28 10:49
 */
@ApiVersion(1)
@RestController
@Api(value = "测试管理", tags = "测试管理")
@RequestMapping("{version}/test")
@Slf4j
public class TestController extends BaseController {

    @Resource
    private IScheduleService scheduleService;

    @Resource
    private ICommonService commonService;

    @ApiOperation(value = "定时器测试")
    @PostMapping("/test")
    public Result create() {
//        scheduleService.doAutoReceive();
        scheduleService.activityExpired();
        return new Result();
    }

    @ApiOperation(value = "阿里云oss上传测试")
    @PostMapping("/oss_test")
    public Result ossTest() {
        String content = "/Users/<USER>/Desktop/image/1671071400635.png";
        String contentUrl = commonService.putObject(content);
        log.info("contentUrl:{}", contentUrl);
        return new Result();
    }


}