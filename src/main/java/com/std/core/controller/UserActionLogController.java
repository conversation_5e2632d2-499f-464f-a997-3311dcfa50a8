package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserActionLog;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.UserActionLogDetailRes;
import com.std.core.pojo.response.UserActionLogListRes;
import com.std.core.pojo.response.UserActionLogPageRes;
import com.std.core.service.IUserActionLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户业务操作记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2024-06-17 12:44
 */
@ApiVersion(1)
@RestController
@Api(value = "用户业务操作记录管理", tags = "用户业务操作记录管理")
@RequestMapping("{version}/user_action_log")
public class UserActionLogController extends BaseController {

    @Resource
    private IUserActionLogService userActionLogService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户业务操作记录', NULL, '/core/v1/user_action_log/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增用户业务操作记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogCreateReq request) {
        User operator = getUserByToken(token);
        userActionLogService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户业务操作记录', NULL, '/core/v1/user_action_log/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除用户业务操作记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userActionLogService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户业务操作记录', NULL, '/core/v1/user_action_log/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改用户业务操作记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogModifyReq request) {
        User operator = getUserByToken(token);
        userActionLogService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户业务操作记录', NULL, '/core/v1/user_action_log/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户业务操作记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserActionLog> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userActionLogService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户业务操作记录', NULL, '/core/v1/user_action_log/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户业务操作记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserActionLog>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserActionLog.class));

        return PageUtil.pageResult(userActionLogService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户业务操作记录', NULL, '/core/v1/user_action_log/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户业务操作记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserActionLog>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userActionLogService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户业务操作记录', NULL, '/core/v1/user_action_log/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户业务操作记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserActionLogDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userActionLogService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户业务操作记录', NULL, '/core/v1/user_action_log/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户业务操作记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserActionLogPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserActionLog.class));

        return PageUtil.pageResult(userActionLogService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户业务操作记录', NULL, '/core/v1/user_action_log/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户业务操作记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserActionLogListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserActionLogListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userActionLogService.listFront(request));
    }

}