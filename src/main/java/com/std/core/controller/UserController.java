package com.std.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.netflix.appinfo.EurekaAccept;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EResourceType;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.MyTeamUserRes;
import com.std.core.pojo.response.SubUserRes;
import com.std.core.pojo.response.TeamUserOssRes;
import com.std.core.pojo.response.UserFlow;
import com.std.core.service.IMenuService;
import com.std.core.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.util.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户Controller
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@ApiVersion(1)
@RestController
@Api(value = "用户管理", tags = "1、用户管理")
@ApiSort(10)
@RequestMapping("{version}/user")
@Slf4j
public class UserController extends BaseController {

    @Resource
    private IUserService userService;

    @Resource
    private IMenuService menuService;

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增用户对象", position = 10)
    @PostMapping(value = "/create")
    public Result<Long> create(
            @RequestHeader(value = "Authorization") String token,
            @ModelAttribute("ip") String ip,
            @RequestBody @Valid UserCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        Long userId = userService.create(request, operator, ip);

        return new Result<>(userId);
    }

    @ApiOperation(value = "删除用户对象", position = 20)
    @PostMapping("/remove/{id}")
    public Result remove(
            @RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改用户对象", position = 30)
    @PostMapping(value = "/modify")
    public Result modify(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyReq request) {
        User operator = getUserByToken(token);
        userService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "运维用户登录", position = 31)
    @PostMapping("/login/ops")
    public Result<JSONObject> opsLogin(
            @RequestBody @Valid UserLoginReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(userService.login(request, EUserKind.OPS.getCode(), ip));
    }

    @ApiOperation(value = "系统用户登录", position = 32)
    @PostMapping("/login/sys")
    public Result<JSONObject> sysLogin(
            @RequestBody @Valid UserLoginReq request, @ModelAttribute("ip") String ip) {
        String userKind = request.getUserKind();
        if (!StringUtils.hasText(userKind)) {
            userKind = EUserKind.OPS.getCode();
        }
        return new Result<>(userService.login(request, userKind, ip));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "管理员代注册C端用户", position = 34)
    @PostMapping(value = "/sys/add/c")
    public Result<Long> create(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserSysCreateCReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.addByOss(request, operator, ip);
        return new Result<>();
    }

    @ApiOperation(value = "完善用户对象", position = 35)
    @PostMapping(value = "/perfect")
    public Result perfect(@RequestHeader(value = "Authorization") String token,
                          @RequestBody @Valid UserPerfectReq request) {
        User operator = getUserByToken(token);
        userService.perfect(request, operator);
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "导入C端用户", position = 38)
    @PostMapping(value = "/sys/import/c")
    public Result<Long> importUser(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserSysImportCReq request) {
        User operator = getUserByToken(token);
//        userService.importByOss(request, operator);
        return new Result<>();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "管理员修改C端用户", position = 37)
    @PostMapping(value = "/sys/edit/c")
    public Result<Long> create(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserSysEditCReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.editByOss(request, operator, ip);

        return new Result<>();
    }

    @ApiOperation(value = "修改个人资料")
    @ApiOperationSupport(order = 36)
    @PostMapping("/edit_profile")
    public Result editProfile(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserEditProfileReq request) {
        User operator = getUserByToken(token);
        userService.editProfile(request, operator);

        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "绑定手机号", position = 40)
    @PostMapping(value = "/bind_mobile")
    public Result bindMobile(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserBindMobileReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.bindMobile(request, operator, ip);

        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "绑定邮箱", position = 41)
    @PostMapping(value = "/bind_email")
    public Result bindEmail(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserBindEmailReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.bindEmail(request, operator, ip);

        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "绑定支付密码", position = 42)
    @PostMapping(value = "/bind_tradePwd")
    public Result bindTradePwd(@RequestHeader(value = "Authorization") String token,
                               @RequestBody @Valid UserBindTradePwdReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.bindTradePwd(request, operator, ip);

        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "用户修改登录密码", position = 43)
    @PostMapping(value = "/modify_pwd")
    public Result modifyLoginPwd(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyLoginPwdReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.modifyLoginPwd(request, operator, ip);
        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }


    @ApiOperation(value = "平台用户修改登录密码", position = 43)
    @PostMapping(value = "/modify_puser_pwd")
    public Result modifyPuserLoginPwd(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid PUserModifyLoginPwdReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token,EUserKind.P);
        userService.modifyPuserPwd(request, operator, ip);
        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "平台用户修改登录密码", position = 43)
    @PostMapping(value = "/modify_puser_pwd_by_oss")
    public Result modifyPuserLoginPwdByOss(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid PUserModifyLoginPwdByOssReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token,EUserKind.SYS);
        userService.modifyPuserPwdByOss(request, operator, ip);
        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "管理员修改登录密码", position = 44)
    @PostMapping(value = "/modify_loginPwdOss")
    public Result modifyLoginPwdOss(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyLoginPwdOSSReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);
        userService.modifyLoginPwdOss(request, operator, ip);
        // 删除用户缓存
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "管理员修改支付密码", position = 100)
    @PostMapping(value = "/modify_transactionPwdOss")
    public Result modifyTransactionPwdOss(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyTransactionPwdOSSReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);
        userService.modifyTransactionPwdOss(request, operator, ip);
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "修改支付密码", position = 45)
    @PostMapping(value = "/modify_tradePwd")
    public Result modifyTradePwd(@RequestHeader(value = "Authorization") String token,
                                 @RequestBody @Valid UserModifyTradePwdReq request, @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.modifyTradePwd(request, operator, ip);
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "修改手机号", position = 46)
    @PostMapping(value = "/modify_mobile")
    public Result modifyMobile(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyMobileReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.P);
        userService.modifyMobile(request, operator, ip);
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "修改邮箱", position = 47)
    @PostMapping(value = "/modify_email")
    public Result modifyEmail(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyEmailReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.modifyEmail(request, operator, ip);
        userService.delUserRedis(operator.getId(), operator.getKind());
        return new Result();
    }

    @ApiOperation(value = "锁定", position = 48)
    @PostMapping(value = "/lock")
    public Result lock(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserLockReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.lock(request, operator, ip);
        return new Result();
    }

    @ApiOperation(value = "解锁", position = 49)
    @PostMapping(value = "/unlock")
    public Result unLock(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserUnLockReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.unLock(request, operator, ip);
        return new Result();
    }

    @ApiOperation(value = "管理员修改手机号", position = 51)
    @PostMapping(value = "/modify_mobile_oss")
    public Result modifyMobileOSS(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyMobileOSSReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token);
        userService.modifyMobileOSS(request, operator, ip);
        return new Result();
    }


    @ApiOperation(value = "忘记密码", position = 52)
    @PostMapping(value = "/forget_loginPwd")
    public Result forgetLoginPwd(@RequestBody @Valid UserForgetLoginPwdReq request) {
        userService.forgetLoginPwd(request);
        return new Result();
    }

    @ApiOperation(value = "分配用户角色", position = 60)
    @PostMapping("/allot_roles")
    public Result allotRoles(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid UserAllotRoleReq request) {
        super.getUserByToken(token);

        userService.allotRoles(request.getUserId(), request.getRoleIdList());

        return new Result();
    }

    @ApiOperation(value = "分配用户组", position = 61)
    @PostMapping("/allot_groups")
    public Result allotGroups(
            @RequestHeader(value = "Authorization") @Valid String token,
            @RequestBody @Valid UserAllotGroupsReq request) {
        super.getUserByToken(token);

        userService.allotGroups(request.getUserId(), request.getGroupIdList());

        return new Result();
    }

    @ApiOperation(value = "根据token查询用户信息", position = 62)
    @PostMapping("/permission_none/my")
    public Result<User> detailByToken(@RequestHeader(value = "Authorization") @Valid String token) {
        User user = getUserByToken(token);

        return new Result<>(user);
    }

    @ApiOperation(value = "根据token查询用户信息", position = 63)
    @PostMapping("/remote/my")
    public Result<User> remoteMy(@RequestHeader(value = "Authorization") @Valid String token) {
        User user = getUserByToken(token);
        return new Result<>(user);
    }

    @ApiOperation(value = "根据token查询菜单", position = 64)
    @PostMapping("/permission_none/permission/{type}")
    public Result<List<Menu>> getPermissionList(
            @RequestHeader(value = "Authorization") @Valid String token,
            @PathVariable("type") String type) {
        User operateUser = getUserByToken(token);

        List<Menu> menuList = null;
        if (EResourceType.MENU.getCode().equals(type)) {
            menuList =
                    userService.listResourceByUser(
                            operateUser, EResourceType.CLIENT.getCode(),
                            EResourceType.MENU.getCode());
        } else if (EResourceType.ALL.getCode().equals(type)) {
            menuList = userService.listResourceByUser(operateUser);
        }

        return new Result<>(menuList);
    }

    @ApiOperation(value = "根据菜单获取按钮", position = 65)
    @PostMapping("/permission_none/permission/btn/{parentId}")
    public Result<List<Menu>> getButtonList(
            @RequestHeader(value = "Authorization") @Valid String token,
            @PathVariable("parentId") @Valid Long parentId) {
        User operateUser = getUserByToken(token);

        List<Menu> list = userService.listResourceByUser(operateUser, parentId);
        if (CollectionUtils.isNotEmpty(list)) {
            list.remove(list.get(0));
        }
        return new Result<>(list);
    }

    @ApiOperation(value = "查询资源", position = 66)
    @PostMapping("/permission/action")
    public Result<List<Action>> getActionList(
            @RequestHeader(value = "Authorization") @Valid String token) {
        User operateUser = getUserByToken(token);

        return new Result<>(userService.listActionByUser(operateUser));
    }

    @ApiOperation(value = "查询菜单", position = 67)
    @PostMapping(value = "/client/menu")
    public Result<List<Menu>> menuByClient(
            @RequestHeader(value = "Authorization") @Valid String token) {
        User operateUser = getUserByToken(token);
        return new Result<>(menuService.listInfoByTopMenuId(operateUser));
    }

    @ApiOperation(value = "查询用户对象", position = 80)
    @PostMapping("/detail/{id}")
    public Result<User> detail(
            @RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userService.detail(id));
    }

    @ApiOperation(value = "用户对象", position = 80)
    @PostMapping("/user_flow")
    public Result<UserFlow> userFlow(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);

        return new Result<>(userService.userFlow());
    }

    @ApiOperation(value = "分页条件查询用户", position = 90)
    @PostMapping(value = "/page")
    public Result<PageInfo<User>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));

        return PageUtil.pageResult(userService.page(request));
    }

    @ApiOperation(value = "列表条件查询用户", position = 100)
    @PostMapping(value = "/list")
    public Result<List<User>> list(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userService.list(request));
    }

    @ApiOperation(value = "列表条件查询除去商家的用户(非主播，非商家用户列表)")
    @PostMapping(value = "/cUserlist")
    public Result<List<User>> cUserlist(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);

        return new Result<>(userService.cUserlist());
    }

    @ApiOperation(value = "oss:查询用户团队")
    @ApiOperationSupport(order = 25)
    @PostMapping(value = "/team_deatil")
    public Result<List<TeamUserOssRes>> teamDeatil(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq req) {
        User operator = getUserByToken(token, EUserKind.SYS);
        return new Result<>(userService.teamDeatil(req.getId()));
    }

    @ApiOperation(value = "（APP）我的团队统计")
    @PostMapping(value = "/team_user")
    public Result<MyTeamUserRes> teamUser(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        return new Result<>(userService.teamUser(operator));
    }

    @ApiOperation(value = "（APP）我的直推明细")
    @PostMapping(value = "/sub_user_list")
    public Result<PageInfo<SubUserRes>> subUserList(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        return new Result<>(new PageInfo<>(userService.subUserList(operator)));
    }


    @ApiOperation(value = "oss-设置等级")
    @PostMapping(value = "/set_level")
    public Result setLevel(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SetUserLevelReq req) {
        User operator = getUserByToken(token);
        userService.setLevel(req);
        return new Result<>();
    }

    @ApiOperation(value = "oss-调整等级链上级")
    @PostMapping(value = "/set_refuser")
    public Result setRefuser(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SetUserFidReq req) {
        User operator = getUserByToken(token);
        userService.setRefuser(req);
        return new Result<>();
    }


    @ApiOperation(value = "后台代注册平台账户")
    @PostMapping(value = "/create_user_by_oss")
    public Result createUserByOss(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRegisterByOssReq req) {
        User operator = getUserByToken(token,EUserKind.SYS);
        userService.registerByOss(req,operator);
        return new Result<>();
    }


    @ApiOperation(value = "平台用户登录", position = 32)
    @PostMapping("/public/login/p_user")
    public Result<JSONObject> pUserLogin(@RequestBody @Valid PUserLoginReq request, @ModelAttribute("ip") String ip) {
        return new Result<>(userService.login(request, EUserKind.P.getCode(), ip));
    }


    @ApiOperation(value = "微信授权登录", position = 35)
    @PostMapping("/login/wxAppletsLogin")
    public Result<JSONObject> wxAppletsLogin(
            @RequestBody @Valid UserWxAppletsLoginReq request, @ModelAttribute("ip") String ip) {
        log.info("微信登录入参:+{}", request);
        JSONObject jsonObject = userService.wxAppletsLogin(request);
        log.info("wechatAppLoginRes:{}", jsonObject);
        return new Result<>(jsonObject);
//        return new Result<>();
    }
}
