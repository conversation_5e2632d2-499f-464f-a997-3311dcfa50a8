package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserLog;
import com.std.core.pojo.request.UserLogListReq;
import com.std.core.pojo.request.UserLogPageReq;
import com.std.core.service.IUserLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户日志Controller
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
@ApiVersion(1)
@RestController
@Api(value = "用户日志管理", tags = "8、用户日志管理")
@ApiSort(80)
@RequestMapping("{version}/user_log")
public class UserLogController extends BaseController {

    @Resource
    private IUserLogService userLogService;

    @ApiOperation(value = "查询用户日志对象", position = 40)
    @PostMapping("/detail/{id}")
    public Result<UserLog> detail(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userLogService.detail(id));
    }

    @ApiOperation(value = "分页条件查询用户日志", position = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserLog>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserLogPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserLog.class));

        return PageUtil.pageResult(userLogService.page(request));
    }

    @ApiOperation(value = "列表条件查询用户日志", position = 70)
    @PostMapping(value = "/list")
    public Result<List<UserLog>> list(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserLogListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userLogService.list(request));
    }
}
