package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserNodeLevel;
import com.std.core.pojo.request.UserNodeLevelCreateReq;
import com.std.core.pojo.request.UserNodeLevelListReq;
import com.std.core.pojo.request.UserNodeLevelModifyReq;
import com.std.core.pojo.request.UserNodeLevelPageReq;
import com.std.core.service.IUserNodeLevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 节点用户Controller
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
@ApiVersion(1)
@RestController
@Api(value = "节点用户管理", tags = "节点用户管理")
@RequestMapping("{version}/user_node_level")
public class UserNodeLevelController extends BaseController {

    @Resource
    private IUserNodeLevelService userNodeLevelService;

    @ApiOperation(value = "设置节点用户")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserNodeLevelCreateReq request) {
        User operator = getUserByToken(token);
        userNodeLevelService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "取消节点用户")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userNodeLevelService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改节点用户")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserNodeLevelModifyReq request) {
        User operator = getUserByToken(token);
        userNodeLevelService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询节点用户")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserNodeLevel> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userNodeLevelService.detail(id));
    }

    @ApiOperation(value = "分页条件查询节点用户")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserNodeLevel>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserNodeLevelPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserNodeLevel.class));

        return PageUtil.pageResult(userNodeLevelService.page(request));
    }

    @ApiOperation(value = "列表条件查询节点用户")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<UserNodeLevel>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserNodeLevelListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userNodeLevelService.list(request));
    }

}