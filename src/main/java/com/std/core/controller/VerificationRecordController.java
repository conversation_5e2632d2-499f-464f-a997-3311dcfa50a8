package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.VerificationRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.VerificationRecordCreateReq;
import com.std.core.pojo.request.VerificationRecordListReq;
import com.std.core.pojo.request.VerificationRecordListFrontReq;
import com.std.core.pojo.request.VerificationRecordModifyReq;
import com.std.core.pojo.request.VerificationRecordPageReq;
import com.std.core.pojo.request.VerificationRecordPageFrontReq;
import com.std.core.pojo.response.*;
import com.std.core.service.IVerificationRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 核销Controller
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 01:10
 */
@ApiVersion(1)
@RestController
@Api(value = "核销管理", tags = "核销管理")
@RequestMapping("{version}/verification_record")
public class VerificationRecordController extends BaseController {

    @Resource
    private IVerificationRecordService verificationRecordService;


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增核销', NULL, '/core/v1/verification_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增核销")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<VerificationRecordCreateRes> create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordCreateReq request) {
        User operator = getUserByToken(token);
        return new Result<>(verificationRecordService.create(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除核销', NULL, '/core/v1/verification_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除核销")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        verificationRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改核销', NULL, '/core/v1/verification_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改核销")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordModifyReq request) {
        User operator = getUserByToken(token);
        verificationRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询核销', NULL, '/core/v1/verification_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询核销")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<VerificationRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(verificationRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询核销', NULL, '/core/v1/verification_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询核销")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<VerificationRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), VerificationRecord.class));

        return PageUtil.pageResult(verificationRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询核销', NULL, '/core/v1/verification_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询核销")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<VerificationRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(verificationRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询核销', NULL, '/core/v1/verification_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询核销")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<VerificationRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(verificationRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询核销', NULL, '/core/v1/verification_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询核销")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<VerificationRecordPageByDays>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), VerificationRecord.class));

        return PageUtil.pageResult(verificationRecordService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询核销', NULL, '/core/v1/verification_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询核销")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<VerificationRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid VerificationRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(verificationRecordService.listFront(request));
    }

}