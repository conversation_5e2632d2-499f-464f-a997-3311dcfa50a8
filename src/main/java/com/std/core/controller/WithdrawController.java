package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.Withdraw;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawSuccessRes;
import com.std.core.service.IWithdrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 取现订单Controller
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@ApiVersion(1)
@RestController
@Api(value = "取现订单管理", tags = "13、取现订单管理", position = 14)
@RequestMapping("{version}/withdraw")
public class WithdrawController extends BaseController {

    @Resource
    private IWithdrawService withdrawService;

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取现", position = 10)
    @PostMapping(value = "/withdraw")
    public Result<WithdrawSuccessRes> withdraw(@RequestHeader(value = "Authorization") String token,
                                               @RequestBody @Valid WithdrawCreateReq request) {
        User operator = getUserByToken(token);
//        String withdrawCode = ;
        return new Result<>(withdrawService.create(request, operator));
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取现到银行卡", position = 11)
    @PostMapping(value = "/withdrawToBankcard")
    public Result withdrawToBankcard(@RequestHeader(value = "Authorization") String token,
                                     @RequestBody @Valid WithdrawToBankcardReq request) {
        User operator = getUserByToken(token);
        String withdrawCode = withdrawService.create(request, operator);
        return new Result();
    }


//    @ApiOperation(value = "一卡通取现到银行卡", position = 11)
//    @PostMapping(value = "/yktWithdrawToBankcard")
//    public Result yktWithdrawToBankcard(@RequestHeader(value = "Authorization") String token,
//                                     @RequestBody @Valid YktWithdrawToBankcardReq request) {
//        User operator = getUserByToken(token);
//        String withdrawCode = withdrawService.create(request, operator);
//        return new Result();
//    }


    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取现审核", position = 12)
    @PostMapping(value = "/withdraw_approve")
    public Result approve(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawApproveReq request) {
        User operator = getUserByToken(token);
        withdrawService.approve(request, operator);
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取现支付", position = 13)
    @PostMapping(value = "/withdraw_pay")
    public Result pay(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawPayReq request) {
        User operator = getUserByToken(token);
        withdrawService.pay(request, operator);
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "批量重审", position = 14)
    @PostMapping(value = "/withdraw_batch_approve_no")
    public Result batchApproveNo(@RequestHeader(value = "Authorization") String token,
                                 @RequestBody @Valid WithdrawBatchApproveNoReq request) {
        User operator = getUserByToken(token);
        withdrawService.approve(request, operator);
        return new Result();
    }

    @ApiOperation(value = "查询取现订单对象", position = 40)
    @PostMapping("/detail/{id}")
    public Result<Withdraw> detail(@RequestHeader(value = "Authorization") String token,
                                   @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(withdrawService.detail(id));
    }

    @ApiOperation(value = "分页条件查询取现订单", position = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<Withdraw>> page(@RequestHeader(value = "Authorization") String token,
                                           @RequestBody @Valid WithdrawPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Withdraw.class));

        return PageUtil.pageResult(withdrawService.page(request, operator));
    }

    @ApiOperation(value = "（APP）分页条件查询我的取现订单", position = 51)
    @PostMapping(value = "/my/page")
    public Result<PageInfo<Withdraw>> myPage(@RequestHeader(value = "Authorization") String token,
                                             @RequestBody @Valid WithdrawMyPageReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), Withdraw.class));

        return PageUtil.pageResult(withdrawService.myPage(request, operator));
    }


    @ApiOperation(value = "列表条件查询取现订单", position = 70)
    @PostMapping(value = "/list")
    public Result<List<Withdraw>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(withdrawService.list(request));
    }

}