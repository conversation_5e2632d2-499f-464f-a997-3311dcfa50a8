package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.WithdrawRule;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawNoteRes;
import com.std.core.service.IWithdrawRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 取现规则Controller
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 20:12
 */
@ApiVersion(1)
@RestController
@Api(value = "取现规则管理", tags = "取现规则管理")
@RequestMapping("{version}/withdraw_rule")
public class WithdrawRuleController extends BaseController {

    @Resource
    private IWithdrawRuleService withdrawRuleService;

    @ApiOperation(value = "新增取现规则")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawRuleCreateReq request) {
        User operator = getUserByToken(token);
        withdrawRuleService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除取现规则")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
        User operator = getUserByToken(token);
        withdrawRuleService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改取现规则")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawRuleModifyReq request) {
        User operator = getUserByToken(token);
        withdrawRuleService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询取现规则")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<WithdrawRule> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Integer id) {
        User operator = getUserByToken(token);

        return new Result<>(withdrawRuleService.detail(id));
    }

    @ApiOperation(value = "查询提币手续费")
    @ApiOperationSupport(order = 45)
    @PostMapping("/detail_fee")
    public Result<WithdrawNoteRes> detailFee(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid WithdrawNoteReq request) {
        User operator = getUserByToken(token);

        return new Result<>(withdrawRuleService.detailFee(request));
    }

    @ApiOperation(value = "分页条件查询取现规则")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<WithdrawRule>> page(@RequestHeader(value = "Authorization") String token,
                                               @RequestBody @Valid WithdrawRulePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), WithdrawRule.class));

        return PageUtil.pageResult(withdrawRuleService.page(request));
    }

    @ApiOperation(value = "OSS：分页条件查询取现规则（除流通NAT）")
    @ApiOperationSupport(order = 55)
    @PostMapping(value = "/page_rule")
    public Result<PageInfo<WithdrawRule>> pageRule(@RequestHeader(value = "Authorization") String token,
                                                   @RequestBody @Valid WithdrawRulePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), WithdrawRule.class));

        return PageUtil.pageResult(withdrawRuleService.pageRule(request));
    }

    @ApiOperation(value = "列表条件查询取现规则")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result
            <List<WithdrawRule>> list(@RequestHeader(value = "Authorization") String token,
                                      @RequestBody @Valid WithdrawRuleListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(withdrawRuleService.list(request));
    }

}