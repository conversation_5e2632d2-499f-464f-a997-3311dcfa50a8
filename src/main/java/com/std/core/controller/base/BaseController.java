package com.std.core.controller.base;

import com.alibaba.fastjson.JSON;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.AuthException;
import com.std.common.exception.BizException;
import com.std.common.utils.JWTUtil;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.*;
import com.std.core.pojo.domain.User;
import com.std.core.service.IUserService;
import com.std.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 22:10
 */
@Slf4j
public abstract class BaseController {

    @Autowired
    private IUserService userService;
    @Autowired
    protected RedisUtil redisUtil;
    @Value("${spring.profiles.active}")
    private String profilesActive;

    protected User getUserByToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new AuthException(
                    ECommonErrorCode.TOKEN_EMPTY.getCode(), ECommonErrorCode.TOKEN_EMPTY.getValue());
        }

        Long userId = JWTUtil.getUserInfo(token);
        String userCacheKey = String.format(RedisKeyList.MSG_USER_INFO_KEY, userId.toString());
        User user;
        if (redisUtil.hasKey(userCacheKey)) {
            user = JSON.parseObject(JSON.toJSONString(redisUtil.get(userCacheKey)), User.class);
        } else {
            user = userService.detailBrief(userId);
            if (null != user) {
                redisUtil.set(userCacheKey, user);
            }
        }
        if (null == user) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"您的账号权限异常，请联系管理员");
        }
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的账号已被平台封禁，请联系管理员");
        }

        if (EUserStatus.PERMANENT_BAN.getCode().equals(user.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的账号已被平台永久封号");
        }


        // 线上环境只能一处登陆,其他环境看系统参数配置
        if (EProfileActive.PROD.getCode().equals(profilesActive)) {
            checkToken(token, user);
        } else {
//            checkToken(token, user);
        }
        return user;
    }

    protected User getUserByToken(String token, EUserKind userKind) {
        if (StringUtils.isBlank(token)) {
            throw new AuthException(
                    ECommonErrorCode.TOKEN_EMPTY.getCode(), ECommonErrorCode.TOKEN_EMPTY.getValue());
        }

        Long userId = JWTUtil.getUserInfo(token);
        String userCacheKey = String.format(RedisKeyList.MSG_USER_INFO_KIND_KEY, userId.toString(), userKind.getCode());
        User user;
        if (redisUtil.hasKey(userCacheKey)) {
            user = JSON.parseObject(JSON.toJSONString(redisUtil.get(userCacheKey)), User.class);
        } else {
            try {
                user = userService.detail(userId, userKind);
            } catch (Exception e) {
                log.error("根据token 获取用户失败:{}", userId);
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的账号已被平台封禁，请联系管理员");
            }

            if (null != user) {
                redisUtil.set(userCacheKey, user);
            }
        }

        if (null == user) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"您的账号权限异常，请联系管理员");
        }
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的账号已被平台封禁，请联系管理员");
        }

        if (EUserStatus.PERMANENT_BAN.getCode().equals(user.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的账号已被平台永久封号");
        }
        // 线上环境只能一处登陆,其他环境看系统参数配置
        if (EProfileActive.PROD.getCode().equals(profilesActive)) {
            checkToken(token, user);
        } else {
//            checkToken(token, user);
        }
        return user;
    }

    private void checkToken(String token, User user) {
        //判断是否存在key
        if (!redisUtil.hasKey(user.getId().toString())) {
            throw new BizException(ECommonErrorCode.TOKEN_ERROR);
        }

        if (redisUtil.getExpire(user.getId().toString()) >= System.currentTimeMillis()) {
            redisUtil.del(user.getId().toString());
            throw new BizException(ECommonErrorCode.TOKEN_ERROR);
        }

        if (!redisUtil.getString(user.getId().toString()).equals(token)) {
            throw new BizException(EErrorCode.TOKEN_EXPIRED);
        }
    }
}
