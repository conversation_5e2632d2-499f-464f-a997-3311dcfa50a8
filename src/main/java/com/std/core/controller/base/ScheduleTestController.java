package com.std.core.controller.base;

import com.std.common.version.ApiVersion;
import com.std.core.service.*;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 订单日志表Controller
 *
 * <AUTHOR> xiongzhiqin
 * @since : 2020-07-27 09:29
 */
@ApiVersion(1)
@RestController
@Api(value = "定时器", tags = "定时器")
@RequestMapping("{version}/schedule")
public class ScheduleTestController extends BaseController {

    @Resource
    private IScheduleService scheduleService;






}
