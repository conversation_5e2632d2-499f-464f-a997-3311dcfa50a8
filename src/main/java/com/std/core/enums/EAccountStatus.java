package com.std.core.enums;

/**
 * @author: xieyj
 * @since: 2016年11月11日 上午10:54:16
 * @history:
 */
public enum EAccountStatus {

    /**
     * 状态（1正常 2程序冻结 3人工冻结）
     *
     */
    /**
     * 正常
     */
    NORMAL("1", "正常"),

    /**
     * 程序锁定
     */
    PROGRAME_LOCK("2", "程序锁定"),

    /**
     * 人工锁定
     */
    HAND_LOCK("3", "人工锁定");

    EAccountStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
