package com.std.core.enums;

/**
 * @author: xieyj
 * @since: 2016年11月11日 上午10:54:16
 * @history:
 */
public enum EAccountType {
    /**
     * C端账户
     */
    Customer("C", "C端账号"),

    /**
     * B端账户
     */
    Merchant("B", "B端账号"),

    /**
     * 平台账户
     */
    Plat("P", "平台账号");

    EAccountType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
