package com.std.core.enums;

/**
 * @author: haiqingzheng
 * @since: 2019/1/14 19:45
 */
public enum EActionStatus {
    PRE("pre", "预上线"),

    ONLINE("online", "上线"),

    QUIT("quit", "弃用");

    EActionStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
