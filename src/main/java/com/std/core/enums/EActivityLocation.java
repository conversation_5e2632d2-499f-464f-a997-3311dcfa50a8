package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 活动Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 16:46
*/
public enum EActivityLocation {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'activity.location', '活动位置');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.location', '0', '普通');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.location', '1', '热门');


    /**
    * 普通
    */
    ACTIVITY_LOCATION_0("0", "普通"),

    /**
    * 热门
    */
    ACTIVITY_LOCATION_1("1", "热门"),

    ;

    private String code;
    private String value;

    EActivityLocation(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EActivityLocation> getActivityLocationResultMap() {
        Map<String, EActivityLocation> map = new HashMap<String, EActivityLocation>();
        for (EActivityLocation type : EActivityLocation.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EActivityLocation getActivityLocation(String code) {
        Map<String, EActivityLocation> map = getActivityLocationResultMap();
        EActivityLocation result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EActivityLocation=" + code);
        }

        return result;
    }

}
