package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 活动预约单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 23:20
*/
public enum EActivityOrderPayType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'activity_order.paytype', '活动预约单支付类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.paytype', '0', '微信');


    /**
    * 微信
    */
    ACTIVITY_ORDER_PAYTYPE_0("0", "微信"),

    ;

    private String code;
    private String value;

    EActivityOrderPayType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EActivityOrderPayType> getActivityOrderPayTypeResultMap() {
        Map<String, EActivityOrderPayType> map = new HashMap<String, EActivityOrderPayType>();
        for (EActivityOrderPayType type : EActivityOrderPayType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EActivityOrderPayType getActivityOrderPayType(String code) {
        Map<String, EActivityOrderPayType> map = getActivityOrderPayTypeResultMap();
        EActivityOrderPayType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EActivityOrderPayType=" + code);
        }

        return result;
    }

}
