package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 活动预约单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 23:20
*/
public enum EActivityOrderStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'activity_order.status', '活动预约单状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '0', '待支付');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '1', '取消支付');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '2', '预约成功,未使用');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '3', '已使用');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '4', '已过期');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '5', '取消中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_order.status', '6', '已取消');


//    /**
//    * 待支付
//    */
//    ACTIVITY_ORDER_STATUS_0("0", "待支付"),
//
//    /**
//    * 取消支付
//    */
//    ACTIVITY_ORDER_STATUS_1("1", "取消支付"),

    /**
    * 预约成功,未使用
    */
    ACTIVITY_ORDER_STATUS_0("0", "预约成功,未使用"),

    /**
    * 已使用
    */
    ACTIVITY_ORDER_STATUS_1("1", "已使用"),

    /**
    * 已过期
    */
    ACTIVITY_ORDER_STATUS_2("2", "已过期"),

    /**
    * 取消中
    */
    ACTIVITY_ORDER_STATUS_3("3", "取消中"),

    /**
    * 已取消
    */
    ACTIVITY_ORDER_STATUS_4("4", "已取消"),

    ;

    private String code;
    private String value;

    EActivityOrderStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EActivityOrderStatus> getActivityOrderStatusResultMap() {
        Map<String, EActivityOrderStatus> map = new HashMap<String, EActivityOrderStatus>();
        for (EActivityOrderStatus type : EActivityOrderStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EActivityOrderStatus getActivityOrderStatus(String code) {
        Map<String, EActivityOrderStatus> map = getActivityOrderStatusResultMap();
        EActivityOrderStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EActivityOrderStatus=" + code);
        }

        return result;
    }

}
