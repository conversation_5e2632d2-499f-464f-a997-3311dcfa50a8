package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 活动Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 16:46
*/
public enum EActivityStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'activity.status', '活动状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.status', '1', '未开始');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.status', '2', '售票中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.status', '3', '已结束');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity.status', '4', '已下架');


    /**
    * 待上架
    */
    ACTIVITY_STATUS_0("0", "待上架"),

    /**
    * 未开始
    */
    ACTIVITY_STATUS_1("1", "未开始"),

    /**
    * 售票中
    */
    ACTIVITY_STATUS_2("2", "售票中"),

    /**
    * 已结束
    */
    ACTIVITY_STATUS_3("3", "已结束"),

    /**
    * 已下架
    */
    ACTIVITY_STATUS_4("4", "已下架"),

    /**
     * 已删除
     */
    ACTIVITY_STATUS_5("5", "已删除"),
    ACTIVITY_STATUS_6("6", "已售罄"),
    ACTIVITY_STATUS_7("7", "预约结束"),

    ;

    private String code;
    private String value;

    EActivityStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EActivityStatus> getActivityStatusResultMap() {
        Map<String, EActivityStatus> map = new HashMap<String, EActivityStatus>();
        for (EActivityStatus type : EActivityStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EActivityStatus getActivityStatus(String code) {
        Map<String, EActivityStatus> map = getActivityStatusResultMap();
        EActivityStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EActivityStatus=" + code);
        }

        return result;
    }

}
