package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 票档Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 17:26
*/
public enum EActivityTicketLineStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'activity_ticket_line.status', '票档状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_ticket_line.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_ticket_line.status', '1', '上架中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'activity_ticket_line.status', '2', '已下架');


    /**
    * 待上架
    */
    ACTIVITY_TICKET_LINE_STATUS_0("0", "待上架"),

    /**
    * 上架中
    */
    ACTIVITY_TICKET_LINE_STATUS_1("1", "上架中"),

    /**
    * 已下架
    */
    ACTIVITY_TICKET_LINE_STATUS_2("2", "已下架"),

    ;

    private String code;
    private String value;

    EActivityTicketLineStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EActivityTicketLineStatus> getActivityTicketLineStatusResultMap() {
        Map<String, EActivityTicketLineStatus> map = new HashMap<String, EActivityTicketLineStatus>();
        for (EActivityTicketLineStatus type : EActivityTicketLineStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EActivityTicketLineStatus getActivityTicketLineStatus(String code) {
        Map<String, EActivityTicketLineStatus> map = getActivityTicketLineStatusResultMap();
        EActivityTicketLineStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EActivityTicketLineStatus=" + code);
        }

        return result;
    }

}
