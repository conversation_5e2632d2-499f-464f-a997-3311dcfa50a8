package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EAreaDeep {

    /**
     * 省
     */
    PROVINCE(2, "省"),

    /**
     * 市
     */
    CITY(3, "市"),

    /**
     * 区
     */
    COUNTY(4, "区");

    EAreaDeep(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    private Integer code;

    private String value;

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
