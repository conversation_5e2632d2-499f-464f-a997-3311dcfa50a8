package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EArticleLocation {

    LOCATION_0("0", "学院"),

    LOCATION_1("1", "web端底部"),

    LOCATION_2("2", "主播端在线教程");

    EArticleLocation(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
