package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 预约单大订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-30 14:59
*/
public enum EBigActivityOrderPayType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'big_activity_order.paytype', '预约单大订单支付类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'big_activity_order.paytype', '0', '微信');


    /**
    * 微信
    */
    BIG_ACTIVITY_ORDER_PAYTYPE_0("0", "微信"),

    ;

    private String code;
    private String value;

    EBigActivityOrderPayType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EBigActivityOrderPayType> getBigActivityOrderPayTypeResultMap() {
        Map<String, EBigActivityOrderPayType> map = new HashMap<String, EBigActivityOrderPayType>();
        for (EBigActivityOrderPayType type : EBigActivityOrderPayType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EBigActivityOrderPayType getBigActivityOrderPayType(String code) {
        Map<String, EBigActivityOrderPayType> map = getBigActivityOrderPayTypeResultMap();
        EBigActivityOrderPayType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EBigActivityOrderPayType=" + code);
        }

        return result;
    }

}
