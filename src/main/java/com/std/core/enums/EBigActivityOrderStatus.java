package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 预约单大订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-30 14:59
*/
public enum EBigActivityOrderStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'big_activity_order.status', '预约单大订单状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'big_activity_order.status', '0', '待支付');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'big_activity_order.status', '1', '支付失败');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'big_activity_order.status', '2', '支付成功');


    /**
    * 待支付
    */
    BIG_ACTIVITY_ORDER_STATUS_0("0", "待支付"),

    /**
    * 支付失败
    */
    BIG_ACTIVITY_ORDER_STATUS_1("1", "支付失败"),

    /**
    * 支付成功
    */
    BIG_ACTIVITY_ORDER_STATUS_2("2", "支付成功"),

    ;

    private String code;
    private String value;

    EBigActivityOrderStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EBigActivityOrderStatus> getBigActivityOrderStatusResultMap() {
        Map<String, EBigActivityOrderStatus> map = new HashMap<String, EBigActivityOrderStatus>();
        for (EBigActivityOrderStatus type : EBigActivityOrderStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EBigActivityOrderStatus getBigActivityOrderStatus(String code) {
        Map<String, EBigActivityOrderStatus> map = getBigActivityOrderStatusResultMap();
        EBigActivityOrderStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EBigActivityOrderStatus=" + code);
        }

        return result;
    }

}
