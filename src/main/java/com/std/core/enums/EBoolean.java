package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EBoolean {

    /**
     * 是
     */
    YES("1", "是"),

    /**
     * 否
     */
    NO("0", "否");

    EBoolean(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EBoolean> getBooleanResultMap() {
        Map<String, EBoolean> map = new HashMap<String, EBoolean>();
        for (EBoolean type : EBoolean.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EBoolean getBooleanResultType(String code) {
        Map<String, EBoolean> map = getBooleanResultMap();
        EBoolean result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EBoolean=" + code);
        }

        return result;
    }
}
