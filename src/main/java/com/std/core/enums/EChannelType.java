package com.std.core.enums;

/**
 * 渠道类型分两大类：外部渠道和唯一的内部渠道（内部账）
 *
 * @author: lei
 * @since: 2018年8月23日 下午9:48:56
 * @history:
 */
public enum EChannelType {
    /**
     * 内部账
     */
    INNER("inner", "内部账"),

    /**
     * 人工线下
     */
    OFF_LINE("off_line", "人工线下"),

    /**
     * 支付宝
     */
    ALIPAY("alipay", "支付宝支付"),

    /**
     * 微信
     */
    WECHAT("wechat", "微信公众号支付");

    EChannelType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
