package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

public enum EChargeStatus {

    /**
     * 待支付
     */
    To_Pay("1", "待支付"),

    /**
     * 支付失败
     */
    Pay_NO("2", "支付失败"),

    /**
     * 支付成功
     */
    Pay_YES("3", "支付成功"),

    /**
     * 支付超时
     */
    Pay_Expired("4", "支付超时");

    EChargeStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


    public static Map<String, EChargeStatus> getChargeStatusResultMap() {
        Map<String, EChargeStatus> map = new HashMap<String, EChargeStatus>();
        for (EChargeStatus type : EChargeStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EChargeStatus getChargeStatus(String code) {
        Map<String, EChargeStatus> map = getChargeStatusResultMap();
        EChargeStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EChargeStatus=" + code);
        }

        return result;
    }
   
}
