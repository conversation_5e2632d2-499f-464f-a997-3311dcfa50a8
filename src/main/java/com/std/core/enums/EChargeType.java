package com.std.core.enums;

import java.util.HashMap;
import java.util.Map;

//渠道类型分两大类：外部渠道和唯一的内部渠道（内部账）

public enum EChargeType {
    X_CHARGE("0", "用户分发地址充值"), M_CHARGE("1", "散取地址充值"), S_CHARGE("2", "补给地址充值");

    public static Map<String, EChargeType> getChannelTypeResultMap() {
        Map<String, EChargeType> map = new HashMap<String, EChargeType>();
        for (EChargeType type : EChargeType.values()) {
            map.put(type.getCode(), type);
        }
        return map;
    }

    EChargeType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
