package com.std.core.enums;

import com.std.common.exception.BizException;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 20:38
 */
public enum EClientMenu {

    /**
     * 运维中心根菜单ID
     */
    OPS(1001L, "运维中心"),

    /**
     * 厂商端根菜单ID
     */
    SYS(2001L, "平台管理后台"),

    MER(3001L, "商家管理后台"),
    /**
     * C端根菜单ID
     */
    C(4001L, "C端"),

    P(5001L, "核销端"),

    ;

    EClientMenu(Long code, String value) {
        this.code = code;
        this.value = value;
    }

    private long code;

    private String value;

    public long getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Long topMenuIdByUser(String kind) {
        long parentId = 0L;
        if (EUserKind.OPS.getCode().equals(kind)) {
            parentId = EClientMenu.OPS.getCode();
        } else if (EUserKind.SYS.getCode().equals(kind)) {
            parentId = EClientMenu.SYS.getCode();
        } else if (EUserKind.C.getCode().equals(kind)) {
            parentId = EClientMenu.C.getCode();
        } else if (EUserKind.P.getCode().equals(kind)) {
            parentId = EClientMenu.P.getCode();
        } else {
            throw new BizException(EBizErrorCode.UNDONE.getCode(), EBizErrorCode.UNDONE.getValue());
        }
        return parentId;
    }
}
