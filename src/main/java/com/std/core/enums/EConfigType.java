package com.std.core.enums;

/**
 * <AUTHOR> silver
 * @since : 2020-02-26 19:16
 */
public interface EConfigType {


    /**
     * 七牛
     */
    enum QINIU implements EConfigType {
        /**
         *
         */
        QINIU("qiniu", "七牛"),
        QINIU_ACCESS_KEY("qiniu_access_key", "公钥"),
        QINIU_SECRET_KEY("qiniu_secret_key", "私钥"),
        QINIU_BUCKET("qiniu_bucket", "空间"),
        QINIU_DOMAIN("qiniu_domain", "域名"),
        ;

        QINIU(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 任务分发
     */
    enum TASK_DISTRIBUTE implements EConfigType {
        /**
         *
         */
        TYPE("task_distribute", "任务分发"),
        TASK_DISTRIBUTE_PRIORITY_PRICE("task_distribute_priority_price", "【任务分发配置】价格优先级 0=价格越高给高套餐 1=价格越高给低套餐'"),
        TASK_DISTRIBUTE_PRIORITY_TIME("task_distribute_priority_time", "【任务分发配置】时间优先级 0=时间越早优先 1=时间越晚优先"),
        ;

        TASK_DISTRIBUTE(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 收益配置
     */
    enum INCOME implements EConfigType {
        /**
         *
         */
        TYPE("income", "收益配置"),
        INVITE_LEVEL("invite_level", "【推荐奖规则】推荐层级"),
        INVITE_BURN_MODE("invite_burn_mode", "【推荐奖规则】是否开启烧伤模式 0=否 1=是"),
        INVITE_BUY_LIMIT("invite_buy_limit", "【推荐奖规则】推荐收益是否需要自身购买 0=否 1=是"),
        NODE_INCOME_LEVEL("node_income_level", "【团队奖规则】节点收益团队最大层数"),
        NOTE_TEAM_TYPE("note_team_type", "【团队奖规则】节点收益团队类型 0=普通 1=伞形"),
        NODE_BUY_LIMIT("node_buy_limit", "【团队奖规则】节点收益是否需要自身购买 0=否 1=是");

        INCOME(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 乐拍最后秒数增加延迟配置
     */
    enum AuctionProductDelayedTime implements EConfigType {
        /**
         *
         */
        TYPE("auction_config", "乐拍延迟配置"),
        AUCTION_DELAYED_TIME("auctionDelayedTime", "乐拍延迟秒数配置");

        AuctionProductDelayedTime(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }


    /**
     * 订单配置
     */
    enum ORDERCONFID implements EConfigType {
        TYPE("order_config", "订单模块配置"),
        ORDER_EXPIRE_TIME("orderExpireTime", "订单超时过期-单位分钟"),
        ORDER_EXPIRE_DELIVERY("orderExpireDelivery", "订单到期收货-单位天"),
        SERVICE_APPLY_END("service_apply_end", "订单过了售后期不在售后-单位天"),
        USER_COMPLAINTS_EXPIRE("userComplaintsExpire", "用户申诉过期-单位天"),
        SYS_CANCEL_AFTER_SALES("sysCancelAfterSales", "系统取消售后-单位天"),
        SYS_SERVICE_CHARGE("sys_service_charge", "平台服务费提成"),
        SYS_SCORE_DEDUCTION("sys_score_deduction", "积分最大抵扣值比例"),
        SERVICE_APPLY_APPROVAL_TIME("service_apply_approval_time", "商家超时处理售后时间"),
        APPLY_PLATFORM_JOIN_EXPIRE("apply_platform_join_expire", "用户申请平台介入超时时间"),
        ;

        ORDERCONFID(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 营销模块配置
     */
    enum MARKETING implements EConfigType {
        TYPE("marketing_config", "营销模块配置"),
        MINE_TOTAL("mine_total", "矿池总量");

        MARKETING(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 信用分配置
     */
    enum CREDIT_SCORE implements EConfigType {
        TYPE("credit_score_config", "信用分配置"),
        TOTAL_CREDIT_SCORE("total_credit_score", "信用分总分"),
        UNLOCK_CREDIT_SCORE("unlock_credit_score", "用户申诉之后解冻分数"),

        PUNISH_LEVEL_1("punish_level_1", "第一档惩罚分数线"),
        PUNISH_LEVEL_2("punish_level_2", "第二档惩罚分数线"),
        PUNISH_LEVEL_3("punish_level_3", "第三档惩罚分数线"),
        PUNISH_LEVEL_4("punish_level_4", "第四档惩罚分数线"),

        RETURN_ORDER_SCORE("return_order_score", "退单扣分配置"),
        RETURN_ORDER_NUM("return_order_num", "退单订单数配置"),
        RETURN_ORDER_PERCENTAGE("return_order_percentage", "退单率配置"),

        AUCTION_NOT_PAY("auction_not_pay", "竞价商品未付款配置"),
        NEGATIVE_COMMENT("negative_comment", "差评的标准分数配置"),
        NEGATIVE_COMMENT_SCORE("negative_comment_score", "差评扣分配置"),
        NEGATIVE_COMMENT_NUM("negative_comment_num", "差评评论数配置"),
        NEGATIVE_COMMENT_PERCENTAGE("negative_comment_percentage", "差评率配置"),

        NEGATIVE_COMMENT_EXTRA_DECREASE_SCORE("negative_comment_extra_decrease_score", "差评额外一次性扣分配置"),
        NEGATIVE_COMMENT_EXTRA_DECREASE_NUM("negative_comment_extra_decrease_num", "差评额外扣分评论数配置"),
        NEGATIVE_COMMENT_EXTRA_DECREASE_PERCENTAGE("negative_comment_extra_decrease_percentage", "差评额外扣分差评率配置"),

        AUTO_RECOVERY("auto_recovery", "考核期间自动恢复分数配置"),
        EXAMINE_PERIOD("examine_period", "考核期天数配置"),
        INVITE_OR_SELF_BUY_AMOUNT("invite_or_self_buy_amount", "用户或者用户推荐人购买任何商品每满一定金额配置"),
        INVITE_OR_SELF_BUY("invite_or_self_buy", "用户或者用户推荐人购买任何商品每满一定额度恢复分数配置");


        CREDIT_SCORE(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 一口价商品配置配置
     */
    enum GOODS implements EConfigType {
        TYPE("goods", "一口价商品配置配置"),
        ENSURE("ensure", "保障"),
        ENSURE_DETAIL("ensure_detail", "保障详细内容"),
        AFTER_SALES_NOTICE("after_sales_notice", "售后注意事项");

        GOODS(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 拼单商品配置配置
     */
    enum SHARE_PRODUCT implements EConfigType {
        TYPE("share_product", "拼单商品配置配置"),
        REGULATION("regulation", "规则");

        SHARE_PRODUCT(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 乐拍商品配置配置
     */
    enum AUCTION_PRODUCT implements EConfigType {
        TYPE("auction_product", "乐拍商品配置配置"),
        COMING_END_MIN("coming_end_min", "截拍与现在时间比最小值"),
        COMING_END_MAX("coming_end_max", "截拍与现在时间比最大值"),
        //        COMING_END_TIME("coming_end_time", "即将截拍时间"),
        SHOULD_NOTICE("should_notice", "用户须知");

        AUCTION_PRODUCT(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    String getCode();

    String getValue();


}
