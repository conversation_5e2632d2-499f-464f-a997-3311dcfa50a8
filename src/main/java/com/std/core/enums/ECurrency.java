package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: xieyj
 * @since: 2016年12月24日 下午1:51:38
 * @history:
 */
public enum ECurrency {

    /**
     * 人民币
     */
    CNY("CNY", "人民币"),




    ;

    ECurrency(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, ECurrency> getMap() {
        Map<String, ECurrency> map = new HashMap<String, ECurrency>();
        for (ECurrency currency : ECurrency.values()) {
            map.put(currency.getCode(), currency);
        }
        return map;
    }

    public static ECurrency get(String code) {
        Map<String, ECurrency> map = getMap();
        ECurrency result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "KEY=" + code);
        }

        return result;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
