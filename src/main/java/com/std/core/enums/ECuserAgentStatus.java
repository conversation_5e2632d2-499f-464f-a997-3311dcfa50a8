package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * C端用户Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public enum ECuserAgentStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('cuser.agentstatus', '{"0":"合伙中","1":"已解除"}', 'C端用户合作状态');

    /**
     * 合伙中
     */
    CUSER_AGENTSTATUS_0("0", "合伙中"),

    /**
     * 已解除
     */
    CUSER_AGENTSTATUS_1("1", "已解除"),

    ;

    private String code;
    private String value;

    ECuserAgentStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECuserAgentStatus> getCuserAgentStatusResultMap() {
        Map<String, ECuserAgentStatus> map = new HashMap<String, ECuserAgentStatus>();
        for (ECuserAgentStatus type : ECuserAgentStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECuserAgentStatus getCuserAgentStatus(String code) {
        Map<String, ECuserAgentStatus> map = getCuserAgentStatusResultMap();
        ECuserAgentStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECuserAgentStatus=" + code);
        }

        return result;
    }

}
