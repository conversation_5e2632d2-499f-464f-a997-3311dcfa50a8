package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * C端用户Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public enum ECuserLevel {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('cuser.level', '{"0":"普通用户","1":"渠道商"}', 'C端用户状态');

    /**
     * 普通用户
     */
    CUSER_LEVEL_0("0", "普通用户"),


    CUSER_LEVEL_1("1", "白银用户"),

    CUSER_LEVEL_2("2", "黄金用户"),

    CUSER_LEVEL_3("3", "钻石用户"),

    ;

    private String code;
    private String value;

    ECuserLevel(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECuserLevel> getCuserLevelResultMap() {
        Map<String, ECuserLevel> map = new HashMap<String, ECuserLevel>();
        for (ECuserLevel type : ECuserLevel.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ECuserLevel getCuserLevel(String code) {
        Map<String, ECuserLevel> map = getCuserLevelResultMap();
        ECuserLevel result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECuserLevel=" + code);
        }

        return result;
    }

}
