package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 系统客服Enum
*
* <AUTHOR> xieyj
* @since : 2020-06-07 00:02
*/
public enum ECustomerServiceType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('customer_service.type', '{"1":"普通客服","2":"渠道客服"}', '系统客服类型');

    /**
    * 普通客服
    */
    CUSTOMER_SERVICE_TYPE_0("1", "普通客服"),

    /**
    * 渠道客服
    */
    CUSTOMER_SERVICE_TYPE_1("2", "渠道客服"),

    ;

    private String code;
    private String value;

    ECustomerServiceType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECustomerServiceType> getCustomerServiceTypeResultMap() {
        Map<String, ECustomerServiceType> map = new HashMap<String, ECustomerServiceType>();
        for (ECustomerServiceType type : ECustomerServiceType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static ECustomerServiceType getCustomerServiceType(String code) {
        Map<String, ECustomerServiceType> map = getCustomerServiceTypeResultMap();
        ECustomerServiceType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "ECustomerServiceType=" + code);
        }

        return result;
    }

}
