package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EDictType {


    PARENT("0", "父类"),

    CHILD("1", "子类");

    EDictType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
