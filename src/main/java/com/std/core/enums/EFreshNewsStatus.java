package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 新鲜事Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 22:43
*/
public enum EFreshNewsStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'fresh_news.status', '新鲜事状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'fresh_news.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'fresh_news.status', '1', '上架中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'fresh_news.status', '2', '已下架');


    /**
    * 待上架
    */
    FRESH_NEWS_STATUS_0("0", "待上架"),

    /**
    * 上架中
    */
    FRESH_NEWS_STATUS_1("1", "上架中"),

    /**
    * 已下架
    */
    FRESH_NEWS_STATUS_2("2", "已下架"),

    FRESH_NEWS_STATUS_3("3", "已删除"),

    ;

    private String code;
    private String value;

    EFreshNewsStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EFreshNewsStatus> getFreshNewsStatusResultMap() {
        Map<String, EFreshNewsStatus> map = new HashMap<String, EFreshNewsStatus>();
        for (EFreshNewsStatus type : EFreshNewsStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EFreshNewsStatus getFreshNewsStatus(String code) {
        Map<String, EFreshNewsStatus> map = getFreshNewsStatusResultMap();
        EFreshNewsStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EFreshNewsStatus=" + code);
        }

        return result;
    }

}
