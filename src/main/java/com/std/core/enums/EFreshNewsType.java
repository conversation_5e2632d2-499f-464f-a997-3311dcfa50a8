package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 新鲜事Enum
*
* <AUTHOR> mjd
* @since : 2024-12-25 22:43
*/
public enum EFreshNewsType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'fresh_news.type', '新鲜事类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'fresh_news.type', '0', '跳转到外部');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'fresh_news.type', '1', '跳转本系统');


    /**
    * 跳转到外部
    */
    FRESH_NEWS_TYPE_0("0", "跳转到外部"),

    /**
    * 跳转本系统
    */
    FRESH_NEWS_TYPE_1("1", "跳转本系统"),

    ;

    private String code;
    private String value;

    EFreshNewsType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EFreshNewsType> getFreshNewsTypeResultMap() {
        Map<String, EFreshNewsType> map = new HashMap<String, EFreshNewsType>();
        for (EFreshNewsType type : EFreshNewsType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EFreshNewsType getFreshNewsType(String code) {
        Map<String, EFreshNewsType> map = getFreshNewsTypeResultMap();
        EFreshNewsType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EFreshNewsType=" + code);
        }

        return result;
    }

}
