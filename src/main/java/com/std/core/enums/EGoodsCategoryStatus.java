package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品类型Enum
*
* <AUTHOR> mjd
* @since : 2024-12-26 15:55
*/
public enum EGoodsCategoryStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_category.status', '商品类型状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_category.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_category.status', '1', '上架中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_category.status', '2', '已下架');


    /**
    * 待上架
    */
    GOODS_CATEGORY_STATUS_0("0", "待上架"),

    /**
    * 上架中
    */
    GOODS_CATEGORY_STATUS_1("1", "上架中"),

    /**
    * 已下架
    */
    GOODS_CATEGORY_STATUS_2("2", "已下架"),
    GOODS_CATEGORY_STATUS_3("3", "已删除"),

    ;

    private String code;
    private String value;

    EGoodsCategoryStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsCategoryStatus> getGoodsCategoryStatusResultMap() {
        Map<String, EGoodsCategoryStatus> map = new HashMap<String, EGoodsCategoryStatus>();
        for (EGoodsCategoryStatus type : EGoodsCategoryStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsCategoryStatus getGoodsCategoryStatus(String code) {
        Map<String, EGoodsCategoryStatus> map = getGoodsCategoryStatusResultMap();
        EGoodsCategoryStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsCategoryStatus=" + code);
        }

        return result;
    }

}
