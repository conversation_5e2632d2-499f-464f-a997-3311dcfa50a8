package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品规格Enum
*
* <AUTHOR> mjd
* @since : 2024-12-26 20:59
*/
public enum EGoodsNormsStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_norms.status', '商品规格状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_norms.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_norms.status', '1', '上架中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_norms.status', '2', '已下架');


    /**
    * 待上架
    */
    GOODS_NORMS_STATUS_0("0", "待上架"),

    /**
    * 上架中
    */
    GOODS_NORMS_STATUS_1("1", "上架中"),

    /**
    * 已下架
    */
    GOODS_NORMS_STATUS_2("2", "已下架"),

    GOODS_NORMS_STATUS_3("3", "已删除"),

    ;

    private String code;
    private String value;

    EGoodsNormsStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsNormsStatus> getGoodsNormsStatusResultMap() {
        Map<String, EGoodsNormsStatus> map = new HashMap<String, EGoodsNormsStatus>();
        for (EGoodsNormsStatus type : EGoodsNormsStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsNormsStatus getGoodsNormsStatus(String code) {
        Map<String, EGoodsNormsStatus> map = getGoodsNormsStatusResultMap();
        EGoodsNormsStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsNormsStatus=" + code);
        }

        return result;
    }

}
