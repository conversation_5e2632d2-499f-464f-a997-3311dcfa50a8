package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-29 22:43
*/
public enum EGoodsOrderPayType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_order.paytype', '商品订单支付类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.paytype', '0', '微信');


    /**
    * 微信
    */
    GOODS_ORDER_PAYTYPE_0("0", "微信"),

    ;

    private String code;
    private String value;

    EGoodsOrderPayType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsOrderPayType> getGoodsOrderPayTypeResultMap() {
        Map<String, EGoodsOrderPayType> map = new HashMap<String, EGoodsOrderPayType>();
        for (EGoodsOrderPayType type : EGoodsOrderPayType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsOrderPayType getGoodsOrderPayType(String code) {
        Map<String, EGoodsOrderPayType> map = getGoodsOrderPayTypeResultMap();
        EGoodsOrderPayType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsOrderPayType=" + code);
        }

        return result;
    }

}
