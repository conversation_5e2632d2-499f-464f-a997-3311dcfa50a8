package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-29 22:43
*/
public enum EGoodsOrderReceiveWay {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_order.receiveway', '商品订单收货方式');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.receiveway', '0', '到付');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.receiveway', '1', '包邮');


    /**
    * 到付
    */
    GOODS_ORDER_RECEIVEWAY_0("0", "到付"),

    /**
    * 包邮
    */
    GOODS_ORDER_RECEIVEWAY_1("1", "包邮"),

    ;

    private String code;
    private String value;

    EGoodsOrderReceiveWay(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsOrderReceiveWay> getGoodsOrderReceiveWayResultMap() {
        Map<String, EGoodsOrderReceiveWay> map = new HashMap<String, EGoodsOrderReceiveWay>();
        for (EGoodsOrderReceiveWay type : EGoodsOrderReceiveWay.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsOrderReceiveWay getGoodsOrderReceiveWay(String code) {
        Map<String, EGoodsOrderReceiveWay> map = getGoodsOrderReceiveWayResultMap();
        EGoodsOrderReceiveWay result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsOrderReceiveWay=" + code);
        }

        return result;
    }

}
