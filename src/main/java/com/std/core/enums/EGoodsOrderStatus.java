package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-29 22:43
*/
public enum EGoodsOrderStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_order.status', '商品订单状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.status', '0', '待付款');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.status', '1', '待发货');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.status', '2', '待收货');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.status', '3', '已完成');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.status', '4', '已取消');


    /**
    * 待付款
    */
    GOODS_ORDER_STATUS_0("0", "待付款"),

    /**
    * 待发货
    */
    GOODS_ORDER_STATUS_1("1", "待发货"),

    /**
    * 待收货
    */
    GOODS_ORDER_STATUS_2("2", "待收货"),

    /**
    * 已完成
    */
    GOODS_ORDER_STATUS_3("3", "已完成"),

    /**
    * 已取消
    */
    GOODS_ORDER_STATUS_4("4", "已取消"),

    /**
     * 支付失败
     */
    GOODS_ORDER_STATUS_5("5", "支付失败"),
    ;

    private String code;
    private String value;

    EGoodsOrderStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsOrderStatus> getGoodsOrderStatusResultMap() {
        Map<String, EGoodsOrderStatus> map = new HashMap<String, EGoodsOrderStatus>();
        for (EGoodsOrderStatus type : EGoodsOrderStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsOrderStatus getGoodsOrderStatus(String code) {
        Map<String, EGoodsOrderStatus> map = getGoodsOrderStatusResultMap();
        EGoodsOrderStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsOrderStatus=" + code);
        }

        return result;
    }

}
