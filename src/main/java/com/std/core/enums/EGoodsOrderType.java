package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品订单Enum
*
* <AUTHOR> mjd
* @since : 2024-12-29 22:43
*/
public enum EGoodsOrderType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_order.type', '商品订单类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.type', '0', '普通订单');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_order.type', '1', '购物车订单');


    /**
    * 普通订单
    */
    GOODS_ORDER_TYPE_0("0", "普通订单"),

    /**
    * 购物车订单
    */
    GOODS_ORDER_TYPE_1("1", "购物车订单"),

    ;

    private String code;
    private String value;

    EGoodsOrderType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsOrderType> getGoodsOrderTypeResultMap() {
        Map<String, EGoodsOrderType> map = new HashMap<String, EGoodsOrderType>();
        for (EGoodsOrderType type : EGoodsOrderType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsOrderType getGoodsOrderType(String code) {
        Map<String, EGoodsOrderType> map = getGoodsOrderTypeResultMap();
        EGoodsOrderType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsOrderType=" + code);
        }

        return result;
    }

}
