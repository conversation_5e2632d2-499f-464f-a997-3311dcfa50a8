package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 商品Enum
*
* <AUTHOR> mjd
* @since : 2024-12-26 20:44
*/
public enum EGoodsStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods.status', '商品状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods.status', '0', '待上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods.status', '1', '上架中');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods.status', '2', '已下架');


    /**
    * 待上架
    */
    GOODS_STATUS_0("0", "待上架"),

    /**
    * 上架中
    */
    GOODS_STATUS_1("1", "上架中"),

    /**
    * 已下架
    */
    GOODS_STATUS_2("2", "已下架"),
    /**
    * 已删除
    */
    GOODS_STATUS_3("3", "已删除"),

    ;

    private String code;
    private String value;

    EGoodsStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsStatus> getGoodsStatusResultMap() {
        Map<String, EGoodsStatus> map = new HashMap<String, EGoodsStatus>();
        for (EGoodsStatus type : EGoodsStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsStatus getGoodsStatus(String code) {
        Map<String, EGoodsStatus> map = getGoodsStatusResultMap();
        EGoodsStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsStatus=" + code);
        }

        return result;
    }

}
