package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 购物车Enum
*
* <AUTHOR> mjd
* @since : 2024-12-27 16:53
*/
public enum EGoodsTrolleyStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'goods_trolley.status', '购物车状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_trolley.status', '0', '已购买');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_trolley.status', '1', '未购买');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'goods_trolley.status', '2', '已删除');


    /**
    * 已购买
    */
    GOODS_TROLLEY_STATUS_0("0", "已购买"),

    /**
    * 未购买
    */
    GOODS_TROLLEY_STATUS_1("1", "未购买"),

    /**
    * 已删除
    */
    GOODS_TROLLEY_STATUS_2("2", "已删除"),

    ;

    private String code;
    private String value;

    EGoodsTrolleyStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGoodsTrolleyStatus> getGoodsTrolleyStatusResultMap() {
        Map<String, EGoodsTrolleyStatus> map = new HashMap<String, EGoodsTrolleyStatus>();
        for (EGoodsTrolleyStatus type : EGoodsTrolleyStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EGoodsTrolleyStatus getGoodsTrolleyStatus(String code) {
        Map<String, EGoodsTrolleyStatus> map = getGoodsTrolleyStatusResultMap();
        EGoodsTrolleyStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EGoodsTrolleyStatus=" + code);
        }

        return result;
    }

}
