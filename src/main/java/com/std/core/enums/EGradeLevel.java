package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * C端用户Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public enum EGradeLevel {


    /**
     * 普通用户
     */
    CUSER_LEVEL_0("0", "普通用户"),


    CUSER_LEVEL_1("1", "顾问"),

    CUSER_LEVEL_2("2", "主任"),

    CUSER_LEVEL_3("3", "经理"),

    ;

    private String code;
    private String value;

    EGradeLevel(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EGradeLevel> getCuserLevelResultMap() {
        Map<String, EGradeLevel> map = new HashMap<String, EGradeLevel>();
        for (EGradeLevel type : EGradeLevel.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EGradeLevel getCuserLevel(String code) {
        Map<String, EGradeLevel> map = getCuserLevelResultMap();
        EGradeLevel result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ECuserLevel=" + code);
        }

        return result;
    }

}
