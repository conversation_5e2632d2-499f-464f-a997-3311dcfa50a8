package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 收益Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-06-06 23:13
 */
public enum EIncomeAmountType {

    /**
     * 任务金
     */
    TYPE_0("0", "消费金"),
    TYPE_1("1", "推荐商家收入金"),
    TYPE_2("2", "分享收入金");

    private String code;
    private String value;

    EIncomeAmountType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EIncomeAmountType> getIncomeAmountTypeResultMap() {
        Map<String, EIncomeAmountType> map = new HashMap<String, EIncomeAmountType>();
        for (EIncomeAmountType type : EIncomeAmountType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EIncomeAmountType getIncomeAmountType(String code) {
        Map<String, EIncomeAmountType> map = getIncomeAmountTypeResultMap();
        EIncomeAmountType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EIncomeAmountType=" + code);
        }

        return result;
    }

}
