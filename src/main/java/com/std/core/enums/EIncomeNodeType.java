package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 收益Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-06-06 23:13
 */
public enum EIncomeNodeType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('income.nodetype', '{"0":"1星","1":"2星","2":"3星"}', '收益节点类型');

    /**
     * 1星
     */
    INCOME_NODETYPE_0("0", "1星"),

    /**
     * 2星
     */
    INCOME_NODETYPE_1("1", "2星"),

    /**
     * 3星
     */
    INCOME_NODETYPE_2("2", "3星"),

    ;

    private String code;
    private String value;

    EIncomeNodeType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EIncomeNodeType> getIncomeNodeTypeResultMap() {
        Map<String, EIncomeNodeType> map = new HashMap<String, EIncomeNodeType>();
        for (EIncomeNodeType type : EIncomeNodeType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EIncomeNodeType getIncomeNodeType(String code) {
        Map<String, EIncomeNodeType> map = getIncomeNodeTypeResultMap();
        EIncomeNodeType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EIncomeNodeType=" + code);
        }

        return result;
    }

}
