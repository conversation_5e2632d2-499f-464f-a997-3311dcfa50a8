package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 收益Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-06-06 23:13
 */
public enum EIncomeTeamType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('income.teamtype', '{"0":"1代","1":"2代"}', '收益团队类型');

    /**
     * 1代
     */
    INCOME_TEAMTYPE_0("0", "1代"),

    /**
     * 2代
     */
    INCOME_TEAMTYPE_1("1", "2代"),

    ;

    private String code;
    private String value;

    EIncomeTeamType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EIncomeTeamType> getIncomeTeamTypeResultMap() {
        Map<String, EIncomeTeamType> map = new HashMap<String, EIncomeTeamType>();
        for (EIncomeTeamType type : EIncomeTeamType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EIncomeTeamType getIncomeTeamType(String code) {
        Map<String, EIncomeTeamType> map = getIncomeTeamTypeResultMap();
        EIncomeTeamType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EIncomeTeamType=" + code);
        }

        return result;
    }

}
