package com.std.core.enums;

/**
* 类型Enum
*
* <AUTHOR> <PERSON>
* @since : 2020-06-06 23:13
*/
public enum EIncomeType {

    /**
    * 自身收益
    */
    INCOME_TYPE_0("0", "自身收益"),

    /**
    * 推荐收益
    */
    INCOME_TYPE_1("1", "推荐收益"),

    /**
     * 节点收益
     */
    INCOME_TYPE_2("2", "节点收益");

    private String code;
    private String value;

    EIncomeType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
