package com.std.core.enums;

/**
 *
 */
public enum EJourArriveAccountType {

    /**
     * 入账类型（1支出 2收入）
     */

    /**
     * 支出
     */
    EXPENDITURE("1", "支出"),

    /**
     * 收入
     */
    INCOME("2", "收入");

    EJourArriveAccountType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
