package com.std.core.enums;

/**
 * <AUTHOR> silver
 * @since : 2020-02-26 19:16
 */

public interface EJourBizTypeSeller {

    /**
     * 兑换相关业务
     */
    enum Exchange implements EJourCommon {
        /**
         *
         */
        Exchange("exchange", "兑换", "兑换", ""),
        CNY_TO_BOND("cnyToBond", "余额兑换成保证金", "余额兑换成保证金", "cnyToBond"),
        BOND_TO_CNY("bondToCny", "保证金兑换成余额冻结", "保证金兑换成余额冻结", "bondToCny"),
        CNY_TO_LUCKBEAN("cnyToLuckBean", "余额兑换成幸运豆", "余额兑换成幸运豆", ""),
        ALLOWANCE_TO_CNY("allowanceToCny", "小南额兑换成余额冻结", "小南额兑换成余额冻结", ""),
        ;


        Exchange(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 取现相关业务
     */
    enum Withdraw implements EJourCommon {
        /**
         *
         */
        Withdraw("withdraw", "取现", "取现到%s%s银行卡", "withdraw to %s %s bankcard"),
        Withdraw_Fee("withdraw fee", "取现手续费", "取现手续费", "withdraw fee"),
        Withdraw_Forzen("withdraw forzen", "取现冻结", "取现到%s%s银行卡", "withdraw to %s %s bankcard"),
        Withdraw_UnForzen("withdraw unforzen", "取现解冻", "取现到%s%s银行卡", "withdraw to %s %s bankcard");

        Withdraw(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 充值相关业务
     */
    enum Charge implements EJourCommon {
        /**
         *
         */
        Charge("charge", "充值", "来自%s充值渠道", "from %s charge channel");

        Charge(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    /**
     * 一口价
     */
    enum Goods implements EJourCommon {
        /**
         *
         */
        GOODS("goods", "一口价", "一口价的商品", "goods"),
        PURCHASE_GOODS("purchase_goods", "购买一口价商品", "%s购买一口价商品", " %s purchase goods"),
        AFFIRM_RECEIPT_GOODS("affirm_receipt_good", "确认收货一口价商品",
                "确认收货一口价商品", "affirm_receipt_good"),
        CANCEL_ORDER_REFUND("cancel_order_refund", "商家取消订单退款",
                "商家取消订单退款", "cancel_order_refund"),
        AFTER_SALES_REFUND("after_sales_refund", "售后退款",
                "售后退款", "after_sales_refund");

        Goods(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 乐拍
     */
    enum Auction implements EJourCommon {
        /**
         *
         */
        Auction("auction", "乐拍", "乐拍", "auction"),
        Bond("bond", "保证金", "%s的乐拍保证金", "%s auction bond"),
        Auction_Pay("auctionPay", "%s的乐拍支付", "%s的乐拍支付", "%s auction pay"),
        ;

        Auction(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 孤品
     */
    enum Solitary implements EJourCommon {
        /**
         *
         */
        SOLITARY("solitary", "孤品", "孤品", "solitary"),
        INTENTION_PRICE("intention_price", "意向金", "%s意向金", "%s intention price"),
        ;

        Solitary(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 主播提成
     */
    enum AnchorCommission implements EJourCommon {
        /**
         *
         */
        AnchorCommission("anchorCommission", "主播提成", "主播提成", "anchor commission"),
        GoodsCommission("goods_commission", "一口价提成", "一口价提成", "goods commission"),
        ;

        AnchorCommission(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 主播赔偿
     */
    enum AnchorCompensate implements EJourCommon {
        /**
         *
         */
        AnchorCompensate("anchorCompensate", "主播赔偿", "主播赔偿", "anchor compensate"),
        GoodsCompensate("goods_compensate", "一口价赔偿", "一口价赔偿", "goods compensate"),
        ;

        AnchorCompensate(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

}
