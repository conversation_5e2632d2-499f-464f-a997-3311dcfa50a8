package com.std.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> silver
 * @since : 2020-02-26 19:16
 */

public interface EJourBizTypeSystem {

    /**
     * 取现相关业务
     */
    enum Withdraw implements EJourCommon {
        /**
         *
         */
        Withdraw("withdraw", "取现", "用户提现", "user withdraw"),
        Withdraw_User_Fee("withdraw user fee", "取现用户手续费", "取现用户手续费", "withdraw user fee"),
        Withdraw_Bank_Fee("withdraw bank fee", "取现渠道手续费", "取现渠道手续费", "withdraw bank fee"),
        ;

        Withdraw(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 充值相关业务
     */
    enum Charge implements EJourCommon {
        /**
         *
         */
        Charge("charge", "充值", "来自%s用户%s充值渠道", " from user %s %s charge channel"),
        Settle("settle", "结算", "结算", "settle");

        Charge(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 公账投资
     */
    enum Invest implements EJourCommon {
        /**
         *
         */
        Invest("invest", "公账投资", "公账投资", "corporate account invest"),
        addlInvest("add invest", "投资", "投资", "corporate account add invest"),
        cancelInvest("cancel invest", "撤资", "撤资", "corporate account cancel invest"),
        ;

        Invest(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    /**
     * 用户消费
     */
    enum UserConsume implements EJourCommon {
        /**
         *
         */
        UserConsume("user consume", "用户消费", "用户消费", "user consume"),
        BuyMeal("buy meal", "购买套餐", "购买[%s]", "buy meal"),
        BuyMember("buy member", "购买会员", "购买会员", "buy member"),
        PublishTask("publish task", "发布任务费用", "发布任务费用", "publish task"),
        PublishTask_Back("publish task back", "发布任务费用退回", "发布任务费用退回", "publish task back"),
        PublishTaskFee("publish task fee", "发布任务手续费", "发布任务手续费", "publish task fee");

        UserConsume(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 用户收益
     */
    enum UserIncome implements EJourCommon {
        /**
         *
         */
        UserIncome("user income", "收益", "收益", "user income"),
        SelfIncome("self income", "任务金", "任务金", "self income"),
        InviteIncome("invite income", "推广收益", "推广收益", "invite income"),
        NodeIncome("node income", "团队奖励", "团队奖励", "node income"),
        ;

        UserIncome(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 结算
     */
    enum Settle implements EJourCommon {
        /**
         *
         */
        Settle("settle", "结算", "结算", "settle"),
        TrustToCorp("trust to corp", "托管账户往公帐账户结算", "托管账户往公帐账户结算", "settle"),
        BizToClinic("biz to clinic", "厂家业务账户往诊所账户结算", "厂家业务账户往诊所【%s】账户结算", "settle to [%s]"),
        ClinicToBiz("clinic to biz", "诊所授信结算", "诊所授信结算", "settle"),
        SettleFee("settle fee", "结算手续费", "结算手续费", "settle fee"),
        ;

        Settle(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 其他
     */
    enum Other implements EJourCommon {
        /**
         *
         */
        Operation("operation", "公账营运支出", "公账营运支出", "corporate account cost"),
        Other("other", "其他收入", "其他收入", "other income"),
        OTHER_INCOME("other_income", "其他收入", "其他收入", "other_income"),
        OTHER_EXPENDITURE("other_expenditure", "其他支出", "其他支出", "other_expenditure"),
        ;

        Other(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 商户提成
     */
    enum COMMISSION_SELLER implements EJourCommon {
        /**
         *
         */
        COMMISSION_SELLER("commission_seller", "商户提成", "商户提成", "commission seller"),
        GOODS_COMMISSION("goods_commission", "一口价提成", "一口价提成", "goods commission income"),
        AUCTION_COMMISSION("auction_commission", "乐拍提成", "乐拍提成", "auction commission income"),
        SOLITARY_COMMISSION("solitary_commission", "孤品提成", "孤品提成", "solitary commission income"),
        ;


        COMMISSION_SELLER(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 任务包
     */
    enum Assignment implements EJourCommon {
        /**
         *
         */
        Assignment("assignment", "任务", "任务", "assignment"),
        Purchase_assignment_pack("purchase_assignment_pack", "用户购买任务包", "用户购买任务包", "purchase assignment pack"),
        Assignment_award("assignment_award", "任务包分销", "任务包分销", "assignment award"),
        ;

        Assignment(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    String getCode();

    String getValue();

    String getRemark();

    String getEnRemark();

    static EJourCommon matchCode(String code) {
        EJourCommon eJourCommon = null;
        List<EJourCommon[]> list = new ArrayList<>();
        list.add(EJourBizTypeSystem.Withdraw.values());
        list.add(EJourBizTypeSystem.Charge.values());
        list.add(EJourBizTypeSystem.UserConsume.values());
        list.add(EJourBizTypeSystem.UserIncome.values());

        for (EJourCommon[] jourCommons : list) {
            for (EJourCommon jourCommon : jourCommons) {
                if (code.equals(jourCommon.getCode())) {
                    eJourCommon = jourCommon;
                }
            }
        }

        return eJourCommon;
    }
}
