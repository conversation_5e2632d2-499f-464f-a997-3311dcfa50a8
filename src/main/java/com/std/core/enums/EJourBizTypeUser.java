package com.std.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> silver
 * @since : 2020-02-26 19:16
 */

public interface EJourBizTypeUser {
    enum AssetBiz implements EJourCommon {
        AmountChange("amount change", "金额变动", "%s", "amount change"),
        ManualChange("manual change", "后台变更", "%s", ""),
        AppleChange("manual change", "苹果充值", "%s", ""),

        ;

        private String code;
        private String value;
        private String remark;
        private String enRemark;

        AssetBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 划转
     */
    enum Transfer implements EJourCommon {
        /**
         *
         */
        Transfer("transfer", "转账", "转账", "transfer"),
        TransferOut("transfer out", "转出", "转给：%S%S", "transfer out"),
        TransferIn("transfer in", "转入", "来自：%S%S", "transfer in");

        Transfer(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    /**
     * 闪兑
     */
    enum Exchange implements EJourCommon {
        /**
         *
         */
        Exchange("exchange", "兑换", "兑换", "exchange"),
        ExchangeOut("exchange out", "兑换兑出", "兑换比例：%S：%S，手续费：%S", "exchange out"),
        ExchangeIn("exchange in", "兑换兑入", "兑换比例：%S：%S", "exchange in");

        Exchange(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 一卡通业务
     */
    enum YktBiz implements EJourCommon {
        /**
         *
         */

        Offline_Import("offline import", "线下导入", "线下导入", "offline import"),
        Offline_Import_In("offline import in", "线下导入", "", "offline import in"),

        Mall_Use("mall use", "商城使用", "商城使用", "mall use"),
        Mall_Use_Out("mall use out", "订单抵扣", "订单编号：%S", "mall use out"),
        Mall_Use_Frozen("mall use frozen", "商城抵扣冻结", "订单编号：%S", "mall use frozen"),
        Mall_Use_Unfrozen("mall use unfrozen", "商城抵扣解冻", "订单编号：%S", "mall use unfrozen"),

        Agent_Use("agent use", "专区使用", "专区使用", "agent use"),
        Agent_Use_Out("agent use out", "订单抵扣", "订单编号：%S", "agent use out"),
        Agent_Use_Frozen("agent use frozen", "专区抵扣冻结", "订单编号：%S", "agent use frozen"),
        Agent_Use_Unfrozen("agent use unfrozen", "专区抵扣解冻", "订单编号：%S", "agent use unfrozen"),
        Agent_Assets_Out("agent assets out", "专区管理费抵扣", "订单编号：%S", "agent assets out"),
        Agent_Assets_Frozen("agent assets frozen", "专区管理费抵扣冻结", "订单编号：%S", "agent assets frozen"),
        Agent_Assets_Unfrozen("agent assets unfrozen", "专区管理费抵扣解冻", "订单编号：%S", "agent assets unfrozen"),


        Withdrawal("withdrawal", "提现", "提现", "withdrawal"),
        Ykt_Withdraw_Out("ykt withdraw out", "提现", "提现到%S%S银行卡", "ykt withdraw out"),
        Withdraw_Frozen("withdraw frozen", "提现冻结", "提现到%S%S银行卡", "withdraw frozen"),
        Withdraw_Unfrozen("withdraw unfrozen", "提现解冻", "提现到%S%S银行卡", "withdraw unfrozen"),

        Amount_Change("amount change", "金额变动", "金额变动", "amount change"),
        Manual_Change("manual change", "手动加减金额", "%S", "manual change"),
        /********************************************/
        Ykt("Ykt", "一卡通", "一卡通", "Ykt"),
        Ykt_Withdraw("Ykt Withdraw", "一卡通提现", "一卡通提现", "Ykt Withdraw"),
        Order_Deduction("Order Deduction", "订单抵扣", "订单抵扣", "Order Deduction"),
        //        Offline_Import("Offline Import", "线下导入", "线下导入", "Offline Import"),
        Ykt_Frozen("Ykt Frozen", "一卡通冻结", "一卡通冻结", "Ykt Frozen"),
        Ykt_Unfrozen("Ykt Unfrozen", "一卡通解冻", "一卡通解冻", "Ykt Unfrozen"),

        Sacrifice("sacrifice", "链上祭祀", "链上祭祀", "sacrifice"),
        Sacrifice_Goods_Out("agent goods out", "购买祭祀商品抵扣", "购买记录编号：%S", "agent goods out"),
        Sacrifice_Venue_Out("agent venue out", "购买祭祀场馆抵扣", "购买记录编号：%S", "agent venue out"),

        Sacrifice_Goods_Frozen("agent goods frozen", "购买祭祀商品抵扣冻结", "购买记录编号：%S", "agent goods frozen"),
        Sacrifice_Venue_Frozen("agent venue frozen", "购买祭祀场馆抵扣冻结", "购买记录编号：%S", "agent venue frozen"),

        Sacrifice_Goods_Unfrozen("agent goods unfrozen", "购买祭祀商品抵扣解冻", "购买记录编号：%S", "agent goods unfrozen"),
        Sacrifice_Venue_Unfrozen("agent venue unfrozen", "购买祭祀场馆抵扣解冻", "购买记录编号：%S", "agent venue unfrozen"),
        ;

        YktBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 银杏果业务
     */
    enum YxFruitBiz implements EJourCommon {
        /**
         *
         */
        Rebate("rebate", "返利", "返利", "rebate"),
        Yxfruit_Rebate_In("yxfruit rebate in", "一卡通返利", "一卡通编号：%S", "yxfruit rebate in"),
        Agent_Zk_In("agent zk in", "专区折扣回馈", "折扣回馈-%S", "agent zk in"),
        Agent_Tg_In("agent tg in", "专区推广佣金", "推广佣金-%S", "agent tg in"),
        Agent_Yj_In("agent yj in", "专区业绩奖励", "业绩奖励-%S", "agent yj in"),


        Withdraw("withdraw", "提现", "提现", "withdraw"),
        Yxfruit_Withdraw_Out("yxfruit withdraw out", "提现", "提现到%S%S银行卡", "yxfruit withdraw out"),

        Withdraw_Frozen("withdraw frozen", "提现冻结", "提现到%S%S银行卡", "withdraw frozen"),
        Withdraw_Unfrozen("withdraw unfrozen", "提现解冻", "提现到%S%S银行卡", "withdraw unfrozen"),

        /*********************/
        YxFruit("YxFruit", "银杏果", "银杏果", "YxFruit"),
        YxFruit_Creat("YxFruit Creat", "一卡通返利", "一卡通返利", "YxFruit Creat"),
        YxFruit_Withdrawal("YxFruit Withdrawal", "银杏果提现", "银杏果提现", "YxFruit Withdrawal"),
        ;

        YxFruitBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 银杏叶业务
     */
    enum YxLeeafBiz implements EJourCommon {
        /**
         *
         */
        Rebate("rebate", "返利", "返利", "rebate"),
        Yxleaf_Rebate_In("yxleaf rebate in", "一卡通返利", "一卡通编号：%S", "yxleaf rebate in"),
        Yxleaf_Rebate_In_Kyk("yxleaf rebate in kyk", "庄园康养卡返利", "庄园康养卡编号：%S", "yxleaf rebate in kyk"),


        Transfer("transfer", "转账", "转账", "transfer"),
        Transfer_Out("transfer out", "转出", "转给：%S", "transfer out"),
        Transfer_In("transfer in", "转入", "来自：%S", "transfer in"),

        Exchange("exchange", "兑换", "兑换", "exchange"),
        Exchange_In("exchange in", "兑入", "兑换比例：%S，手续费：%S", "exchange in"),
        Exchange_Out("exchange out", "兑出", "兑换比例：%S，手续费：%S", "exchange out"),

        /****************************/
        YxLeaf("YxLeaf", "银杏叶", "银杏叶", "YxLeaf"),
        YxLeaf_Creat("YxLeaf Creat", "一卡通返利", "一卡通返利", "YxLeaf Creat"),
        Exchange_To_Integral("Exchange To Integral", "兑换成商城积分", "兑换成商城积分", "Exchange To Integral"),
        YxLeaf_Transfer("YxLeaf Transfer", "银杏叶转账", "银杏叶转账", "YxLeaf Transfer"),
        ;

        YxLeeafBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 银杏宝业务
     */
    enum YxBabyBiz implements EJourCommon {
        /**
         *
         */
        Buy_Obtain("buy obtain", "购买获得", "购买获得", "buy obtain"),
        Yxbaby_Buy_in("yxbaby buy in", "银杏宝购买获得", "", "yxbaby buy in"),

        Offline_Import("offline import", "线下导入", "线下导入", "offline import"),
        Yxbaby_import_in("yxbaby import in", "银杏宝线下导入", "", "yxbaby import in"),

        Redeem("redeem", "赎回", "赎回", "redeem"),
        Yxbaby_Redeem_In("yxbaby redeem in", "银杏宝赎回", "", "yxbaby redeem in"),

        Use("use", "使用", "使用", "use"),
        Yxbaby_Use_Out("yxbaby use out", "银杏宝使用", "", "yxbaby use out"),

        Substitution("Substitution", "置换", "置换", "Substitution"),
        Yxbaby_Substitution_Out("yxbaby substitution out", "银杏宝置换", "", "yxbaby substitution out"),

        Pledge("pledge", "质押", "质押", "pledge"),
        Yxbaby_Pledge_Out("yxbaby pledge out", "银杏宝质押", "", "yxbaby pledge out"),

        Amount_Change("amount change", "金额变动", "金额变动", "amount change"),
        Manual_Change("manual change", "后台变更", "", ""),
        /***********************************************************/
        YxBaby("YxBaby", "银杏宝", "银杏宝", "YxBaby"),
        YxBaby_Import("YxBaby Import", "银杏宝线下导入", "银杏宝线下导入", "YxBaby Import"),
        YxBaby_Redeem("YxBaby Redeem", "银杏宝赎回", "银杏宝赎回", "YxBaby Redeem"),
        YxBaby_Use("YxBaby Use", "银杏宝使用", "银杏宝使用", "YxBaby Use"),
        YxBaby_Substitution("YxBaby Substitution", "银杏宝置换", "银杏宝置换", "YxBaby Substitution"),
        YxBaby_Pledge("YxBaby Pledge", "银杏宝质押获得", "银杏宝质押获得", "YxBaby Pledge"),

        ;

        YxBabyBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 质押NAT业务
     */
    enum PledgeNatBiz implements EJourCommon {
        /**
         *
         */
        Pledge("pledge", "质押", "质押", "pledge"),
        Yxbaby_Pledge_In("yxbaby pledge in", "银杏宝质押获得", "银杏宝编号:%S", "yxbaby pledge in"),

        Self_Transfer("self transfer", "划转", "划转", "self transfer"),
        Circulation_Out("circulation out", "转出到可流通", "", "circulation out"),

        Ore("ore", "矿场", "矿场", "ore"),
        To_Ore_Out("to ore out", "投入矿场", "", ""),
        Ore_Release_In("ore release in", "矿场释放", "收益：%s", "ore release in"),

        /*******************/
        PledgeNat("PledgeNat", "质押NAT", "质押NAT", "PledgeNat"),
        To_Circulation("to circulation", "转入到可流通", "转入到可流通", "to circulation"),
        Ore_Release("Ore Release", "矿场释放", "矿场释放", "Ore Release"),
        TO_Ore("TO Ore", "转入矿场", "转入矿场", "TO Ore"),
        YxBaby_Pledge("YxBaby Pledge", "银杏宝质押获得", "银杏宝质押获得", "YxBaby Pledge"),

        ;

        PledgeNatBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 流通NAT
     */
    enum CirculationNatBiz implements EJourCommon {
        /**
         *
         */
        Charge("charge", "充币", "充币", "charge"),
        Turn_In("turn in", "充币", "来自：%S地址", "turn in"),
        Fast_Turn_In("fast turn in", "充币", "", "fast turn in"),

        Withdraw("withdraw", "提币", "提币", "withdraw"),
        Turn_Out("turn out", "提币", "提币到%S地址，手续费：%S", "turn out"),
        Nat_Forzen("nat forzen", "提币冻结", "提币到%S地址", "nat forzen"),
        Nat_Unforzen("nat unforzen", "提币解冻", "提币到%S地址", "nat unforzen"),

        Self_Transfer("self transfer", "划转", "划转", "self transfer"),
        Circulation_In("circulation in", "质押NAT转入", "", "circulation in"),

        Exchange("exchange", "兑换", "兑换", "exchange"),
        Exchange_In("exchange in", "兑入", "兑换比例：%S，手续费：%S", "exchange in"),
        Exchange_Out("exchange out", "兑出", "兑换比例：%S，手续费：%S", "exchange out"),

        Activity("activity", "活动", "活动", "activity"),
        Join_Activity_Out("join activity out", "参加活动", "%S活动第%S期，手续费：%S", "join activity out"),
        Automatic_Input_Out("automatic input out", "自动投入", "%S活动第%S期，手续费：%S", "automatic input out"),
        Manual_Input_Out("manual input out", "手动投入", "%S活动第%S期，手续费：%S", "manual input out"),
        Backstage_Input_Out("backstage input out", "后台投入", "%S活动第%S期，手续费：%S", "backstage input out"),
        Activity_Profit_In("activity profit in", "活动收益", "%S活动第%S期", "activity profit in"),

        Agent_Use("agent use", "专区使用", "专区使用", "withdraw"),
        Agent_Use_Out("agent use out", "订单抵扣", "订单编号：%S", "agent use out"),
        Agent_Use_Frozen("agent use frozen", "专区订单抵扣冻结", "订单编号：%S", "agent use frozen"),
        Agent_Use_Unfrozen("agent use unfrozen", "专区订单抵扣解冻", "订单编号：%S", "agent use unfrozen"),
        Agent_Assets_Out("agent assets out", "专区管理费抵扣", "订单编号：%S", "agent assets out"),
        Agent_Assets_Frozen("agent assets frozen", "专区管理费抵扣冻结", "订单编号：%S", "agent assets frozen"),
        Agent_Assets_Unfrozen("agent assets unfrozen", "专区管理费抵扣解冻", "订单编号：%S", "agent assets unfrozen"),
        Agent_Rebate("agent rebate", "专区返利", "专区返利", "agent rebate"),
        Agent_zk_Rebate("agent zk rebate", "专区折扣回馈", "折扣回馈-%S", "agent rebate"),
        Agent_tg_Rebate("agent tg rebate", "专区推广佣金", "推广佣金-%S", "agent rebate"),
        Agent_yj_Rebate("agent yj rebate", "专区业绩奖励", "业绩奖励-%S", "agent rebate"),


        Unlock("unlock", "解锁包", "解锁包", "unlock"),
        Yxbaby_Substitution_In("yxbaby substitution in", "银杏宝置换获得", "银杏宝编号:%S", "yxbaby substitution in"),
        Yxbaby_Use_In("yxbaby use in ", "银杏宝使用获得", "银杏宝编号:%S", "yxbaby use in "),
        Offline_Import_Unlock_In("offline import unlock in", "解锁包释放", "解锁包编号:%S", "offline import unlock in"),

        Redeem("redeem", "赎回", "赎回", "redeem"),
        Yxbaby_Redeem_Out("yxbaby redeem out", "赎回银杏宝", "银杏宝编号：%S", "yxbaby redeem out"),

        Amount_Change("amount change", "金额变动", "金额变动", "amount change"),
        Manual_Change("manual change", "后台变更", "%S", ""),
        ;

        CirculationNatBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 业务大类
     */
    enum BigCategoryBiz implements EJourCommon {
        /**
         *
         */
        Transfer("Transfer", "划转", "划转", "Transfer"),
        Ore("ore", "矿场", "矿场", "ore"),
        YxBaby_Pledge("YxBaby Pledge", "银杏宝质押", "银杏宝质押", "YxBaby Pledge"),

        ;

        BigCategoryBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 活动业务
     */
    enum ActivityBiz implements EJourCommon {
        /**
         *
         */
        Activity("Activity", "活动", "活动", "Activity"),
        Join_Activity("Join Activity", "参加活动", "参加活动", "Join Activity"),
        Automatic_Input("Automatic Input", "自动投入", "自动投入", "Automatic Input"),
        Service_Charge("Service Charge", "活动手续费", "活动手续费", "Service Charge"),
        Manual_Input("Manual Input", "手动投入", "手动投入", "Manual Input"),
        Backstage_Input("Backstage Input", "后台投入", "后台投入", "Backstage Input"),
        Activity_Profit("Activity Profit", "活动收益", "活动收益", "Activity Profit");

        ActivityBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 充值相关业务
     */
    enum Charge implements EJourCommon {
        /**
         *
         */
        Charge("charge", "充值", "充值", "charge"),
        Charge_Wechat("charge wechat", "微信充值", "微信充值", "charge from wechat"),
        Charge_Alipay("charge alipay", "支付宝充值", "支付宝充值", "charge from alipay"),

        Turn_In("turn in", "充币", "充币", "turn in");

        Charge(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 取现相关业务
     */
    enum Withdraw implements EJourCommon {
        /**
         *
         */
        Withdraw("withdraw", "取现", "取现到%s%s银行卡", "withdraw to %s %s bankcard"),
        Withdraw_Fee("withdraw fee", "取现手续费", "取现手续费", "withdraw fee"),
        Withdraw_Forzen("withdraw forzen", "取现冻结", "取现到%s%s银行卡", "withdraw to %s %s bankcard"),
        Withdraw_UnForzen("withdraw unforzen", "取现解冻", "取现到%s%s银行卡", "withdraw to %s %s bankcard"),

        Turn_Out("turn out", "提币", "提币", "turn out"),
        Turn_Fee("turn fee", "提币手续费", "提币手续费", "turn fee"),
        Turn_Forzen("turn forzen", "提币冻结", "提币冻结", "turn forzen"),
        Turn_UnForzen("turn unforzen", "提币解冻", "提币解冻", "turn unforzen");

        Withdraw(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

//    /**
//     * 一卡通业务小类
//     */
//    enum yktBizType implements EJourCommon {
//        /**
//         *
//         */
//        Withdraw("Withdraw", "提现", "提现", "Withdraw"),
//        consumption("consumption", "商城消费", "商城消费", "consumption"),
//        OfflineImport("OfflineImport", "线下导入", "线下导入", "OfflineImport")
//        ;
//
//        yktBizType(String code, String value, String remark, String enRemark) {
//            this.code = code;
//            this.value = value;
//            this.remark = remark;
//            this.enRemark = enRemark;
//        }
//
//        private String code;
//
//        private String value;
//
//        private String remark;
//
//        private String enRemark;
//
//        @Override
//        public String getCode() {
//            return code;
//        }
//
//        @Override
//        public String getValue() {
//            return value;
//        }
//
//        @Override
//        public String getRemark() {
//            return remark;
//        }
//
//        @Override
//        public String getEnRemark() {
//            return enRemark;
//        }
//    }

    /**
     * 用户消费
     */
    enum UserConsume implements EJourCommon {
        /**
         *
         */
        UserConsume("user consume", "消费", "消费", "user consume"),
        BuyMeal("buy meal", "购买套餐", "购买[%s]", "buy meal", "user consume"),
        BuyMeal_Forzen("buy meal forzen", "购买套餐冻结", "购买[%s]冻结", "buy meal forzen"),
        BuyMeal_UnForzen("buy meal unforzen", "购买套餐解冻", "购买[%s]解冻", "buy meal unforzen"),
        BuyMember("buy member", "购买会员", "购买会员", "buy member"),
        BuyMember_Forzen("buy member forzen", "购买会员冻结", "购买会员冻结", "buy member forzen"),
        BuyMember_UnForzen("buy member unforzen", "购买会员解冻", "购买会员解冻", "buy member unforzen"),
        PublishTask("publish task", "发布任务费用", "发布任务费用", "publish task"),
        PublishTask_Back("publish task back", "发布任务费用退回", "发布任务费用退回", "publish task back"),
        PublishTaskFee("publish task fee", "发布任务手续费", "发布任务手续费", "publish task fee"),

        KykConsume("kyk consume", "康养卡消费", "康养卡消费", "kyk consume", "kyk consume"),
        KykConsume_Forzen("kyk consume forzen", "康养卡消费冻结", "康养卡消费冻结", "kyk consume forzen"),
        KykConsume_UnForzen("kyk consume unforzen", "康养卡消费解冻", "康养卡消费解冻", "kyk consume unforzen"),
                ;

        UserConsume(String code, String value, String remark, String enRemark, String parentCode) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
            this.parentCode = parentCode;
        }

        UserConsume(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        private String parentCode;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }

        public String getParentCode() {
            return parentCode;
        }
    }

    /**
     * 用户消费
     */
    enum KykBiz implements EJourCommon {
        /**
         *
         */
        Offline_Import("offline import", "线下导入", "线下导入", "offline import"),
        UserConsume("user consume", "消费", "消费", "user consume"),
        KykAdd("kyk add", "充值", "%S", "kyk add"),

        KykConsume("kyk consume", "康养卡消费", "%S", "kyk consume", "kyk consume"),
        KykConsume_Forzen("kyk consume forzen", "康养卡消费冻结", "康养卡消费冻结", "kyk consume forzen"),
        KykConsume_UnForzen("kyk consume unforzen", "康养卡消费解冻", "康养卡消费解冻", "kyk consume unforzen"),

        ;

        KykBiz(String code, String value, String remark, String enRemark, String parentCode) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
            this.parentCode = parentCode;
        }

        KykBiz(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        private String parentCode;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }

        public String getParentCode() {
            return parentCode;
        }
    }

    /**
     * 用户收益
     */
    enum UserIncome implements EJourCommon {
        /**
         *
         */
        UserIncome("user income", "收益", "收益", "user income"),
        SelfIncome("self income", "新手区自购返现", "新手区自购返现", "self income"),
        ShareIncome("share income", "新手区分享赚取", "新手区分享赚取", "share income"),
        InviteIncome("invite income", "推广收益", "推广收益", "invite income"),
        NodeIncome("node income", "团队奖励", "团队奖励", "node income"),
        RECOMMEND_SELLER("recommend seller", "推荐商家收入分成", "推荐商家收入分成", "recommend seller"),
        RECOMMEND_SELLER_BOND("recommend seller bond", "推荐商家保证金分成", "推荐商家保证金分成", "recommend seller bond"),

        ;

        UserIncome(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 积分
     */
    enum Integral implements EJourCommon {
        /**
         *
         */
        Use("use", "商城使用", "商城使用", "use"),
        Mall_Use_Out("mall use out", "订单抵扣", "订单编号：%S", "mall use out"),

//        Transfer("transfer", "转账", "转账", "transfer"),
//        Transfer_In("transfer in", "转入", "来自：用户昵称（真实姓名）", "transfer in"),
//        Transfer_Out("transfer out", "转出", "转给：用户昵称（真实姓名）", "transfer out"),

        Mall_Use_Frozen("mall use frozen", "商城抵扣冻结", "订单号：%S", "mall use frozen"),
        Mall_Use_Unfrozen("mall use unfrozen", "商城抵扣解冻", "订单号：%S", "mall use unfrozen"),

        /*************************************************************************/
        Integral("Integral", "积分", "积分", "Integral"),
        Integral_Frozen("integral frozen", "积分冻结", "积分冻结", "integral frozen"),
        Integral_thaw("integral thaw", "积分解冻", "积分解冻", "integral thaw"),
        Integral_Get("integral Get", "积分使用", "积分使用", "integral Get"),
        Integral_Product("integral Product", "购买商品积分抵扣", "购买商品积分抵扣", "integral Product"),
        Integral_Transfer("integral Transfer", "积分转账", "积分转账", "integral Transfer"),

        Sacrifice("sacrifice", "链上祭祀", "链上祭祀", "sacrifice"),
        Sacrifice_Goods_Out("sacrifice goods out", "祭祀商品抵扣", "购买记录编号：%S", "sacrifice goods out"),
        Sacrifice_Venue_Out("sacrifice venue out", "购买供奉场馆抵扣", "购买记录编号：%S", "sacrifice venue out"),

        Sacrifice_Goods_Frozen("sacrifice goods frozen", "祭祀商品抵扣冻结", "购买记录编号：%S", "sacrifice goods frozen"),
        Sacrifice_Venue_Frozen("sacrifice venue frozen", "购买供奉场馆抵扣冻结", "购买记录编号：%S", "sacrifice venue frozen"),

        Sacrifice_Goods_Unfrozen("sacrifice goods unfrozen", "祭祀商品抵扣解冻", "购买记录编号：%S", "sacrifice goods unfrozen"),
        Sacrifice_Venue_Unfrozen("sacrifice venue unfrozen", "购买供奉场馆抵扣解冻", "购买记录编号：%S", "sacrifice venue unfrozen"),
        ;

        Integral(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 解锁包
     */
    enum Unlock implements EJourCommon {
        /**
         *
         */
        Unlock("Unlock", "解锁包", "解锁包", "Unlock"),
        Unlock_Release("Unlock_Release", "解锁包释放", "解锁包释放", "Unlock_Release");


        Unlock(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }

    /**
     * 充值
     */
    enum RECHARGE implements EJourCommon {
        /**
         *
         */
        RECHARGE("recharge", "充值", "充值", "recharge"),
        USER_RECHARGE("user_recharge", "用户充值", "用户充值", "user_recharge");


        RECHARGE(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    /**
     * 售后
     */
    enum AFTER_SALES implements EJourCommon {
        /**
         *
         */
        AFTER_SALES("after_sales", "售后", "一口价的商品", "goods"),
        AFTER_SALES_REFUND("after_sales_refund", "售后退款",
                "售后退款", "after_sales_refund");

        AFTER_SALES(String code, String value, String remark, String enRemark) {
            this.code = code;
            this.value = value;
            this.remark = remark;
            this.enRemark = enRemark;
        }

        private String code;

        private String value;

        private String remark;

        private String enRemark;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        @Override
        public String getRemark() {
            return remark;
        }

        @Override
        public String getEnRemark() {
            return enRemark;
        }
    }


    /**
     *
     */
    static EJourCommon matchCode(String code) {
        EJourCommon eJourCommon = null;
        List<EJourCommon[]> list = new ArrayList<>();
        list.add(EJourBizTypeUser.Withdraw.values());
        list.add(EJourBizTypeUser.Charge.values());
        list.add(EJourBizTypeUser.UserConsume.values());
        list.add(EJourBizTypeUser.UserIncome.values());

        for (EJourCommon[] jourCommons : list) {
            for (EJourCommon jourCommon : jourCommons) {
                if (code.equals(jourCommon.getCode())) {
                    eJourCommon = jourCommon;
                }
            }
        }

        return eJourCommon;
    }
}
