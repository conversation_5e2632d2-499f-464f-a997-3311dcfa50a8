package com.std.core.enums;

/**
 *
 */
public enum EJourStatus {


    /**
     * 待对账
     */
    To_Check("1", "待对账"),

    /**
     * 已对账且账已平
     */
    Checked_YES("3", "已对账且账已平"),

    /**
     * 帐不平待调账审批
     */
    Checked_NO("4", "帐不平待调账审批"),

    /**
     * 已对账且账不平
     */
    Adjusted("5", "已对账且账不平"),

    /**
     * 无需对账
     */
    noAdjust("6", "无需对账"),

    /**
     * 待入账
     */
    TO_IN_ACCOUNT("11", "待入账"),

    /**
     * 已入账
     */
    IN_ACCOUNT("12", "已入账"),

    /**
     * 入账失败
     */
    IN_ACCOUNT_FAIL("13", "入账失败"),

    /**
     * 待出账
     */
    TO_BE_OUT_ACCOUNT("14", "待出账"),

    /**
     * 出账成功
     */
    OUT_ACCOUNT_SUCCESS("15", "出账成功"),

    /**
     * 出账失败
     */
    OUT_ACCOUNT_FAIL("16", "出账失败");

    EJourStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
