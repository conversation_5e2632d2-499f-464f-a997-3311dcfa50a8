package com.std.core.enums;

/**
 * 账单交易种类
 *
 * @author: ycj
 * @since: 2019/1/14 19:43
 */
public enum EJourTransactionKind {

    goods("一口价购买", "一口价购买"),

    Auction("乐拍中拍支付", "乐拍中拍支付"),

    Share("拼单商品购买", "拼单商品购买"),

    AFTER_SALES_REFUND("退货款", "退货款"),

    Bond("保证金赔付", "保证金赔付"),

    Charge("充值记录", "充值记录"),

    intention_price("孤品：意向金", "孤品：意向金"),

    cnyToLuckBean("余额兑换成幸运豆", "余额兑换成幸运豆"),

    cnyToBond("余额兑换成保证金", "余额兑换成保证金"),

    bondToCny("保证金兑换成余额", "保证金兑换成余额"),

    allowanceToCny("小南额兑换成余额", "小南额兑换成余额")
    ;

    EJourTransactionKind(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
