package com.std.core.enums;

/**
 *
 */
public enum EJourType {

    /**
     * 流水类型（1余额流水 2冻结流水）
     */

    /**
     * 余额流水
     */
    BALANCE("1", "余额流水"),

    /**
     * 冻结流水
     */
    FROZEN("2", "冻结流水");

    EJourType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
