package com.std.core.enums;

/**
 * <AUTHOR> silver
 * @since : 2020-02-26 19:16
 */

public interface ELanguageResource {

    /**
     * 流水
     */
    enum Jour implements ELanguageResource {
        /**
         *
         */
        Jour("Jour", "流水"),
        remark("remark", "备注");

        Jour(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 文章类型
     */
    enum ArticleType implements ELanguageResource {
        /**
         *
         */
        ArticleType("ArticleType", "文章类型"),
        name("name", "名称");

        ArticleType(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 文章
     */
    enum Article implements ELanguageResource {
        /**
         */
        title("title", "标题"),
        content("content", "内容");

        Article(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 产品类型
     */
    enum ProductType implements ELanguageResource {
        /**
         */
        name("name", "名称"),
        description("description", "描述"),
        suitPeople("suitPeople", "适用人群"),
        advantage("advantage", "优势"),
        ;

        ProductType(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 产品
     */
    enum Product implements ELanguageResource {
        /**
         */
        name("name", "名称"),
        description("description", "描述"),
        ;

        Product(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 诊所
     */
    enum Clinic implements ELanguageResource {
        /**
         */
        name("name", "名称"),
        description("description", "描述"),
        province("province", "省"),
        city("city", "市"),
        area("area", "区"),
        address("address", "地址"),

        ;

        Clinic(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 病例消息
     */
    enum CaseMessage implements ELanguageResource {
        /**
         *
         */
        CaseMessage("CaseMessage", "病例消息"),
        content("content", "内容");

        CaseMessage(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 病例进度
     */
    enum CaseProgress implements ELanguageResource {
        /**
         *
         */
        CaseProgress("CaseProgress", "病例进度"),
        nodeName("nodeName", "节点名称");

        CaseProgress(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 物流
     */
    enum Logistics implements ELanguageResource {
        /**
         *
         */
        Logistics("Logistics", "物流"),
        logisticsName("logisticsName", "物件名称");

        Logistics(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 治疗方案
     */
    enum CurePlan implements ELanguageResource {
        /**
         *
         */
        CurePlan("CurePlan", "治疗方案"),
        FirstName("firstName", "一级名称"),
        SecondName("secondName", "二级名称");

        CurePlan(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 病例
     */
    enum Case implements ELanguageResource {
        /**
         *
         */
        Case("Case", "病例"),
        grantRecord("grantRecord", "发放记录");

        Case(String code, String value) {
            this.code = code;
            this.value = value;
        }

        private String code;

        private String value;

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    String getCode();

    String getValue();

}
