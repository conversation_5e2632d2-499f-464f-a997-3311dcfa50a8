package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 一卡通账户Enum
 *
 * <AUTHOR> ycj
 * @since : 2020-10-20 14:11
 */
public enum ELockBizType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('ykt.status', '{"1":"正常","-1":"废弃"}', '一卡通账户状态 1:正常，-1：废弃');

    /**
     * 释放解锁计划
     */
    RELEASE_PLAN("0", "释放解锁计划"),

    /**
     * 自动投入
     */
    AUTOMATIC_INPUT("1", "自动投入"),

    /**
     * 自动收益
     */
    AUTOMATIC_INCOME("2", "自动收益"),
    /**
     * 一卡通返利
     */
    YKT_REBATE("3", "一卡通返利"),

    AGENT_INCOME("4", "专区经纪人收益"),

    KYK_REBATE("5", "康养卡返利"),

    ;

    private String code;
    private String value;

    ELockBizType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ELockBizType> getYktStatusResultMap() {
        Map<String, ELockBizType> map = new HashMap<String, ELockBizType>();
        for (ELockBizType type : ELockBizType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ELockBizType getYktStatus(String code) {
        Map<String, ELockBizType> map = getYktStatusResultMap();
        ELockBizType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EYktStatus=" + code);
        }

        return result;
    }

}
