package com.std.core.enums;

/**
 * 菜单种类
 *
 * @author: haiqingzheng
 * @since: 2018-12-30 17:22
 */
public enum EMenuKind {

    /**
     * 运维中心
     */
    OPS("0", "运维中心"),

    /**
     * 数据中心
     */
    NET("1", "数据中心"),

    /**
     * 消防监管
     */
    FIRE("2", "消防监管"),

    /**
     * 物业监管
     */
    PROPERTY("3", "物业监管"),

    /**
     * 单位自管
     */
    OWNER("4", "单位自管"),

    /**
     * 维保单位
     */
    MAINTENANCE("5", "维保单位");

    EMenuKind(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
