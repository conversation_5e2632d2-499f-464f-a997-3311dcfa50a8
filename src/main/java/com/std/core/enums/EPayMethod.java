package com.std.core.enums;

/**
 *
 */
public enum EPayMethod {

    APP("app", "APP支付"),

    H5("h5", "h5支付"),

    WEB("web", "web支付");

    EPayMethod(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
