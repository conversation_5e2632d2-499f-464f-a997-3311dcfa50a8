package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 支付记录Enum
*
* <AUTHOR> LEO
* @since : 2020-09-12 16:58
*/
public enum EPayRecordBizStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pay_record.bizstatus', '{"0":"待处理","1":"已处理"}', '支付记录状态');

    /**
    * 待处理
    */
    PAY_RECORD_BIZSTATUS_0("0", "待处理"),

    /**
    * 已处理
    */
    PAY_RECORD_BIZSTATUS_1("1", "已处理"),

    ;

    private String code;
    private String value;

    EPayRecordBizStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPayRecordBizStatus> getPayRecordBizStatusResultMap() {
        Map<String, EPayRecordBizStatus> map = new HashMap<String, EPayRecordBizStatus>();
        for (EPayRecordBizStatus type : EPayRecordBizStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EPayRecordBizStatus getPayRecordBizStatus(String code) {
        Map<String, EPayRecordBizStatus> map = getPayRecordBizStatusResultMap();
        EPayRecordBizStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EPayRecordBizStatus=" + code);
        }

        return result;
    }

}
