package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付记录Enum
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
public enum EPayRecordBizType {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pay_record.biztype', '{"0":"充值","1":"一口价订单支付"}', '支付记录业务类型');

    /**
     * 充值
     */
    PAY_RECORD_BIZTYPE_0("0", "充值"),

    /**
     * 商城商品购买
     */
    PAY_RECORD_BIZTYPE_1("1", "活动预约"),

    PAY_RECORD_BIZTYPE_2("2", "专区商品购买"),


    ;

    private String code;
    private String value;

    EPayRecordBizType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPayRecordBizType> getPayRecordBizTypeResultMap() {
        Map<String, EPayRecordBizType> map = new HashMap<String, EPayRecordBizType>();
        for (EPayRecordBizType type : EPayRecordBizType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EPayRecordBizType getPayRecordBizType(String code) {
        Map<String, EPayRecordBizType> map = getPayRecordBizTypeResultMap();
        EPayRecordBizType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EPayRecordBizType=" + code);
        }

        return result;
    }

}
