package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 支付记录Enum
*
* <AUTHOR> LEO
* @since : 2020-09-12 16:58
*/
public enum EPayRecordStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('pay_record.status', '{"0":"待回调","1":"支付成功","2":"支付失败"}', '支付记录状态');

    /**
    * 待回调
    */
    PAY_RECORD_STATUS_0("0", "待回调"),

    /**
    * 支付成功
    */
    PAY_RECORD_STATUS_1("1", "支付成功"),

    /**
    * 支付失败
    */
    PAY_RECORD_STATUS_2("2", "支付失败"),

    ;

    private String code;
    private String value;

    EPayRecordStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EPayRecordStatus> getPayRecordStatusResultMap() {
        Map<String, EPayRecordStatus> map = new HashMap<String, EPayRecordStatus>();
        for (EPayRecordStatus type : EPayRecordStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EPayRecordStatus getPayRecordStatus(String code) {
        Map<String, EPayRecordStatus> map = getPayRecordStatusResultMap();
        EPayRecordStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EPayRecordStatus=" + code);
        }

        return result;
    }

}
