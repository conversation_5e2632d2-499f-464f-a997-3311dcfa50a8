package com.std.core.enums;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EPayStatus {

    /**
     * 状态（1待支付 2支付成功 3支付失败 4支付超时）
     */
    TO_PAY("1", "待支付"),
    PAY_SUCCESS("2", "支付成功"),
    PAY_FAIL("3", "支付失败"),
    PAY_EXPIRED("4", "支付超时");

    EPayStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
