package com.std.core.enums;

/**
 *
 */
public enum EPayType {
    /**
     * 支付方式（余额balance 支付宝支付alipay 微信公众号支付wechat）
     */
    BALANCE("balance", "余额支付"),

    ALIPAY("alipay", "支付宝支付"),

    WECHAT("wechat", "微信支付");

    EPayType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String matchOnline(String code) {
        String result = "微信";
        if (ALIPAY.code.equals(code)) {
            result = "支付宝";
        }
        return result;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
