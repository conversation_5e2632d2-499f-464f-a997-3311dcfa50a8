package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 标的Enum
*
* <AUTHOR> ycj
* @since : 2024-11-22 14:39
*/
public enum EProductStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'product.status', '标的状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'product.status', '0', '下架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'product.status', '1', '代上架');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'product.status', '2', '上架中');


    /**
    * 下架
    */
    PRODUCT_STATUS_0("0", "下架"),

    /**
    * 代上架
    */
    PRODUCT_STATUS_1("1", "代上架"),

    /**
    * 上架中
    */
    PRODUCT_STATUS_2("2", "上架中"),

    ;

    private String code;
    private String value;

    EProductStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EProductStatus> getProductStatusResultMap() {
        Map<String, EProductStatus> map = new HashMap<String, EProductStatus>();
        for (EProductStatus type : EProductStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EProductStatus getProductStatus(String code) {
        Map<String, EProductStatus> map = getProductStatusResultMap();
        EProductStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EProductStatus=" + code);
        }

        return result;
    }

}
