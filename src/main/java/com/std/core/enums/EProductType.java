package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 标的Enum
*
* <AUTHOR> ycj
* @since : 2024-11-22 14:39
*/
public enum EProductType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'product.type', '标的类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'product.type', '0', '定期');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'product.type', '1', '活期');


    /**
    * 定期
    */
    PRODUCT_TYPE_0("0", "定期"),

    /**
    * 活期
    */
    PRODUCT_TYPE_1("1", "活期"),

    ;

    private String code;
    private String value;

    EProductType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EProductType> getProductTypeResultMap() {
        Map<String, EProductType> map = new HashMap<String, EProductType>();
        for (EProductType type : EProductType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EProductType getProductType(String code) {
        Map<String, EProductType> map = getProductTypeResultMap();
        EProductType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EProductType=" + code);
        }

        return result;
    }

}
