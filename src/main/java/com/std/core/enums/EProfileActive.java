package com.std.core.enums;

/**
 * 类型Enum
 *
 * <AUTHOR> <PERSON>
 * @since : 2020-06-06 23:13
 */
public enum EProfileActive {

    /**
     * 本地环境
     */
    LOCAL("local", "本地环境"),

    /**
     * 研发环境
     */
    DEV("dev", "研发环境"),

    DEV2("dev2", "研发环境2"),

    /**
     * 测试环境
     */
    TEST("test", "测试环境"),

    /**
     * 生产环境
     */
    PROD("prod", "生产环境");

    private String code;
    private String value;

    EProfileActive(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
