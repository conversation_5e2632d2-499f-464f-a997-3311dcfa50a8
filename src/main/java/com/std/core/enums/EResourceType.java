package com.std.core.enums;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 20:37
 */
public enum EResourceType {

    /**
     * 端
     */
    CLIENT("client", "端"),

    /**
     * 菜单
     */
    MENU("menu", "菜单"),

    /**
     * 按钮
     */
    BUTTON("button", "按钮"),

    /**
     * 功能菜单
     */
    FUNC_MENU("func_menu", "功能菜单"),

    /**
     * 接口
     */
    ACTION("action", "接口"),

    /**
     * 所有
     */
    ALL("all", "所有");

    EResourceType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
