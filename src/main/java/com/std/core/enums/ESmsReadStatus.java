package com.std.core.enums;

/**
 * 阅读状态
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 21:01
 */
public enum ESmsReadStatus {

    /**
     * 已阅读
     */
    READ("1", "已阅读"),

    /**
     * 已删除
     */
    DELETE("2", "已删除");

    private String code;
    private String value;

    ESmsReadStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
