package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 公告关联类型
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 21:01
 */
public enum ESmsRefType {

    SYSTEM_SMS("0", "系统公告"),
    SYSTEM_MESSAGE("system message", "系统消息"),
    SHOPPING_MALL("shopping mall", "商城消息"),
    YXBABY_MESSAGE("yxbaby message", "银杏宝消息"),
    CHANGE_AMOUNT("change amount", "账户金额变动提醒"),
    YKT_AMOUNT("ykt amount", "一卡通金额变动提醒"),
    ACTIVITY_AMOUNT("activity amount", "活动相关消息"),
    ;


    private String code;

    private String value;

    ESmsRefType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESmsRefType> getSmsTargetResultMap() {
        Map<String, ESmsRefType> map = new HashMap<String, ESmsRefType>();
        for (ESmsRefType type : ESmsRefType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESmsRefType getNoticeStatus(String code) {
        Map<String, ESmsRefType> map = getSmsTargetResultMap();
        ESmsRefType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ESmsTarget=" + code);
        }

        return result;
    }

}
