package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 公告状态(0-草稿 1-已发送 2-已回撤)
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 21:01
 */
public enum ESmsStatus {

    /**
     * 已发送
     */
    DRAFT("0", "草稿"),

    /**
     * 已发送
     */
    SENDED("1", "已发送"),

    /**
     * 已撤回
     */
    REVOKED("2", "已撤回");

    private String code;
    private String value;

    ESmsStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESmsStatus> getSmsStatusResultMap() {
        Map<String, ESmsStatus> map = new HashMap<String, ESmsStatus>();
        for (ESmsStatus type : ESmsStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESmsStatus getNoticeStatus(String code) {
        Map<String, ESmsStatus> map = getSmsStatusResultMap();
        ESmsStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ESmsStatus=" + code);
        }

        return result;
    }

}
