package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 针对人群
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 21:01
 */
public enum ESmsTarget {

    CUSTOMER("C", "普通用户"),

    PLAT_USER("P", "平台用户");

    private String code;

    private String value;

    ESmsTarget(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESmsTarget> getSmsTargetResultMap() {
        Map<String, ESmsTarget> map = new HashMap<String, ESmsTarget>();
        for (ESmsTarget type : ESmsTarget.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESmsTarget getSmsTarget(String code) {
        Map<String, ESmsTarget> map = getSmsTargetResultMap();
        ESmsTarget result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ESmsTarget=" + code);
        }

        return result;
    }

}
