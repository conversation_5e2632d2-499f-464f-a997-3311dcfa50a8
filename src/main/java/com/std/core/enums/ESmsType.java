package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 公告类型
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 21:01
 */
public enum ESmsType {

    SYSTEM("1", "系统公告"),

    MY("2", "我的消息");

    private String code;

    private String value;

    ESmsType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ESmsType> getSmsTargetResultMap() {
        Map<String, ESmsType> map = new HashMap<String, ESmsType>();
        for (ESmsType type : ESmsType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static ESmsType getNoticeStatus(String code) {
        Map<String, ESmsType> map = getSmsTargetResultMap();
        ESmsType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "ESmsTarget=" + code);
        }

        return result;
    }

}
