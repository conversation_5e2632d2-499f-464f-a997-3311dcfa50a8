package com.std.core.enums;

/**
 * 系统账户
 */
public interface ESystemAccount {

    /**
     * 系统用户账户前缀
     */
    enum SYS_ACCOUNT_PREF implements ESystemAccount {
        /**
         * 系统用户账户前缀
         */
        SYS_ACCOUNT_PREF("SYS_ACCOUNT_", "系统用户账户前缀"),
        SYS_ACCOUNT_DROP("SYS_ACCOUNT_DROP", "人民币空投账户"),

        SYS_ACCOUNT_DROP_DEW("SYS_ACCOUNT_DROP_DEW", "露珠空投账户"),

        ;

        private String code;

        private String value;

        SYS_ACCOUNT_PREF(String code, String value) {
            this.code = code;
            this.value = value;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }
    }

    /**
     * 系统托管账户
     */
    enum TRUST implements ESystemAccount {
        /**
         * 线下托管账户
         */
//        OFFLINE("OFFLINE", "线下托管账户"),

        /**
         * 微信托管账户
         */
        WECHAT("WECHAT", "微信托管账户"),

        /**
         * "支付宝托管账户"
         */
        ALIPAY("ALIPAY", "支付宝托管账户"),
        ;

        private String code;

        private String value;

        TRUST(String code, String value) {
            this.code = code;
            this.value = value;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public String getAccountNumber() {
            return SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode().concat(code);
        }
    }

    /**
     * 系统公账账户
     */
    enum CORP implements ESystemAccount {
        /**
         * 系统公账账户
         */
        CORP("CORP", "公账账户");

        private String code;

        private String value;

        CORP(String code, String value) {
            this.code = code;
            this.value = value;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public String getAccountNumber() {
            return SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode().concat(code);
        }
    }

    /**
     * 系统业务账户
     */
    enum BIZ implements ESystemAccount {

        /**
         * 系统业务账户
         */
        BIZ("BIZ", "系统业务账户");

        private String code;

        private String value;

        BIZ(String code, String value) {
            this.code = code;
            this.value = value;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getValue() {
            return value;
        }

        public String getAccountNumber() {
            return SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode().concat(code);
        }

        public String getAccountNumber(String currency) {
            return SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode().concat(code).concat("_")
                    .concat(currency.toUpperCase());
        }
    }

    String getCode();

    String getValue();
}
