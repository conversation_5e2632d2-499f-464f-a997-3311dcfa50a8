package com.std.core.enums;

/**
 * 上下架状态
 *
 * <AUTHOR> silver
 * @since : 2020-02-24 11:30
 */
public enum EUpDownStatus {

    /**
     * 下架
     */
    DOWN("0", "下架"),

    /**
     * 上架
     */
    UP("1", "上架");

    EUpDownStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}

    
    