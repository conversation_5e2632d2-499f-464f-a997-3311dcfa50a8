package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
* 用户业务操作记录Enum
*
* <AUTHOR> ycj
* @since : 2024-06-17 12:44
*/
public enum EUserActionLogType {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'user_action_log.type', '用户业务操作记录类型');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'user_action_log.type', '0', '微信解绑');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'user_action_log.type', '1', '绑定微信');


    /**
    * 微信解绑
    */
    USER_ACTION_LOG_TYPE_0("0", "微信解绑"),

    /**
    * 绑定微信
    */
    USER_ACTION_LOG_TYPE_1("1", "绑定微信"),

    ;

    private String code;
    private String value;

    EUserActionLogType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EUserActionLogType> getUserActionLogTypeResultMap() {
        Map<String, EUserActionLogType> map = new HashMap<String, EUserActionLogType>();
        for (EUserActionLogType type : EUserActionLogType.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EUserActionLogType getUserActionLogType(String code) {
        Map<String, EUserActionLogType> map = getUserActionLogTypeResultMap();
        EUserActionLogType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EUserActionLogType=" + code);
        }

        return result;
    }

}
