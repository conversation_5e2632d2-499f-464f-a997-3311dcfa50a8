package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户/角色/菜单端类型
 */
public enum EUserKind {

    /**
     * 运维中心
     */
    OPS("OPS", "运维中心", 100000000000000000L, "运维中心管理员"),

    /**
     * 厂商
     */
    SYS("SYS", "管理后台", 200000000000000000L, "系统超级管理员"),

    P("P", "平台用户", null, "平台用户"),

    /**
     * C端
     */
    C("C", "C端", null, null);

    public static Map<String, EUserKind> getUserKindResultMap() {
        Map<String, EUserKind> map = new HashMap<String, EUserKind>();
        for (EUserKind type : EUserKind.values()) {
            map.put(type.getCode(), type);
        }
        return map;
    }

    public static void getUserKind(Object code) {
        Map<String, EUserKind> map = getUserKindResultMap();
        EUserKind result = map.get(code.toString());
        if (result == null) {
            throw new BizException(
                    ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(),
                    "EUserKind=" + code);
        }
    }

    EUserKind(String code, String value, Long adminUserId, String adminUserName) {
        this.code = code;
        this.value = value;
        this.adminUserId = adminUserId;
        this.adminUserName = adminUserName;
    }

    private String code;

    private String value;

    private Long adminUserId;

    private String adminUserName;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public Long getAdminUserId() {
        return adminUserId;
    }

    public String getAdminUserName() {
        return adminUserName;
    }
}
