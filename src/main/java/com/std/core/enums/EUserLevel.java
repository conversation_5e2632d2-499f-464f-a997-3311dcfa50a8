package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Silver
 * @since : 2019-01-11 22:10
 */
public enum EUserLevel {

    L0("0", "普通用户"),
    L1("1", "白银"),
    L2("2", "黄金"),
    L3("3", "钻石");

    EUserLevel(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, EUserLevel> getMap() {
        Map<String, EUserLevel> map = new HashMap<String, EUserLevel>();
        for (EUserLevel data : EUserLevel.values()) {
            map.put(data.getCode(), data);
        }
        return map;
    }

    public static EUserLevel getEnum(String code) {
        Map<String, EUserLevel> map = getMap();
        EUserLevel result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "KEY=" + code);
        }
        return result;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
