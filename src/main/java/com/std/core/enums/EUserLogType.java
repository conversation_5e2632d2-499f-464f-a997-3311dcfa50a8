package com.std.core.enums;

public enum EUserLogType {

    /**
     * 分类（1登录 2绑定手机号 3绑定邮箱 4绑定支付密码 5修改手机号 6修改登录密码 7修改邮箱 8修改支付密码）
     */

    /**
     * 登录
     */
    LOGIN("login", "登录"),
    /**
     * 登录
     */
    USER_OSS("user_oss", "用户操作"),
    /**
     * 绑定手机号
     */
    BIND_MOBILE("bind_mobile", "绑定手机号"),

    /**
     * 管理员代用户注册
     */
    REGISTER_OSS("register_oss", "管理员代用户注册"),
    /**
     * 绑定邮箱
     */
    BIND_EMAIL("bind_email", "绑定邮箱"),

    /**
     * 绑定支付密码
     */
    BIND_TRADEPWD("bind_tradepwd", "绑定支付密码"),

    /**
     * 修改手机号
     */
    MODIFY_MOBILE("modify_mobile", "修改手机号"),

    /**
     * 管理员修改手机号
     */
    MODIFY_MOBILE_OSS("modify_mobile_oss", "管理员修改手机号"),

    /**
     * 修改登录密码
     */
    MODIFY_LOGINPWD("modify_loginpwd", "修改登录密码"),

    /**
     * 管理员修改登录密码
     */
    MODIFY_LOGINPWD_OSS("modify_loginpwd_oss", "管理员修改登录密码"),
    /**
     * 管理员修改支付密码
     */
    MODIFY_TRANSACTION_OSS("MODIFY_TRANSACTION_OSS", "管理员修改支付密码"),

    /**
     * 管理员实名认证
     */
    MODIFY_AUTHENTICATION_OSS("MODIFY_AUTHENTICATION_OSS", "管理员实名认证"),
    /**
     * 修改邮箱
     */
    MODIFY_EMAIL("modify_email", "修改邮箱"),

    /**
     * 修改支付密码
     */
    MODIFY_TRADEPWD("modify_tradepwd", "修改支付密码"),

    /**
     * 管理员修改支付密码
     */
    MODIFY_TRADEPWD_OSS("modify_tradepwd_oss", "管理员修改支付密码"),
    /**
     * 管理员修改用户个人信息
     */
    PERSONAL_INFORMATION_OSS("personal_information_oss", "管理员修改用户个人信息"),
    /**
     * 锁定
     */
    LOCK("lock", "锁定"),
    /**
     * C端用户操作
     */
    CUSER("CUSER", "C端用户操作"),
    /**
     * 解锁
     */
    UNLOCK("unlock", "解锁"),

    /**
     * 管理员产品种类操作
     */
    PRODUCT_TYPE_OSS("PRODUCT_TYPE_OSS", "产品种类操作"),
    /**
     * 新增种类操作
     */
    PRODUCT_TYPE_CREATE_OSS("PRODUCT_TYPE_CREATE_OSS", "产品种类操作"),

    /**
     * 管理员产品操作
     */
    PRODUCT_OSS("PRODUCT_OSS", "产品操作"),
    /**
     * 管理员新增产品
     */
    PRODUCT_CREATE_OSS("PRODUCT_CREATE_OSS", "产品操作"),
    /**
     * 用户订单操作
     */
    USER_ORDER("USER_ORDER", "订单操作"),
    /**
     * 用户下单操作
     */
    USER_ORDER_CREAT("USER_ORDER_CREAT", "订单操作"),

    /**
     * 管理员一卡通导入
     */
    YKT_IMPORT_OSS("YKT_IMPORT_OSS", "一卡通操作"),
    /**
     * 管理员废弃一卡通
     */
    YKT_REMOVE_OSS("YKT_REMOVE_OSS", "一卡通操作"),
    /**
     * 管理员解锁包操作
     */
    Unlock_OSS("Unlock_OSS", "解锁包操作"),
    /**
     * 管理员新增解锁包
     */
    Unlock_CREATE_OSS("Unlock_CREATE_OSS", "解锁包操作"),
    /**
     * 消息发送
     */
    MESSAGE_SENDING("MESSAGE_SENDING", "消息发送"),
    /**
     * 新增公告
     */
    NOTICE_CREATE("NOTICE_CREATE", "公告操作"),
    /**
     * 公告发布
     */
    NOTICE_ON("NOTICE_ON", "公告操作"),
    /**
     * 公告撤下
     */
    NOTICE_DOWN("NOTICE_DOWN", "公告操作"),
    /**
     * 公告修改
     */
    NOTICE_MODIFY("NOTICE_MODIFY", "公告操作"),
    /**
     * 实名认证操作
     */
    REAL_AUTHENTICATUON("REAL_AUTHENTICATUON", "实名认证操作"),
    /**
     * 愿力值操作
     */
    WILLING("WILLING", "愿力值操作"),
    /**
     * 银杏宝操作
     */
    YXBABY("YXBABY", "银杏宝操作"),
    /**
     * 轮播图操作
     */
    BANNER("BANNER", "轮播图操作"),
    /**
     * 活动操作
     */
    ACTIVITY("ACTIVITY", "活动操作"),
    /**
     * 地址操作
     */
    ADDRESS("ADDRESS", "地址操作"),
    /**
     * 账户操作操作
     */
    ACCOUNT("ACCOUNT", "账户操作"),
    /**
     * 供奉人信息操作
     */
    WORSHIPINFO("WORSHIPINFO", "供奉人信息操作"),
    /**
     * 系统参数修改
     */
    CONFIG("CONFIG", "系统参数修改"),
    /**
     * 数据字典修改
     */
    DICT("DICT", "数据字典修改");

    EUserLogType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
