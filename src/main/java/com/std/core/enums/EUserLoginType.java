package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

public enum EUserLoginType {

    /**
     * H5微信登录
     */
    H5_LOGIN("0", "H5微信登录"),

    /**
     * APP微信登录
     */
    APP_LOGIN("1", "APP微信登录"),
    ;

    EUserLoginType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, EUserLoginType> getUserStatusResultMap() {
        Map<String, EUserLoginType> map = new HashMap<String, EUserLoginType>();
        for (EUserLoginType type : EUserLoginType.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EUserLoginType getUserStatus(String code) {
        Map<String, EUserLoginType> map = getUserStatusResultMap();
        EUserLoginType result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EUserStatus=" + code);
        }

        return result;
    }
    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
