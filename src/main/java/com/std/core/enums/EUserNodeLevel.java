package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 节点等级
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
public enum EUserNodeLevel {

    /**
     * 普通用户
     */
    LEVEL_0("0", "普通用户"),

    /**
     * 优秀团队
     */
    LEVEL_1("1", "优秀团队"),

    /**
     * 精英团队
     */
    LEVEL_2("2", "精英团队"),

    /**
     * 至尊团队
     */
    LEVEL_3("3", "至尊团队"),

    ;

    private String code;
    private String value;

    EUserNodeLevel(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EUserNodeLevel> getUserNodeLevelWayResultMap() {
        Map<String, EUserNodeLevel> map = new HashMap<String, EUserNodeLevel>();
        for (EUserNodeLevel type : EUserNodeLevel.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EUserNodeLevel getUserNodeLevelWay(String code) {
        Map<String, EUserNodeLevel> map = getUserNodeLevelWayResultMap();
        EUserNodeLevel result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EUserNodeLevel=" + code);
        }

        return result;
    }

}
