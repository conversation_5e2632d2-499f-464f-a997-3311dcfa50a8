package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
 * 节点用户Enum
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
public enum EUserNodeLevelWay {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('user_node_level.way', '{"0":"程序自动","1":"人工手动"}', '节点用户设置方式');

    /**
     * 程序自动
     */
    AUTO("0", "程序自动"),

    /**
     * 人工手动
     */
    MANUAL("1", "人工手动"),

    ;

    private String code;
    private String value;

    EUserNodeLevelWay(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EUserNodeLevelWay> getUserNodeLevelWayResultMap() {
        Map<String, EUserNodeLevelWay> map = new HashMap<String, EUserNodeLevelWay>();
        for (EUserNodeLevelWay type : EUserNodeLevelWay.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EUserNodeLevelWay getUserNodeLevelWay(String code) {
        Map<String, EUserNodeLevelWay> map = getUserNodeLevelWayResultMap();
        EUserNodeLevelWay result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EUserNodeLevelWay=" + code);
        }

        return result;
    }

}
