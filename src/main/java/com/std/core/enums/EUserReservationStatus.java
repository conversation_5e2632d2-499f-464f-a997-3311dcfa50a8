package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

public enum EUserReservationStatus {

    /**
     * 正常
     */
    NORMAL("0", "正常"),

    /**
     * 限制
     */
    LOCK("1", "限制"),
    ;


    EUserReservationStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, EUserReservationStatus> getUserReservationStatusResultMap() {
        Map<String, EUserReservationStatus> map = new HashMap<String, EUserReservationStatus>();
        for (EUserReservationStatus type : EUserReservationStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EUserReservationStatus getUserReservationStatus(String code) {
        Map<String, EUserReservationStatus> map = getUserReservationStatusResultMap();
        EUserReservationStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EUserReservationStatus=" + code);
        }

        return result;
    }
    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
