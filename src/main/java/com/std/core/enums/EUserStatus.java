package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

public enum EUserStatus {

    /**
     * 正常
     */
    NORMAL("normal", "正常"),

    /**
     * 锁定冻结,可以申诉解封
     */
    LOCK("lock", "锁定冻结"),

    NONLIVE("non_live", "禁止开播"),
    /**
     * 临时账户
     */
    TEMPORARILY("temporarily", "临时账户"),
    /**
     * 永久封号
     */
    PERMANENT_BAN("permanent_ban", "永久封号");


    EUserStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static Map<String, EUserStatus> getUserStatusResultMap() {
        Map<String, EUserStatus> map = new HashMap<String, EUserStatus>();
        for (EUserStatus type : EUserStatus.values()) {
            map.put(type.getCode(), type);
        }

        return map;
    }

    public static EUserStatus getUserStatus(String code) {
        Map<String, EUserStatus> map = getUserStatusResultMap();
        EUserStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
                    ECommonErrorCode.E500001.getValue(), "EUserStatus=" + code);
        }

        return result;
    }
    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
