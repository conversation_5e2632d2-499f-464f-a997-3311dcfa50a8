package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 核销Enum
*
* <AUTHOR> mjd
* @since : 2024-12-30 01:10
*/
public enum EVerificationRecordStatus {

// 数据字典执行SQL
// INSERT INTO `tsys_dict` (`type`, `key`, `value`) VALUES ('0', 'verification_record.status', '核销状态');
// INSERT INTO `tsys_dict` (`type`, `parent_key`, `key`, `value`) VALUES ('1', 'verification_record.status', '0', '已核销');


    /**
    * 已核销
    */
    VERIFICATION_RECORD_STATUS_0("0", "已核销"),

    ;

    private String code;
    private String value;

    EVerificationRecordStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, EVerificationRecordStatus> getVerificationRecordStatusResultMap() {
        Map<String, EVerificationRecordStatus> map = new HashMap<String, EVerificationRecordStatus>();
        for (EVerificationRecordStatus type : EVerificationRecordStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static EVerificationRecordStatus getVerificationRecordStatus(String code) {
        Map<String, EVerificationRecordStatus> map = getVerificationRecordStatusResultMap();
        EVerificationRecordStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "EVerificationRecordStatus=" + code);
        }

        return result;
    }

}
