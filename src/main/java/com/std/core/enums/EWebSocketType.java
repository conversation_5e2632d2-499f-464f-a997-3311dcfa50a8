package com.std.core.enums;

/**
 * websocket类型
 */
public enum EWebSocketType {


    /**
     * 链接标示
     */
    CONNECT_SUCCESS("connect.success","链接成功"),

    /**
     * 实时在线人数推送
     */
    TODAY_SUMMARY("today.summary", "在线人数推送"),

    /**
     * 购买套餐推送
     */
    BUY_VIP_MEAL("buy.vip.meal", "购买套餐推送"),

    /**
     * 购买会员推送
     */
    BUY_MEMBER_MEAL("buy.member.meal", "会员套餐推送");


    EWebSocketType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
