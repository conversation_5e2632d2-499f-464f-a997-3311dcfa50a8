package com.std.core.enums;

/**
 * 业务类型（withdraw取现 transfer内部划转）
 */
public enum EWithdrawBizType {
    /**
     * 取现
     */
    withdraw("withdraw", "内部账"),

    /**
     * 内部划转
     */
    transfer("transfer", "内部划转"),
    ;

    EWithdrawBizType(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

}
