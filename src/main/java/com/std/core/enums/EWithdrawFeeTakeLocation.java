package com.std.core.enums;


/**
 * 取现手续费扣除位置
 *
 * <AUTHOR> kl
 * @since : 2019-07-02 18:28
 */
public enum EWithdrawFeeTakeLocation {
    /**
     *
     */
    WITHDRAW_AMOUNT("0", "取现金额中"),

    BALANCE("1", "余额中");

    EWithdrawFeeTakeLocation(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
