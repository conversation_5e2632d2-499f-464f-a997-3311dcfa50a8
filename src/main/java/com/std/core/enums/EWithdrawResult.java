/**
 * @Title ECoinType.java @Package com.ogc.standard.enums @Description
 * <AUTHOR>
 * @date 2018年3月13日 上午11:30:16
 * @version V1.0
 */
package com.std.core.enums;

/**
 * @author: haiqingzheng
 * @since: 2018年3月13日 上午11:30:16
 * @history:
 */
public enum EWithdrawResult {
    SUCCESS("success", "成功"),
    FAILED("failed", "失败");

    EWithdrawResult(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
