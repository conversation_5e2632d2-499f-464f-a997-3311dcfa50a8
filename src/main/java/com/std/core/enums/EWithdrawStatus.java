package com.std.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 取现状态
 */
public enum EWithdrawStatus {

    /**
     * 待审批
     */
    To_Approve("1", "待审批"),

    /**
     * 审批不通过
     */
    Approved_NO("2", "审批不通过"),

    /**
     * 审批通过待支付
     */
    Approved_YES("3", "审批通过待支付"),

    /**
     * 支付失败
     */
    Pay_NO("4", "支付失败"),

    /**
     * 支付成功
     */
    Pay_YES("5", "支付成功");

    EWithdrawStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    private String code;

    private String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static List<String> getWithdrawingStatusList() {
        List<String> resultList = new ArrayList<>();
        resultList.add(To_Approve.getCode());
        resultList.add(Approved_YES.getCode());
        return resultList;
    }
}
