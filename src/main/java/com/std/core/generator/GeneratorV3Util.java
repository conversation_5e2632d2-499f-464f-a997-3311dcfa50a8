package com.std.core.generator;

import com.std.common.code.CodeBuilderSpringBoot;
import com.std.common.code.CodeBuilderSpringBootOSS;
import com.std.common.code.CodeGenerator;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/12/16 11:31 上午
 */
public class GeneratorV3Util {

    public static void main(String[] args) {

        CodeBuilderSpringBoot javaBuilder = CodeBuilderSpringBoot.builder()
                .project("czzmsg-core")
                .author("ycj")
                .dbUrl("*******************************************")
                .userName("root")
                .userPassword("9psDINnoiDp3WW")
                .dbName("czzmsg_test")

                .tableName("tbiz_daily_income_summary")
                .tableComment("每日收益")

//                .basePackage("不填默认 com.std.core")
//                .commonPath("不填默认 com.std.common")
//                .workspace("/Users/<USER>/IdeaProjects/mjd/czzmsg-core/")
                .workspace("/Users/<USER>/IdeaProjects/czzmsg/czzmsg-core/")


                .build();

//        CodeBuilderSpringBootOSS ossBuilder = CodeBuilderSpringBootOSS.builder()
//                .project("xfb-core")
//                .author("zhoudong")
//                .header("biz/task")
//
//                .workspace("/Users/<USER>/Desktop/aaaaa/")
//
//                .build();

        CodeGenerator.generateSpringBoot(javaBuilder, null);
    }

}
