package com.std.core.mapper;

import com.std.core.pojo.domain.Account;
import com.std.core.pojo.response.AccountAmountSumListRes;
import com.std.core.pojo.response.AccountDeductRes;
import com.std.core.pojo.response.AccountIntegralListRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
public interface AccountMapper {

    /**
     * 选择性插入
     *
     * @param record 账户
     * @return 影响行数
     */
    int insertSelective(Account record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 账户
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Account record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 账户
     */
    Account selectByPrimaryKey(Long id);

    Account selectByUserId(Long id);

    /**
     * 锁定账户
     */
    Account selectByPrimaryForUpdate(Long id);


    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 账户列表
     */
    List<Account> selectByCondition(Account condition);

    /**
     * 查询用户账户信息
     */
    List<Account> select(Account condition);

    /**
     * 统计总余额
     */
    BigDecimal selectTotalAmount(Account condition);

    List<Account> selectSubSellerAccountByCondition(Account condition);

    List<Account> selectSellerAccountByCondition(Account condition);

    List<Account> selectAnchorAccountByCondition(Account condition);

    AccountAmountSumListRes selectamountSumList(String currency);

    AccountAmountSumListRes selectamountSumListExcept(String currency);

    AccountAmountSumListRes selectamountSumListNotExcept(String currency);

    List<AccountIntegralListRes> selectIntegralList(Integer limit);

    List<AccountDeductRes> selectAccountList();
}
