package com.std.core.mapper;

import com.std.core.pojo.domain.Action;
import java.util.List;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:17
 */
public interface ActionMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(Action record);

    Action selectByPrimaryKey(Long id);

    long selectByActionUrl(String url);

    List<Action> selectByCondition(Action action);

    List<Action> selectByConditionByMenu(Long menuId);

    List<Action> selectByConditionByRole(List<Long> roleList);

    int updateByPrimaryKeySelective(Action record);
}
