package com.std.core.mapper;

import com.std.core.pojo.domain.Activity;
import java.util.List;

/**
 * 活动Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
public interface ActivityMapper {

    /**
     * 选择性插入
     *
     * @param record 活动
     * @return 影响行数
     */
    int insertSelective(Activity record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 活动
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Activity record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 活动
     */
    Activity selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 活动列表
     */
    List<Activity> selectByCondition(Activity condition);

    Activity selectForUpdate(Long id);
}
