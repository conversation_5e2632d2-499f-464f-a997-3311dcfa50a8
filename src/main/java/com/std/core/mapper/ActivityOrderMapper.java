package com.std.core.mapper;

import com.std.core.pojo.domain.ActivityOrder;
import com.std.core.pojo.request.IncomeGroupReq;
import com.std.core.pojo.response.IncomeItemRes;
import com.std.core.pojo.response.MinMaxTimeRes;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 活动预约单Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
public interface ActivityOrderMapper {

    /**
     * 选择性插入
     *
     * @param record 活动预约单
     * @return 影响行数
     */
    int insertSelective(ActivityOrder record);

    /**
     * 批量插入
     * @param activityOrderList
     */
    void insertBatchSelective(List<ActivityOrder> activityOrderList);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 活动预约单
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ActivityOrder record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 活动预约单
     */
    ActivityOrder selectByPrimaryKey(Long id);
    ActivityOrder selectForUpdate(Long id);
    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 活动预约单列表
     */
    List<ActivityOrder> selectByCondition(ActivityOrder condition);

    int selectSumNumber(ActivityOrder condition);

    List<IncomeItemRes> selectActivityIncomeGroupBy(IncomeGroupReq req);
    MinMaxTimeRes selectMinMaxCreateTime();
    BigDecimal selectActivityIncomeSum(IncomeGroupReq req);


    BigDecimal selectIncomeByDate(String startTime, String endTime);
}
