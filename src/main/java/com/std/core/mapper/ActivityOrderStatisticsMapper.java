package com.std.core.mapper;

import com.std.core.pojo.domain.ActivityOrderStatistics;

import java.util.Date;
import java.util.List;

/**
 * 活动预约统计Mapper
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
public interface ActivityOrderStatisticsMapper {

    /**
     * 选择性插入
     *
     * @param record 活动预约统计
     * @return 影响行数
     */
    int insertSelective(ActivityOrderStatistics record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 活动预约统计
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ActivityOrderStatistics record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 活动预约统计
     */
    ActivityOrderStatistics selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 活动预约统计列表
     */
    List<ActivityOrderStatistics> selectByCondition(ActivityOrderStatistics condition);

    void updateTotalTickets(Long activityId, Date orderDate, Integer totalTickets, Date updateDatetime);

    List<Date> selectFullyBookedDates(Long activityId, Integer dayLimit, Date date);
}
