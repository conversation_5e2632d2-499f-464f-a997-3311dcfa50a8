package com.std.core.mapper;

import com.std.core.pojo.domain.ActivityBuy;
import com.std.core.pojo.domain.ActivityTicketLine;
import java.util.List;

/**
 * 票档Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
public interface ActivityTicketLineMapper {

    /**
     * 选择性插入
     *
     * @param record 票档
     * @return 影响行数
     */
    int insertSelective(ActivityTicketLine record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 票档
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ActivityTicketLine record);
    void updateDelete(Long id);
    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 票档
     */
    ActivityTicketLine selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 票档列表
     */
    List<ActivityTicketLine> selectByCondition(ActivityTicketLine condition);
    int updateInventoryWithVersion(Long id, Integer number);
    int updateAddInventory(Long id, Integer number);
    int addStock(Long id, Integer stock);
    int sumInventoryByCondition(ActivityTicketLine condition);



}
