package com.std.core.mapper;

import com.std.core.pojo.domain.Address;

import java.util.List;

/**
 * 收货地址Mapper
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
public interface AddressMapper {

    /**
     * 选择性插入
     *
     * @param record 收货地址
     * @return 影响行数
     */
    int insertSelective(Address record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 收货地址
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Address record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 收货地址
     */
    Address selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 收货地址列表
     */
    List<Address> selectByCondition(Address condition);


    void updateOtherDefault(Long userId);
}
