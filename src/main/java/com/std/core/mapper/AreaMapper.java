package com.std.core.mapper;

import com.std.core.pojo.domain.Area;
import java.util.List;

/**
 * 地区表Mapper
 *
 * <AUTHOR> zhoudong
 * @since : 2020-08-10 17:06
 */
public interface AreaMapper {

    /**
     * 选择性插入
     *
     * @param record 地区表
     * @return 影响行数
     */
    int insertSelective(Area record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 地区表
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Area record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 地区表
     */
    Area selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 地区表列表
     */
    List<Area> selectByCondition(Area condition);

}
