package com.std.core.mapper;

import com.std.core.pojo.domain.Article;
import java.util.List;

/**
 * 文章Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
public interface ArticleMapper {

    /**
     * 选择性插入
     *
     * @param record 文章
     * @return 影响行数
     */
    int insertSelective(Article record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 文章
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Article record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 文章
     */
    Article selectByPrimaryKey(Long id);

    /**
     *
     */
    List<Article> selectTitleByCondition(Article condition);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 文章列表
     */
    List<Article> selectByCondition(Article condition);

    /**
     * PC端-文章分页显示
     * @param condition
     * @return
     */
    List<Article> selectPCPage(Article condition);
}
