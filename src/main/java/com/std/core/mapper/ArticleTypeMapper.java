package com.std.core.mapper;

import com.std.core.pojo.domain.ArticleType;
import java.util.List;

/**
 * 文章类型Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
public interface ArticleTypeMapper {

    /**
     * 选择性插入
     *
     * @param record 文章类型
     * @return 影响行数
     */
    int insertSelective(ArticleType record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 文章类型
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ArticleType record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 文章类型
     */
    ArticleType selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 文章类型列表
     */
    List<ArticleType> selectByCondition(ArticleType condition);


}
