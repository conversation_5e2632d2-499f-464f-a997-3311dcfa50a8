package com.std.core.mapper;

import com.std.core.pojo.domain.Bankcard;
import java.util.List;

/**
 * 银行卡Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
public interface BankcardMapper {

    /**
     * 选择性插入
     *
     * @param record 银行卡
     * @return 影响行数
     */
    int insertSelective(Bankcard record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 银行卡
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Bankcard record);

    /**
     *
     */
    int updateByRef(Bankcard record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 银行卡
     */
    Bankcard selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 银行卡列表
     */
    List<Bankcard> selectByCondition(Bankcard condition);

}
