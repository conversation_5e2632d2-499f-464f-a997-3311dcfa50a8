package com.std.core.mapper;

import com.std.core.pojo.domain.BigActivityOrder;

import java.util.Date;
import java.util.List;

/**
 * 预约单大订单Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
public interface BigActivityOrderMapper {

    /**
     * 选择性插入
     *
     * @param record 预约单大订单
     * @return 影响行数
     */
    int insertSelective(BigActivityOrder record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 预约单大订单
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(BigActivityOrder record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 预约单大订单
     */
    BigActivityOrder selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 预约单大订单列表
     */
    List<BigActivityOrder> selectByCondition(BigActivityOrder condition);

    BigActivityOrder selectBySerialNumber(String serialNumber);

    List<BigActivityOrder> selectTimeOutOrder(Integer closeTime, Date date);
}
