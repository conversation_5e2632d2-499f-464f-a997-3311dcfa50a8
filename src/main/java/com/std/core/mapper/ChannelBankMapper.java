package com.std.core.mapper;

import com.std.core.pojo.domain.ChannelBank;
import java.util.List;

/**
 * 渠道银行Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
public interface ChannelBankMapper {

    /**
     * 选择性插入
     *
     * @param record 渠道银行
     * @return 影响行数
     */
    int insertSelective(ChannelBank record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 渠道银行
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ChannelBank record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 渠道银行
     */
    ChannelBank selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 渠道银行列表
     */
    List<ChannelBank> selectByCondition(ChannelBank condition);

}
