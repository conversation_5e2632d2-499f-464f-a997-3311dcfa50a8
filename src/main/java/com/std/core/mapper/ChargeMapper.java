package com.std.core.mapper;

import com.std.core.pojo.domain.Charge;
import java.math.BigDecimal;
import java.util.List;

/**
 * 充值订单Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
public interface ChargeMapper {

    /**
     * 选择性插入
     *
     * @param record 充值订单
     * @return 影响行数
     */
    int insertSelective(Charge record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 充值订单
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Charge record);

    /**
     *
     */
    int updateStatusByPryGroup(Charge record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 充值订单
     */
    Charge selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 充值订单列表
     */
    List<Charge> selectByCondition(Charge condition);

    /**
     * 获取有效充值人数
     */
    List<Long> selectEffectUserList(Charge condition);

    /**
     * 统计充值金额
     */
    BigDecimal selectTotalAmount(Charge condition);

    Long selectTotalCountByCondition(Charge condition);

}
