package com.std.core.mapper;

import com.std.core.pojo.domain.Cnavigate;
import java.util.List;

/**
 * 导航Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
public interface CnavigateMapper {

    /**
     * 选择性插入
     *
     * @param record 导航
     * @return 影响行数
     */
    int insertSelective(Cnavigate record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 导航
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Cnavigate record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 导航
     */
    Cnavigate selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 导航列表
     */
    List<Cnavigate> selectByCondition(Cnavigate condition);

}
