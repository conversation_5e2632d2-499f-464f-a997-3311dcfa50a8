package com.std.core.mapper;

import com.std.core.pojo.domain.Cuser;
import org.apache.ibatis.annotations.Options;

import java.util.List;

/**
 * C端用户Mapper
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public interface CuserMapper {

    /**
     * 选择性插入
     *
     * @param record C端用户
     * @return 影响行数
     */
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertSelective(Cuser record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record C端用户
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Cuser record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return C端用户
     */
    Cuser selectByPrimaryKey(Long id);

    /**
     * 根据用户序号查询C端用户信息
     * @param id
     * @return
     */
    Cuser selectByUserId(Long id);
    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return C端用户列表
     */
    List<Cuser> selectByCondition(Cuser condition);

    List<Cuser> selectRichByCondition(Cuser condition);

    /**
     * 分页查询
     * @param condition
     * @return
     */
    List<Cuser> select(Cuser condition);

    /**
     * 模糊查询
     * @param condition
     * @return
     */
    List<Cuser> selectVague(Cuser condition);

    void deleteByUser(Long userId);
    void updateUntieWx(Long userId);
}
