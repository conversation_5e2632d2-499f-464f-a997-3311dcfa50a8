package com.std.core.mapper;

import com.std.core.pojo.domain.DailyIncomeSummary;
import com.std.core.pojo.response.DailyIncomeSummaryDetailRes;
import com.std.core.pojo.response.DailyIncomeSummaryListRes;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 每日收益Mapper
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
public interface DailyIncomeSummaryMapper {

    /**
     * 选择性插入
     *
     * @param record 每日收益
     * @return 影响行数
     */
    int insertSelective(DailyIncomeSummary record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 每日收益
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(DailyIncomeSummary record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 每日收益
     */
    DailyIncomeSummary selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 每日收益列表
     */
    List<DailyIncomeSummary> selectByCondition(DailyIncomeSummary condition);

    DailyIncomeSummary selectByDate(String incomeDate);

    void updateActivityIncomeSummary(String incomeDate, BigDecimal activityAmount);

    void updateGoodsIncomeSummary(String incomeDate, BigDecimal goodsAmount);

    List<DailyIncomeSummaryListRes> selectByDay(Date startDate, Date endDate, String type);

    List<DailyIncomeSummaryListRes> selectByWeek(Date startDate, Date endDate, String type);

    List<DailyIncomeSummaryListRes> selectByMonth(Date startDate, Date endDate, String type);

    DailyIncomeSummaryDetailRes selectIncomeTotal(String startDate, String endDate);
}
