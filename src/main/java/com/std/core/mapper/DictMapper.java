package com.std.core.mapper;

import com.std.core.pojo.domain.Dict;
import java.util.List;

/**
 * 数据字典Mapper
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:49
 */
public interface DictMapper {

    /**
     * 选择性插入
     *
     * @param record 数据字典
     * @return 影响行数
     */
    int insertSelective(Dict record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 选择性更新
     *
     * @param record 数据字典
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Dict record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 数据字典
     */
    Dict selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 数据字典列表
     */
    List<Dict> selectByCondition(Dict condition);

}
