package com.std.core.mapper;

import com.std.core.pojo.domain.FreshNews;
import java.util.List;

/**
 * 新鲜事Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
public interface FreshNewsMapper {

    /**
     * 选择性插入
     *
     * @param record 新鲜事
     * @return 影响行数
     */
    int insertSelective(FreshNews record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 新鲜事
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(FreshNews record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 新鲜事
     */
    FreshNews selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 新鲜事列表
     */
    List<FreshNews> selectByCondition(FreshNews condition);

}
