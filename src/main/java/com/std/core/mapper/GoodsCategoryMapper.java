package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsCategory;
import java.util.List;

/**
 * 商品类型Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
public interface GoodsCategoryMapper {

    /**
     * 选择性插入
     *
     * @param record 商品类型
     * @return 影响行数
     */
    int insertSelective(GoodsCategory record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 商品类型
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsCategory record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 商品类型
     */
    GoodsCategory selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 商品类型列表
     */
    List<GoodsCategory> selectByCondition(GoodsCategory condition);

}
