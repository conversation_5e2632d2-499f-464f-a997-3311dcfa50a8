package com.std.core.mapper;

import com.std.core.pojo.domain.Goods;
import java.util.List;

/**
 * 商品Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
public interface GoodsMapper {

    /**
     * 选择性插入
     *
     * @param record 商品
     * @return 影响行数
     */
    int insertSelective(Goods record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 商品
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Goods record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 商品
     */
    Goods selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 商品列表
     */
    List<Goods> selectByCondition(Goods condition);

}
