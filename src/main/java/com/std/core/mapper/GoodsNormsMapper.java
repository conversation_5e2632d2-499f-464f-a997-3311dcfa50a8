package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsNorms;
import java.util.List;

/**
 * 商品规格Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:59
 */
public interface GoodsNormsMapper {

    /**
     * 选择性插入
     *
     * @param record 商品规格
     * @return 影响行数
     */
    int insertSelective(GoodsNorms record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 商品规格
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsNorms record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 商品规格
     */
    GoodsNorms selectByPrimaryKey(Long id);
    GoodsNorms selectForUpdate(Long id);
    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 商品规格列表
     */
    List<GoodsNorms> selectByCondition(GoodsNorms condition);


    int addStock(Long id, Integer stock);

    int updateSubtractStockSelective(Long id, Integer stock);


    void updateAddInventorySelective(Long id, Integer stock);
}
