package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsOrderDetail;
import java.util.List;

/**
 * 商品订单详情Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:53
 */
public interface GoodsOrderDetailMapper {

    /**
     * 选择性插入
     *
     * @param record 商品订单详情
     * @return 影响行数
     */
    int insertSelective(GoodsOrderDetail record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 商品订单详情
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsOrderDetail record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 商品订单详情
     */
    GoodsOrderDetail selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 商品订单详情列表
     */
    List<GoodsOrderDetail> selectByCondition(GoodsOrderDetail condition);

    void insertBatchSelective(List<GoodsOrderDetail> goodsOrderDetailList);
}
