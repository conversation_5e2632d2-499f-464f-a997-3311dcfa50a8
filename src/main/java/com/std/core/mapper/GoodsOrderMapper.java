package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsOrder;
import com.std.core.pojo.request.IncomeGroupReq;
import com.std.core.pojo.response.IncomeItemRes;
import com.std.core.pojo.response.MinMaxTimeRes;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品订单Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
public interface GoodsOrderMapper {

    /**
     * 选择性插入
     *
     * @param record 商品订单
     * @return 影响行数
     */
    int insertSelective(GoodsOrder record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 商品订单
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsOrder record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 商品订单
     */
    GoodsOrder selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 商品订单列表
     */
    List<GoodsOrder> selectByCondition(GoodsOrder condition);

    GoodsOrder selectForUpdate(Long id);

    List<GoodsOrder> selectTimeOutOrder(Integer closeTime, Date date);

    List<IncomeItemRes> selectActivityIncomeGroupBy(IncomeGroupReq req);

    MinMaxTimeRes selectMinMaxCreateTime();
    BigDecimal selectActivityIncomeSum(IncomeGroupReq req);

    BigDecimal selectIncomeByDate(String startTime, String endTime);
}
