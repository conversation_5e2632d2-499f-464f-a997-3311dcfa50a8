package com.std.core.mapper;

import com.std.core.pojo.domain.GoodsTrolley;
import java.util.List;

/**
 * 购物车Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
public interface GoodsTrolleyMapper {

    /**
     * 选择性插入
     *
     * @param record 购物车
     * @return 影响行数
     */
    int insertSelective(GoodsTrolley record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 购物车
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GoodsTrolley record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 购物车
     */
    GoodsTrolley selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 购物车列表
     */
    List<GoodsTrolley> selectByCondition(GoodsTrolley condition);

    GoodsTrolley selectForUpdate(Long id);
}
