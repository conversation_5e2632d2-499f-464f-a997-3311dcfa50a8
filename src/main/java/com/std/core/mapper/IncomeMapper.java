package com.std.core.mapper;

import com.std.core.pojo.domain.Income;
import com.std.core.pojo.response.IncomeStatisticsByDayRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收益Mapper
 *
 * <AUTHOR> Leo
 * @since : 2020-06-06 23:13
 */
public interface IncomeMapper {

    /**
     * 选择性插入
     *
     * @param record 收益
     * @return 影响行数
     */
    int insertSelective(Income record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 收益
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Income record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 收益
     */
    Income selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 收益列表
     */
    List<Income> selectByCondition(Income condition);

    /**
     * 统计实际收益
     */
    BigDecimal selectTotalRealAmount(Income condition);

    List<IncomeStatisticsByDayRes> statisticsPageByDay();
}
