package com.std.core.mapper;

import com.std.core.pojo.domain.Jour;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户流水Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
public interface JourMapper {

    /**
     * 选择性插入
     *
     * @param record 账户流水
     * @return 影响行数
     */
    int insertSelective(Jour record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 账户流水
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Jour record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 账户流水
     */
    Jour selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 账户流水列表
     */
    List<Jour> selectByCondition(Jour condition);

    /**
     *
     */
    BigDecimal selectTotalAmount(Jour condition);

    /**
     * 查询分销金额
     * @param condition
     * @return
     */
    BigDecimal selectTransAmount(Jour condition);

}
