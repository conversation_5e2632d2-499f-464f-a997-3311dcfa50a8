package com.std.core.mapper;

import com.std.core.pojo.domain.LanguageResource;
import java.util.List;

/**
 * 语言资源Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-02 10:59
 */
public interface LanguageResourceMapper {

    /**
     * 选择性插入
     *
     * @param record 语言资源
     * @return 影响行数
     */
    int insertSelective(LanguageResource record);

    /**
     *
     */
    int replaceSelective(LanguageResource record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     */
    int deleteByRef(LanguageResource record);

    /**
     * 选择性更新
     *
     * @param record 语言资源
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(LanguageResource record);

    /**
     * 选择性更新
     *
     * @param record 语言资源
     * @return 影响行数
     */
    int updateByRefSelective(LanguageResource record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 语言资源
     */
    LanguageResource selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 语言资源列表
     */
    List<LanguageResource> selectByCondition(LanguageResource condition);

}
