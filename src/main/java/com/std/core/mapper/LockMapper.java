package com.std.core.mapper;

import com.std.core.pojo.domain.Lock;
import java.util.List;

/**
 * 业务锁Mapper
 *
 * <AUTHOR> xieyj
 * @since : 2020-06-12 00:27
 */
public interface LockMapper {

    /**
     * 选择性插入
     *
     * @param record 业务锁
     * @return 影响行数
     */
    int insertSelective(Lock record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 选择性更新
     *
     * @param record 业务锁
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Lock record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 业务锁
     */
    Lock selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 业务锁列表
     */
    List<Lock> selectByCondition(Lock condition);

}
