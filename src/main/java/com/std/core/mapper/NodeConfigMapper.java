package com.std.core.mapper;

import com.std.core.pojo.domain.NodeConfig;
import java.util.List;

/**
 * 星级节点配置Mapper
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
public interface NodeConfigMapper {

    /**
     * 选择性插入
     *
     * @param record 星级节点配置
     * @return 影响行数
     */
    int insertSelective(NodeConfig record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 星级节点配置
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(NodeConfig record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 星级节点配置
     */
    NodeConfig selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 星级节点配置列表
     */
    List<NodeConfig> selectByCondition(NodeConfig condition);

}
