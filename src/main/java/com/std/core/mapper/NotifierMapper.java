package com.std.core.mapper;

import com.std.core.pojo.domain.Notifier;
import java.util.List;

/**
 * 通知人Mapper
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 15:39
 */
public interface NotifierMapper {

    /**
     * 选择性插入
     *
     * @param record 通知人
     * @return 影响行数
     */
    int insertSelective(Notifier record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 选择性更新
     *
     * @param record 通知人
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Notifier record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 通知人
     */
    Notifier selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 通知人列表
     */
    List<Notifier> selectByCondition(Notifier condition);

}
