package com.std.core.mapper;

import com.std.core.pojo.domain.PayRecord;
import java.util.List;

/**
 * 支付记录Mapper
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
public interface PayRecordMapper {

    /**
     * 选择性插入
     *
     * @param record 支付记录
     * @return 影响行数
     */
    int insertSelective(PayRecord record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 支付记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(PayRecord record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 支付记录
     */
    PayRecord selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 支付记录列表
     */
    List<PayRecord> selectByCondition(PayRecord condition);

}
