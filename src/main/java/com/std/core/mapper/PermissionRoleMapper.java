package com.std.core.mapper;

import com.std.core.pojo.domain.PermissionRole;
import java.util.List;

public interface PermissionRoleMapper {

    List<PermissionRole> selectByCondition(PermissionRole condition);

    Long selectCountByCondition(PermissionRole condition);

    int batchInsert(List<PermissionRole> list);

    int deleteByRole(Long roleId);

    int deleteByCondition(PermissionRole condition);

    /**
     * 根据角色编号 和 菜单编号 批量添加
     *
     * @param condition 角色编号 和 菜单编号
     */
    void batchCreate(PermissionRole condition);
}
