package com.std.core.mapper;

import com.std.core.pojo.domain.Product;
import java.util.List;

/**
 * 标的Mapper
 *
 * <AUTHOR> ycj
 * @since : 2024-11-22 14:39
 */
public interface ProductMapper {

    /**
     * 选择性插入
     *
     * @param record 标的
     * @return 影响行数
     */
    int insertSelective(Product record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 标的
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Product record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 标的
     */
    Product selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 标的列表
     */
    List<Product> selectByCondition(Product condition);

}
