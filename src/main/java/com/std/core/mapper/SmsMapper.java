package com.std.core.mapper;

import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.response.SmsNewsRes;

import java.util.List;

/**
 * 消息记录Mapper
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
public interface SmsMapper {

    /**
     * 选择性插入
     *
     * @param record 消息记录
     * @return 影响行数
     */
    int insertSelective(Sms record);

    /**
     * 批量发送
     *
     * @param list
     * @return
     */
    int batchInsertSelective(List<Sms> list);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 消息记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Sms record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 消息记录
     */
    Sms selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 消息记录列表
     */
    List<Sms> selectByCondition(Sms condition);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    List<Sms> select(Sms condition);

    /**
     * 消息中心
     *
     * @param condition
     * @return
     */
    List<Sms> messageCenter(Sms condition);

    /**
     * 未读消息
     *
     * @param condition
     * @return
     */
    Sms unreadMessages(Sms condition);

    /**
     * 未读消息数量
     *
     * @param condition
     * @return
     */
    Sms unreadMessagesNumber(Sms condition);

    /**
     * 未读公告数量
     *
     * @param condition
     * @return
     */
    Sms unreadNoticeNumber(Sms condition);

    List<Sms> messageCenterPage(Sms sms);

    /**
     * OSS消息管理详情查
     *
     * @param sms
     * @return
     */
    SmsNewsRes selectNewsDetail(Sms sms);
}
