package com.std.core.mapper;

import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.SmsRead;
import java.util.List;

/**
 * 公告阅读记录Mapper
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
public interface SmsReadMapper {

    /**
     * 选择性插入
     *
     * @param record 公告阅读记录
     * @return 影响行数
     */
    int insertSelective(SmsRead record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据smscode 删除
     */
    int deleteBySmsCode(Long smsCode);

    /**
     * 选择性更新
     *
     * @param record 公告阅读记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(SmsRead record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 公告阅读记录
     */
    SmsRead selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 公告阅读记录列表
     */
    List<SmsRead> selectByCondition(SmsRead condition);

    /**
     * 查询条数
     */
    Integer selectCount(SmsRead condition);

    /**
     * 根据条件查询我的公告
     *
     * @param condition 查询条件
     * @return 消息记录列表
     */
    List<Sms> selectMySmsByCondition(Sms condition);

    /**
     * 查询我的公告未阅读数量
     */
    Integer selectMyUnreadCount(Sms condition);

    /**
     * 查询我的消息未阅读数量
     */
    Integer selectMsgMyUnreadCount(Sms condition);

}
