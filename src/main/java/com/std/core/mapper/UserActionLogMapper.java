package com.std.core.mapper;

import com.std.core.pojo.domain.UserActionLog;

import java.util.List;

/**
 * 用户业务操作记录Mapper
 *
 * <AUTHOR> ycj
 * @since : 2024-06-17 12:44
 */
public interface UserActionLogMapper {

    /**
     * 选择性插入
     *
     * @param record 用户业务操作记录
     * @return 影响行数
     */
    int insertSelective(UserActionLog record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 用户业务操作记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(UserActionLog record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 用户业务操作记录
     */
    UserActionLog selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 用户业务操作记录列表
     */
    List<UserActionLog> selectByCondition(UserActionLog condition);

}
