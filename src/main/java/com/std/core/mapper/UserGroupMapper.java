package com.std.core.mapper;

import com.std.core.pojo.domain.UserGroup;
import java.util.List;

/**
 * @author: Silver
 * @since: 2019-01-08 19:39
 */
public interface UserGroupMapper {

    int insert(UserGroup userGroup);

    int deleteByUserId(Long userId);

    int batchInsert(List<UserGroup> userGroupList);

    List<UserGroup> selectByCondition(UserGroup record);

    Long selectCountByCondition(UserGroup condition);

}
