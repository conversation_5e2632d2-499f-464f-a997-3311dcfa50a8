package com.std.core.mapper;

import com.std.core.pojo.domain.UserLog;
import java.util.List;

/**
 * 用户日志Mapper
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
public interface UserLogMapper {

    /**
     * 选择性插入
     *
     * @param record 用户日志
     * @return 影响行数
     */
    int insertSelective(UserLog record);

    int batchInsert(List<UserLog> list);
    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 用户日志
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(UserLog record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 用户日志
     */
    UserLog selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 用户日志列表
     */
    List<UserLog> selectByCondition(UserLog condition);

}
