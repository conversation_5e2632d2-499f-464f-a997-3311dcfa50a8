package com.std.core.mapper;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.response.SubUserRes;
import com.std.core.pojo.response.TeamUserOssRes;
import com.std.core.pojo.response.UserDetailRes;

import java.util.List;

/**
 * 用户Mapper
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
public interface UserMapper {

    /**
     * 选择性插入
     *
     * @param record 用户
     * @return 影响行数
     */
    int insertSelective(User record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 用户
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(User record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 用户
     */
    User selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 用户列表
     */
    List<User> selectByCondition(User condition);

    List<User> selectUserRefereeByCondition(User condition);

    /**
     * 批量插入
     */
    void insertBatch(List<User> userList);

    Integer selectCount(User condition);

    /**
     * 获得所有的普通用户
     *
     * @param userKind 用户类型
     * @param status   账户状态
     * @return 用户集合
     */
    List<User> getAllCUser(String userKind, String status);

    /**
     * 我的详细信息
     *
     * @param id       用户id
     * @param currency 币种
     * @param type     用户类型
     * @param status   状态
     * @return 我的
     */
    UserDetailRes selectMineDetail(Long id, String currency, String type, String status);

    List<User> cUserlist();

    List<SubUserRes> selectSubUserList(Long id);

    /**
     * 管理端：查询用户团队
     *
     * @param user
     * @return
     */
    List<TeamUserOssRes> ossTeamDeatil(User user);

    List<Long> selectIdList();

    void batchUpdate(List<User> userList);

    List<User> dissociateChain();

    void batchModifyChannelFlag(List<User> userList, Long channelId);


    User getUserByOpenid(String openid);

    User getUserByAppOpenid(String openid);

    User getUserByUnionId(String unionId);
}
