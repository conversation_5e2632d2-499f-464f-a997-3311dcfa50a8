package com.std.core.mapper;

import com.std.core.pojo.domain.UserNodeLevel;
import java.util.List;

/**
 * 节点用户Mapper
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
public interface UserNodeLevelMapper {

    /**
     * 选择性插入
     *
     * @param record 节点用户
     * @return 影响行数
     */
    int insertSelective(UserNodeLevel record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 节点用户
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(UserNodeLevel record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 节点用户
     */
    UserNodeLevel selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 节点用户列表
     */
    List<UserNodeLevel> selectByCondition(UserNodeLevel condition);

    Long selectMySubUserCount(UserNodeLevel condition);

}
