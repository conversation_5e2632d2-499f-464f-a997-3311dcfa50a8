package com.std.core.mapper;

import com.std.core.pojo.domain.UserRole;
import java.util.List;

public interface UserRoleMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserRole record);

    int insertSelective(UserRole record);

    int deleteByUserId(Long userId);

    UserRole selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserRole record);

    int updateByPrimaryKey(UserRole record);

    List<UserRole> selectByCondition(UserRole record);

    Long selectCountByCondition(UserRole condition);

}
