package com.std.core.mapper;

import com.std.core.pojo.domain.VerificationRecord;
import java.util.List;

/**
 * 核销Mapper
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 01:10
 */
public interface VerificationRecordMapper {

    /**
     * 选择性插入
     *
     * @param record 核销
     * @return 影响行数
     */
    int insertSelective(VerificationRecord record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 核销
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(VerificationRecord record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 核销
     */
    VerificationRecord selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 核销列表
     */
    List<VerificationRecord> selectByCondition(VerificationRecord condition);

}
