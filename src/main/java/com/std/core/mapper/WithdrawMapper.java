package com.std.core.mapper;

import com.std.core.pojo.domain.Withdraw;
import java.math.BigDecimal;
import java.util.List;

/**
 * 取现订单Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
public interface WithdrawMapper {

    /**
     * 选择性插入
     *
     * @param record 取现订单
     * @return 影响行数
     */
    int insertSelective(Withdraw record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 取现订单
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(Withdraw record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 取现订单
     */
    Withdraw selectByPrimaryKey(Long id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 取现订单列表
     */
    List<Withdraw> selectByCondition(Withdraw condition);

    /**
     * 统计取现金额
     */
    BigDecimal selectTotalAmount(Withdraw condition);

    Long selectTotalCount(Withdraw condition);

    BigDecimal selectWithdrawingAmount(String accountNumber);

    BigDecimal selectCurrDayWithdrawAmount(String accountNumber);
}
