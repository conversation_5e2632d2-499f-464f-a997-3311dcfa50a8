package com.std.core.mapper;

import com.std.core.pojo.domain.WithdrawRule;

import java.util.List;

/**
 * 取现规则Mapper
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-11 10:35
 */
public interface WithdrawRuleMapper {

    /**
     * 选择性插入
     *
     * @param record 取现规则
     * @return 影响行数
     */
    int insertSelective(WithdrawRule record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 选择性更新
     *
     * @param record 取现规则
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(WithdrawRule record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 取现规则
     */
    WithdrawRule selectByPrimaryKey(Integer id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 取现规则列表
     */
    List<WithdrawRule> selectByCondition(WithdrawRule condition);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 取现规则列表
     */
    List<WithdrawRule> selectRule(WithdrawRule condition);
}
