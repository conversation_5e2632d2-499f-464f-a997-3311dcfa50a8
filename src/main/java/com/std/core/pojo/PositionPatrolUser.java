package com.std.core.pojo;

import com.std.common.base.BaseDo;
import java.util.Date;
import lombok.Data;

/**
 * 巡逻人员
 *
 * <AUTHOR> haiqingzheng
 * @since : 2019/2/11 18:22
 */
@Data
public class PositionPatrolUser extends BaseDo {

    /**
     * 单位编号
     */
    private Long ownerId;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String remark;

    /** ** Properties *** */

    /**
     * 巡逻时间起
     */
    private Date createTimeStart;

    /**
     * 巡逻时间止
     */
    private Date createTimeEnd;
}
