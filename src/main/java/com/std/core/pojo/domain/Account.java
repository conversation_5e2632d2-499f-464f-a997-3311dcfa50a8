package com.std.core.pojo.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.std.common.base.BaseDo;
import com.std.core.util.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class Account extends BaseDo {

    /**
     * 主键编号
     */
    @ApiModelProperty(name = "id", value = "主键编号")
    private Long id;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种: CNY=人民币 DIAMOND=钻石")
    private String currency;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currencyList", value = "币种")
    private List<String> currencyList;

    /**
     * 类型（C端账号 B端账号 P平台账号）
     */
    @ApiModelProperty(name = "type", value = "类别")
    private String type;

    @ApiModelProperty(name = "category", value = "类别 0:盈亏,1:空投账户")
    private String category;

    /**
     * 状态（1正常 2程序冻结 3人工冻结）
     */
    @ApiModelProperty(name = "status", value = "状态（1正常 2程序冻结 3人工冻结）")
    private String status;

    /**
     * 总资产
     */
    @ApiModelProperty(name = "amount", value = "总资产")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal amount;

    /**
     * 可用余额
     */
    @ApiModelProperty(name = "availableAmount", value = "可用余额")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal availableAmount;

    /**
     * 冻结金额
     */
    @ApiModelProperty(name = "frozenAmount", value = "冻结金额")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal frozenAmount;

    /**
     * 锁仓金额
     */
    @ApiModelProperty(name = "lockAmount", value = "锁仓金额")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal lockAmount;

    /**
     * 锁仓金额
     */
    @ApiModelProperty(name = "inAmount", value = "净流入金额")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal inAmount;

    /**
     * 总出金
     */
    @ApiModelProperty(name = "outAmount", value = "总出金")
    private BigDecimal outAmount;
    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间")
    private Date createDatetime;

    /**
     * 最近一次变动对应的流水编号
     */
    @ApiModelProperty(name = "lastOrder", value = "最近一次变动对应的流水编号")
    private String lastOrder;

    //DB Properties

    /**
     * 用户信息
     */
    @ApiModelProperty(name = "userInfo", value = "用户信息")
    private User userInfo;

    /**
     * 类型列表
     */
    @ApiModelProperty(name = "type", value = "类别（C端账号 B端账号 P平台账号）")
    private List<String> typeList;

    @ApiModelProperty(name = "userKind", value = "用户类型")
    private String userKind;

    @ApiModelProperty(name = "mobile", value = "用户手机")
    private String mobile;

    @ApiModelProperty(name = "nickname", value = "用户昵称")
    private String nickname;

    @ApiModelProperty(name = "userName", value = "用户名称")
    private String userName;

    private String keywords;

    @ApiModelProperty(name = "accountName", value = "账户名称")
    private String accountName;

    @ApiModelProperty(name = "withdrawalAmount", value = "提现中金额")
    private BigDecimal withdrawalAmount;
}
