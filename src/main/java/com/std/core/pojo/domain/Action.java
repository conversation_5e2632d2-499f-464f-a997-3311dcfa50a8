package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 11:26
 */
@Data
public class Action extends BaseDo {

    /**
     * 接口id
     */
    @ApiModelProperty(name = "id", value = "接口id")
    private Long id;

    /**
     * 接口类型
     */
    @ApiModelProperty(name = "type", value = "接口类型")
    private String type;

    /**
     * 接口名称
     */
    @ApiModelProperty(name = "name", value = "接口名称")
    private String name;

    /**
     * 操作码
     */
    @ApiModelProperty(name = "code", value = "操作码")
    private String code;

    /**
     * URL路径
     */
    @ApiModelProperty(name = "url", value = "URL路径")
    private String url;

    /**
     * 入参
     */
    @ApiModelProperty(name = "input", value = "入参")
    private String input;

    /**
     * 出参
     */
    @ApiModelProperty(name = "output", value = "出参")
    private String output;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * URL模糊查询专用
     */
    @ApiModelProperty(name = "urlForQuery", value = "URL模糊查询专用")
    private String urlForQuery;
}
