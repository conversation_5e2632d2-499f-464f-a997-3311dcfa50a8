package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EActivityLocation;
import com.std.core.enums.EActivityStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活动
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@Data
public class Activity extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 活动标题
     */
    @ApiModelProperty(name = "title", value = "活动标题", position = 30)
    private String title;

    /**
     * 位置{0:普通,1:热门}
     */
    @ApiModelProperty(name = "location", value = "位置{0:普通,1:热门}", position = 40)
    private String location;

    /**
     * 活动封面
     */
    @ApiModelProperty(name = "pic", value = "活动封面", position = 50)
    private String pic;

    /**
     * 活动地址
     */
    @ApiModelProperty(name = "address", value = "活动地址", position = 60)
    private String address;


    /**
     * 活动地名
     */
    @ApiModelProperty(name = "addressLocation", value = "活动地名", position = 60)
    private String addressLocation;

    /**
     * 售价
     */
    @ApiModelProperty(name = "price", value = "售价", position = 70)
    private BigDecimal price;

    /**
     * 入馆须知
     */
    @ApiModelProperty(name = "visitorInformation", value = "入馆须知", position = 80)
    private String visitorInformation;

    /**
     * 注意事项
     */
    @ApiModelProperty(name = "notice", value = "注意事项", position = 90)
    private String notice;

    /**
     * 最小购买数量
     */
    @ApiModelProperty(name = "minimumBuyNumber", value = "最小购买数量", position = 100)
    private Integer minimumBuyNumber;

    /**
     *  每日预约上限
     */
    @ApiModelProperty(name = "dayLimit", value = "每日预约上限", position = 100)
    private Integer dayLimit;

    @ApiModelProperty(name = "limit", value = "用户购买上限", position = 100)
    private Integer limit;

    /**
     * 最大购买数量
     */
    @ApiModelProperty(name = "maximumBuyNumber", value = "最大购买数量", position = 110)
    private Integer maximumBuyNumber;

    /**
     * 经度
     */
    @ApiModelProperty(name = "longitude", value = "经度", position = 120)
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(name = "latitude", value = "纬度", position = 120)
    private String latitude;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(name = "startTime", value = "活动开始时间", position = 130)
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(name = "endTime", value = "活动结束时间", position = 140)
    private Date endTime;
    private Date expiredTime;

    /**
     * 状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}", position = 150)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;
    private List<String> noStatusList;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 160)
    private Long creater;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 170)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 180)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 190)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 200)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 210)
    private Date updateDatetime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 220)
    private Integer orderNo;

    /**** Properties ****/

    /**
     * 位置{0:普通,1:热门}
     */
    @ApiModelProperty(name = "locationName", value = "位置{0:普通,1:热门}")
    private String locationName;

    /**
     * 票券列表
     */
    @ApiModelProperty(name = "activityTicketLineList", value = "票券列表")
    private List<ActivityTicketLine> activityTicketLineList ;

    /**
     * 状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}")
    private String statusName;

    public String getLocationName() {
      if (StringUtils.isNotBlank(location)) {
        locationName = EActivityLocation.getActivityLocation(location).getValue();
      }

      return locationName;
    }

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EActivityStatus.getActivityStatus(status).getValue();
      }

      return statusName;
    }



    /**
     * 省份
     */
    @ApiModelProperty(name = "province", value = "省份", required = true, position = 60)
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(name = "city", value = "城市", required = true, position = 80)
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", required = true, position = 100)
    private String county;
    private String flag;



    /**
     * 售票开始时间
     */
    @NotBlank(message = "售票开始时间")
    @ApiModelProperty(name = "buyStartTime", value = "售票开始时间", required = true, position = 130)
    private Date buyStartTime;

    /**
     * 售票结束时间
     */
    @NotBlank(message = "售票结束时间")
    @ApiModelProperty(name = "buyEndTime", value = "售票结束时间", required = true, position = 130)
    private Date buyEndTime;



}
