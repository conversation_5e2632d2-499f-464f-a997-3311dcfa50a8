package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 收货地址
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@Data
public class Address extends BaseDo {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 用户uid（用户类型对应的id）
     */
    @ApiModelProperty(name = "userId", value = "用户uid（用户类型对应的id）", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(name = "name", value = "收货人姓名", position = 30)
    private String name;

    /**
     * 是否默认地址1是，0否,一个用户只能有一个默认地址
     */
    @ApiModelProperty(name = "isDefault", value = "是否默认地址1是，0否,一个用户只能有一个默认地址", position = 40)
    private String isDefault;

    /**
     * 省份名称
     */
    @ApiModelProperty(name = "province", value = "省份名称", position = 50)
    private String province;

    /**
     * 省份ID对应area表中的id
     */
    @ApiModelProperty(name = "provinceId", value = "省份ID对应area表中的id", position = 60)
    private Long provinceId;

    /**
     * 城市名称
     */
    @ApiModelProperty(name = "city", value = "城市名称", position = 70)
    private String city;

    /**
     * 城市ID对应area表中的id
     */
    @ApiModelProperty(name = "cityId", value = "城市ID对应area表中的id", position = 80)
    private Long cityId;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", position = 90)
    private String county;

    /**
     * 区/县ID对应area表中的id
     */
    @ApiModelProperty(name = "countyId", value = "区/县ID对应area表中的id", position = 100)
    private Long countyId;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 110)
    private String address;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "phone", value = "联系方式", position = 120)
    private String phone;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 130)
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 140)
    private Date updateDatetime;



}
