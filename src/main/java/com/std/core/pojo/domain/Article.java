package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 文章
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@Data
public class Article extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 类型编号
     */
    @ApiModelProperty(name = "typeCode", value = "类型编号")
    private Long typeId;

    /**
     * 缩略图
     */
    @ApiModelProperty(name = "pic", value = "缩略图")
    private String pic;

    /**
     * 文章类型
     */
    @ApiModelProperty(name = "type", value = "文章类型(组别)")
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容")
    private String content;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型")
    private String contentType;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）")
    private String status;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号")
    private Integer orderNo;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间")
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     *阅读次数
     */
    @ApiModelProperty(name = "readNumber",value = "阅读次数")
    private Integer readNumber;
    //******DB Properties******/

    /**
     * 是否为前端查询
     */
    private String frontFlag;


}
