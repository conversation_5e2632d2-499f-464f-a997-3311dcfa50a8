package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文章类型
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Data
public class ArticleType extends BaseDo {

    /**
     * 分类序号
     */
    @ApiModelProperty(name = "id", value = "分类序号")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "文章分类名称")
    private String name;

    @ApiModelProperty(name = "icon", value = "图标")
    private String icon;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）")
    private String status;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号")
    private Integer orderNo;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private Long updater;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间")
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    /**
     * 文章数
     */
    @ApiModelProperty(name = "articleNumber", value = "文章数")
    private Integer articleNumber;
    /**
     * 文章分类的位置
     */
    @ApiModelProperty(name = "location", value = "文章分类的位置")
    private String location;

    @ApiModelProperty(name = "locationDesc", value = "位置说明")
    private String locationDesc;
    /**
     * 阅读数
     */
    @ApiModelProperty(name = "readNumber", value = "总阅读数")
    private Integer readNumber;
    //******DB Properties********/

    /**
     * 文章
     */
    @ApiModelProperty(name = "articleList", value = "文章")
    private List<Article> articleList;

}
