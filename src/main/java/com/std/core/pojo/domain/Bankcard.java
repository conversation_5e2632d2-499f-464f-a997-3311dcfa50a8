package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 银行卡
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Data
public class Bankcard extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 类型（1C端 2厂商）
     */
    @ApiModelProperty(name = "type", value = "类型（C端 SYS系统 CLINIC诊所）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号(诊所编号)")
    private Long userId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "bankUserName", value = "银行卡户名")
    private String bankUserName;

    /**
     * 渠道银行编号
     */
    @ApiModelProperty(name = "channelBankId", value = "渠道银行编号")
    private Long channelBankId;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(name = "subbranch", value = "开户支行名称")
    private String subbranch;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "bankcardNumber", value = "银行卡号")
    private String bankcardNumber;

    /**
     * 状态（0失效 1生效）
     */
    @ApiModelProperty(name = "status", value = "状态（0失效 1生效）")
    private String status;

    /**
     * 是否默认
     */
    @ApiModelProperty(name = "defaultFlag", value = "是否默认（0否 1是）")
    private String defaultFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间")
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间")
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    //DB Properties

    /**
     * 渠道银行
     */
    @ApiModelProperty(name = "channelBank", value = "渠道银行")
    private ChannelBank channelBank;


}
