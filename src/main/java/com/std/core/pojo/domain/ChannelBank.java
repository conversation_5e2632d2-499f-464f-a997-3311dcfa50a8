package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 渠道银行
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Data
public class ChannelBank extends BaseDo {

    /**
     * 编号（自增长）
     */
    @ApiModelProperty(name = "id", value = "编号（自增长）")
    private Long id;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * 渠道类型
     */
    @ApiModelProperty(name = "channelType", value = "渠道类型", hidden = true)
    private String channelType;

    /**
     * 渠道给银行的代号
     */
    @ApiModelProperty(name = "channelBank", value = "渠道给银行的代号", hidden = true)
    private String channelBank;

    /**
     * Logo
     */
    @ApiModelProperty(name = "logo", value = "Logo")
    private String logo;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;


}
