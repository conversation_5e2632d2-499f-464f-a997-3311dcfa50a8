package com.std.core.pojo.domain;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class ChargeNotice {

    @NotBlank
    private String accountNumber;

    @NotNull
    private BigDecimal transAmount;

    @NotBlank
    private String refNo;

    @NotBlank
    //充值渠道 0 内部地址往平台充钱 1外部地址往平台充钱
    private String channel;

    // 0 分发地址充值 1散取地址充值 2补给地址充值
    @NotBlank
    private String type;

    //矿工费
    @NotNull
    private BigDecimal txFee;

    @NotNull
    private Integer timestamp;

    @NotBlank
    private String sign;

    @NotBlank
    private String txHash;

}
