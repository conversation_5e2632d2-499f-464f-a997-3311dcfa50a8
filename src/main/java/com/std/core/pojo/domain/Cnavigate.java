package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 导航
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Data
public class Cnavigate extends BaseDo {

    /**
     *
     */
    @ApiModelProperty(name = "id", value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型(banner图：app_banner 开机图：app_start)")
    private String type;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location", value = "位置")
    private String location;

    /**
     * 动作
     */
    @ApiModelProperty(name = "action", value = "动作（0不能点击 1跳转链接 2跳转本系统）")
    private String action;

    /**
     * 访问Url
     */
    @ApiModelProperty(name = "url", value = "访问Url")
    private String url;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片")
    private String pic;


    /**
     * 状态(1 显示 0 不显示)
     */
    @ApiModelProperty(name = "status", value = "状态(1 显示 0 不显示)")
    private String status;

    /**
     * 分组
     */
    @ApiModelProperty(name = "groupName", value = "分组")
    private String groupName;

    /**
     * 相对位置编号
     */
    @ApiModelProperty(name = "orderNo", value = "相对位置编号")
    private Integer orderNo;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;



}
