package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:03
 */
@Data
public class Config extends BaseDo {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型")
    private String type;

    /**
     * 键
     */
    @ApiModelProperty(name = "key", value = "键")
    private String key;

    /**
     * 值
     */
    @ApiModelProperty(name = "value", value = "值")
    private String value;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
    private String showType;
    private String isShow;

}
