package com.std.core.pojo.domain;

import com.std.core.enums.ECuserAgentStatus;
import com.std.core.enums.ECuserLevel;
import com.std.core.enums.EUserStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class Cuser extends User {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 20)
    private Long userId;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称", position = 25)
    private String userName;
    /**
     * 愿力值
     */
    @ApiModelProperty(name = "willingValue", value = "愿力值", position = 30)
    private BigDecimal willingValue;

    /**
     * 会员身份
     */
    @ApiModelProperty(name = "memberFlag", value = "会员身份 0=游客 1=会员", position = 40)
    private String memberFlag;

    /**
     * 套餐身份
     */
    @ApiModelProperty(name = "vipFlag", value = "套餐身份", position = 50)
    private String vipFlag;

    /**
     * 等级
     */
    @ApiModelProperty(name = "level", value = "等级 ", position = 60)
    private String level;

    /**
     * 合作状态
     */
    @ApiModelProperty(name = "agentStatus", value = "合作状态", position = 70)
    private String agentStatus;

    /**
     * 加盟时间
     */
    @ApiModelProperty(name = "agentJoinDatetime", value = "加盟时间", position = 80)
    private Date agentJoinDatetime;

    /**
     * 解除时间
     */
    @ApiModelProperty(name = "agentQuitDatetime", value = "解除时间", position = 90)
    private Date agentQuitDatetime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 恢复时间
     */
    @ApiModelProperty(name = "recoveryTime", value = "恢复时间", position = 101)
    private Date recoveryTime;

    /**
     * 差评扣减状态
     */
    @ApiModelProperty(name = "negationCommentDecreaseStatus", value = "差评扣减状态", position = 102)
    private String negationCommentDecreaseStatus;
    /**
     * 实名认证状态
     */
    @ApiModelProperty(name = "authenticationStatus", value = "实名认证状态 -1:认证不通过，0：未认证，1：认证通过，2：认证中")
    private String authenticationStatus;
    /**** Properties ****/
    @ApiModelProperty(name = "keywords", value = "模糊查询关键字")
    private String keywords;
    @ApiModelProperty(name = "user", value = "用户信息")
    private User user;
    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;
    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "realName", value = "真实姓名")
    private String realName;
    /**
     * 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码")
    private String idNo;


    @ApiModelProperty(name = "refereeUser", value = "推荐人信息", position = 103)
    private User refereeUser;
    /**
     * 正面照
     */
    @ApiModelProperty(name = "frontImg", value = "正面照", position = 80)
    private String frontImg;

    /**
     * 背面照
     */
    @ApiModelProperty(name = "backImg", value = "背面照", position = 90)
    private String backImg;
    /**
     * 推荐人名称
     */
    @ApiModelProperty(name = "recommenderName", value = "推荐人名称")
    private String recommenderName;
//    /**
//     * 推荐人名称
//     */
//    @ApiModelProperty(name = "recommenderMobile", value = "推荐人电话")
//    private String recommenderMobile;

    @ApiModelProperty(name = "amount", value = "账户余额", position = 104)
    private BigDecimal amount;


    @ApiModelProperty(name = "status", value = "状态")
    private String status;
    @ApiModelProperty(name = "nodeLevel", value = "节点等级 0=无星级 1=一星 2=二星 3=三星", position = 105)
    private String nodeLevel;
    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人")
    private Long userReferee;

    /**
     * 状态
     */
    @ApiModelProperty(name = "levelName", value = "等级名称")
    private String levelName;

    /**
     * 合作状态
     */
    @ApiModelProperty(name = "agentStatusName", value = "合作状态")
    private String agentStatusName;
    /**
     * 状态
     */
    @ApiModelProperty(name = "statusName", value = "状态")
    private String statusName;
    /**
     * 推荐人数量
     */
    @ApiModelProperty(name = "referUserCount", value = "推荐人数量", position = 80)
    private Integer referUserCount;

    public String getLevelName() {
        if (StringUtils.isNotBlank(level)) {
            levelName = ECuserLevel.getCuserLevel(level).getValue();
        }

        return levelName;
    }

    public String getAgentStatusName() {
        if (StringUtils.isNotBlank(agentStatus)) {
            agentStatusName = ECuserAgentStatus.getCuserAgentStatus(agentStatus).getValue();
        }

        return agentStatusName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EUserStatus.getUserStatus(status).getValue();
        }

        return agentStatusName;
    }


    @ApiModelProperty(name = "grade", value = "专区等级", position = 90)
    private Integer grade;

    /**
     * 微信unionId
     */
    @ApiModelProperty(name = "unionId", value = "微信unionId", position = 71)
    private String unionId;

    /**
     * 微信小程序openid
     */
    @ApiModelProperty(name = "openid", value = "微信小程序openid", position = 72)
    private String openid;

    /**
     * 微信App openid
     */
    @ApiModelProperty(name = "appOpenid", value = "微信App openid", position = 73)
    private String appOpenid;

    /**
     * 微信昵称
     */
    @ApiModelProperty(name = "wxNickname", value = "微信昵称", position = 73)
    private String wxNickname;

    private String registerTimeStart;
    private String registerTimeEnd;
    private String haveOpenid;
}

