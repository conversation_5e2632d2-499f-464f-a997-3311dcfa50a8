package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数据字典
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:51
 */
@Data
public class Dict extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号")
    private Integer id;

    /**
     * 类型（0父类 1子类）
     */
    @ApiModelProperty(name = "type", value = "类型（0父类 1子类）")
    private String type;

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKey", value = "父亲key")
    private String parentKey;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "key")
    private String key;

    /**
     * value
     */
    @ApiModelProperty(name = "value", value = "value")
    private String value;

    /**
     * 组号
     */
    @ApiModelProperty(name = "groupNo", value = "组号")
    private Integer groupNo;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private Integer orderNo;

    /**
     * 最近修改人
     */
    @ApiModelProperty(name = "updater", value = "最近修改人")
    private String updater;

    /**
     * 最近修改人
     */
    @ApiModelProperty(name = "updateDatetime", value = "最近修改人")
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    // DB Properties

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKeyList", value = "父亲key")
    private List<String> parentKeyList;
}
