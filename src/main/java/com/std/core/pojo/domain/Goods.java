package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EGoodsStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 商品
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@Data
public class Goods extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型序号
     */
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 20)
    private Long typeId;


    @ApiModelProperty(name = "parentId", value = "大类id", position = 20)
    private Long parentId;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称", position = 30)
    private String name;

    /**
     * 商品价格
     */
    @ApiModelProperty(name = "price", value = "商品价格", position = 40)
    private BigDecimal price;

    /**
     * 商品图片
     */
    @ApiModelProperty(name = "pic", value = "商品图片", position = 50)
    private String pic;


    @ApiModelProperty(name = "pics", value = "商品图片", position = 50)
    private String pics;

    /**
     * 商品详情
     */
    @ApiModelProperty(name = "content", value = "商品详情", position = 60)
    private String content;

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:上架中,2:已下架}", position = 70)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;
    private List<String> noStatusList;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 140)
    private Integer orderNo;

    /**** Properties ****/

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:待上架,1:上架中,2:已下架}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EGoodsStatus.getGoodsStatus(status).getValue();
      }

      return statusName;
    }


    private List<GoodsNorms>goodsNormsList;

    private String flag;


}
