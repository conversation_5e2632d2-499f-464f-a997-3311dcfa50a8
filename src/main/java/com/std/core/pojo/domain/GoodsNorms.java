package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EGoodsNormsStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 商品规格
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:59
 */
@Data
public class GoodsNorms extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 商品序号
     */
    @ApiModelProperty(name = "goodId", value = "商品序号", position = 20)
    private Long goodId;

    /**
     * 规格名称
     */
    @ApiModelProperty(name = "name", value = "规格名称", position = 30)
    private String name;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 40)
    private BigDecimal price;

    /**
     * 总量
     */
    @ApiModelProperty(name = "number", value = "总量", position = 50)
    private Integer number;

    /**
     * 库存
     */
    @ApiModelProperty(name = "inventory", value = "库存", position = 60)
    private Integer inventory;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 70)
    private String pic;

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:上架中,2:已下架}", position = 80)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;
    private List<String> noStatusList;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 90)
    private Long creater;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 100)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 110)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 120)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 130)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 140)
    private Date updateDatetime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 150)
    private Integer orderNo;

    /**** Properties ****/

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:待上架,1:上架中,2:已下架}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EGoodsNormsStatus.getGoodsNormsStatus(status).getValue();
      }

      return statusName;
    }



}
