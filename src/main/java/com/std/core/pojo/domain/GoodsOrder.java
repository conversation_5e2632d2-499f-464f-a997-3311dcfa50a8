package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EGoodsOrderType;
import com.std.core.enums.EGoodsOrderStatus;
import com.std.core.enums.EGoodsOrderReceiveWay;
import com.std.core.enums.EGoodsOrderPayType;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 商品订单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@Data
public class GoodsOrder extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    @ApiModelProperty(name = "userId", value = "用户id", position = 10)
    private Long userId;
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 类型{0:普通订单,1:购物车订单}
     */
    @ApiModelProperty(name = "type", value = "类型{0:普通订单,1:购物车订单}", position = 20)
    private String type;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 30)
    private String orderNumber;

    /**
     * 快递单号
     */
    @ApiModelProperty(name = "courierNumber", value = "快递单号", position = 40)
    private String courierNumber;
    private String keywords;

    /**
     * 购买数量
     */
    @ApiModelProperty(name = "number", value = "购买数量", position = 50)
    private Integer number;

    /**
     * 总价
     */
    @ApiModelProperty(name = "totalPrice", value = "总价", position = 60)
    private BigDecimal totalPrice;

    /**
     * 状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}", position = 70)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 收货方式{0:到付,1:包邮}
     */
    @ApiModelProperty(name = "receiveWay", value = "收货方式{0:到付,1:包邮}", position = 80)
    private String receiveWay;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 90)
    private String payType;

    /**
     * 收货地址
     */
    @ApiModelProperty(name = "address", value = "收货地址", position = 100)
    private String address;

    /**
     * 收货人名称
     */
    @ApiModelProperty(name = "userName", value = "收货人名称", position = 110)
    private String userName;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(name = "userMobile", value = "收货人手机号", position = 120)
    private String userMobile;

    /**
     * 快递公司
     */
    @ApiModelProperty(name = "company", value = "快递公司", position = 130)
    private String company;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 140)
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 150)
    private Date createDatetime;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 160)
    private Date payDatetime;

    /**
     * 取消时间
     */
    @ApiModelProperty(name = "cancleDatetime", value = "取消时间", position = 170)
    private Date cancleDatetime;

    private Date autoReceiveDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 180)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 190)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 200)
    private Date updateDatetime;

    /**
     * 确认时间
     */
    @ApiModelProperty(name = "finishDatetime", value = "确认时间", position = 210)
    private Date finishDatetime;

    /**** Properties ****/

    /**
     * 类型{0:普通订单,1:购物车订单}
     */
    @ApiModelProperty(name = "typeName", value = "类型{0:普通订单,1:购物车订单}")
    private String typeName;

    /**
     * 状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}")
    private String statusName;

    /**
     * 收货方式{0:到付,1:包邮}
     */
    @ApiModelProperty(name = "receiveWayName", value = "收货方式{0:到付,1:包邮}")
    private String receiveWayName;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付类型 {0:微信}")
    private String payTypeName;

    private String sendMessage;

    public String getTypeName() {
      if (StringUtils.isNotBlank(type)) {
        typeName = EGoodsOrderType.getGoodsOrderType(type).getValue();
      }

      return typeName;
    }

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EGoodsOrderStatus.getGoodsOrderStatus(status).getValue();
      }

      return statusName;
    }

    public String getReceiveWayName() {
      if (StringUtils.isNotBlank(receiveWay)) {
        receiveWayName = EGoodsOrderReceiveWay.getGoodsOrderReceiveWay(receiveWay).getValue();
      }

      return receiveWayName;
    }

    public String getPayTypeName() {
      if (StringUtils.isNotBlank(payType)) {
        payTypeName = EGoodsOrderPayType.getGoodsOrderPayType(payType).getValue();
      }

      return payTypeName;
    }

    private List<GoodsOrderDetail>goodsOrderDetailList;

    private String transactionId;

    /**
     * 下单筛选开始时间
     */
    @ApiModelProperty(name = "createStartTime", value = "下单筛选开始时间", position = 120)
    private Date createStartTime;

    /**
     * 下单筛选结束时间
     */
    @ApiModelProperty(name = "createEndTime", value = "下单筛选结束时间", position = 120)
    private Date createEndTime;
}
