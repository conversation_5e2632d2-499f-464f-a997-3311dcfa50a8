package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EGoodsTrolleyStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 购物车
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
@Data
public class GoodsTrolley extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 订单序号
     */
    @ApiModelProperty(name = "orderId", value = "订单序号", position = 20)
    private Long orderId;

    /**
     * 商品id
     */
    @ApiModelProperty(name = "goodsId", value = "商品id", position = 30)
    private Long goodsId;

    /**
     * 规格id
     */
    @ApiModelProperty(name = "normsId", value = "规格id", position = 40)
    private Long normsId;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 50)
    private Integer number;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 60)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 状态{0:已购买,1:未购买,2:已删除}
     */
    @ApiModelProperty(name = "status", value = "状态{0:已购买,1:未购买,2:已删除}", position = 70)
    private String status;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 90)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 状态{0:已购买,1:未购买,2:已删除}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:已购买,1:未购买,2:已删除}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EGoodsTrolleyStatus.getGoodsTrolleyStatus(status).getValue();
      }

      return statusName;
    }



}
