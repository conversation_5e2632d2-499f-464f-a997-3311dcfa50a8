package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:06
 */
@Data
public class Group extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 端
     */
    @ApiModelProperty(name = "kind", value = "类型")
    private String kind;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private String orderNo;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人")
    private String creater;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号")
    private Long companyId;

    /** *********DB Prpperties************** */
    /**
     * 组角色列表
     */
    @ApiModelProperty(name = "groupRoleList", value = "组角色列表")
    private List<GroupRole> groupRoleList;
}
