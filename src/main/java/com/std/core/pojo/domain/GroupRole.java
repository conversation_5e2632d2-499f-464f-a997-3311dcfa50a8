package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 用户组角色 @Author: Silver @CreateDate: 2019-01-08 17:06 @Version: 1.0
 */
@Data
public class GroupRole extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 组编号
     */
    @ApiModelProperty(name = "groupId", value = "组编号")
    private Long groupId;

    /**
     * 角色编号
     */
    @ApiModelProperty(name = "roleId", value = "角色编号")
    private Long roleId;

    /** **********DB Properties************** */
    /**
     * 角色
     */
    @ApiModelProperty(name = "role", value = "角色")
    private Role role;

    public GroupRole(Long groupId, Long roleId) {
        this.groupId = groupId;
        this.roleId = roleId;
    }

    public GroupRole() {}
}
