package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EIncomeAmountType;
import com.std.core.enums.EIncomeNodeType;
import com.std.core.enums.EIncomeTeamType;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 收益
 *
 * <AUTHOR> Leo
 * @since : 2020-06-06 23:13
 */
@Data
public class Income extends BaseDo {

    /**
     * 主键编号
     */
    @ApiModelProperty(name = "id", value = "主键编号", position = 10)
    private Long id;

    /**
     * 收益人用户ID
     */
    @ApiModelProperty(name = "userId", value = "收益人用户ID", position = 20)
    private Long userId;

    /**
     * 关联订单编号
     */
    @ApiModelProperty(name = "refId", value = "关联订单编号", position = 30)
    private Long refId;

    /**
     * 关联用户编号
     */
    @ApiModelProperty(name = "refUserId", value = "关联用户编号", position = 31)
    private Long refUserId;

    /**
     * 收益类型（0=自身收益 1=推荐收益 2=节点收益）
     */
    @ApiModelProperty(name = "type", value = "收益类型（0=自身收益 1=推荐收益 2=节点收益）", position = 40)
    private String type;

    /**
     * 团队类型
     */
    @ApiModelProperty(name = "teamType", value = "团队类型", position = 50)
    private String teamType;

    /**
     * 节点类型
     */
    @ApiModelProperty(name = "nodeType", value = "节点类型", position = 60)
    private String nodeType;

    /**
     * 金额类型
     */
    @ApiModelProperty(name = "amountType", value = "金额类型", position = 70)
    private String amountType;

    /**
     * 收益时间
     */
    @ApiModelProperty(name = "incomeTime", value = "收益时间", position = 80)
    private Date incomeTime;

    /**
     * 收益
     */
    @ApiModelProperty(name = "amount", value = "收益", position = 90)
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费", position = 100)
    private BigDecimal fee;

    /**
     * 实际收益
     */
    @ApiModelProperty(name = "realAmount", value = "实际收益", position = 110)
    private BigDecimal realAmount;

    /**
     * 收益状态(0=未结算 1=已结算`)
     */
    @ApiModelProperty(name = "status", value = "收益状态(0=未结算 1=已结算`)", position = 120)
    private String status;

    /**
     * 结算时间
     */
    @ApiModelProperty(name = "settleTime", value = "结算时间", position = 130)
    private Date settleTime;

    /**
     * 结算金额
     */
    @ApiModelProperty(name = "settleAmount", value = "结算金额", position = 140)
    private BigDecimal settleAmount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 150)
    private Date createDatetime;

    /**
     * 收益说明
     */
    @ApiModelProperty(name = "remark", value = "收益说明", position = 160)
    private String remark;

    /**** Properties ****/

    /**
     * 团队类型
     */
    @ApiModelProperty(name = "teamTypeName", value = "团队类型")
    private String teamTypeName;

    /**
     * 节点类型
     */
    @ApiModelProperty(name = "nodeTypeName", value = "节点类型")
    private String nodeTypeName;

    /**
     * 金额类型
     */
    @ApiModelProperty(name = "amountTypeName", value = "金额类型")
    private String amountTypeName;

    public String getTeamTypeName() {
        if (StringUtils.isNotBlank(teamType)) {
            teamTypeName = EIncomeTeamType.getIncomeTeamType(teamType).getValue();
        }

        return teamTypeName;
    }

    public String getNodeTypeName() {
        if (StringUtils.isNotBlank(nodeType)) {
            nodeTypeName = EIncomeNodeType.getIncomeNodeType(nodeType).getValue();
        }

        return nodeTypeName;
    }

    public String getAmountTypeName() {
        if (StringUtils.isNotBlank(amountType)) {
            amountTypeName = EIncomeAmountType.getIncomeAmountType(amountType).getValue();
        }

        return amountTypeName;
    }

    private Date createDatetimeStart;

    private Date createDatetimeEnd;

    private List<User> userList;

}
