package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账户流水
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Data
public class Jour extends BaseDo implements Serializable {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 流水类型（1余额流水 2冻结流水）
     */
    @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类")
    private String bizCategory;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategoryNote", value = "业务大类")
    private String bizCategoryNote;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;

    /**
     * 业务小类说明
     */
    @ApiModelProperty(name = "bizNote", value = "业务小类说明")
    private String bizNote;

    /**
     * 系统内部参考订单号
     */
    @ApiModelProperty(name = "refNo", value = "系统内部参考订单号")
    private String refNo;

    /**
     * 关联用户ID
     */
    @ApiModelProperty(name = "refUserId", value = "关联用户ID")
    private Long refUserId;

    /**
     * 变动金额
     */
    @ApiModelProperty(name = "transAmount", value = "变动金额")
    private BigDecimal transAmount;

    /**
     * 变动前金额
     */
    @ApiModelProperty(name = "preAmount", value = "变动前金额")
    private BigDecimal preAmount;

    /**
     * 变动后金额
     */
    @ApiModelProperty(name = "postAmount", value = "变动后金额")
    private BigDecimal postAmount;

    /**
     * 上一条流水编号
     */
    @ApiModelProperty(name = "prevJourCode", value = "上一条流水编号")
    private String prevJourCode;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间")
    private Date createDatetime;

    //DB Properties
    /**
     * 业务类型列表
     */
    @ApiModelProperty(name = "bizTypeList", value = "业务类型列表")
    private List<String> bizTypeList;

    /**
     * 账户编号列表
     */
    @ApiModelProperty(name = "accountNumberList", value = "账户编号列表")
    private List<String> accountNumberList;

    /**
     * 大类列表
     */
    @ApiModelProperty(name = "bizCategoryList", value = "大类列表")
    private List<String> bizCategoryList;

    /**
     * 大状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表")
    private List<String> statusList;

    /**
     * 关联人员信息
     */
    @ApiModelProperty(name = "refUserInfo", value = "关联人员信息")
    private User refUserInfo;

    /**
     * 到账时间
     */
    @ApiModelProperty(name = "arriveAccountTime", value = "到账时间")
    private Date arriveAccountTime;

    /**
     * 入账类型
     */
    @ApiModelProperty(name = "arriveAccountType", value = "入账类型")
    private String arriveAccountType;

    /**
     * 优惠信息
     */
    @ApiModelProperty(name = "discountInfo", value = "优惠信息")
    private String discountInfo;

    /**
     * 服务费
     */
    @ApiModelProperty(name = "serviceCharge", value = "服务费")
    private BigDecimal serviceCharge;

    /**
     * 结算总金额
     */
    @ApiModelProperty(name = "totalSettleAmount", value = "结算总金额")
    private BigDecimal totalSettleAmount;

    @ApiModelProperty(name = "startTime", value = "时间查询的开始时间")
    private String startTime;

    @ApiModelProperty(name = "endTime", value = "时间查询的结束时间")
    private String endTime;

    @ApiModelProperty(name = "income", value = "收入")
    private String income;

    @ApiModelProperty(name = "expenditure", value = "支出")
    private String expenditure;

    @ApiModelProperty(name = "user", value = "用户信息")
    private User user;
}
