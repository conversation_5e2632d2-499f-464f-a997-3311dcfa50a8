package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 语言资源
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-02 10:59
 */
@Data
public class LanguageResource extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Integer id;

    /**
     * 表名
     */
    @ApiModelProperty(name = "table", value = "表名")
    private String table;

    /**
     * 记录编号
     */
    @ApiModelProperty(name = "refId", value = "记录编号")
    private String refId;

    /**
     * 列名
     */
    @ApiModelProperty(name = "column", value = "列名")
    private String column;

    /**
     * 中文数据
     */
    @ApiModelProperty(name = "znData", value = "中文数据")
    private String znData;

    /**
     * 英文数据
     */
    @ApiModelProperty(name = "enData", value = "英文数据")
    private String enData;


}
