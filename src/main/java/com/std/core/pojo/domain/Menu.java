package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:08
 */
@Data
public class Menu extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 类型（菜单/按钮）
     */
    @ApiModelProperty(name = "type", value = "类型（菜单/按钮）")
    private String type;

    /**
     * 端
     */
    @ApiModelProperty(name = "kind", value = "端")
    private String kind;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * logo图标
     */
    @ApiModelProperty(name = "logo", value = "logo图标")
    private String logo;

    /**
     * URL
     */
    @ApiModelProperty(name = "url", value = "URL")
    private String url;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private Long orderNo;

    /**
     * 展示位置
     */
    @ApiModelProperty(name = "location", value = "展示位置")
    private String location;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    // *******db properties**********

    /**
     * 角色权限查询时带出信息
     */
    @ApiModelProperty(name = "roleList", value = "角色权限查询时带出信息")
    private List<Long> roleList;

    /**
     * 类型列表
     */
    @ApiModelProperty(name = "typeList", value = "类型列表")
    private List<String> typeList;

    /**
     * 路径
     */
    @ApiModelProperty(name = "path", value = "路径")
    private String path;

    /**
     * 深度
     */
    @ApiModelProperty(name = "depth", value = "深度")
    private String depth;

    /**
     * 父菜单
     */
    @ApiModelProperty(name = "parentMenu", value = "父菜单")
    private Menu parentMenu;

    /**
     * 资源列表
     */
    @ApiModelProperty(name = "actionList", value = "资源列表")
    private List<Action> actionList;
}
