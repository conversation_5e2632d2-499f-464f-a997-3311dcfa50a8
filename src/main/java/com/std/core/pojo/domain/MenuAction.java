package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:09
 */
@Data
public class MenuAction extends BaseDo {

    /**
     * 菜单编号
     */
    @ApiModelProperty(name = "menuId", value = "菜单编号")
    private Long menuId;

    /**
     * 接口编号
     */
    @ApiModelProperty(name = "actionId", value = "接口编号")
    private Long actionId;

    // ***********db properties***********
    /**
     * 接口
     */
    @ApiModelProperty(name = "action", value = "接口")
    private Action action;

    public MenuAction(Long menuId, Long actionId) {
        this.menuId = menuId;
        this.actionId = actionId;
    }

    public MenuAction() {
    }
}
