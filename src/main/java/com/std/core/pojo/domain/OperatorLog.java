package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 操作日志
 *
 * <AUTHOR> ycj
 * @since : 2024-04-29 11:25
 */
@Data
public class OperatorLog extends BaseDo {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 操作者uid
     */
    @ApiModelProperty(name = "userId", value = "操作者uid", position = 20)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 操作时间
     */
    @ApiModelProperty(name = "operateTime", value = "操作时间", position = 30)
    private Date operateTime;

    /**
     * 操作时间戳
     */
    @ApiModelProperty(name = "operateTs", value = "操作时间戳", position = 40)
    private Long operateTs;

    /**
     * 操作ip
     */
    @ApiModelProperty(name = "ip", value = "操作ip", position = 50)
    private String ip;

    /**
     * 浏览器头信息
     */
    @ApiModelProperty(name = "userAgent", value = "浏览器头信息", position = 60)
    private String userAgent;

    /**
     * 操作url
     */
    @ApiModelProperty(name = "url", value = "操作url", position = 70)
    private String url;

    /**
     * 表单get内容
     */
    @ApiModelProperty(name = "get", value = "表单get内容", position = 80)
    private String get;

    /**
     * 表单post内容
     */
    @ApiModelProperty(name = "post", value = "表单post内容", position = 90)
    private String post;



}
