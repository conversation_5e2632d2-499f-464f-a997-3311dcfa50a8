package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EPayRecordBizStatus;
import com.std.core.enums.EPayRecordBizType;
import com.std.core.enums.EPayRecordStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付记录
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
@Data
public class PayRecord extends BaseDo {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 支付渠道 wechat、alipy等
     */
    @ApiModelProperty(name = "payType", value = "支付渠道 wechat、alipy等", position = 30)
    private String payType;

    /**
     * 支付方式 app、h5、web等
     */
    @ApiModelProperty(name = "payMethod", value = "支付方式 app、h5、web等", position = 40)
    private String payMethod;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "amount", value = "支付金额", position = 50)
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 60)
    private Date createTime;

    /**
     * 回调时间
     */
    @ApiModelProperty(name = "callbackTime", value = "回调时间", position = 70)
    private Date callbackTime;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 80)
    private String status;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizType", value = "业务类型", position = 90)
    private String bizType;

    /**
     * 关联业务编号
     */
    @ApiModelProperty(name = "bizCode", value = "关联业务编号", position = 100)
    private Long bizCode;

    /**
     * 状态
     */
    @ApiModelProperty(name = "bizStatus", value = "状态", position = 110)
    private String bizStatus;

    /**
     * 支付请求入参
     */
    @ApiModelProperty(name = "request", value = "支付请求入参", position = 110)
    private String request;
    /**
     * 支付回调入参
     */
    @ApiModelProperty(name = "response", value = "支付回调入参", position = 110)
    private String response;
    /**** Properties ****/

    /**
     * 状态
     */
    @ApiModelProperty(name = "statusName", value = "状态")
    private String statusName;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizTypeName", value = "业务类型")
    private String bizTypeName;

    /**
     * 状态
     */
    @ApiModelProperty(name = "bizStatusName", value = "状态")
    private String bizStatusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EPayRecordStatus.getPayRecordStatus(status).getValue();
        }

        return statusName;
    }

    public String getBizTypeName() {
        if (StringUtils.isNotBlank(bizType)) {
            bizTypeName = EPayRecordBizType.getPayRecordBizType(bizType).getValue();
        }

        return bizTypeName;
    }

    public String getBizStatusName() {
        if (StringUtils.isNotBlank(bizStatus)) {
            bizStatusName = EPayRecordBizStatus.getPayRecordBizStatus(bizStatus).getValue();
        }

        return bizStatusName;
    }


}
