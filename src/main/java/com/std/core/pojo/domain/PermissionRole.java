package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:11
 */
@Data
public class PermissionRole extends BaseDo {

    /**
     * 角色编号
     */
    @ApiModelProperty(name = "roleId", value = "角色编号")
    private Long roleId;

    /**
     * 资源编号
     */
    @ApiModelProperty(name = "resourceId", value = "资源编号")
    private Long resourceId;

    /**
     * 资源类型
     */
    @ApiModelProperty(name = "resourceType", value = "资源类型")
    private String resourceType;

    // **********db properties

    /**
     * 资源类型列表
     */
    @ApiModelProperty(name = "resourceTypeList", value = "资源类型列表")
    List<String> resourceTypeList;

    /**
     * 菜单编号
     */
    @ApiModelProperty(name = "menuIdList", value = "菜单编号")
    private List<Long> menuIdList;

    public PermissionRole(Long roleId, Long resourceId, String resourceType) {
        this.roleId = roleId;
        this.resourceId = resourceId;
        this.resourceType = resourceType;
    }

    public PermissionRole() {
    }
}
