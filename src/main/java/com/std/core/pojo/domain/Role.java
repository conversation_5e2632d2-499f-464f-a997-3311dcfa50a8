package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 17:11
 */
@Data
public class Role extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(name = "kind", value = "类型")
    private String kind;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号")
    private Long companyId;

    /**
     * 别名
     */
    @ApiModelProperty(name = "alias", value = "别名")
    private String alias;

    /**
     * 是否是系统默认权限
     */
    @ApiModelProperty(name = "roleId", value = "角色编号")
    private String isDefault;

    /** ** Properties *** */

    /**
     * 查询公司内部及默认权限
     */
    @ApiModelProperty(name = "companyIdAndDefault", value = "查询公司内部及默认权限")
    private Long companyIdAndDefault;
}
