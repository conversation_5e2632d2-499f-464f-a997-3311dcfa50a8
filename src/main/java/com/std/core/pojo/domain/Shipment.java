package com.std.core.pojo.domain;

import lombok.Data;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;


@Entity
    @Table(name = "shipment")
    @Data
    public class Shipment {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Embedded
        private OrderKey orderKey; // 订单信息

        @Column(nullable = false)
        private Integer deliveryMode; // 配送方式

        @Column(nullable = false)
        private Integer logisticsType; // 物流类型

        @OneToMany(mappedBy = "shipment", cascade = CascadeType.ALL)
        private List<ShippingDetail> shippingList = new ArrayList<>();

        @Column(nullable = false)
        private String uploadTime; // 上传时间

        @Embedded
        private Payer payer; // 付款人
    }


