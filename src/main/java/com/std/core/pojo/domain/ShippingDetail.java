package com.std.core.pojo.domain;

import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "shipping_detail")
@Data
public class ShippingDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "shipment_id", nullable = false)
    private Shipment shipment;

    @Column(nullable = false)
    private String trackingNo; // 物流单号

    @Column(nullable = false)
    private String expressCompany; // 快递公司

    @Column(nullable = false)
    private String itemDesc; // 物品描述

    @Embedded
    private Contact contact;
}
