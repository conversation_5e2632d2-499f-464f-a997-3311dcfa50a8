package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.ESmsStatus;
import com.std.core.enums.ESmsTarget;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

/**
 * 公告
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Data
public class Sms extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 针对人群
     */
    @ApiModelProperty(name = "target", value = "针对人群", position = 20)
    private String target;

    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型", position = 30)
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", position = 50)
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", position = 60)
    private String content;
    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型", position = 60)
    private String contentType;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 65)
    private Long userId;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称", position = 67)
    private String userName;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 70)
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 90)
    private Long creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "creatorName", value = "创建人名称", position = 100)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 90)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 100)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 110)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 120)
    private String remark;

    /**
     * 关联类型
     */
    @ApiModelProperty(name = "refType", value = "关联类型", position = 130)
    private String refType;

    /**
     * 关联编号
     */
    @ApiModelProperty(name = "refNo", value = "关联编号", position = 130)
    private String refNo;
    /**
     * 未读数量
     */
    @ApiModelProperty(name = "number", value = "未读数量", position = 135)
    private Integer number;
    /**
     * 用户是否已读站内信
     */
    @ApiModelProperty(name = "isRead", value = "用户是否已读站内信 1=已读 0=未读", position = 137)
    private String isRead;

    /**
     * 消息中心时的记录状态
     */
    @ApiModelProperty(name = "tsrStatus", value = "消息中心时的记录状态", position = 140)
    private String tsrStatus;
    /**
     * 未读消息时的记录状态
     */
    @ApiModelProperty(name = "numberStatua", value = "未读消息时的记录状态", position = 145)
    private String numberStatua;

    @ApiModelProperty(name = "user", value = "用户信息", position = 155, hidden = true)
    private User user;
    @ApiModelProperty(name = "keywords", value = "关键字模糊查", position = 100)
    private String keywords;
    //=====db properties=====


    @ApiModelProperty(name = "statusName", value = "状态名称")
    private String statusName;
    /**
     * 针对人群
     */
    @ApiModelProperty(name = "targetName", value = "针对人群名称", position = 20)
    private String targetName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ESmsStatus.getNoticeStatus(status).getValue();
        }

        return statusName;
    }

    public String getTargetName() {
        if (StringUtils.isNotBlank(target)) {
            targetName = ESmsTarget.getSmsTarget(target).getValue();
        }

        return targetName;
    }
}
