package com.std.core.pojo.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.std.common.base.BaseDo;
import com.std.core.util.InviteCodeUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class User extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 类型（类型（运维中心OPS 平台端SYS 用户端C））
     */
    @ApiModelProperty(name = "kind", value = "类型（运维中心OPS 平台端SYS 用户端C）")
    private String kind;

    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;

    /**
     * 性别
     */
    @ApiModelProperty(name = "sex", value = "性别")
    private String sex;

    /**
     * 生日
     */
    @ApiModelProperty(name = "age", value = "年龄")
    private Integer age;
    /**
     * 证件类型
     */
    @ApiModelProperty(name = "idKind", value = "证件类型")
    private String idKind;

    /**
     * 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码")
    private String idNo;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "realName", value = "姓名")
    private String realName;

    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "area", value = "区")
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址")
    private String address;
    /**
     * 愿力值余额
     */
    @ApiModelProperty(name = "willingValue", value = "愿力值余额")
    private BigDecimal willingValue;
    /**
     * 登录密码
     */
    @JsonIgnore
    private String loginPwd;

    /**
     * 登录密码强度
     */
    @JsonIgnore
    private String loginPwdStrength;

    /**
     * 支付密码
     */
    @JsonIgnore
    private String tradePwd;

    /**
     * 支付密码强度
     */
    @JsonIgnore
    private String tradePwdStrength;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "registerDatetime", value = "创建时间")
    private Date registerDatetime;

    /**
     * 注册IP
     */
    @ApiModelProperty(name = "registerIp", value = "注册IP")
    private String registerIp;

    /**
     * 邀请码
     */
    @ApiModelProperty(name = "inviteNo", value = "邀请码")
    private transient Long inviteNo;

    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人")
    private Long userReferee;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 最后登录时间
     */
    @ApiModelProperty(name = "lastLoginDatetime", value = "最后登录时间")
    private Date lastLoginDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称")
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间")
    private Date updateDatetime;


    /**
     * 预约状态 0:正常1:限制
     */
    @ApiModelProperty(name = "reservationStatus", value = "预约状态 0:正常1:限制")
    private String reservationStatus;

    /**
     * 预约过期次数
     */
    @ApiModelProperty(name = "reservationExpiredNumber", value = "预约过期次数")
    private Integer reservationExpiredNumber;

    /**
     * 预约限制解封时间
     */
    @ApiModelProperty(name = "reservationUnsealTime", value = "预约限制解封时间")
    private Date reservationUnsealTime;
    @ApiModelProperty(name = "reservationUnsealTimeEnd", value = "预约限制解封时间")
    private Date reservationUnsealTimeEnd;

    // *********************db properties*********************

    /**
     * 登录名模糊查询使用
     */
    @ApiModelProperty(name = "loginNameForQuery", value = "登录名模糊查询使用")
    private String loginNameForQuery;

    /**
     * 角色
     */
    @ApiModelProperty(name = "roleList", value = "角色")
    private List<Role> roleList;

    /**
     * 用户组
     */
    @ApiModelProperty(name = "groupList", value = "用户组")
    private List<Group> groupList;

    /**
     * 角色
     */
    @ApiModelProperty(name = "kindName", value = "角色")
    private String kindName;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号")
    private Long companyId;

    /**
     * 支付密码标识
     */
    @ApiModelProperty(name = "tradePwdFlag", value = "支付密码标识")
    private String tradePwdFlag;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 代数
     */
    private Integer agentNo;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 渠道等级
     */
    private Integer channelGrade;

    /**
     * 推荐用户列表
     */
    @ApiModelProperty(name = "refereeUserList", value = "推荐用户列表", hidden = true)
    private List<User> refereeUserList;

    /**
     * 状态列表
     */
    private List<String> statusList;

    /**
     * 用户名
     */
    @ApiModelProperty(name = "userName", value = "用户名", position = 20)
    private String userName;

   /**
     * 昵称修改标识
     */
    @ApiModelProperty(name = "nicknameChangeFlag", value = "昵称修改标识", position = 20)
    private String nicknameChangeFlag;

    /**
     * 今日消耗金(ttask_income amount_type=消耗金，创建时间是今天)
     */
    @ApiModelProperty(name = "costIncome", value = "今日消耗金", position = 80)
    private BigDecimal costIncome;

    /**
     * 截止今日消耗金(ttask_income amount_type=消耗金)
     */
    @ApiModelProperty(name = "totalCostIncome", value = "截止今日消耗金", position = 80)
    private BigDecimal totalCostIncome;

    private String mobileForQuery;

    /**
     * 推荐人数量
     */
    @ApiModelProperty(name = "referUserCount", value = "推荐人数量", position = 80)
    private Integer referUserCount;

    private Date registerDatetimeStart;

    private Date registerDatetimeEnd;

    private Date lastLoginDatetimeStart;

    private Date lastLoginDatetimeEnd;

    public String getInviteCode() {
        if (null != inviteNo) {
            inviteCode = InviteCodeUtil.toSerialCode(inviteNo);
        }
        return inviteCode;
    }

    @ApiModelProperty(name = "gradeList", hidden = true)
    private List<Integer> gradeList;

    private String keywords;
    private String registerTimeStart;
    private String registerTimeEnd;
}
