package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户日志
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
@Data
public class UserLog extends BaseDo {

    /**
     * ID主键
     */
    @ApiModelProperty(name = "id", value = "ID主键")
    private Integer id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称")
    private String userName;
    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容")
    private String content;
    /**
     * 分类（1登录 2绑定手机号 3绑定邮箱 4绑定支付密码 5修改手机号 6修改登录密码 7修改邮箱 8修改支付密码）
     */
    @ApiModelProperty(
            name = "type",
            value = "分类")
    private String type;
    /**
     * 类型说明
     */
    @ApiModelProperty(name = "typeNote", value = "类型说明")
    private String typeNote;
    /**
     * ip
     */
    @ApiModelProperty(name = "ip", value = "ip")
    private String ip;

    /**
     * 客户端（1Web 2Android 3IOS）
     */
    @ApiModelProperty(name = "client", value = "客户端（1Web 2Android 3IOS）")
    private String client;

    /**
     * 操作时定位
     */
    @ApiModelProperty(name = "location", value = "操作时定位")
    private String location;

    /**
     * 创建时间时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间时间")
    private Date createDatetime;
}
