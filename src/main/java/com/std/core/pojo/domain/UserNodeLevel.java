package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EUserNodeLevelWay;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 节点用户
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
@Data
public class UserNodeLevel extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 设置方式
     */
    @ApiModelProperty(name = "way", value = "设置方式 ", position = 30)
    private String way;

    /**
     * 节点等级（自动）
     */
    @ApiModelProperty(name = "nodeLevelAuto", value = "节点等级（自动）", position = 40)
    private Integer nodeLevelAuto;

    /**
     * 节点等级（手动）
     */
    @ApiModelProperty(name = "nodeLevelManual", value = "节点等级（手动）", position = 50)
    private Integer nodeLevelManual;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 60)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间", position = 70)
    private Date updateTime;

    /**** Properties ****/

    /**
     * 用户信息
     */
    @ApiModelProperty(name = "user", value = "用户信息", position = 71)
    private User user;

    /**
     * 节点等级
     */
    private Integer nodeLevel;

    /**
     * 所有的节点等级
     */
    private Integer allNodeLevel;

    /**
     * 设置方式
     */
    @ApiModelProperty(name = "wayName", value = "设置方式 ")
    private String wayName;

    public String getWayName() {
        if (StringUtils.isNotBlank(way)) {
            wayName = EUserNodeLevelWay.getUserNodeLevelWay(way).getValue();
        }

        return wayName;
    }

    public Integer getExtNodeLevel() {
        Integer nodeLevel = null;
        if (EUserNodeLevelWay.AUTO.getCode().equals(way)) {
            nodeLevel = nodeLevelAuto;
        } else if (EUserNodeLevelWay.MANUAL.getCode().equals(way)) {
            nodeLevel = nodeLevelManual;
        }
        return nodeLevel;
    }

}
