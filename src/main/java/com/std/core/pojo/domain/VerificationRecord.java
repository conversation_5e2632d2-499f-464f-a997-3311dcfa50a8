package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EVerificationRecordStatus;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 核销
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 01:10
 */
@Data
public class VerificationRecord extends BaseDo {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 20)
    private Long activityId;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 30)
    private Long ticketLineId;

    /**
     * 预约单编号
     */
    @ApiModelProperty(name = "activityOrderId", value = "预约单编号", position = 40)
    private Long activityOrderId;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "date", value = "预约日期", position = 50)
    private Date date;

    /**
     * 状态{0:已核销}
     */
    @ApiModelProperty(name = "status", value = "状态{0:已核销}", position = 60)
    private String status;
    private String activityName ;
    private String nickname ;
    private String mobile ;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 用户id
     */
    @ApiModelProperty(name = "userId", value = "用户id", position = 70)
    private Long userId;

    /**
     * 用户
     */
    @ApiModelProperty(hidden = true)
    private User user;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    private String keywords;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 80)
    private BigDecimal price;

    /**
     * 入场码
     */
    @ApiModelProperty(name = "code", value = "入场码", position = 90)
    private String code;

    /**
     * 活动图片
     */
    @ApiModelProperty(name = "pic", value = "活动图片", position = 100)
    private String pic;

    /**
     * 票档名称
     */
    @ApiModelProperty(name = "ticketLineName", value = "票档名称", position = 110)
    private String ticketLineName;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "name", value = "姓名", position = 120)
    private String name;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "contract", value = "联系方式", position = 130)
    private String contract;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 140)
    private Long creater;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 150)
    private Date createTime;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 160)
    private String createrName;

    private Date startDate;
    private Date endDate;

    /**** Properties ****/

    /**
     * 状态{0:已核销}
     */
    @ApiModelProperty(name = "statusName", value = "状态{0:已核销}")
    private String statusName;

    public String getStatusName() {
      if (StringUtils.isNotBlank(status)) {
        statusName = EVerificationRecordStatus.getVerificationRecordStatus(status).getValue();
      }

      return statusName;
    }





}
