package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import com.std.core.enums.EActivityOrderPayType;
import com.std.core.enums.EActivityOrderStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动预约单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@Data
public class WechatReturnCode {



    /**
     * 响应 code
     */
    @ApiModelProperty(name = "return_code", value = "响应 code", required = true, position = 130)
    private String return_code;


    /**
     * 退款结果
     */
    @ApiModelProperty(name = "result_code", value = "退款结果", required = true, position = 130)
    private String result_code;

}
