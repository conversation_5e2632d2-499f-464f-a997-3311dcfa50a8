package com.std.core.pojo.domain;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 取现订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class Withdraw extends BaseDo {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 类别（B端账号，C端账号，平台账号）
     */
    @ApiModelProperty(name = "accountType", value = "类别（B端账号，C端账号，平台账号）")
    private String accountType;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 业务类型（withdraw取现 transfer 内部划转）
     */
    @ApiModelProperty(name = "bizType", value = "业务类型（withdraw取现 transfer内部划转）")
    private String bizType;

    /**
     * 取现金额
     */
    @ApiModelProperty(name = "amount", value = "取现金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 实际到账金额
     */
    @ApiModelProperty(name = "actualAmount", value = "实际到账金额")
    private BigDecimal actualAmount;

    /**
     * 提现时账户余额
     */
    @ApiModelProperty(name = "balanceAmount", value = "提现时账户余额")
    private BigDecimal balanceAmount;

    /**
     * 是否开票（0否 1是）
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）")
    private String billFlag;

    /**
     * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
     */
    @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）")
    private String channelType;

    /**
     * 渠道银行
     */
    @ApiModelProperty(name = "channelBank", value = "渠道银行")
    private String channelBank;

    /**
     * 支付渠道账号信息
     */
    @ApiModelProperty(name = "channelAccountInfo", value = "支付渠道账号信息")
    private String channelAccountInfo;

    /**
     * 支付渠道账号
     */
    @ApiModelProperty(name = "channelAccountNumber", value = "支付渠道账号")
    private String channelAccountNumber;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号")
    private String channelOrder;

    /**
     * 状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）
     */
    @ApiModelProperty(name = "status", value = "状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）")
    private String status;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUser", value = "申请人")
    private Long applyUser;

    /**
     * 用户类型（C端用户 CLINIC诊所用户）
     */
    @ApiModelProperty(name = "applyUserKind", value = "用户类型（C端用户 CLINIC诊所用户）")
    private String applyUserKind;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明")
    private String applyNote;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetime", value = "申请时间")
    private Date applyDatetime;

    /**
     * 审批人
     */
    @ApiModelProperty(name = "approveUser", value = "审批人")
    private Long approveUser;

    /**
     * 审批说明
     */
    @ApiModelProperty(name = "approveNote", value = "审批说明")
    private String approveNote;

    /**
     * 审批时间
     */
    @ApiModelProperty(name = "approveDatetime", value = "审批时间")
    private Date approveDatetime;

    /**
     * 支付回录人
     */
    @ApiModelProperty(name = "payUser", value = "支付回录人")
    private String payUser;

    /**
     * 支付回录说明
     */
    @ApiModelProperty(name = "payNote", value = "支付回录说明")
    private String payNote;

    /**
     * 支付渠道手续费
     */
    @ApiModelProperty(name = "payFee", value = "支付渠道手续费")
    private BigDecimal payFee;

    /**
     * 支付回录时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付回录时间")
    private Date payDatetime;

    //DB Properties

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUserInfo", value = "申请人", hidden = true)
    private User applyUserInfo;

    /**
     * 审核人信息
     */
    @ApiModelProperty(name = "approveUserInfo", value = "审核人信息", hidden = true)
    private User approveUserInfo;

//    /**
//     * 申请诊所编号
//     */
//    @ApiModelProperty(name = "applyClinicInfo", value = "申请诊所编号")
//    private Clinic applyClinicInfo;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUserName", value = "申请人")
    private String applyUserName;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetimeStart", value = "申请时间")
    private Date applyDatetimeStart;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "申请时间")
    private Date applyDatetimeEnd;

    /**
     * 支付时间起
     */
    @ApiModelProperty(name = "payDatetimeStart", value = "支付回录时间起")
    private Date payDatetimeStart;

    /**
     * 支付时间止
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "支付回录时间止")
    private Date payDatetimeEnd;

    /**
     * 编号
     */
    @ApiModelProperty(name = "idForQuery", value = "编号")
    private Long idForQuery;

    @ApiModelProperty(name = "statusList", value = "状态列表")
    private List<String> statusList;

    private String keywords;

}
