package com.std.core.pojo.domain;

import java.math.BigDecimal;

/**
 * 取现通知
 */
public class WithdrawNotice {

    private String orderNo;

    private String result;

    private BigDecimal mineFee;

    private BigDecimal changeAmount;

    private String remark;

    private Integer timestamp;

    private String sign;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public BigDecimal getMineFee() {
        return mineFee;
    }

    public void setMineFee(BigDecimal mineFee) {
        this.mineFee = mineFee;
    }

    public BigDecimal getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Integer timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
