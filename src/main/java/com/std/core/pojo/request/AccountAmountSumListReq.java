package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountAmountSumListReq extends BaseListReq {

    /**
     *
     */
    @ApiModelProperty(name = "IsExcept", value = "统计是否除外标识 0：统计时不包括已除外的用户，1：只统计除外的用户，2：所有用户 数据字典account_statistics_flag")
    @NotBlank(message = "标识不能为空")
    private String statisticsFlag;

    /**
     *
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;
}
