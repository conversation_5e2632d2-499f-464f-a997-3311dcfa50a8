package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> ycj
 * @since : 2020/11/18 13:35
 */
@Data
public class AccountBatchManualChangeReq {

    @ApiModelProperty(name = "list", value = "list", position = 10)
    @NotEmpty(message = "用户手机号不能为空")
    List<BatchManualChangeReq> list;

}
