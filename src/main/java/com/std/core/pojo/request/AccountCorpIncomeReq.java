package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 公账充值
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountCorpIncomeReq {

    /**
     * 账户编号
     */
    @NotBlank
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    private String accountNumber;

    /**
     * 金额
     */
    @NotNull
    @ApiModelProperty(name = "amount", value = "金额", required = true, position = 20)
    private BigDecimal amount;

    /**
     * 备注
     */
    @NotBlank
    @ApiModelProperty(name = "remark", value = "备注", required = true, position = 30)
    private String remark;

}
