package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 公账充值
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDetailByNumberReq {

    /**
     * 账户编号
     */
    @NotBlank
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    private String accountNumber;

}
