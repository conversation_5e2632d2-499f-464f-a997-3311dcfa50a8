package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDetailByOssReq {


    @ApiModelProperty(name = "userId", value = "用户id", required = true, position = 2)
    private Long userId;

    /**
     * 币种
     */
    @NotBlank
    @ApiModelProperty(name = "currency", value = "币种：CNY人民币，YXG:银杏果,YXY:银杏叶,NAT_LT:NAT流通,NAT_ZY:NAT质押,SCORE:积分", required = true, position = 2)
    private String currency;

}
