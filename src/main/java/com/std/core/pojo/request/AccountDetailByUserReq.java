package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDetailByUserReq {

    /**
     * 币种
     */
    @NotBlank
    @ApiModelProperty(name = "currency", value = "币种：CNY人民币，diamond:钻石", required = true, position = 2)
    private String currency;

    /**
     * 取现金额获取标志(1=接口返回/0=不返回)
     */
    @ApiModelProperty(name = "withdrawAmountFlag", value = "取现金额获取标志(1=接口返回/0=不返回)", required = true, position = 2)
    private String withdrawAmountFlag;


}
