package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 钻石兑换爻
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDiamondExchangeYaoReq {

    @ApiModelProperty(name = "yinYaoAmount", value = "阴爻数量")
    private BigDecimal yinYaoAmount;

    @ApiModelProperty(name = "pwd", value = "支付密码", position = 20)
    @NotBlank(message = "支付密码不能为空")
    private String pwd;

}
