package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDropDetailReq {

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currency;

}
