package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 账户锁定/解锁
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountIsLockrReq {

    @NotBlank
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    private String accountNumber;

    @NotBlank
    @ApiModelProperty(name = "status", value = "状态（1正常 3锁定）", required = true, position = 20)
    private String status;
}
