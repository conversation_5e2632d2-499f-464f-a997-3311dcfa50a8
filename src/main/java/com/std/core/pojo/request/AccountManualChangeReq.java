package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> ycj
 * @since : 2020/11/18 13:35
 */
@Data
public class AccountManualChangeReq {
    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 10)
    @NotNull(message = "用户序号不能为空")
    private Long userId;
    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种", position = 20)
    @NotBlank(message = "币种不能为空")
    private String currency;
    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型 0：支出，1：收入", position = 30)
    @NotBlank(message = "类型不能为空")
    private String type;
    /**
     * 变动金额
     */
    @ApiModelProperty(name = "amount", value = "变动金额", position = 40)
    @NotNull(message = "变动金额不能为空")
    @DecimalMin(value = "0", message = "变动金额不能小于0")
    private BigDecimal amount;

    @ApiModelProperty(name = "remark", value = "备注", position = 50)
//    @NotBlank(message = "备注不能为空")
    private String remark;
}
