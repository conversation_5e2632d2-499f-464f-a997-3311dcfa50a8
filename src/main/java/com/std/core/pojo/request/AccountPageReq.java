package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分页查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountPageReq extends BasePageReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currencyList", value = "币种")
    private List<String> currencyList;

    /**
     * 类别（C端账号 B端账号 P平台账号）
     */
    @ApiModelProperty(name = "type", value = "类别（C端账号 B端账号 P平台账号）")
    private String type;

    /**
     * 状态（1正常 2程序冻结 3人工冻结）
     */
    @ApiModelProperty(name = "status", value = "状态（1正常 2程序冻结 3人工冻结）")
    private String status;

    /**
     * 类型列表
     */
    @ApiModelProperty(name = "typeList", value = "类别（C端账号 B端账号 P平台账号）")
    private List<String> typeList;

    /**
     * 用户关键字信息
     */
    @ApiModelProperty(name = "keywords", value = "用户关键字信息")
    private String keywords;

}
