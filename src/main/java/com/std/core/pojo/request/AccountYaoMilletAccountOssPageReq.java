package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountYaoMilletAccountOssPageReq extends BasePageReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 用户关键字信息
     */
    @ApiModelProperty(name = "keywords", value = "用户关键字信息")
    private String keywords;

}
