package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-07 13:43
 * @description:
 */
@Data
public class ActionCreateReq {

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型", required = true)
    @NotBlank
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true)
    @NotBlank
    private String name;

    /**
     * 操作码
     */
    @ApiModelProperty(name = "code", value = "操作码")
    private String code;

    /**
     * url
     */
    @ApiModelProperty(name = "url", value = "url", required = true)
    @NotBlank
    private String url;

    /**
     * 入参
     */
    @ApiModelProperty(name = "input", value = "入参")
    private String input;

    /**
     * 出参
     */
    @ApiModelProperty(name = "output", value = "出参")
    private String output;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", required = true)
    @NotBlank
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
