package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-07 13:43
 * @description:
 */
@Data
public class ActionListReq extends BaseListReq {

    /**
     * 接口类型
     */
    @ApiModelProperty(name = "type", value = "接口类型")
    private String type;

    /**
     * 接口名称
     */
    @ApiModelProperty(name = "name", value = "接口名称")
    private String name;

    /**
     * 操作码
     */
    @ApiModelProperty(name = "code", value = "操作码")
    private String code;

    /**
     * URL路径
     */
    @ApiModelProperty(name = "url", value = "URL路径")
    private String url;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;
}
