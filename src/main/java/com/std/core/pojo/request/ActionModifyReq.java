package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-07 13:43
 * @description:
 */
@Data
public class ActionModifyReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", required = true)
    @NotNull
    private Long id;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型")
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 操作码
     */
    @ApiModelProperty(name = "code", value = "操作码")
    private String code;

    /**
     * url
     */
    @ApiModelProperty(name = "url", value = "url")
    private String url;

    /**
     * 入参
     */
    @ApiModelProperty(name = "input", value = "入参")
    private String input;

    /**
     * 出参
     */
    @ApiModelProperty(name = "output", value = "出参")
    private String output;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
