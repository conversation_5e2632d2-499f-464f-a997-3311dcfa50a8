package com.std.core.pojo.request;

import com.std.core.pojo.domain.ActivityTicketLine;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增活动
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@Data
public class ActivityCreateReq {

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 活动标题
     */
    @NotBlank(message = "活动标题不能为空")
    @ApiModelProperty(name = "title", value = "活动标题", position = 30)
    private String title;


    /**
     * 活动封面
     */
    @NotBlank(message = "活动封面不能为空")
    @ApiModelProperty(name = "pic", value = "活动封面", position = 50)
    private String pic;

    /**
     * 活动地址
     */
    @NotBlank(message = "活动地址不能为空")
    @ApiModelProperty(name = "address", value = "活动地址", position = 60)
    private String address;

    /**
     * 入馆须知
     */
//    @NotBlank(message = "入馆须知不能为空")
    @ApiModelProperty(name = "visitorInformation", value = "入馆须知", position = 80)
    private String visitorInformation;

    /**
     * 注意事项
     */
//    @NotBlank(message = "注意事项不能为空")
    @ApiModelProperty(name = "notice", value = "注意事项", position = 90)
    private String notice;

    /**
     * 最小购买数量
     */
    @NotNull(message = "最小购买数量不能为空")
    @ApiModelProperty(name = "minimumBuyNumber", value = "最小购买数量", position = 100)
    private Integer minimumBuyNumber;

    /**
     * 限制数量
     */
    @NotNull(message = "限制数量不能为空")
    @ApiModelProperty(name = "limit", value = "限制数量", position = 100)
    private Integer limit;

    /**
     * 最大购买数量
     */
    @NotNull(message = "最大购买数量不能为空")
    @ApiModelProperty(name = "maximumBuyNumber", value = "最大购买数量", position = 110)
    private Integer maximumBuyNumber;




    /**
     * 活动开始时间
     */
    @NotBlank(message = "活动开始时间不能为空")
    @ApiModelProperty(name = "startTime", value = "活动开始时间", required = true, position = 130)
    private String startTime;

    /**
     * 活动结束时间
     */
    @NotBlank(message = "活动结束时间不能为空")
    @ApiModelProperty(name = "endTime", value = "活动结束时间", required = true, position = 140)
    private String endTime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 220)
    private Integer orderNo;


    /**
     *  每日预约上限
     */
    @ApiModelProperty(name = "dayLimit", value = "每日预约上限", position = 100)
    private Integer dayLimit;

    /**
     * 票档列表
     */
    @ApiModelProperty(name = "activityTicketLineList", value = "票档列表", required = true, position = 220)
    private List<ActivityTicketLineCreateReq> activityTicketLineList;

    /**
     * 省份
     */
    @ApiModelProperty(name = "province", value = "省份", required = true, position = 60)
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(name = "city", value = "城市", required = true, position = 80)
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", required = true, position = 100)
    @NotBlank(message = "区/县不能为空")
    private String county;


    /**
     * 活动地名
     */
    @ApiModelProperty(name = "addressLocation", value = "活动地名", position = 60)
    private String addressLocation;


    /**
     * 售票开始时间
     */
    @NotBlank(message = "售票开始时间")
    @ApiModelProperty(name = "buyStartTime", value = "售票开始时间", required = true, position = 130)
    private String buyStartTime;

    /**
     * 售票结束时间
     */
    @NotBlank(message = "售票结束时间")
    @ApiModelProperty(name = "buyEndTime", value = "售票结束时间", required = true, position = 130)
    private String buyEndTime;

}
