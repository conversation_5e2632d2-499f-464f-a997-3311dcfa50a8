package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 新增活动预约单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@Data
public class ActivityOrderCreateReq {

    /**
     * 票档id
     */
    @NotNull(message = "票档id不能为空")
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;


    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Length(min = 1, max = 20, message = "姓名错误")
    @ApiModelProperty(name = "name", value = "姓名", position = 80)
    private String name;

    /**
     * 联系方式
     */
    @NotBlank(message = "联系方式不能为空")
    @Length(min = 1, max = 64, message = "联系方式错误")
    @ApiModelProperty(name = "contact", value = "联系方式", position = 90)
    private String contact;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @ApiModelProperty(name = "number", value = "数量", position = 110)
    @Min(value = 1, message = "数量不能小于1")
    private Integer number;

    /**
     * 预约日期
     */
    @NotBlank(message = "预约日期不能为空")
    @ApiModelProperty(name = "date", value = "预约日期", required = true, position = 130)
    private String date;


}
