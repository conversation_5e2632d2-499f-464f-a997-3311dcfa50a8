package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ActivityOrderModifyDateReq {
    @NotNull(message = "序号不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private  Long id;


    @NotBlank(message = "预约日期不能为空")
    @ApiModelProperty(name = "date", value = "预约日期", position = 20)
    private  String date;
}
