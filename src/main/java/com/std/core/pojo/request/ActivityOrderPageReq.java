package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询活动预约单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@Data
public class ActivityOrderPageReq extends BasePageReq {

    /**
     * 订单id
     */
    @ApiModelProperty(name = "id", value = "订单id", position = 10)
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 20)
    private String orderNumber;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 30)
    private Long userId;

    /**
     * 用户关键信息（手机号/身份证/姓名）
     */
    @ApiModelProperty(name = "keywords", value = "用户关键信息（手机号/身份证/姓名）", position = 31)
    private String keywords;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 40)
    private Long activityId;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;

    /**
     * 票档名称
     */
    @ApiModelProperty(name = "ticketLineName", value = "票档名称", position = 60)
    private String ticketLineName;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "name", value = "姓名", position = 80)
    private String name;
    /**
     * 是否修改 0:未修改 1:已经修改
     */
    @ApiModelProperty(name = "isModify", value = "是否修改 0:未修改 1:已经修改", position = 120)
    private String isModify;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "date", value = "预约日期", position = 130)
    private String date;

    /**
     * 修改前日期
     */
    @ApiModelProperty(name = "oldDate", value = "修改前日期", position = 140)
    private Date oldDate;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 160)
    private String payType;

    /**
     * 状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}", position = 170)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 171)
    private List<String> statusList;

}
