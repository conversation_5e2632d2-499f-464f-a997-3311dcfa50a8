package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增活动预约统计
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
@Data
public class ActivityOrderStatisticsCreateReq {

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "orderDate", value = "预约日期", required = true, position = 30)
    @NotBlank(message = "预约日期不能为空")
    private String orderDate;

    /**
     * 预约门票总数(包含未支付完成的)
     */
    @ApiModelProperty(name = "totalTickets", value = "预约门票总数(包含未支付完成的)", position = 40)
    private Integer totalTickets;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", required = true, position = 50)
    private String updateDatetime;

}
