package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询活动预约统计
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
@Data
public class ActivityOrderStatisticsPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "orderDate", value = "预约日期", position = 30)
    private Date orderDate;

    /**
     * 预约门票总数(包含未支付完成的)
     */
    @ApiModelProperty(name = "totalTickets", value = "预约门票总数(包含未支付完成的)", position = 40)
    private Integer totalTickets;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 50)
    private Date updateDatetime;

}
