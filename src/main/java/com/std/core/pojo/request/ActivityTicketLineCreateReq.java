package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增票档
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
@Data
public class ActivityTicketLineCreateReq {
    /**
     * 活动序号
     */
    @ApiModelProperty(name = "Id", value = "活动序号", position = 20)
    private Long Id;

    /**
     * 活动序号
     */
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;

    /**
     * 票档名称
     */
    @NotBlank(message = "票档名称不能为空")
    @ApiModelProperty(name = "name", value = "票档名称", position = 30)
    private String name;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    @ApiModelProperty(name = "price", value = "价格", position = 40)
    private BigDecimal price;

    /**
     * 总量
     */
    @NotNull(message = "总量不能为空")
    @ApiModelProperty(name = "number", value = "总量", position = 50)
    private Integer number;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 140)
    private Integer orderNo;

}
