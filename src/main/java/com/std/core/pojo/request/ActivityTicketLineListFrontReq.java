package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 列表查询票档
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
@Data
public class ActivityTicketLineListFrontReq extends BaseListReq {

    /**
     * 活动序号
     */
    @NotNull(message = "活动序号不能为空")
    @ApiModelProperty(name = "activityId", value = "活动序号", position = 20)
    private Long activityId;


}
