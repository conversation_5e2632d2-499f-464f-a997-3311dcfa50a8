package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 新增收货地址
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@Data
public class AddressCreateReq {

    /**
     * 收货人姓名
     */
    @ApiModelProperty(name = "name", value = "收货人姓名", required = true, position = 30)
    @NotBlank(message = "收货人姓名不能为空")
    private String name;

    /**
     * 省份ID对应area表中的id
     */
    @ApiModelProperty(name = "province", value = "省份", required = true, position = 60)
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 城市ID对应area表中的id
     */
    @ApiModelProperty(name = "city", value = "城市", required = true, position = 80)
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区/县ID对应area表中的id
     */
    @ApiModelProperty(name = "county", value = "区/县", required = true, position = 100)
    @NotBlank(message = "区/县不能为空")
    private String county;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", required = true, position = 110)
    @NotBlank(message = "详细地址不能为空")
    @Length(max = 50, message = "详细地址不能超过50个字符")
    private String address;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "phone", value = "联系方式", required = true, position = 120)
    @NotBlank(message = "联系方式不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

}
