package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改收货地址
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@Data
public class AddressModifyReq {

    /**
     * 自增主键
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(name = "name", value = "收货人姓名", position = 30)
    private String name;

    /**
     * 是否默认地址1是，0否,一个用户只能有一个默认地址
     */
    @ApiModelProperty(name = "isDefault", value = "是否默认地址1是，0否,一个用户只能有一个默认地址", position = 40)
    private String isDefault;


    /**
     * 省份ID对应area表中的id
     */
    @ApiModelProperty(name = "province", value = "省份", required = true, position = 60)
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 城市ID对应area表中的id
     */
    @ApiModelProperty(name = "city", value = "城市", required = true, position = 80)
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区/县ID对应area表中的id
     */
    @ApiModelProperty(name = "county", value = "区/县", required = true, position = 100)
    @NotBlank(message = "区/县不能为空")
    private String county;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 110)
    private String address;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "phone", value = "联系方式", position = 120)
    private String phone;

}
