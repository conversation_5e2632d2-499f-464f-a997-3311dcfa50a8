package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class AlipayRefundReq {

    @NotNull
    @ApiModelProperty(name = "payGroup", value = "支付组")
    private BigDecimal payGroup;

    /**
     *
     */
    @NotNull
    @ApiModelProperty(name = "refundAmount", value = "退款金额")
    private BigDecimal refundAmount;

}
