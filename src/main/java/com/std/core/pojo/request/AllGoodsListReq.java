package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 列表查询所有营销商品
 *
 * <AUTHOR> zhou<PERSON>
 * @since : 2020-08-11 19:43
 */
@Data
public class AllGoodsListReq extends BasePageReq {

    @ApiModelProperty(name = "id", value = "小分类id", position = 10)
    @NotNull(message = "小分类id不能为空")
    private Long category;

}
