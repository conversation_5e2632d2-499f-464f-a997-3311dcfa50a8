package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 分配菜单接口
 * @Author: Silver
 * @CreateDate: 2019-01-07 14:41
 * @Version: 1.0
 */
@Data
public class AllotActionReq {

    /**
     * 菜单编号
     */
    @ApiModelProperty(name = "id", value = "菜单编号", required = true)
    @NotNull(message = "菜单编号不能为空")
    private Long id;

    /**
     * 资源编号
     */
    @ApiModelProperty(name = "actionIdList", value = "资源编号")
    private List<Long> actionIdList;
}
