package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:27
 */
@Data
public class AllotRolePermissionReq {

    /**
     * 角色编号
     */
    @ApiModelProperty(name = "roleId", value = "角色编号", required = true)
    @NotNull(message = "角色编号不能为空")
    private Long roleId;

    /**
     * 菜单编号列表
     */
    @ApiModelProperty(name = "menuIdList", value = "菜单编号列表", required = true)
    @NotNull(message = "菜单编号不能为空")
    private List<Long> menuIdList;

}
