package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:33
 */
@Data
public class AllotRolesReq {

    /**
     * 组编号
     */
    @ApiModelProperty(name = "groupId", value = "组编号", required = true)
    @NotNull(message = "组编号不能为空")
    private Long groupId;

    /**
     * 角色编号列表
     */
    @ApiModelProperty(name = "roleIdList", value = "角色编号列表")
    private List<Long> roleIdList;

}
