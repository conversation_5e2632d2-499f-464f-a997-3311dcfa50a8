package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 新增地区表
*
* <AUTHOR> zhoudong
* @since : 2020-08-10 17:06
*/
@Data
public class AreaCreateReq {

      /**
      * 地区名称
      */
      @ApiModelProperty(name = "name", value = "地区名称", required = true, position = 20)
      @NotBlank(message = "地区名称不能为空")
      private String name;

      /**
      * 地区父ID
      */
      @ApiModelProperty(name = "pid", value = "地区父ID", required = true, position = 30)
      @NotNull(message = "地区父ID不能为空")
      private Long pid;

      /**
      * 排序
      */
      @ApiModelProperty(name = "sort", value = "排序", required = true, position = 40)
      @NotNull(message = "排序不能为空")
      private Integer sort;

      /**
      * 地区深度，从1开始
      */
      @ApiModelProperty(name = "deep", value = "地区深度，从1开始", required = true, position = 50)
      @NotNull(message = "地区深度，从1开始不能为空")
      private Integer deep;

      /**
      * 
      */
      @ApiModelProperty(name = "shortName", value = "", required = true, position = 60)
      @NotBlank(message = "不能为空")
      private String shortName;

      /**
      * 是否启用（1：是；0：否）
      */
      @ApiModelProperty(name = "enabled", value = "是否启用（1：是；0：否）", required = true, position = 70)
      @NotNull(message = "是否启用（1：是；0：否）不能为空")
      private Integer enabled;


}
