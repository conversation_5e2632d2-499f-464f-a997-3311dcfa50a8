package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询地区表
 *
 * <AUTHOR> zhoudong
 * @since : 2020-08-10 17:06
 */
@Data
public class AreaListReq extends BaseListReq {


    /**
     * 地区父ID
     */
    @ApiModelProperty(name = "pid", value = "地区父ID", position = 30)
    private Long pid;


    /**
     * 地区深度，从2开始
     */
    @ApiModelProperty(name = "deep", value = "地区深度，,2省，3市，4区", position = 50)
    private Integer deep;


}
