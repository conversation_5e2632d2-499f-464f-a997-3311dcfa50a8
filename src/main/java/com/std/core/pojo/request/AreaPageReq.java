package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询地区表
 *
 * <AUTHOR> zhoudong
 * @since : 2020-08-10 17:06
 */
@Data
public class AreaPageReq extends BasePageReq {

    /**
     * 索引ID
     */
    @ApiModelProperty(name = "id", value = "索引ID", position = 10)
    private Long id;

    /**
     * 地区名称
     */
    @ApiModelProperty(name = "name", value = "地区名称", position = 20)
    private String name;

    /**
     * 地区父ID
     */
    @ApiModelProperty(name = "pid", value = "地区父ID", position = 30)
    private Long pid;


    /**
     * 地区深度，从1开始
     */
    @ApiModelProperty(name = "deep", value = "地区深度，从1开始", position = 50)
    private Integer deep;


}
