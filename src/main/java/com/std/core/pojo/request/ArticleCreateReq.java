package com.std.core.pojo.request;

import com.std.common.base.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 新增文章
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@Data
public class ArticleCreateReq extends BaseReq {

    /**
     * 类型编号
     */
    @ApiModelProperty(name = "typeId", value = "文章类型编号", required = true, position = 10)
    @Min(0)
    private Long typeId;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 11)
    private String pic;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", required = true, position = 20)
    @NotBlank
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", required = true, position = 30)
    @NotBlank
    private String content;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型", required = true, position = 30)
    @NotBlank
    private String contentType;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 40)
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 50)
    private String remark;


}
