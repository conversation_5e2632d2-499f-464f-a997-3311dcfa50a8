package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询文章
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@Data
public class ArticleListReq extends BaseListReq {

    /**
     * 类型编号
     */
    @ApiModelProperty(name = "typeId", value = "类型编号")
    private Long typeId;

    /**
     * 文章类型
     */
    @ApiModelProperty(name = "type", value = "文章类型")
    private String type;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）")
    private String status;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题")
    private String title;

}
