package com.std.core.pojo.request;

import com.std.common.base.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改文章
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@Data
public class ArticleModifyReq extends BaseReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号", required = true)
    private Long id;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片")
    private String pic;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容")
    private String content;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型")
    private String contentType;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号")
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    @ApiModelProperty(name = "typeId", value = "文章类型ID")
    private Long typeId;


}
