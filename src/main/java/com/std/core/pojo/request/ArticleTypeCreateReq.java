package com.std.core.pojo.request;

import com.std.common.base.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 新增文章类型
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Data
public class ArticleTypeCreateReq extends BaseReq {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true, position = 10)
    @NotBlank
    private String name;

    @ApiModelProperty(name = "icon", value = "图标", required = true, position = 15)
    private String icon;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号，次序", position = 20)
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 30)
    private String remark;
    /**
     * 文章分类的位置
     */
    @ApiModelProperty(name = "location", value = "文章分类的位置")
    private String location;
}
