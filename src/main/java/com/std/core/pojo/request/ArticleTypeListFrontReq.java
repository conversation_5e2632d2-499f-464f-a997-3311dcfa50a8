package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询活动类型
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:53
 */
@Data
public class ArticleTypeListFrontReq extends BaseListReq {

    /**
     * 
     */
    @ApiModelProperty(name = "id", value = "", position = 10)
    private Long id;

    /**
     * 分类名称
     */
    @ApiModelProperty(name = "name", value = "分类名称", position = 20)
    private String name;

    /**
     * 图标
     */
    @ApiModelProperty(name = "icon", value = "图标", position = 30)
    private String icon;

    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）", position = 40)
    private String status;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 50)
    private Integer orderNo;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 70)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 90)
    private String remark;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location", value = "位置", position = 100)
    private String location;

}
