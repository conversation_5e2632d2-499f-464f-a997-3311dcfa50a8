package com.std.core.pojo.request;

import com.std.common.base.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 修改文章类型
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Data
public class ArticleTypeModifyReq extends BaseReq {

    /**
     *
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", required = true, position = 10)
    private Long id;

    @ApiModelProperty(name = "icon", value = "图标", required = true, position = 15)
    private String icon;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 20)
    private String name;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 30)
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 40)
    private String remark;

    @ApiModelProperty(name = "location", value = "文章分类的位置")
    private String location;

}
