package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询文章类型
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Data
public class ArticleTypePageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;
    /**
     * 状态（0下架 1上架）
     */
    @ApiModelProperty(name = "status", value = "状态（0下架 1上架）")
    private String status;

    @ApiModelProperty(name = "location", value = "位置")
    private String location;


}
