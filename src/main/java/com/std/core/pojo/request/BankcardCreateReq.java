package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增银行卡
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Data
public class BankcardCreateReq {

    /**
     * 类型（1C端 2厂商）
     */
    @ApiModelProperty(name = "type", value = "类型（C端 SYS系统 CLINIC诊所）", required = true, position = 10)
    @NotBlank
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号(诊所编号)", required = true, position = 20)
    @NotNull
    private Long userId;

    /**
     * 渠道银行编号
     */
    @NotNull
    @ApiModelProperty(name = "channelBankId", value = "渠道银行编号", required = true, position = 30)
    private Long channelBankId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "bankUserName", value = "银行卡户名", required = true, position = 40)
    @NotBlank
    private String bankUserName;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(name = "subbranch", value = "开户支行名称", required = true, position = 50)
    @NotBlank
    private String subbranch;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "bankcardNumber", value = "银行卡号", required = true, position = 60)
    @NotBlank
    private String bankcardNumber;

    /**
     * 是否默认
     */
    @ApiModelProperty(name = "defaultFlag", value = "是否默认（0否 1是）", position = 70)
    private String defaultFlag;




    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;




    /**
     * 状态（0失效 1生效）
     */
    @ApiModelProperty(name = "status", value = "状态（0失效 1生效）")
    private String status;




    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
