package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询银行卡
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Data
public class BankcardListReq extends BaseListReq {

    /**
     * 类型（1C端 2厂商）
     */
    @ApiModelProperty(name = "type", value = "类型（C端 SYS系统 CLINIC诊所）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号(诊所编号)")
    private Long userId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "bankUserName", value = "银行卡户名")
    private String bankUserName;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * 状态（0失效 1生效）
     */
    @ApiModelProperty(name = "status", value = "状态（0失效 1生效）")
    private String status;


}
