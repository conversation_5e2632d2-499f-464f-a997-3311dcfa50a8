package com.std.core.pojo.request;

import com.std.core.pojo.domain.ChannelBank;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 修改银行卡
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Data
public class BankcardModifyReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 渠道银行编号
     */
    @ApiModelProperty(name = "channelBankId", value = "渠道银行编号", position = 30)
    private Long channelBankId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "bankUserName", value = "银行卡户名", position = 40)
    private String bankUserName;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(name = "subbranch", value = "开户支行名称", position = 50)
    private String subbranch;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "bankcardNumber", value = "银行卡号", position = 60)
    private String bankcardNumber;

    /**
     * 是否默认
     */
    @ApiModelProperty(name = "defaultFlag", value = "是否默认（0否 1是）", position = 70)
    private String defaultFlag;


    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;




    /**
     * 状态（0失效 1生效）
     */
    @ApiModelProperty(name = "status", value = "状态（0失效 1生效）")
    private String status;




    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    //DB Properties
//
//    /**
//     * 渠道银行
//     */
//    @ApiModelProperty(name = "channelBank", value = "渠道银行")
//    private ChannelBank channelBank;
}
