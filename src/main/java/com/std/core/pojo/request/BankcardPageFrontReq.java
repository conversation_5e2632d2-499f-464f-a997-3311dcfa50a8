package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询银行卡
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Data
public class BankcardPageFrontReq extends BasePageReq {

    /**
     * 银行卡户名
     */
    @ApiModelProperty(name = "bankUserName", value = "银行卡户名")
    private String bankUserName;

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * 状态（0失效 1生效）
     */
    @ApiModelProperty(name = "status", value = "状态（0失效 1生效）")
    private String status;


}
