package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 15:53
 */
@Data
public class BankcardSetDefaultReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 1)
    private Long id;


}
