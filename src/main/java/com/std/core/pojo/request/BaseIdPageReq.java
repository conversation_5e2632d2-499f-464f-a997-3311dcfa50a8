package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> ycj
 * @since : 2020/9/15 18:32
 */
@Data
public class BaseIdPageReq extends BasePageReq {
    /**
     * 序号
     */
    @ApiModelProperty(name = "name",value = "序号",position = 10)
    @NotNull(message = "序号不能为空")
    private Long id;
}
