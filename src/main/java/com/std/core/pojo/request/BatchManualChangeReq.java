package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class BatchManualChangeReq {

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "mobile", value = "用户手机号",required = true, position = 10)
    @NotBlank(message = "用户手机号不能为空")
    private String mobile;
    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种",required = true, position = 20)
    @NotBlank(message = "币种不能为空")
    private String currency;
    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型 0：支出，1：收入",required = true, position = 30)
    @NotBlank(message = "类型不能为空")
    private String type;
    /**
     * 变动金额
     */
    @ApiModelProperty(name = "amount", value = "变动金额",required = true, position = 40)
    @NotNull(message = "变动金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(name = "remark", value = "备注", position = 50)
//    @NotBlank(message = "备注不能为空")
    private String remark;
}
