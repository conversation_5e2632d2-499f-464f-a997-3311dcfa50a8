package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class BatchUpDownReq {
    @NotEmpty
    @ApiModelProperty(name = "idList", value = "序号列表", position = 20)
    private List<Long> idList;

    @ApiModelProperty(name = "status", value = "状态", position = 30)
    private String status;
}
