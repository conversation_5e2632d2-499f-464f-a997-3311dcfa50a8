package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增预约单大订单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
@Data
public class BigActivityOrderCreateReq {

    /**
     * 流水号
     */
    @ApiModelProperty(name = "serialNumber", value = "流水号", position = 20)
    private String serialNumber;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", required = true, position = 30)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 40)
    private Long activityId;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;

    /**
     * 总价
     */
    @ApiModelProperty(name = "totalPrice", value = "总价", position = 60)
    private BigDecimal totalPrice;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 70)
    private Integer number;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 80)
    private String payType;

    /**
     * 状态 {0:待支付,1:支付失败,2:支付成功}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待支付,1:支付失败,2:支付成功}", position = 90)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 100)
    private String createDatetime;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", required = true, position = 110)
    private String payDatetime;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", required = true, position = 140)
    private String updateDatetime;

}
