package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 新增文章类型
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Data
public class CNavigateBatchUpDownReq {

    /**
     * 编号列表
     */
    @NotEmpty
    @ApiModelProperty(name = "ids", value = "编号列表", position = 10, required = true)
    private Long[] ids;

    /**
     * 操作（0下架 1上架）
     */
    @NotBlank
    @ApiModelProperty(name = "result", value = "操作（2下架 1上架）", position = 20, required = true)
    private String result;

}
