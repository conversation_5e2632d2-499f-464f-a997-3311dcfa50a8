package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class CUserLoginReq {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号必填")
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 30)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    /**
     * 手机验证码
     */
    @NotBlank(message = "手机验证码必填")
    @ApiModelProperty(name = "smsCaptcha", value = "手机验证码", required = true, position = 40)
    private String smsCaptcha;

}
