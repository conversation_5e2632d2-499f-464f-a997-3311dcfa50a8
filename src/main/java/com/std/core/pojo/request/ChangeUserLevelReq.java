package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/9/15 12:31
 */
@Data
public class ChangeUserLevelReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", required = true, position = 10)
    @NotNull(message = "用户ID不能为空")
    private Long userId;


    @ApiModelProperty(name = "level", value = "用户等级", required = true, position = 20)
    @NotNull(message = "用户等级不能为空")
    private String level;

}
