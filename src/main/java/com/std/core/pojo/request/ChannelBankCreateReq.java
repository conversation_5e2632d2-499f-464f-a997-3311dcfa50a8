package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 新增渠道银行
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Data
public class ChannelBankCreateReq {

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号", required = true, position = 10)
    @NotBlank
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称", required = true, position = 20)
    @NotBlank
    private String bankName;

    /**
     * Logo
     */
    @ApiModelProperty(name = "logo", value = "Logo", position = 30)
    private String logo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 40)
    private String remark;

}
