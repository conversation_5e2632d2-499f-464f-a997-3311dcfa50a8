package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改渠道银行
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Data
public class ChannelBankModifyReq {

    /**
     * 编号（自增长）
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号（自增长）")
    private Long id;


    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;

    /**
     * Logo
     */
    @ApiModelProperty(name = "logo", value = "Logo")
    private String logo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
