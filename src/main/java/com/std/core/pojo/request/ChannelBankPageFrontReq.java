package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询渠道银行
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Data
public class ChannelBankPageFrontReq extends BasePageReq {

    /**
     * 银行编号
     */
    @ApiModelProperty(name = "bankCode", value = "银行编号")
    private String bankCode;

    /**
     * 银行名称
     */
    @ApiModelProperty(name = "bankName", value = "银行名称")
    private String bankName;


}
