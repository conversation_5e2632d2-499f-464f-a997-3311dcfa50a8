package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class ChargeApproveReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 审核结果
     */
    @NotBlank
    @ApiModelProperty(name = "approveResult", value = "审核结果(0不通过 1通过)", required = true, position = 11)
    private String approveResult;

    /**
     * 支付回录人
     */
    @NotBlank
    @ApiModelProperty(name = "payUser", value = "支付回录人", required = true, position = 20)
    private String payUser;

    /**
     * 支付回录说明
     */
    @ApiModelProperty(name = "payNote", value = "支付回录说明", position = 30)
    private String payNote;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号", position = 40)
    private String channelOrder;

}
