package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改充值订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class ChargeModifyReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 充值金额
     */
    @ApiModelProperty(name = "amount", value = "充值金额")
    private Double amount;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 关联业务类型
     */
    @ApiModelProperty(name = "bizType", value = "关联业务类型")
    private String bizType;

    /**
     * 关联业务备注
     */
    @ApiModelProperty(name = "bizNote", value = "关联业务备注")
    private String bizNote;

    /**
     * 关联订单号
     */
    @ApiModelProperty(name = "bizNo", value = "关联订单号")
    private String bizNo;

    /**
     * 状态（1待支付 2支付失败 3支付成功）
     */
    @ApiModelProperty(name = "status", value = "状态（1待支付 2支付失败 3支付成功）")
    private String status;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUser", value = "申请人")
    private String applyUser;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明")
    private String applyNote;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetime", value = "申请时间")
    private String applyDatetime;

    /**
     * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
     */
    @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）")
    private String channelType;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号")
    private String channelOrder;

    /**
     * 支付渠道账号
     */
    @ApiModelProperty(name = "channelAccount", value = "支付渠道账号")
    private String channelAccount;

    /**
     * 支付回录人
     */
    @ApiModelProperty(name = "payUser", value = "支付回录人")
    private String payUser;

    /**
     * 支付回录说明
     */
    @ApiModelProperty(name = "payNote", value = "支付回录说明")
    private String payNote;

    /**
     * 支付回录时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付回录时间")
    private String payDatetime;


}
