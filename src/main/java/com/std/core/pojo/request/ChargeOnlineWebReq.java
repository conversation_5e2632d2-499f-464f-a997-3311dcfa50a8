package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class ChargeOnlineWebReq {

    /**
     * 充值金额
     */
    @NotNull
    @ApiModelProperty(name = "amount", value = "充值金额", required = true, position = 20)
    private BigDecimal amount;

    /**
     * 支付渠道类型（支付宝支付alipay 微信公众号支付wechat）
     */
    @NotBlank
    @ApiModelProperty(name = "channelType", value = "支付宝支付alipay 微信公众号支付wechat）", required = true, position = 30)
    private String channelType;

}
