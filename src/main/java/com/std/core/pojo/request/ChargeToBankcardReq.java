package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 线下充值
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Data
public class ChargeToBankcardReq {

    /**
     * 用户类型（C端用户 CLINIC诊所用户）
     */
    @NotBlank
    @ApiModelProperty(name = "userKind", value = "用户类型（C端用户 CLINIC诊所用户）", required = true, position = 1)
    private String userKind;

    /**
     * 账户编号
     */
    @NotBlank
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    private String accountNumber;

    /**
     * 充值金额
     */
    @NotNull
    @ApiModelProperty(name = "amount", value = "充值金额", required = true, position = 20)
    private BigDecimal amount;

    /**
     * 是否开票
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）", position = 21)
    private String billFlag;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明", position = 30)
    private String applyNote;

    /**
     * 银行卡编号
     */
    @NotNull
    @ApiModelProperty(name = "bankcardId", value = "银行卡编号", required = true, position = 40)
    private Long bankcardId;

}
