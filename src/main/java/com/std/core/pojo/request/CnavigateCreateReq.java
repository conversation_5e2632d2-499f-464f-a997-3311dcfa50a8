package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 新增导航
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Data
public class CnavigateCreateReq {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true, position = 10)
    @NotBlank
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型(banner图：app_banner 开机图：app_start)", required = true, position = 30)
    private String type;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location", value = "位置 0:开机图，1：首页bannner,2:活动页轮播图", position = 31)
    private String location;

    /**
     * 动作
     */
    @ApiModelProperty(name = "action", value = "动作（0不能点击 1跳转链接 2跳转本系统）", position = 32)
    private String action;

    /**
     * 访问Url
     */
    @ApiModelProperty(name = "url", value = "访问Url", required = true, position = 40)
    private String url;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", required = true, position = 50)
    @NotBlank(message = "图片不能为空")
    private String pic;

    /**
     * 分组
     */
    @ApiModelProperty(name = "groupName", value = "分组", position = 90)
    private String groupName;

    /**
     * 相对位置编号
     */
    @ApiModelProperty(name = "orderNo", value = "相对位置编号", position = 100)
    private Integer orderNo;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号", position = 110)
    private Long parentId;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;


}
