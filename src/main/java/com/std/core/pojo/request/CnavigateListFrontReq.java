package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询导航
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Data
public class CnavigateListFrontReq extends BaseListReq {

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型 轮播图：app_banner，开机图：app_start")
    private String type;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location app首页轮播图：1，活动页轮播图：2；", value = "位置")
    private String location;

    /**
     * 分组
     */
    @ApiModelProperty(name = "groupName", value = "分组")
    private String groupName;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

}
