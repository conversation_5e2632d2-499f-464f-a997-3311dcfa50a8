package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改导航
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Data
public class CnavigateModifyReq {

    /**
     *
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "", required = true, position = 1)
    private Long id;
    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 10)
    private String name;


    /**
     * 访问Url
     */
    @ApiModelProperty(name = "url", value = "访问Url", required = true, position = 40)
    private String url;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", required = true, position = 50)
    private String pic;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location", value = "位置", position = 80)
    private String location;

    /**
     * 动作
     */
    @ApiModelProperty(name = "action", value = "动作（0不能点击 1跳转链接 2跳转本系统）", position = 32)
    private String action;

    /**
     * 分组
     */
    @ApiModelProperty(name = "groupName", value = "分组", position = 90)
    private String groupName;

    /**
     * 相对位置编号
     */
    @ApiModelProperty(name = "orderNo", value = "相对位置编号", position = 100)
    private Integer orderNo;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号", position = 110)
    private Long parentId;



}
