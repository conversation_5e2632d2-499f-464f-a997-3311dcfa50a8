package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询导航
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Data
public class CnavigatePageReq extends BasePageReq {

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型")
    private String type;

    /**
     * 状态(1 显示 0 不显示)
     */
    @ApiModelProperty(name = "status", value = "状态(1 显示 0 不显示)")
    private String status;

    /**
     * 位置
     */
    @ApiModelProperty(name = "location", value = "位置")
    private String location;

    /**
     * 分组
     */
    @ApiModelProperty(name = "groupName", value = "分组")
    private String groupName;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 动作
     */
    @ApiModelProperty(name = "action", value = "动作（0不能点击 1跳转链接 2跳转本系统）")
    private String action;

}
