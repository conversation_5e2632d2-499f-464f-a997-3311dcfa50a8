package com.std.core.pojo.request;


import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:28
 */
@Data
public class ConfigCreateReq {

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型", required = true)
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "key", required = true)
    @NotBlank(message = "键不能为空")
    private String key;

    /**
     * value
     */
    @ApiModelProperty(name = "value", value = "value", required = true)
    @NotBlank(message = "值不能为空")
    private String value;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
