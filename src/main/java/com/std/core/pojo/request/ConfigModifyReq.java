package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:33
 */
@Data
public class ConfigModifyReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "id", required = true)
    @NotBlank
    private String id;

    /**
     * value
     */
    @ApiModelProperty(name = "value", value = "value", required = true)
    @NotBlank(message = "值不能为空")
    private String value;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
