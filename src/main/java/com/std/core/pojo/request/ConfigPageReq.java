package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 15:45
 */
@Data
public class ConfigPageReq extends BasePageReq {

    /**
     * 类型
     */
    @ApiModelProperty(name = "type", value = "类型")
    private String type;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "key")
    private String key;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人")
    private String updater;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

}
