package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserAgentPageReq extends BasePageReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 1)
    private Long userId;

    /**
     * 状态
     */
    @ApiModelProperty(name = "agentStatus", value = "状态 dict=user.status", position = 2)
    private String agentStatus;


}
