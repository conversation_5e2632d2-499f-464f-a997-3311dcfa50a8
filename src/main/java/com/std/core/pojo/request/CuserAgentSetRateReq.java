package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/6/6 5:16 下午
 */
@Data
public class CuserAgentSetRateReq {

    @ApiModelProperty(name = "id", value = "C端用户ID", position = 1)
    @NotNull(message = "C端用户ID不能为空")
    private Long id;

    @ApiModelProperty(name = "configMap", value = "配置值 key值如下：pushlishRate=发标折扣，inviteRate1=推荐收益（1代），inviteRate2=推荐收益（2代），chargeNodeRate1=一星节点收益（充值金），taskNodeRate1=一星节点收益（任务金），chargeNodeRate2=二星节点收益（充值金），taskNodeRate2=二星节点收益（任务金），chargeNodeRate3=三星节点收益（充值金），taskNodeRate3=三星节点收益（任务金）", position = 2)
    private Map<String, String> configMap;

}

    
    