package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* 新增C端用户
*
* <AUTHOR> Leo
* @since : 2020-05-18 20:09
*/
@Data
public class CuserCreateReq {

      /**
      * 用户ID
      */
      @ApiModelProperty(name = "userId", value = "用户ID", required = true, position = 20)
      @NotNull(message = "用户ID不能为空")
      private Long userId;

      /**
      * 信用分
      */
      @ApiModelProperty(name = "creditScore", value = "信用分", required = true, position = 30)
      @NotNull(message = "信用分不能为空")
      private Double creditScore;

      /**
      * 会员身份
      */
      @ApiModelProperty(name = "memberFlag", value = "会员身份", required = true, position = 40)
      @NotBlank(message = "会员身份不能为空")
      private String memberFlag;

      /**
      * 套餐身份
      */
      @ApiModelProperty(name = "vipFlag", value = "套餐身份", required = true, position = 50)
      @NotBlank(message = "套餐身份不能为空")
      private String vipFlag;

      /**
      * 状态
      */
      @ApiModelProperty(name = "level", value = "状态", required = true, position = 60)
      @NotBlank(message = "状态不能为空")
      private String level;

      /**
      * 合作状态
      */
      @ApiModelProperty(name = "agentStatus", value = "合作状态", required = true, position = 70)
      @NotBlank(message = "合作状态不能为空")
      private String agentStatus;

      /**
      * 加盟时间
      */
      @ApiModelProperty(name = "agentJoinDatetime", value = "加盟时间", required = true, position = 80)
      @NotBlank(message = "加盟时间不能为空")
      private String agentJoinDatetime;

      /**
      * 解除时间
      */
      @ApiModelProperty(name = "agentQuitDatetime", value = "解除时间", required = true, position = 90)
      @NotBlank(message = "解除时间不能为空")
      private String agentQuitDatetime;

      /**
      * 创建时间
      */
      @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 100)
      @NotBlank(message = "创建时间不能为空")
      private String createDatetime;


}
