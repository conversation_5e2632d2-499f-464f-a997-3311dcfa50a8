package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 列表查询C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserListReq extends BaseListReq {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 20)
    private Long userId;

    /**
     * 信用分
     */
    @ApiModelProperty(name = "creditScore", value = "信用分", position = 30)
    private Double creditScore;

    /**
     * 会员身份
     */
    @ApiModelProperty(name = "memberFlag", value = "会员身份", position = 40)
    private String memberFlag;

    /**
     * 套餐身份
     */
    @ApiModelProperty(name = "vipFlag", value = "套餐身份", position = 50)
    private String vipFlag;

    /**
     * 状态
     */
    @ApiModelProperty(name = "level", value = "状态", position = 60)
    private String level;

    /**
     * 合作状态
     */
    @ApiModelProperty(name = "agentStatus", value = "合作状态", position = 70)
    private String agentStatus;

    /**
     * 加盟时间
     */
    @ApiModelProperty(name = "agentJoinDatetime", value = "加盟时间", position = 80)
    private String agentJoinDatetime;

    /**
     * 解除时间
     */
    @ApiModelProperty(name = "agentQuitDatetime", value = "解除时间", position = 90)
    private String agentQuitDatetime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 恢复时间
     */
    @ApiModelProperty(name = "recoveryTime", value = "恢复时间", position = 110)
    private Date recoveryTime;

    /**
     * 差评扣减状态状态
     */
    @ApiModelProperty(name = "negationCommentDecreaseStatus", value = "状态", position = 120)
    private String negationCommentDecreaseStatus;

}
