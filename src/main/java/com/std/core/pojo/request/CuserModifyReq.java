package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 修改C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserModifyReq {

    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 20)
    private Long userId;

    /**
     * 信用分
     */
    @ApiModelProperty(name = "creditScore", value = "信用分", position = 30)
    private Double creditScore;

    /**
     * 会员身份
     */
    @ApiModelProperty(name = "memberFlag", value = "会员身份", position = 40)
    private String memberFlag;

    /**
     * 套餐身份
     */
    @ApiModelProperty(name = "vipFlag", value = "套餐身份", position = 50)
    private String vipFlag;

    /**
     * 状态
     */
    @ApiModelProperty(name = "level", value = "状态", position = 60)
    private String level;

    /**
     * 合作状态
     */
    @ApiModelProperty(name = "agentStatus", value = "合作状态", position = 70)
    private String agentStatus;

    /**
     * 加盟时间
     */
    @ApiModelProperty(name = "agentJoinDatetime", value = "加盟时间", position = 80)
    private String agentJoinDatetime;

    /**
     * 解除时间
     */
    @ApiModelProperty(name = "agentQuitDatetime", value = "解除时间", position = 90)
    private String agentQuitDatetime;

    /**
     * 实名认证状态
     */
    @ApiModelProperty(name = "authenticationStatus", value = "实名认证状态 -1:认证不通过，0：未认证，1：认证通过，2：认证中")
    private String authenticationStatus;
    /**
     * 愿力值
     */
    @ApiModelProperty(name = "willingValue", value = "愿力值", position = 30)
    private BigDecimal willingValue;

    @ApiModelProperty(name = "openid", value = "微信openid", position = 30)
    private String openid;

}
