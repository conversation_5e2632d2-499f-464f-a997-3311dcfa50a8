package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserPageReq extends BasePageReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 1)
    private Long userId;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态 dict=user.status", position = 2)
    private String status;

    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人", position = 3)
    private Long userReferee;
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName", value = "用户名称", position = 25)
    private String userName;

    /**
     * 用户名称
     */
    @ApiModelProperty(name = "level", value = "用户等级", position = 30)
    private String level;

    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;
    /**
     * 姓名
     */
    @ApiModelProperty(name = "realName", value = "姓名")
    private String realName;

    @ApiModelProperty(name = "keywords", value = "模糊查询关键字")
    private String keywords;
    private String registerTimeStart;
    private String registerTimeEnd;
}
