package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 分页查询C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserRecommendReq {

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 1)
    private Long userId;

    /**
     * 推荐人
     */
    @ApiModelProperty(name = "userReferee", value = "推荐人", position = 3)
    private Long userReferee;

    @ApiModelProperty(name = "batchNo", value = "批次号", position = 5)
    @NotBlank(message = "批次不能为空")
    private String batchNo;

}
