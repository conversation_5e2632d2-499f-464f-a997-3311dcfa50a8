package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增每日收益
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryCreateReq {

    /**
     * 活动收益
     */
    @ApiModelProperty(name = "activityAmount", value = "活动收益", position = 20)
    private BigDecimal activityAmount;

    /**
     * 商品收益
     */
    @ApiModelProperty(name = "goodsAmount", value = "商品收益", position = 30)
    private BigDecimal goodsAmount;

    /**
     * 订单数
     */
    @ApiModelProperty(name = "orderCount", value = "订单数", position = 40)
    private Integer orderCount;

    /**
     * 收益日期
     */
    @ApiModelProperty(name = "incomeDate", value = "收益日期", required = true, position = 50)
    private String incomeDate;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 60)
    private String createDatetime;

}
