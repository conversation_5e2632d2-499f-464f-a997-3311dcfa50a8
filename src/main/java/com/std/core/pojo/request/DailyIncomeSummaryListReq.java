package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 列表查询每日收益
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryListReq extends BaseListReq {


    @ApiModelProperty(value = "开始时间", example = "2025-04-01")
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-04-21")
    private String endTime;

    @ApiModelProperty(value = "分组周期：day / week / month", example = "week")
    private String period;

    @ApiModelProperty(value = "0：活动 1：商品")
    @NotBlank
    private String type;
}
