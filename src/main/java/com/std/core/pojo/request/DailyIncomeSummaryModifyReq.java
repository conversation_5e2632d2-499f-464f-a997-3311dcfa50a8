package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改每日收益
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动收益
     */
    @ApiModelProperty(name = "activityAmount", value = "活动收益", position = 20)
    private BigDecimal activityAmount;

    /**
     * 商品收益
     */
    @ApiModelProperty(name = "goodsAmount", value = "商品收益", position = 30)
    private BigDecimal goodsAmount;

    /**
     * 订单数
     */
    @ApiModelProperty(name = "orderCount", value = "订单数", position = 40)
    private Integer orderCount;

    /**
     * 收益日期
     */
    @ApiModelProperty(name = "incomeDate", value = "收益日期", position = 50)
    private String incomeDate;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private String createDatetime;

}
