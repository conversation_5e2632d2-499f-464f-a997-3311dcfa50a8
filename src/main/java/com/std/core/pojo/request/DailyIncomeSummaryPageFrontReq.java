package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询每日收益
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动收益
     */
    @ApiModelProperty(name = "activityAmount", value = "活动收益", position = 20)
    private BigDecimal activityAmount;

    /**
     * 商品收益
     */
    @ApiModelProperty(name = "goodsAmount", value = "商品收益", position = 30)
    private BigDecimal goodsAmount;

    /**
     * 订单数
     */
    @ApiModelProperty(name = "orderCount", value = "订单数", position = 40)
    private Integer orderCount;

    /**
     * 收益日期
     */
    @ApiModelProperty(name = "incomeDate", value = "收益日期", position = 50)
    private Date incomeDate;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

}
