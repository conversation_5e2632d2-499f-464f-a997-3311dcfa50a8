package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 列表查询每日收益
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryTotalReq {


    @ApiModelProperty(value = "开始时间", example = "2025-04-01")
//    @NotBlank
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-04-21")
//    @NotBlank
    private String endTime;

}
