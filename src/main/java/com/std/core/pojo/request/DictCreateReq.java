package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-17 14:02
 */
@Data
public class DictCreateReq {

    /** 父亲key */
    @ApiModelProperty(name = "parentKey", required = true, value = "父亲key")
    private String parentKey;

    /** key */
    @ApiModelProperty(name = "key", required = true, value = "键")
    private String key;

    /** 值 */
    @ApiModelProperty(name = "value", required = true, value = "值")
    private String value;

    /** 序号 */
    @ApiModelProperty(name = "orderNo", required = true, value = "序号")
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
