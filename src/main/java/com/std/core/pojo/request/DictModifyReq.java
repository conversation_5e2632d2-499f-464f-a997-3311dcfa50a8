package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-17 14:02
 */
@Data
public class DictModifyReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号")
    private Integer id;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "键")
    private String key;

    /**
     * 值
     */
    @ApiModelProperty(name = "value", value = "值")
    private String value;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private Integer orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
