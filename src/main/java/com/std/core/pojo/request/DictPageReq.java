package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-17 14:02
 */
@Data
public class DictPageReq extends BasePageReq {

    /**
     * 类型（0父类 1子类）
     */
    @ApiModelProperty(name = "type", value = "类型（0父类 1子类）")
    private String type;

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKey", value = "父亲key")
    private String parentKey;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "key")
    private String key;

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKeyList", value = "父亲key")
    private List<String> parentKeyList;
}
