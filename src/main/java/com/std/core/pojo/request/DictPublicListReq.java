package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 列表查询数据字典
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:49
 */
@Data
public class DictPublicListReq extends BaseListReq {

    /**
     * 类型（0父类 1子类）
     */
    @ApiModelProperty(name = "type", value = "类型（0父类 1子类）")
    private String type;

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKey", value = "父亲key")
    @NotBlank
    private String parentKey;

    /**
     * key
     */
    @ApiModelProperty(name = "key", value = "key")
    private String key;

    /**
     * 父亲key
     */
    @ApiModelProperty(name = "parentKeyList", value = "父亲key")
    private List<String> parentKeyList;
}
