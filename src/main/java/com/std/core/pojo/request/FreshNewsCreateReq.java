package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增新鲜事
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
@Data
public class FreshNewsCreateReq {

    /**
     * 封面图片
     */
    @NotBlank(message = "封面图片不能为空")
    @ApiModelProperty(name = "pic", value = "封面图片", position = 20)
    private String pic;

    /**
     * 类型{0:跳转到外部,1:跳转本系统}
     */
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(name = "type", value = "类型{0:跳转到外部,1:跳转本系统}", position = 30)
    private String type;

    /**
     * 类型{0:新鲜事,1:往期回顾}
     */
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(name = "newsType", value = "类型{0:新鲜事,1:往期回顾}", position = 30)
    private String newsType;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    @ApiModelProperty(name = "content", value = "内容", position = 50)
    private String content;

    /**
     * 内容类型
     */
    @NotBlank(message = "内容类型不能为空")
    @ApiModelProperty(name = "contentType", value = "内容类型", position = 50)
    private String contentType;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 140)
    private Integer orderNo;

}
