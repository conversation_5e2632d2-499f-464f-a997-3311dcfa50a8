package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改新鲜事
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
@Data
public class FreshNewsModifyReq {

    @ApiModelProperty(name = "id", value = "序号", position = 20)
    private Long id;

    /**
     * 封面图片
     */
    @NotBlank(message = "封面图片不能为空")
    @ApiModelProperty(name = "pic", value = "封面图片", position = 20)
    private String pic;

    /**
     * 类型{0:跳转到外部,1:跳转本系统}
     */
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(name = "type", value = "类型{0:跳转到外部,1:跳转本系统}", position = 30)
    private String type;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型")
    private String contentType;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    @ApiModelProperty(name = "content", value = "内容", position = 50)
    private String content;



    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 140)
    @NotNull(message = "顺序不能为空")
    private Integer orderNo;

}
