package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 分页查询新鲜事
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
@Data
public class FreshNewsPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 封面图片
     */
    @ApiModelProperty(name = "pic", value = "封面图片", position = 20)
    private String pic;

    /**
     * 类型{0:跳转到外部,1:跳转本系统}
     */
    @ApiModelProperty(name = "type", value = "类型{0:跳转到外部,1:跳转本系统}", position = 30)
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", position = 50)
    private String content;

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:上架中,2:已下架}", position = 60)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 61)
    private List<String> statusList;

    /**
     * 发布时间
     */
    @ApiModelProperty(name = "upDatetime", value = "发布时间", position = 70)
    private Date upDatetime;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 80)
    private Long creater;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 90)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 110)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 120)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 130)
    private Date updateDatetime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 140)
    private Integer orderNo;

    /**
     * 类型{0:新鲜事,1:往期回顾}
     */
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(name = "newsType", value = "类型{0:新鲜事,1:往期回顾}", position = 30)
    private String newsType;

}
