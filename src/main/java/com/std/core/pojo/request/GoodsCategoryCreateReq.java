package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增商品类型
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
@Data
public class GoodsCategoryCreateReq {

    /**
     * 类型0:大类 1:小类
     */
    @NotBlank(message = "类型0:大类 1:小类不能为空")
    @ApiModelProperty(name = "type", value = "类型0:大类 1:小类", position = 20)
    private String type;

    /**
     * 上级类型
     */
    @ApiModelProperty(name = "parentId", value = "上级类型", position = 30)
    private String parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true, position = 40)
    @NotBlank(message = "名称不能为空")
    private String name;


    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 90)
    private Integer orderNo;

}
