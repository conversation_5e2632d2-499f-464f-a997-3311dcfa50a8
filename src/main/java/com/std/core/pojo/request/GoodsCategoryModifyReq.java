package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改商品类型
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
@Data
public class GoodsCategoryModifyReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;


    private Long parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true, position = 40)
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 90)
    private Integer orderNo;

}
