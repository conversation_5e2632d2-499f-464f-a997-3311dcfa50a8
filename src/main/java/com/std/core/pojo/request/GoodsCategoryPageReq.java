package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询商品类型
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
@Data
public class GoodsCategoryPageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 类型0:大类 1:小类
     */
    @ApiModelProperty(name = "type", value = "类型0:大类 1:小类", position = 20)
    private String type;

    /**
     * 上级类型
     */
    @ApiModelProperty(name = "parentId", value = "上级类型", position = 30)
    private String parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 状态 {0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:上架中,2:已下架}", position = 50)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 51)
    private List<String> statusList;


    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 90)
    private Integer orderNo;

}
