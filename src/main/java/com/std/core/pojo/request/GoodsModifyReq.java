package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 修改商品
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@Data
public class GoodsModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型序号
     */
    @NotNull(message = "类型序号不能为空")
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 20)
    private Long typeId;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @ApiModelProperty(name = "name", value = "商品名称", position = 30)
    private String name;

    /**
     * 商品图片
     */
    @NotBlank(message = "商品图片不能为空")
    @ApiModelProperty(name = "pic", value = "商品图片", position = 50)
    private String pic;

    /**
     * 商品详情
     */
    @NotBlank(message = "商品详情不能为空")
    @ApiModelProperty(name = "content", value = "商品详情", position = 60)
    private String content;



    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 140)
    private Integer orderNo;

    @NotEmpty
    @ApiModelProperty(name = "goodsNormsList", value = "商品规格列表", required = true, position = 140)
    private List<GoodsNormsCreateReq> goodsNormsList;

}
