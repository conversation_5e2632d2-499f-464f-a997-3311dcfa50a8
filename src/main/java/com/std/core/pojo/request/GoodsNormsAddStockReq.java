package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class GoodsNormsAddStockReq {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    @NotNull(message = "礼卡id不能为空")
    private Long id;

    /**
     * 总库存
     */
    @ApiModelProperty(name = "stock", value = "总库存", position = 60)
    @NotNull(message = "库存不能为空")
    @Min(1)
    private Integer stock;
}
