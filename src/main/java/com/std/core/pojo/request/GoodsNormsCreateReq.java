package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增商品规格
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:59
 */
@Data
public class GoodsNormsCreateReq {

    /**
     * 商品序号
     */
    @ApiModelProperty(name = "goodId", value = "商品序号", position = 20)
    private Long goodId;

    /**
     * 规格名称
     */
    @NotBlank(message = "规格名称不能为空")
    @ApiModelProperty(name = "name", value = "规格名称", position = 30)
    private String name;

    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    @ApiModelProperty(name = "price", value = "价格", position = 40)
    private BigDecimal price;

    /**
     * 总量
     */
    @NotNull(message = "总量不能为空")
    @ApiModelProperty(name = "number", value = "总量", position = 50)
    private Integer number;


    /**
     * 图片
     */
    @NotBlank(message = "图片不能为空")
    @ApiModelProperty(name = "pic", value = "图片", position = 70)
    private String pic;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", required = true, position = 150)
    private Integer orderNo;

}
