package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class GoodsNormsExchangeReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;


    /**
     * 规格名称
     */
    @ApiModelProperty(name = "name", value = "规格名称", position = 30)
    private String name;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 40)
    private BigDecimal price;


    /**
     * 库存
     */
    @ApiModelProperty(name = "inventory", value = "库存", position = 60)
    private Integer inventory;

    /**
     * 图片
     */
    @ApiModelProperty(name = "pic", value = "图片", position = 70)
    private String pic;

}
