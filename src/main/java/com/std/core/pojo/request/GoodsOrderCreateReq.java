package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增商品订单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@Data
public class GoodsOrderCreateReq {

    /**
     * 购买数量
     */
    @NotNull(message = "购买数量不能为空")
    @Min(value = 1, message = "购买数量不能小于1")
    @ApiModelProperty(name = "number", value = "购买数量", position = 50)
    private Integer number;

    /**
     * 规格id
     */
    @NotNull(message = "规格id不能为空")
    @ApiModelProperty(name = "normsId", value = "规格id", position = 60)
    private Long normsId;

    /**
     * 收货地址
     */
    @NotNull(message = "收货地址不能为空")
    @ApiModelProperty(name = "addressId", value = "收货地址", position = 100)
    private Long addressId;

    @ApiModelProperty(name = "remark", value = "备注", position = 100)
    private String remark;



}
