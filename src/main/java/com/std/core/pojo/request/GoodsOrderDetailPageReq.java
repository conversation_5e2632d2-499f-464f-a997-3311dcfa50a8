package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询商品订单详情
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:53
 */
@Data
public class GoodsOrderDetailPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 订单序号
     */
    @ApiModelProperty(name = "orderId", value = "订单序号", position = 20)
    private Long orderId;

    /**
     * 订单编号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单编号", position = 30)
    private String orderNumber;

    /**
     * 商品id
     */
    @ApiModelProperty(name = "goodsId", value = "商品id", position = 40)
    private Long goodsId;

    /**
     * 规格id
     */
    @ApiModelProperty(name = "normsId", value = "规格id", position = 50)
    private Long normsId;

    /**
     * 购买数量
     */
    @ApiModelProperty(name = "number", value = "购买数量", position = 60)
    private Integer number;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称", position = 70)
    private String name;

    /**
     * 商品价格
     */
    @ApiModelProperty(name = "price", value = "商品价格", position = 80)
    private BigDecimal price;

    /**
     * 商品图片
     */
    @ApiModelProperty(name = "pic", value = "商品图片", position = 90)
    private String pic;

    /**
     * 规格名称
     */
    @ApiModelProperty(name = "normsName", value = "规格名称", position = 100)
    private String normsName;

    /**
     * 商品详情
     */
    @ApiModelProperty(name = "content", value = "商品详情", position = 110)
    private String content;

}
