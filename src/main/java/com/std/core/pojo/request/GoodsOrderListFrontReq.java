package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询商品订单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@Data
public class GoodsOrderListFrontReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型{0:普通订单,1:购物车订单}
     */
    @ApiModelProperty(name = "type", value = "类型{0:普通订单,1:购物车订单}", position = 20)
    private String type;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 30)
    private String orderNumber;

    /**
     * 快递单号
     */
    @ApiModelProperty(name = "courierNumber", value = "快递单号", position = 40)
    private String courierNumber;

    /**
     * 购买数量
     */
    @ApiModelProperty(name = "number", value = "购买数量", position = 50)
    private Integer number;

    /**
     * 总价
     */
    @ApiModelProperty(name = "totalPrice", value = "总价", position = 60)
    private BigDecimal totalPrice;

    /**
     * 状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}", position = 70)
    private String status;

    /**
     * 收货方式{0:到付,1:包邮}
     */
    @ApiModelProperty(name = "receiveWay", value = "收货方式{0:到付,1:包邮}", position = 80)
    private String receiveWay;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 90)
    private String payType;

    /**
     * 收货地址
     */
    @ApiModelProperty(name = "address", value = "收货地址", position = 100)
    private String address;

    /**
     * 收货人名称
     */
    @ApiModelProperty(name = "userName", value = "收货人名称", position = 110)
    private String userName;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(name = "userMobile", value = "收货人手机号", position = 120)
    private String userMobile;

    /**
     * 快递公司
     */
    @ApiModelProperty(name = "company", value = "快递公司", position = 130)
    private String company;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 140)
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 150)
    private Date createDatetime;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 160)
    private Date payDatetime;

    /**
     * 取消时间
     */
    @ApiModelProperty(name = "cancleDatetime", value = "取消时间", position = 170)
    private Date cancleDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 180)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 190)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 200)
    private Date updateDatetime;

    /**
     * 确认时间
     */
    @ApiModelProperty(name = "finishDatetime", value = "确认时间", position = 210)
    private Date finishDatetime;

}
