package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询商品订单
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@Data
public class GoodsOrderPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型{0:普通订单,1:购物车订单}
     */
    @ApiModelProperty(name = "type", value = "类型{0:普通订单,1:购物车订单}", position = 20)
    private String type;
    private String keywords;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 30)
    private String orderNumber;

    /**
     * 快递单号
     */
    @ApiModelProperty(name = "courierNumber", value = "快递单号", position = 40)
    private String courierNumber;

    /**
     * 状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待付款,1:待发货,2:待收货,3:已完成,4:已取消}", position = 70)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 71)
    private List<String> statusList;

    /**
     * 收货方式{0:到付,1:包邮}
     */
    @ApiModelProperty(name = "receiveWay", value = "收货方式{0:到付,1:包邮}", position = 80)
    private String receiveWay;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 90)
    private String payType;

    /**
     * 收货地址
     */
    @ApiModelProperty(name = "address", value = "收货地址", position = 100)
    private String address;

    /**
     * 收货人名称
     */
    @ApiModelProperty(name = "userName", value = "收货人名称", position = 110)
    private String userName;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(name = "userMobile", value = "收货人手机号", position = 120)
    private String userMobile;

    /**
     * 快递公司
     */
    @ApiModelProperty(name = "company", value = "快递公司", position = 130)
    private String company;

    /**
     * 下单筛选开始时间
     */
    @ApiModelProperty(name = "createStartTime", value = "下单筛选开始时间", position = 120)
    private String createStartTime;

    /**
     * 下单筛选结束时间
     */
    @ApiModelProperty(name = "createEndTime", value = "下单筛选结束时间", position = 120)
    private String createEndTime;
}
