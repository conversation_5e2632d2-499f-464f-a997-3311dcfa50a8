package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class GoodsOrderSendReq {

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    @ApiModelProperty(name = "courierNumber", value = "快递单号", position = 40)
    private String courierNumber;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    @ApiModelProperty(name = "id", value = "订单id", position = 40)
    private Long id ;

    /**
     * 快递公司
     */
    @NotBlank(message = "快递公司不能为空")
    @ApiModelProperty(name = "company", value = "快递公司", position = 130)
    private String company;

    /**
     * 发货时间
     */
    @NotBlank(message = "发货时间")
    @ApiModelProperty(name = "sendTime", value = "发货时间", position = 130)
    private String sendTime;
}
