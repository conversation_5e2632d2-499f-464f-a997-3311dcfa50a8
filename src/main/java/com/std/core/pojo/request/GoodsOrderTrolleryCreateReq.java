package com.std.core.pojo.request;

import com.std.core.pojo.domain.GoodsOrderTrollery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GoodsOrderTrolleryCreateReq {

    @NotNull(message = "收货地址不能为空")
    @ApiModelProperty(name = "addressId", value = "收货地址", position = 100)
    private Long addressId;


    @NotEmpty
    @ApiModelProperty(name = "goodsOrderTrolleryList", value = "商品规格集合", position = 100)
    private List<String> goodsOrderTrolleryList;

    @ApiModelProperty(name = "remark", value = "备注", position = 100)
    private String remark;

}
