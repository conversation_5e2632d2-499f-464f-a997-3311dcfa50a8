package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询商品
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@Data
public class GoodsPageFrontReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型序号
     */
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 20)
    private Long typeId;

    /**
     * 大类类型序号
     */
    @ApiModelProperty(name = "parentId", value = "大类类型序号", position = 20)
    private Long parentId;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称", position = 30)
    private String name;


    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:上架中,2:已下架}", position = 70)
    private String status;

    /**
     * 状态列表
     */
    @ApiModelProperty(name = "statusList", value = "状态列表", position = 71)
    private List<String> statusList;


    /**
     * 排序标识
     */
    @ApiModelProperty(name = "sortFlag", value = "排序标识 0:价格正序1:价格倒叙", position = 120)
    private String sortFlag;


}
