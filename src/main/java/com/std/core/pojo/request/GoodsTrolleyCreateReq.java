package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增购物车
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
@Data
public class GoodsTrolleyCreateReq {
    /**
     * 规格id
     */
    @ApiModelProperty(name = "normsId", value = "规格id", position = 40)
    @NotNull(message = "规格id不能为空")
    private Long normsId;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 50)
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量不能小于1")
    private Integer number;
}
