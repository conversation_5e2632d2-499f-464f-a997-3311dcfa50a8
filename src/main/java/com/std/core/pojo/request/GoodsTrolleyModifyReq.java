package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import lombok.Data;

/**
 * 修改购物车
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
@Data
public class GoodsTrolleyModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;


    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 50)
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量不能小于1")
    private Integer number;

}
