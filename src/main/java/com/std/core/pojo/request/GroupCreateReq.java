package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:35
 */
@Data
public class GroupCreateReq {

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 类型
     */
    @ApiModelProperty(name = "kind", value = "类型，dict=group.kind")
    private String kind;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private String orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
