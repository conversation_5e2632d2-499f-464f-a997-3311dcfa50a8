package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:36
 */
@Data
public class GroupListReq extends BaseListReq {

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 端
     */
    @ApiModelProperty(name = "kind", value = "端")
    private String kind;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号")
    private String companyId;
}
