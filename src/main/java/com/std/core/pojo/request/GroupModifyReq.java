package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:37
 */
@Data
public class GroupModifyReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", required = true)
    @NotNull(message = "编号不能为空")
    @Min(0)
    private Long id;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private String orderNo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
