package com.std.core.pojo.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 收入统计（分组报表）请求
 */
@Data
public class IncomeGroupReq {

    @ApiModelProperty(value = "开始时间", example = "2025-04-01")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-04-21")
    private Date endTime;

    @ApiModelProperty(value = "分组周期：day / week / month", example = "week")
    private String period;

    @ApiModelProperty(value = "0：活动 1：商品")
    private String type;
}
