package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询账户流水
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Data
public class JourBySysAllowncePageReq extends BasePageReq {


    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类")
    private String bizCategory;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;


}
