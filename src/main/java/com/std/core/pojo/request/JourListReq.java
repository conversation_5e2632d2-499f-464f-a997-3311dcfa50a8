package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 列表查询账户流水
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Data
public class JourListReq extends BaseListReq {

    /**
     * 流水类型（1余额流水 2冻结流水）
     */
    @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类")
    private String bizCategory;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;

    /**
     * 系统内部参考订单号
     */
    @ApiModelProperty(name = "refNo", value = "系统内部参考订单号")
    private String refNo;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 业务类型列表
     */
    @ApiModelProperty(name = "bizTypeList", value = "业务类型列表")
    private List<String> bizTypeList;

    /**
     * 账户编号列表
     */
    @ApiModelProperty(name = "accountNumberList", value = "账户编号列表")
    private List<String> accountNumberList;

    /**
     * 大类列表
     */
    @ApiModelProperty(name = "bizCategoryList", value = "大类列表")
    private List<String> bizCategoryList;

}
