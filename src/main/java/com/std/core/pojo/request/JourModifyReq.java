package com.std.core.pojo.request;

import javax.validation.constraints.NotNull;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
* 修改账户流水
*
* <AUTHOR> ycj
* @since : 2020-10-21 13:53
*/
@Data
public class JourModifyReq {

      /**
      * 编号
      */
      @NotNull(message = "id不能为空")
      @ApiModelProperty(name = "id", value = "编号", position = 10)
      private Long id;

      /**
      * 流水类型（1余额流水 2冻结流水）
      */
      @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）", position = 20)
      private String type;

      /**
      * 用户编号
      */
      @ApiModelProperty(name = "userId", value = "用户编号", position = 30)
      private Long userId;

      /**
      * 账户编号
      */
      @ApiModelProperty(name = "accountNumber", value = "账户编号", position = 40)
      private String accountNumber;

      /**
      * 账户类型
      */
      @ApiModelProperty(name = "accountType", value = "账户类型", position = 50)
      private String accountType;

      /**
      * 币种
      */
      @ApiModelProperty(name = "currency", value = "币种", position = 60)
      private String currency;

      /**
      * 业务大类
      */
      @ApiModelProperty(name = "bizCategory", value = "业务大类", position = 70)
      private String bizCategory;

      /**
      * 业务大类
      */
      @ApiModelProperty(name = "bizCategoryNote", value = "业务大类", position = 80)
      private String bizCategoryNote;

      /**
      * 业务小类
      */
      @ApiModelProperty(name = "bizType", value = "业务小类", position = 90)
      private String bizType;

      /**
      * 业务小类说明
      */
      @ApiModelProperty(name = "bizNote", value = "业务小类说明", position = 100)
      private String bizNote;

      /**
      * 系统内部参考订单号
      */
      @ApiModelProperty(name = "refNo", value = "系统内部参考订单号", position = 110)
      private String refNo;

      /**
      * 关联的用户ID
      */
      @ApiModelProperty(name = "refUserId", value = "关联的用户ID", position = 120)
      private Long refUserId;

      /**
      * 变动金额
      */
      @ApiModelProperty(name = "transAmount", value = "变动金额", position = 130)
      private BigDecimal transAmount;

      /**
      * 变动前金额
      */
      @ApiModelProperty(name = "preAmount", value = "变动前金额", position = 140)
      private BigDecimal preAmount;

      /**
      * 变动后金额
      */
      @ApiModelProperty(name = "postAmount", value = "变动后金额", position = 150)
      private BigDecimal postAmount;

      /**
      * 上一条流水编号
      */
      @ApiModelProperty(name = "prevJourCode", value = "上一条流水编号", position = 160)
      private String prevJourCode;

      /**
      * 状态 1待对账 3已对账且账已平 4账不平待调账审批 5已对账且账不平 6无需对账 11待入账 12已入账 13入账失败 14待出账 15出账成功 16出账失败
      */
      @ApiModelProperty(name = "status", value = "状态 1待对账 3已对账且账已平 4账不平待调账审批 5已对账且账不平 6无需对账 11待入账 12已入账 13入账失败 14待出账 15出账成功 16出账失败", position = 170)
      private String status;

      /**
      * 创建时间
      */
      @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 190)
      private String createDatetime;

      /**
      * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
      */
      @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）", position = 200)
      private String channelType;

      /**
      * 支付渠道单号
      */
      @ApiModelProperty(name = "channelOrder", value = "支付渠道单号", position = 210)
      private String channelOrder;

      /**
      * 拟对账时间
      */
      @ApiModelProperty(name = "workDate", value = "拟对账时间", position = 220)
      private String workDate;

      /**
      * 对账人
      */
      @ApiModelProperty(name = "checkUser", value = "对账人", position = 230)
      private String checkUser;

      /**
      * 对账说明
      */
      @ApiModelProperty(name = "checkNote", value = "对账说明", position = 240)
      private String checkNote;

      /**
      * 对账时间
      */
      @ApiModelProperty(name = "checkDatetime", value = "对账时间", position = 250)
      private String checkDatetime;

      /**
      * 调账人
      */
      @ApiModelProperty(name = "adjustUser", value = "调账人", position = 260)
      private String adjustUser;

      /**
      * 调账说明
      */
      @ApiModelProperty(name = "adjustNote", value = "调账说明", position = 270)
      private String adjustNote;

      /**
      * 调账时间
      */
      @ApiModelProperty(name = "adjustDatetime", value = "调账时间", position = 280)
      private String adjustDatetime;

      /**
      * 交易序号
      */
      @ApiModelProperty(name = "transOrderNo", value = "交易序号", position = 290)
      private Integer transOrderNo;

      /**
      * 到账时间
      */
      @ApiModelProperty(name = "arriveAccountTime", value = "到账时间", position = 300)
      private String arriveAccountTime;

      /**
      * 入账类型
      */
      @ApiModelProperty(name = "arriveAccountType", value = "入账类型", position = 310)
      private String arriveAccountType;

      /**
      * 优惠信息
      */
      @ApiModelProperty(name = "discountInfo", value = "优惠信息", position = 320)
      private String discountInfo;

      /**
      * 服务费
      */
      @ApiModelProperty(name = "serviceCharge", value = "服务费", position = 330)
      private BigDecimal serviceCharge;

      /**
      * 结算总金额
      */
      @ApiModelProperty(name = "totalSettleAmount", value = "结算总金额", position = 340)
      private BigDecimal totalSettleAmount;


}
