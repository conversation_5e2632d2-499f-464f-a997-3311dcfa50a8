package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询我的账户流水
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Data
public class JourMyPageReq extends BasePageReq {

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 流水类型（1余额流水 2冻结流水）
     */
    @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）")
    private String type;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类")
    private String bizCategory;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;


    @ApiModelProperty(name = "changeType", value = "收支类型 0：收入，1：支出")
    private String changeType;

}
