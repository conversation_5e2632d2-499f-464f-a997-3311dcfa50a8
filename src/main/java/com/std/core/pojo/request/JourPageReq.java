package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 分页查询账户流水
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Data
public class JourPageReq extends BasePageReq {

    /**
     * 流水类型（1余额流水 2冻结流水）
     */
    @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    @NotEmpty
    private String accountNumber;

    /**
     * 账户类型
     */
    @ApiModelProperty(name = "accountType", value = "账户类型")
    private String accountType;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种:CNY = 人民币，CNY_BZJ = 保证金")
    private String currency;

    /**
     * 业务大类
     */
    @ApiModelProperty(name = "bizCategory", value = "业务大类")
    private String bizCategory;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    @ApiModelProperty(name = "startTime", value = "记账开始时间")
    private String startTime;

    @ApiModelProperty(name = "endTime", value = "记账结束时间")
    private String endTime;


}
