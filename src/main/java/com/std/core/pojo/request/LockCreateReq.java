package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 新增业务锁
*
* <AUTHOR> xieyj
* @since : 2020-06-12 00:27
*/
@Data
public class LockCreateReq {

      /**
      * 类型
      */
      @ApiModelProperty(name = "bizType", value = "类型", required = true, position = 20)
      @NotBlank(message = "类型不能为空")
      private String bizType;

      /**
      * 关联编号
      */
      @ApiModelProperty(name = "refCode", value = "关联编号", required = true, position = 30)
      @NotBlank(message = "关联编号不能为空")
      private String refCode;

      /**
      * 创建时间
      */
      @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 40)
      @NotBlank(message = "创建时间不能为空")
      private String createDatetime;


}
