package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分页查询业务锁
 *
 * <AUTHOR> xieyj
 * @since : 2020-06-12 00:27
 */
@Data
public class LockPageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Integer id;

    /**
     * 类型
     */
    @ApiModelProperty(name = "bizType", value = "类型", position = 20)
    private String bizType;

    /**
     * 关联编号
     */
    @ApiModelProperty(name = "refCode", value = "关联编号", position = 30)
    private String refCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 40)
    private Date createDatetime;


}
