package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:40
 */
@Data
public class MenuModifyReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", required = true)
    @NotNull(message = "编号不能为空")
    private Long id;

    /**
     * 父编号
     */
    @ApiModelProperty(name = "parentId", value = "父编号")
    private Long parentId;

    /**
     * 类型（端/菜单）
     */
    @ApiModelProperty(name = "type", value = "类型（端/菜单）")
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * logo图标
     */
    @ApiModelProperty(name = "logo", value = "logo图标")
    private String logo;

    /**
     * URL
     */
    @ApiModelProperty(name = "url", value = "URL")
    private String url;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号")
    private String orderNo;

    /**
     * 展示位置
     */
    @ApiModelProperty(name = "location", value = "展示位置")
    private String location;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
