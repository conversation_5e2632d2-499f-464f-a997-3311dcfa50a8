package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class MerchantLoginReq {

    /**
     * 登录名
     */
    @NotBlank(message = "登录名必填")
    @ApiModelProperty(name = "loginName", value = "登录名", required = true)
    private String loginName;

    /**
     * 登录密码
     */
    @NotBlank(message = "登录密码必填")
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true)
    private String loginPwd;

}
