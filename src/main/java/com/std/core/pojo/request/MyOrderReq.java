package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MyOrderReq extends BasePageReq {

    /**
     * 状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}", position = 170)
    private String status;

    private List<String> statusList;

}
