package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询星级节点配置
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
@Data
public class NodeConfigListReq extends BaseListReq {

    /**
     * ID
     */
    @ApiModelProperty(name = "id", value = "ID", position = 10)
    private Long id;

    /**
     * 节点名称
     */
    @ApiModelProperty(name = "name", value = "节点名称", position = 20)
    private String name;

    /**
     * 节点星级
     */
    @ApiModelProperty(name = "level", value = "节点星级", position = 30)
    private Integer level;

    /**
     * 团队业绩最小值
     */
    @ApiModelProperty(name = "teamPerformance", value = "团队业绩最小值", position = 40)
    private BigDecimal teamPerformance;

    /**
     * 直推团队最小数量
     */
    @ApiModelProperty(name = "firstTeamNumber", value = "直推团队最小数量", position = 50)
    private Integer firstTeamNumber;

    /**
     * 直推团队业绩最小值
     */
    @ApiModelProperty(name = "firstTeamPerformance", value = "直推团队业绩最小值", position = 60)
    private BigDecimal firstTeamPerformance;

    /**
     * 直推人员星级
     */
    @ApiModelProperty(name = "firstPersonLevel", value = "直推人员星级", position = 70)
    private Integer firstPersonLevel;

    /**
     * 直推人员星级节点人数最小值
     */
    @ApiModelProperty(name = "firstPersonLevelNumber", value = "直推人员星级节点人数最小值", position = 80)
    private Integer firstPersonLevelNumber;

    /**
     * 节点收益比例
     */
    @ApiModelProperty(name = "rate", value = "节点收益比例", position = 90)
    private BigDecimal rate;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 100)
    private String creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "creatorName", value = "创建人名称", position = 110)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 120)
    private Date createTime;

    /**
     * 最后更新人
     */
    @ApiModelProperty(name = "updater", value = "最后更新人", position = 130)
    private String updater;

    /**
     * 最后更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "最后更新人名称", position = 140)
    private String updaterName;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "最后更新时间", position = 150)
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 160)
    private String remark;


}
