package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 新增通知人
*
* <AUTHOR> LEO
* @since : 2020-10-31 15:39
*/
@Data
public class NotifierCreateReq {

      /**
      * 类型: 0提币通知人
      */
      @ApiModelProperty(name = "type", value = "类型: 0提币通知人", required = true, position = 20)
      @NotBlank(message = "类型: 0提币通知人不能为空")
      private String type;

      /**
      * 开始时间0-23
      */
      @ApiModelProperty(name = "startDate", value = "开始时间0-23", required = true, position = 30)
      @NotNull(message = "开始时间0-23不能为空")
      private Integer startDate;

      /**
      * 结束时间0-23
      */
      @ApiModelProperty(name = "endDate", value = "结束时间0-23", required = true, position = 40)
      @NotNull(message = "结束时间0-23不能为空")
      private Integer endDate;

      /**
      * 姓名
      */
      @ApiModelProperty(name = "name", value = "姓名", required = true, position = 50)
      @NotBlank(message = "姓名不能为空")
      private String name;

      /**
      * 手机号码
      */
      @ApiModelProperty(name = "phone", value = "手机号码", position = 60)
      private String phone;

      /**
      * 邮箱
      */
      @ApiModelProperty(name = "email", value = "邮箱", position = 70)
      private String email;


}
