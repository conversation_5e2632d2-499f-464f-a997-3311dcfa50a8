package com.std.core.pojo.request;

import javax.validation.constraints.NotNull;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
* 修改通知人
*
* <AUTHOR> LEO
* @since : 2020-10-31 15:39
*/
@Data
public class NotifierModifyReq {

      /**
      * 
      */
      @NotNull(message = "id不能为空")
      @ApiModelProperty(name = "id", value = "", position = 10)
      private Integer id;

      /**
      * 类型: 0提币通知人
      */
      @ApiModelProperty(name = "type", value = "类型: 0提币通知人", position = 20)
      private String type;

      /**
      * 开始时间0-23
      */
      @ApiModelProperty(name = "startDate", value = "开始时间0-23", position = 30)
      private Integer startDate;

      /**
      * 结束时间0-23
      */
      @ApiModelProperty(name = "endDate", value = "结束时间0-23", position = 40)
      private Integer endDate;

      /**
      * 姓名
      */
      @ApiModelProperty(name = "name", value = "姓名", position = 50)
      private String name;

      /**
      * 手机号码
      */
      @ApiModelProperty(name = "phone", value = "手机号码", position = 60)
      private String phone;

      /**
      * 邮箱
      */
      @ApiModelProperty(name = "email", value = "邮箱", position = 70)
      private String email;


}
