package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询通知人
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 15:39
 */
@Data
public class NotifierPageReq extends BasePageReq {

    /**
     * 类型: 0提币通知人
     */
    @ApiModelProperty(name = "type", value = "类型: 0提币通知人", position = 20)
    private String type;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "name", value = "姓名", position = 50)
    private String name;

    /**
     * 手机号码
     */
    @ApiModelProperty(name = "phone", value = "手机号码", position = 60)
    private String phone;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", position = 70)
    private String email;


}
