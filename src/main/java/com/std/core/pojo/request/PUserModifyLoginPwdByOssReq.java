package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class PUserModifyLoginPwdByOssReq {

    /**
     * 新登录密码
     */
    @NotBlank(message = "新登录密码必填")
    @ApiModelProperty(name = "newLoginPwd", value = "新登录密码", required = true, position = 30)
    @Size(min = 6, max = 12, message = "新登录密码长度6-12位")
    private String newLoginPwd;

    @NotNull(message = "用户ID不能为空")
    private Long userId;
}

