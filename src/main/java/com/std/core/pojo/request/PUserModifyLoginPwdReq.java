package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class PUserModifyLoginPwdReq {

    /**
      * 手机号
     */
    @NotBlank(message = "手机号码不能为空")
    @ApiModelProperty(name = "mobile", value = "手机号码", required = true, position = 10)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    @NotBlank(message = "短信验证码")
    @ApiModelProperty(name = "smsCode", value = "短信验证码", required = true, position = 30)
    private String smsCode;


    /**
     * 新登录密码
     */
    @NotBlank(message = "新登录密码必填")
    @ApiModelProperty(name = "newLoginPwd", value = "新登录密码", required = true, position = 30)
    @Size(min = 6, max = 12, message = "新登录密码长度6-12位")
    private String newLoginPwd;

}
