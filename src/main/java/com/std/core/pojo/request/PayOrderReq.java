package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付订单
 *
 * <AUTHOR> x<PERSON>gzhiqin
 * @since : 2020-07-15 13:41
 */
@Data
public class PayOrderReq {

    /**
     * 订单序号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "订单序号", position = 10)
    private Long id;

    /**
     * 0:微信，1：支付宝，2：未使用
     */
    @ApiModelProperty(name = "payment", value = "0:微信，1：支付宝，2：未使用", required = true, position = 20)
    private String payment;

    @ApiModelProperty(name = "pwd", value = "支付密码", position = 40)
    private String pwd;

    /**
     * 是否使用积分抵扣
     */
    @ApiModelProperty(name = "isUseIntegral", value = "是否使用积分抵扣 0：不使用，1：使用", required = true, position = 230)
    @NotBlank(message = "是否使用积分抵扣 不能为空")
    private String isUseIntegral;

    /**
     * 是否使用一卡通抵扣
     */
    @ApiModelProperty(name = "isUseYkt", value = "是否使用一卡通抵扣 0：不使用，1：使用", required = true, position = 250)
    @NotBlank(message = "是否使用一卡通抵扣 不能为空")
    private String isUseYkt;

    /**
     * 一卡通使用编号
     */
    @ApiModelProperty(name = "yktNumber", value = "一卡通使用编号", position = 270)
    private String yktNumber;
}
