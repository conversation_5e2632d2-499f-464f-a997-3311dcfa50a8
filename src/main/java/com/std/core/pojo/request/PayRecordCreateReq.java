package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增支付记录
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
@Data
public class PayRecordCreateReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", required = true, position = 20)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    /**
     * 支付渠道 wechat、alipy等
     */
    @ApiModelProperty(name = "payType", value = "支付渠道 wechat、alipy等", required = true, position = 30)
    @NotBlank(message = "支付渠道 wechat、alipy等不能为空")
    private String payType;

    /**
     * 支付方式 app、h5、web等
     */
    @ApiModelProperty(name = "payMethod", value = "支付方式 app、h5、web等", required = true, position = 40)
    @NotBlank(message = "支付方式 app、h5、web等不能为空")
    private String payMethod;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "amount", value = "支付金额", required = true, position = 50)
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", required = true, position = 60)
    @NotBlank(message = "创建时间不能为空")
    private String createTime;

    /**
     * 回调时间
     */
    @ApiModelProperty(name = "callbackTime", value = "回调时间", required = true, position = 70)
    @NotBlank(message = "回调时间不能为空")
    private String callbackTime;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", required = true, position = 80)
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizType", value = "业务类型", required = true, position = 90)
    @NotBlank(message = "业务类型不能为空")
    private String bizType;

    /**
     * 关联业务编号
     */
    @ApiModelProperty(name = "bizCode", value = "关联业务编号", required = true, position = 100)
    @NotNull(message = "关联业务编号不能为空")
    private Long bizCode;

    /**
     * 状态
     */
    @ApiModelProperty(name = "bizStatus", value = "状态", required = true, position = 110)
    @NotBlank(message = "状态不能为空")
    private String bizStatus;


}
