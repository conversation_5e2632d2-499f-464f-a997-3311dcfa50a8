package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询支付记录
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
@Data
public class PayRecordListReq extends BaseListReq {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private Long userId;

    /**
     * 支付渠道 wechat、alipy等
     */
    @ApiModelProperty(name = "payType", value = "支付渠道 wechat、alipy等", position = 30)
    private String payType;

    /**
     * 支付方式 app、h5、web等
     */
    @ApiModelProperty(name = "payMethod", value = "支付方式 app、h5、web等", position = 40)
    private String payMethod;

    /**
     * 支付金额
     */
    @ApiModelProperty(name = "amount", value = "支付金额", position = 50)
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 60)
    private Date createTime;

    /**
     * 回调时间
     */
    @ApiModelProperty(name = "callbackTime", value = "回调时间", position = 70)
    private Date callbackTime;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 80)
    private String status;

    /**
     * 业务类型
     */
    @ApiModelProperty(name = "bizType", value = "业务类型", position = 90)
    private String bizType;

    /**
     * 关联业务编号
     */
    @ApiModelProperty(name = "bizCode", value = "关联业务编号", position = 100)
    private Long bizCode;

    /**
     * 状态
     */
    @ApiModelProperty(name = "bizStatus", value = "状态", position = 110)
    private String bizStatus;


}
