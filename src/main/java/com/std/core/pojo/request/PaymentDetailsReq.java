package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2020/9/19 11:16
 */
@Data
public class PaymentDetailsReq {

    /**
     * 关联类型（不同类型的商品表）
     */
    @ApiModelProperty(name = "refType", value = "关联类型（不同类型的商品表）", position = 10)
    private String refType;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status",value = "状态",position = 20)
    private String status;
    /**
     * 订单流水号
     */
    @ApiModelProperty(name = "serialNumber", value = "订单流水号", position = 50)
    private Long serialNumber;

}
