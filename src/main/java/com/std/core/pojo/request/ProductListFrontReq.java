package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询标的
 *
 * <AUTHOR> ycj
 * @since : 2024-11-22 14:39
 */
@Data
public class ProductListFrontReq extends BaseListReq {

    /**
     * 主键id
     */
    @ApiModelProperty(name = "id", value = "主键id", position = 10)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 20)
    private String name;

    /**
     * 锁定时长
     */
    @ApiModelProperty(name = "lockTime", value = "锁定时长", position = 30)
    private Integer lockTime;

    /**
     * 类型{0:定期,1:活期}
     */
    @ApiModelProperty(name = "type", value = "类型{0:定期,1:活期}", position = 40)
    private String type;

    /**
     * 起投金额
     */
    @ApiModelProperty(name = "initialCapital", value = "起投金额", position = 50)
    private BigDecimal initialCapital;

    /**
     * 投资步长
     */
    @ApiModelProperty(name = "step", value = "投资步长", position = 60)
    private BigDecimal step;

    /**
     * 小时收益
     */
    @ApiModelProperty(name = "hourlyInterest", value = "小时收益", position = 70)
    private BigDecimal hourlyInterest;

    /**
     * 月化收益
     */
    @ApiModelProperty(name = "yearInterest", value = "月化收益", position = 80)
    private BigDecimal yearInterest;

    /**
     * 状态{0:下架,1:代上架,2:上架中}
     */
    @ApiModelProperty(name = "status", value = "状态{0:下架,1:代上架,2:上架中}", position = 90)
    private String status;

    /**
     * 排序
     */
    @ApiModelProperty(name = "order", value = "排序", position = 100)
    private Integer order;

    /**
     * 简介
     */
    @ApiModelProperty(name = "intro", value = "简介", position = 110)
    private String intro;

    /**
     * 最近修改人
     */
    @ApiModelProperty(name = "updater", value = "最近修改人", position = 120)
    private Long updater;

    /**
     * 修改人姓名
     */
    @ApiModelProperty(name = "updateName", value = "修改人姓名", position = 130)
    private String updateName;

    /**
     * 最近修改时间
     */
    @ApiModelProperty(name = "updatetime", value = "最近修改时间", position = 140)
    private Date updatetime;

}
