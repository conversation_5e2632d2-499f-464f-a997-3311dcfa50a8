package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:45
 */
@Data
public class RoleCreateReq {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(name = "roleId", value = "角色编号")
    private String remark;
}
