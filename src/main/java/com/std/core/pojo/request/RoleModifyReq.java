package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:45
 */
@Data
public class RoleModifyReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", required = true)
    @NotNull(message = "角色序号不存在")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;
}
