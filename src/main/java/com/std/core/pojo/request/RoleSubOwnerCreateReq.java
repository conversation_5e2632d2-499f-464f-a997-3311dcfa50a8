package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:45
 */
@Data
public class RoleSubOwnerCreateReq {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 所属端类型
     */
    @ApiModelProperty(name = "kind", value = "所属端类型", required = true)
    @NotBlank(message = "所属端类型不能为空")
    private String kind;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号", required = true)
    @NotNull(message = "公司ID不能为空")
    private Long companyId;
}
