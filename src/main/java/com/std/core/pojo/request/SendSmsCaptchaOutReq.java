package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-04 09:17
 */
@Data
public class SendSmsCaptchaOutReq {

    /**
     * 手机号
     */
    @NotBlank
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 10)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    /**
     * 业务类型
     */
    @NotBlank
    @ApiModelProperty(
            name = "bizType",
            value = "业务类型(数据字典：smsOut.bizType)",
            required = true,
            position = 20)
    private String bizType;
}
