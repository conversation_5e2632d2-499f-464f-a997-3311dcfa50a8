package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/20 17:23
 */
@Data
public class SmsBatchCreateReq {
    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 5)
    private Long id;
    /**
     * 用户序号集合
     */
    @ApiModelProperty(name = "idList", value = "用户序号集合", position = 10)
    private List<Long> idList;
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(name = "title", value = "标题", required = true, position = 20)
    private String title;
    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型, 1:系统消息，2：我的消息", required = true, position = 30)
    private String type;
    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    @ApiModelProperty(name = "content", value = "内容", required = true, position = 40)
    private String content;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 50)
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 60)
    private Long creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "creatorName", value = "创建人名称", position = 70)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;
}
