package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改消息记录
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Data
public class SmsModifyReq {

    /**
     * 序号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 针对人群
     */
    @ApiModelProperty(name = "target", value = "针对人群, dict=sms.target", position = 20)
    private String target;

    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型, dict=sms.type", position = 30)
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", position = 50)
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", position = 60)
    private String content;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "contentType", value = "内容类型")
    private String contentType;
}
