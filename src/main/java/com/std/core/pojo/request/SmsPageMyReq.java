package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 我的分页查询系统公告
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Data
public class SmsPageMyReq extends BasePageReq {

    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型dict=sms.type 1=系统公告 2=我的消息", position = 10)
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", position = 20)
    private String title;

    /**
     * 是否已阅读
     */
    @ApiModelProperty(name = "isRead", value = "是否已阅读1=是0=否", position = 20)
    private String isRead;

}
