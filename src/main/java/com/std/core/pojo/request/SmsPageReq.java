package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询消息记录
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Data
public class SmsPageReq extends BasePageReq {

    /**
     * 针对人群
     */
    @ApiModelProperty(name = "target", value = "针对人群", position = 20)
    private String target;

    /**
     * 消息类型
     */
    @ApiModelProperty(name = "type", value = "消息类型", position = 30)
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", position = 50)
    private String title;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 70)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 90)
    private String updater;

    @ApiModelProperty(name = "keywords", value = "关键字模糊查", position = 100)
    private String keywords;
}
