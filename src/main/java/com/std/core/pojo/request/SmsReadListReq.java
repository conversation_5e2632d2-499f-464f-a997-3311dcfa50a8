package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询公告阅读记录
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
@Data
public class SmsReadListReq extends BaseListReq {

    /**
     * ID主键
     */
    @ApiModelProperty(name = "id", value = "ID主键", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private String userId;

    /**
     * 消息编号
     */
    @ApiModelProperty(name = "smsCode", value = "消息编号", position = 30)
    private String smsCode;

    /**
     * 接受方式(站内消息，APP推送,短信)
     */
    @ApiModelProperty(name = "receiveWay", value = "接受方式(站内消息，APP推送,短信)", position = 40)
    private String receiveWay;

    /**
     * 状态 0-未阅读 1-已阅读 2-已删除
     */
    @ApiModelProperty(name = "status", value = "状态 0-未阅读 1-已阅读 2-已删除", position = 50)
    private String status;

    /**
     * 推送时间
     */
    @ApiModelProperty(name = "createDatetime", value = "推送时间", position = 60)
    private Date createDatetime;

    /**
     * 阅读时间
     */
    @ApiModelProperty(name = "readDatetime", value = "阅读时间", position = 70)
    private Date readDatetime;

    /**
     * 删除时间
     */
    @ApiModelProperty(name = "deleteDatetime", value = "删除时间", position = 80)
    private Date deleteDatetime;


}
