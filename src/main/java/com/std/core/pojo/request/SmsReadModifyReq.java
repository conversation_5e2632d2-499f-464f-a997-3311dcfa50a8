package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改公告阅读记录
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
@Data
public class SmsReadModifyReq {

    /**
     * ID主键
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "ID主键", position = 10)
    private Long id;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", position = 20)
    private String userId;

    /**
     * 消息编号
     */
    @ApiModelProperty(name = "smsCode", value = "消息编号", position = 30)
    private String smsCode;

    /**
     * 接受方式(站内消息，APP推送,短信)
     */
    @ApiModelProperty(name = "receiveWay", value = "接受方式(站内消息，APP推送,短信)", position = 40)
    private String receiveWay;

    /**
     * 状态 0-未阅读 1-已阅读 2-已删除
     */
    @ApiModelProperty(name = "status", value = "状态 0-未阅读 1-已阅读 2-已删除", position = 50)
    private String status;

    /**
     * 推送时间
     */
    @ApiModelProperty(name = "createDatetime", value = "推送时间", position = 60)
    private String createDatetime;

    /**
     * 阅读时间
     */
    @ApiModelProperty(name = "readDatetime", value = "阅读时间", position = 70)
    private String readDatetime;

    /**
     * 删除时间
     */
    @ApiModelProperty(name = "deleteDatetime", value = "删除时间", position = 80)
    private String deleteDatetime;


}
