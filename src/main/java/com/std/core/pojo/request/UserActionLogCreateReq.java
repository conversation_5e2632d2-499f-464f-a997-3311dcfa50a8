package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增用户业务操作记录
 *
 * <AUTHOR> ycj
 * @since : 2024-06-17 12:44
 */
@Data
public class UserActionLogCreateReq {

    /**
     * 类型 {0:微信解绑,1:绑定微信}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:微信解绑,1:绑定微信}", position = 20)
    private String type;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 30)
    private Long userId;

    /**
     * 原数据
     */
    @ApiModelProperty(name = "oldData", value = "原数据", position = 40)
    private String oldData;

    /**
     * 新数据
     */
    @ApiModelProperty(name = "newData", value = "新数据", position = 50)
    private String newData;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", required = true, position = 60)
    private String createDatetime;

}
