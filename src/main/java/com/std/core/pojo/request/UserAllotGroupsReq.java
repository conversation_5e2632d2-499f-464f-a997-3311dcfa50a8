package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:47
 */
@Data
public class UserAllotGroupsReq {

    @ApiModelProperty(name = "userId", value = "userId", required = true)
    @NotNull(message = "userId不能为空")
    private Long userId;

    @ApiModelProperty(name = "groupIdList", value = "组列表")
    private List<Long> groupIdList;
}
