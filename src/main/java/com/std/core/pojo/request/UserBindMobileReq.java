package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserBindMobileReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号必填")
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 20)
    private String mobile;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码必填")
    @ApiModelProperty(name = "smsCaptcha", value = "验证码", required = true, position = 30)
    private String smsCaptcha;

}
