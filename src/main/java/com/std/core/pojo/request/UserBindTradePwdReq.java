package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR> xieyj
 * @since : 2019-01-18 16:49
 */
@Data
public class UserBindTradePwdReq {

    /**
     * 支付密码
     */
    @NotBlank(message = "支付密码必填")
    @ApiModelProperty(name = "tradePwd", value = "支付密码", required = true, position = 20)
    private String tradePwd;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码必填")
    @ApiModelProperty(name = "smsCaptcha", value = "验证码 业务类型=BIND_TRADEPWD", required = true, position = 30)
    private String smsCaptcha;

}
