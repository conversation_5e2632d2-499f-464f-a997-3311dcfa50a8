package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class UserBindWxGzhLoginReq {

    /**
     * code
     */
    @NotBlank(message = "code必填")
    @ApiModelProperty(name = "code", value = "code", required = true)
    private String code;

//    /**
//     * 登录方式 0-H5 1-App
//     */
//    @ApiModelProperty(name = "loginType", value = "登录方式 0-H5 1-App", hidden = true)
//    private String loginType;

}
