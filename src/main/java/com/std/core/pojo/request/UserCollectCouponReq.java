package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 新增优惠券
*
* <AUTHOR> zhou<PERSON>
* @since : 2020-09-02 15:25
*/
@Data
public class UserCollectCouponReq {

      /**
      * 优惠券id
      */
      @ApiModelProperty(name = "couponId", value = "优惠券id", required = true, position = 20)
      @NotNull
      private Long couponId;
}
