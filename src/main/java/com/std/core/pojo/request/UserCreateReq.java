package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 新增用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class UserCreateReq {

    /**
     * 类型
     */
    @NotNull(message = "类型不能为空")
    @ApiModelProperty(name = "kind", value = "类型（运维中心OPS 管理端SYS C端）", position = 20, required = true)
    private String kind;

    /**
     * 登录名称
     */
    @NotNull(message = "登录名不能为空")
    @ApiModelProperty(name = "loginName", value = "登录名称", position = 30, required = true)
    private String loginName;

    /**
     * 真实名称
     */
    @ApiModelProperty(name = "realName", value = "真实名称", position = 40)
    private String realName;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称", position = 50)
    private String nickname;

    /**
     * 性别（1男 2女）
     */
    @ApiModelProperty(name = "sex", value = "性别（1男 2女）", position = 70)
    private String sex;

    /**
     * 生日
     */
    @ApiModelProperty(name = "birthday", value = "生日", position = 80)
    private String birthday;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", position = 90)
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", position = 100)
    private String email;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像", position = 110)
    private String photo;

    /**
     * 描述
     */
    @ApiModelProperty(name = "description", value = "描述", position = 120)
    private String description;
    /**
     * 愿力值余额
     */
    @ApiModelProperty(name = "willingValue", value = "愿力值余额")
    private BigDecimal willingValue;
    /**
     * 登录密码
     */
    @NotNull(message = "登录密码不能为空")
    @ApiModelProperty(name = "loginPwd", value = "登录密码", position = 130, required = true)
    private String loginPwd;

    @ApiModelProperty(name = "roleCode", value = "角色编号", position = 120)
    private Long roleCode;

    @ApiModelProperty(name = "idKind", value = "证件类型0身份证", position = 121)
    private String idKind;

    @ApiModelProperty(name = "idNo", value = "证件号码", position = 122)
    private String idNo;

    @ApiModelProperty(name = "tradePwd", value = "支付密码", position = 123)
    private String tradePwd;
    private String remark;


}
