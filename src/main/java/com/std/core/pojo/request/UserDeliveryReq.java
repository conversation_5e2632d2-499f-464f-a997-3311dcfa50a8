package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 用户发货
*
* <AUTHOR> xiongzhiqin
* @since : 2020-07-23 10:42
*/
@Data
public class UserDeliveryReq {

      /**
       * 退货售后id
       */
      @NotNull
      @ApiModelProperty(name = "id", value = "退货售后id", position = 10)
      private Long id;

      /**
       * 物流单号
       */
      @NotBlank(message = "物流单号不能为空")
      @ApiModelProperty(name = "tracking", value = "物流单号", required = true, position = 30)
      private String tracking;

      /**
      * 物流公司id，express表的id外键
      */
      @NotNull(message = "物流公司id，express表的id外键不能为空")
      @ApiModelProperty(name = "expressId", value = "物流公司id，express表的id外键", required = true, position = 40)
      private Long expressId;

}
