package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Email;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/4/9 9:47 下午
 */
@Data
public class UserEditProfileReq {

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", position = 10)
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像", position = 11)
    private String photo;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称 长度4-12位", position = 12)
    private String nickname;

    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省", position = 13)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市", position = 20)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "area", value = "区", position = 30)
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 40)
    private String address;
}
