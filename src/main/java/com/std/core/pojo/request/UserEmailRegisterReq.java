package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/26 16:45
 */
@Data
public class UserEmailRegisterReq {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱必填")
    @ApiModelProperty(name = "mobile", value = "邮箱", required = true, position = 20)
    private String email;

}
