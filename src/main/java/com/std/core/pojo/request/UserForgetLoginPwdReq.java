package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserForgetLoginPwdReq {

    /**
     * 手机号
     */
    @NotBlank
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 10)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    @NotBlank
    @ApiModelProperty(name = "userKind", value = "用户类型- C:用户端；A:主播端；S：供应商端", required = true, position = 20)
    private String userKind;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码必填")
    @ApiModelProperty(name = "smsCaptcha", value = "验证码 业务类型=FORGET_LOGINPWD", required = true, position = 30)
    private String smsCaptcha;

    /**
     * 登录密码
     */
    @NotBlank
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true, position = 40)
    @Size(min = 6, max = 12, message = "密码长度6-12位")
    private String loginPwd;
}
