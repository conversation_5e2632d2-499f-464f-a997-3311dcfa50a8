package com.std.core.pojo.request;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/25 14:43
 */
@Data
public class UserLevelReq {

    @NotNull(message = "序号不能为空")
    @ApiModelProperty(name = "id",value = "序号",position = 10)
    private Long id;
    /**
     * 等级
     */
    @NotNull(message = "等级不能为空")
    @ApiModelProperty(name = "level",value = "等级 v1:普通等级，1：白银用户，2：黄金用户，3：钻石用户",position = 20)
    private String level;
}
