package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserLockReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "ids", value = "编号", required = true, position = 10)
    private Long[] ids;

    private String remark;

}
