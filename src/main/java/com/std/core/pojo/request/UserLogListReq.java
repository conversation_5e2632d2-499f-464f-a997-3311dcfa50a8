package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询用户日志
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
@Data
public class UserLogListReq extends BaseListReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private String userId;

    /**
     * 分类（1登录 2绑定手机号 3绑定邮箱 4绑定支付密码 5修改手机号 6修改登录密码 7修改邮箱 8修改支付密码）
     */
    @ApiModelProperty(
            name = "type",
            value = "分类（1登录 2绑定手机号 3绑定邮箱 4绑定支付密码 5修改手机号 6修改登录密码 7修改邮箱 8修改支付密码）")
    private String type;

    /**
     * 客户端（1Web 2Android 3IOS）
     */
    @ApiModelProperty(name = "client", value = "客户端（1Web 2Android 3IOS）")
    private String client;
}
