package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserLoginByMobileReq {

    /**
     * 登录名
     */
    @NotBlank(message = "手机号必填")
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 10)
    private String mobile;

    /**
     * 登录密码
     */
    @NotBlank(message = "登录密码必填")
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true, position = 20)
    private String loginPwd;

}
