package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyEmailReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 旧邮箱
     */
    @NotBlank(message = "旧邮箱必填")
    @ApiModelProperty(name = "oldEmail", value = "旧邮箱", required = true, position = 20)
    private String oldEmail;

    /**
     * 新邮箱
     */
    @NotBlank(message = "新邮箱必填")
    @ApiModelProperty(name = "newEmail", value = "新邮箱", required = true, position = 30)
    private String newEmail;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码必填")
    @ApiModelProperty(name = "smsCaptcha", value = "验证码", required = true, position = 40)
    private String smsCaptcha;

}
