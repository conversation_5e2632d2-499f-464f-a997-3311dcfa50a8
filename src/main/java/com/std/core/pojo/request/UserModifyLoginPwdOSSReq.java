package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyLoginPwdOSSReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 登录密码
     */
    @NotBlank(message = "登录密码必填")
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true, position = 20)
    @Size(min = 6, max = 12, message = "新登录密码长度6-12位")
    private String loginPwd;

}
