package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyLoginPwdReq {

    /**
     * 旧登录密码
     */
    @NotBlank(message = "旧登录密码必填")
    @ApiModelProperty(name = "oldLoginPwd", value = "旧登录密码", required = true, position = 20)
    private String oldLoginPwd;

    /**
     * 新登录密码
     */
    @NotBlank(message = "新登录密码必填")
    @ApiModelProperty(name = "newLoginPwd", value = "新登录密码", required = true, position = 30)
    @Size(min = 6, max = 12, message = "新登录密码长度6-12位")
    private String newLoginPwd;
}
