package com.std.core.pojo.request;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 */
@Data
public class UserModifyMobileOSSReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 新手机号
     */
    @NotBlank(message = "新手机号必填")
    @ApiModelProperty(name = "newMobile", value = "新手机号", required = true, position = 30)
    private String newMobile;

    @ApiModelProperty(name = "realName", value = "真实姓名", position = 35)
    private String realName;

    /**
     * 用户信息
     */
    @ApiModelProperty(name = "user", value = "用户信息", hidden = true)
    private User user;

    public UserModifyMobileOSSReq() {
    }

    public UserModifyMobileOSSReq(@NotNull Long id,
            @NotBlank(message = "新手机号必填") String newMobile) {
        this.id = id;
        this.newMobile = newMobile;
    }

    public UserModifyMobileOSSReq(@NotNull Long id,
            @NotBlank(message = "新手机号必填") String newMobile, User user) {
        this.id = id;
        this.newMobile = newMobile;
        this.user = user;
    }
}
