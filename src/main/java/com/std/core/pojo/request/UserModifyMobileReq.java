package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyMobileReq {

    /**
     * 新手机号
     */
    @NotBlank(message = "新手机号必填")
    @ApiModelProperty(name = "newMobile", value = "新手机号", required = true, position = 30)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String newMobile;

    /**
     * 原手机验证码
     */
    @NotBlank(message = "原手机验证码必填")
    @ApiModelProperty(name = "smsCaptchaOld", value = "原手机验证码 业务类型=MODIFY_MOBILE", required = true, position = 40)
    private String smsCaptchaOld;

    /**
     * 新手机验证码
     */
    @NotBlank(message = "新手机验证码必填")
    @ApiModelProperty(name = "smsCaptchaNew", value = "新手机验证码 业务类型=MODIFY_MOBILE", required = true, position = 50)
    private String smsCaptchaNew;
}
