package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 修改用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class UserModifyReq {

    /**
     * 编号
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 真实名称
     */
    @ApiModelProperty(name = "realName", value = "真实名称", position = 20)
    private String realName;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称", position = 30)
    private String nickname;

    /**
     * 性别（1男 2女）
     */
    @ApiModelProperty(name = "sex", value = "性别（1男 2女）", position = 40)
    private String sex;

    @ApiModelProperty(name = "status",value = "状态",position = 45)
    private String status;
    /**
     * 愿力值余额
     */
    @ApiModelProperty(name = "willingValue",value = "愿力值余额")
    private BigDecimal willingValue;
    /**
     * 生日
     */
    @ApiModelProperty(name = "birthday", value = "生日", position = 50)
    private String birthday;

    /**
     * 手机号
     */
//    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(name = "mobile", value = "手机号", position = 60)
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", position = 70)
    private String email;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像", position = 80)
    private String photo;

    /**
     * 证件类型
     */
    @ApiModelProperty(name = "idKind", value = "证件类型")
    private String idKind;

    /**
     * 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码")
    private String idNo;

    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省", position = 90)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市", position = 100)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "area", value = "区", position = 110)
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 120)
    private String address;
    private String remark;

}
