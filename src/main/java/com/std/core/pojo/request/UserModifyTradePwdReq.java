package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR> xieyj
 * @since : 2019-01-18 16:49
 */
@Data
public class UserModifyTradePwdReq {

    /**
     * 旧支付密码
     */
    @NotBlank(message = "旧支付密码必填")
    @ApiModelProperty(name = "oldTradePwd", value = "旧支付密码", required = true, position = 20)
    private String oldTradePwd;

    /**
     * 新支付密码
     */
    @NotBlank(message = "新支付密码必填")
    @ApiModelProperty(name = "newTradePwd", value = "新支付密码", required = true, position = 30)
    private String newTradePwd;

}
