package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/28 15:07
 */
@Data
public class UserModifyTransactionPwdOSSReq {

    /**
     * 用户编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "用户编号", required = true, position = 10)
    private Long id;

    /**
     * 登录密码
     */
    @NotBlank(message = "支付密码必填")
    @ApiModelProperty(name = "transactionPwd", value = "支付密码", required = true, position = 20)
    private String transactionPwd;

}
