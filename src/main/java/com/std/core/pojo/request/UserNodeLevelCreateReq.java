package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增节点用户
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
@Data
public class UserNodeLevelCreateReq {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号", required = true, position = 1)
    @NotNull(message = "用户编号不能为空")
    private Long userId;

    /**
     * 节点等级（手动）
     */
    @ApiModelProperty(name = "nodeLevelManual", value = "节点等级（手动）", position = 2)
    @NotNull(message = "节点等级不能为空")
    private Integer nodeLevelManual;

}
