package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询用户
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Data
public class UserPageReq extends BasePageReq {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 类型（运维中心OPS 厂商端SYS C端 诊所端CLINIC 医生端DOCTOR）
     */
    @ApiModelProperty(name = "kind", value = "类型（运维中心OPS 管理端SYS C端")
    private String kind;

    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;

    /**
     * 真实名称
     */
    @ApiModelProperty(name = "realName", value = "真实名称")
    private String realName;
    private String keywords;
    private String registerTimeStart;
    private String registerTimeEnd;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;
}
