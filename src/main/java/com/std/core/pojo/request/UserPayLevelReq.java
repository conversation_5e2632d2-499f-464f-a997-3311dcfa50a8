package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/9/15 12:31
 */
@Data
public class UserPayLevelReq {


    @ApiModelProperty(name = "level", value = "等级:2=vip2,3=vip3 ", required = true, position = 10)
    @NotNull(message = "等级不能为空")
    private Integer level;

}
