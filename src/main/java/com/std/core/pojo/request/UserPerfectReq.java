package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.Data;

/**
 * 完善用户对象
 *
 * <AUTHOR> cyl
 * @since : 2020-04-29 22:47
 */
@Data
public class UserPerfectReq {

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空")
    @ApiModelProperty(name = "idKind", value = "证件类型", required = true, position = 20)
    private String idKind;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    @ApiModelProperty(name = "idNo", value = "证件号码", required = true, position = 30)
    @Pattern(regexp = "^\\d{15}|\\d{18}|\\d{17}(\\d|X|x)$", message = "身份证号码格式不正确")
    private String idNo;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(name = "realName", value = "姓名", required = true, position = 40)
    private String realName;

    /**
     * 照片
     */
    @ApiModelProperty(name = "photo", value = "照片", position = 50)
    private String photo;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", position = 60)
    @Email(message = "邮箱格式不正确")
    private String email;


    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省", position = 70)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市", position = 80)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "area", value = "区", position = 90)
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 100)
    private String address;

}

    
    