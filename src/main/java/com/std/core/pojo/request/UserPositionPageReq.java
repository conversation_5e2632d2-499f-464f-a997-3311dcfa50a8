package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:15
 */
@Data
public class UserPositionPageReq extends BasePageReq {

    @ApiModelProperty(name = "loginName", value = "登录名")
    private String loginName;

    @ApiModelProperty(name = "realName", value = "真实姓名")
    private String realName;

    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    @ApiModelProperty(name = "position", value = "职位")
    private String position;

    @ApiModelProperty(name = "jobNumber", value = "工号")
    private String jobNumber;

    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    @ApiModelProperty(name = "clientType", value = "端类型")
    private String clientType;
}
