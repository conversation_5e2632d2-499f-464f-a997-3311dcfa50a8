package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 确认收货
 *
 * <AUTHOR> xiongzhiqin
 * @since : 2020-07-20 13:41
 */
@Data
public class UserReceiptGoodsReq {

    /**
     * 订单流水号
     */
    @NotNull
    @ApiModelProperty(name = "serialNumber", value = "订单流水号", position = 10)
    private Long serialNumber;

    /**
     * 商家id
     */
    @NotNull
    @ApiModelProperty(name = "sellerId", value = "商家id", position = 20)
    private Long sellerId;
}
