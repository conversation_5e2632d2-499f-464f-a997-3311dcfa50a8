package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/4/7 9:22 下午
 */
@Data
public class UserRegisterByOssReq {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号码不能为空")
    @ApiModelProperty(name = "mobile", value = "手机号码", required = true, position = 10)
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;


    @NotBlank(message = "登录密码不能为空")
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true, position = 40)
    @Size(min = 6, max = 12, message = "密码长度6-12位")
    private String loginPwd;

    @NotBlank(message = "昵称不能为空")
    @ApiModelProperty(name = "nickname", value = "昵称不能为空", required = true, position = 40)
    private String nickname;


}
