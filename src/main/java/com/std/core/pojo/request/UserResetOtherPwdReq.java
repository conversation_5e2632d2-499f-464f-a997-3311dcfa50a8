package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:50
 */
@Data
public class UserResetOtherPwdReq {

    @ApiModelProperty(name = "id", value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long id;

    @ApiModelProperty(name = "operatePwd", value = "操作人密码", required = true)
    @NotBlank(message = "操作人密码不能为空")
    private String operatePwd;

    @ApiModelProperty(name = "newLoginPwd", value = "用户新密码", required = true)
    @NotBlank(message = "用户新密码不能为空")
    private String newLoginPwd;
}
