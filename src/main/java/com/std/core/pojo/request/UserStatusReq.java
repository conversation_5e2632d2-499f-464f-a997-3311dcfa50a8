package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/25 15:05
 */
@Data
public class UserStatusReq {
    /**
     * 序号
     */
    @ApiModelProperty(name="id",value = "序号",position = 10)
    private Long id;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status",value = "状态 normal：正常，lock：锁定冻结，non_live：禁止开播，permanent_ban：永久封号",position = 20)
    private String status;
}
