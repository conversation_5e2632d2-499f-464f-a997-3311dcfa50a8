package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2019-01-07 17:09
 */
@Data
public class UserSubOwnerCreateReq {

    /**
     * 登录名
     */
    @ApiModelProperty(name = "loginName", value = "登录名", required = true)
    @NotBlank(message = "登录名不能为空")
    private String loginName;

    /**
     * 登录密码
     */
    @ApiModelProperty(name = "loginName", value = "登录名", required = true)
    @NotBlank(message = "登录密码不能为空")
    private String loginPwd;

    /**
     * 用户类型
     */
    @ApiModelProperty(name = "kind", value = "用户类型", required = true)
    @NotBlank(message = "所属端类型不能为空")
    private String kind;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "realName", value = "真实姓名", required = true)
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 职位
     */
    @ApiModelProperty(name = "position", value = "职位")
    private String position;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(name = "email", value = "电子邮箱")
    private String email;

    /**
     * 工号
     */
    @ApiModelProperty(name = "jobNumber", value = "工号")
    private String jobNumber;

    /**
     * 扩展字段
     */
    @ApiModelProperty(name = "extend", value = "扩展字段")
    private Long[] extend;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    private String remark;

    /**
     * 公司编号
     */
    @ApiModelProperty(name = "companyId", value = "公司编号", required = true)
    @NotNull(message = "公司编号不能为空")
    private Long companyId;
}
