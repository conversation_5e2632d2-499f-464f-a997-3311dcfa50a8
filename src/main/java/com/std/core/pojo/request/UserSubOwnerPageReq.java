package com.std.core.pojo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:15
 */
@Data
public class UserSubOwnerPageReq extends BasePageReq {

    /**
     * 登录名
     */
    @ApiModelProperty(name = "loginName", value = "登录名")
    @JsonProperty("loginName")
    private String loginNameForQuery;

    /**
     * 真实姓名
     */
    @ApiModelProperty(name = "realName", value = "真实姓名")
    private String realName;

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    /**
     * 职位
     */
    @ApiModelProperty(name = "position", value = "职位")
    private String position;

    /**
     * 工号
     */
    @ApiModelProperty(name = "jobNumber", value = "工号")
    private String jobNumber;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 所属端类型
     */
    @ApiModelProperty(name = "clientType", value = "所属端类型")
    private String clientType;

    /**
     * 单位编号
     */
    @ApiModelProperty(name = "companyId", value = "单位编号")
    private Long companyId;
}
