package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR> cyl
 * @since : 2020-04-29 10:09
 */
@Data
public class UserSysImportCReq {

    /**
     * 用户列表
     */
    @ApiModelProperty(name = "userList", value = "用户列表", required = true, position = 10)
    @NotEmpty(message = "用户列表不能为空")
    private List<UserSysCreateCReq> userList;
}

    
    