package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/4/19 8:02 下午
 */
@Data
public class UserSysModifyCReq {

    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", required = true, position = 10)
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", required = true, position = 20)
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像", required = true, position = 30)
    @NotBlank(message = "照片不能为空")
    private String photo;

    /**
     * 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码", required = true, position = 40)
    @NotBlank(message = "证件号码不能为空")
    @Pattern(regexp = "^\\d{15}|\\d{18}|\\d{17}(\\d|X|x)$", message = "身份证号码格式不正确")
    private String idNo;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "realName", value = "姓名", required = true, position = 50)
    @NotBlank(message = "姓名不能为空")
    private String realName;

    /**
     * 省
     */
    @ApiModelProperty(name = "province", value = "省", position = 60)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(name = "city", value = "市", position = 70)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(name = "area", value = "区", position = 80)
    private String area;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 90)
    private String address;

    /**
     * 登录密码
     */
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true, position = 100)
    @NotBlank(message = "登录密码不能为空")
    @Size(min = 8, max = 12, message = "密码长度8-12位")
    private String loginPwd;

    /**
     * 备注
     */
    @ApiModelProperty(name = "remark", value = "备注", position = 110)
    private String remark;
}
