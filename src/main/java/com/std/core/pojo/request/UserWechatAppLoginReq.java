package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/6 15:03
 */
@Data
public class UserWechatAppLoginReq {


    @ApiModelProperty(name = "userId", value = "用户序号", required = true)
    @NotNull(message = "用户序号不能为空")
    private Long userId;

    @ApiModelProperty(name = "mobile", value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    @NotBlank(message = "手机验证码必填")
    @ApiModelProperty(name = "smsCode", value = "手机验证码  业务类型=C_REG_WECHAT", required = true, position = 20)
    private String smsCode;
}
