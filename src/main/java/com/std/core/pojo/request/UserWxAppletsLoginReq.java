package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:49
 */
@Data
public class UserWxAppletsLoginReq {

    /**
     * code
     */
    @NotBlank(message = "code")
    @ApiModelProperty(name = "code", value = "code", required = true)
    private String code;

//
//    /**
//     * encryptedData
//     */
//    @NotBlank(message = "encryptedData")
//    @ApiModelProperty(name = "encryptedData", value = "encryptedData", required = true)
//    private String encryptedData;
//
//    /**
//     * iv
//     */
//    @NotBlank(message = "iv")
//    @ApiModelProperty(name = "iv", value = "iv", required = true)
//    private String iv;


    /**
     * encryptedData
     */
    @NotBlank(message = "encryptedDataByPhone")
    @ApiModelProperty(name = "encryptedDataByPhone", value = "encryptedDataByPhone", required = true)
    private String encryptedDataByPhone;

    /**
     * iv
     */
    @NotBlank(message = "ivByPhone")
    @ApiModelProperty(name = "ivByPhone", value = "ivByPhone", required = true)
    private String ivByPhone;

    @ApiModelProperty(name = "inviteNo", value = "邀请码")
    private String inviteNo;


}
