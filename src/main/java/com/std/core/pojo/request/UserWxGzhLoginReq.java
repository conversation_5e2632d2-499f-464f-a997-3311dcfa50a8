package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class UserWxGzhLoginReq {

    /**
     * code
     */
    @NotBlank(message = "code必填")
    @ApiModelProperty(name = "code", value = "code", required = true)
    private String code;

    /**
     * 登录密码
     */
    @NotBlank(message = "state必填")
    @ApiModelProperty(name = "state", value = "state", required = true)
    private String state;

    /**
     * 邀请码
     */
    @ApiModelProperty(name = "inviteCode", value = "邀请码")
    private String inviteCode;

    /**
     * 登录方式 0-H5 1-App
     */
    @ApiModelProperty(name = "loginType", value = "登录方式 0-H5 1-App", hidden = true)
    private String loginType;

}
