package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 新增核销
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 01:10
 */
@Data
public class VerificationRecordCreateReq {


    /**
     * 预约单编号
     */
    @NotNull(message = "预约单编号不能为空")
    @ApiModelProperty(name = "activityOrderId", value = "预约单编号", position = 40)
    private Long activityOrderId;

}
