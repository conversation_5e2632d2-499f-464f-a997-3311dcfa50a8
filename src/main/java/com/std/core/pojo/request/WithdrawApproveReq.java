package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawApproveReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 审核结果
     */
    @NotBlank
    @ApiModelProperty(name = "approveResult", value = "审核结果(0不通过 1通过)", required = true, position = 20)
    private String approveResult;

    /**
     * 审批说明
     */
    @ApiModelProperty(name = "approveNote", value = "审批说明", position = 30)
    private String approveNote;

}
