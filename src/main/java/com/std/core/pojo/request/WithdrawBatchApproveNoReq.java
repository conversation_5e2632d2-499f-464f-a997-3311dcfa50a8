package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawBatchApproveNoReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "ids", value = "编号", required = true, position = 10)
    private Long[] ids;

    /**
     * 审批说明
     */
    @ApiModelProperty(name = "approveNote", value = "审批说明", position = 30)
    private String approveNote;

}
