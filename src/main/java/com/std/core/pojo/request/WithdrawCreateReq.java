package com.std.core.pojo.request;

import com.std.core.pojo.domain.Bankcard;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 新增取现订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawCreateReq {

    /**
     * 账户编号
     */
    @NotBlank(message = "账户编号不能为空")
    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
    private String accountNumber;

    /**
     * 取现金额
     */
    @NotNull(message = "取现金额不能为空")
    @ApiModelProperty(name = "amount", value = "取现金额", required = true, position = 20)
    private BigDecimal amount;

    /**
     * 是否开票（0否 1是）
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）", position = 30)
    private String billFlag;

    /**
     * 渠道银行
     */
    @NotBlank(message = "渠道银行不能为空")
    @ApiModelProperty(name = "channelBank", value = "渠道银行", required = true, position = 40)
    private String channelBank;

    /**
     * 支付渠道账号信息
     */
    @NotBlank(message = "支付渠道账号信息不能为空")
    @ApiModelProperty(name = "channelAccountInfo", value = "支付渠道账号信息", required = true, position = 50)
    private String channelAccountInfo;

    /**
     * 支付渠道账号
     */
    @NotBlank(message = "支付渠道账号不能为空")
    @ApiModelProperty(name = "channelAccountNumber", value = "支付渠道账号", required = true, position = 60)
    private String channelAccountNumber;

    /**
     * 支付密码
     */
    @NotBlank(message = "支付密码不能为空")
    @ApiModelProperty(name = "tradePwd", value = "支付密码", required = true, position = 71)
    private String tradePwd;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明", position = 80)
    private String applyNote;

    @ApiModelProperty(name = "currency", value = "提币币种", position = 90)
    private String currency;

    public WithdrawCreateReq(WithdrawToBankcardReq req, String accountNumber, Bankcard bankcard) {
        this.accountNumber = accountNumber;
        this.amount = req.getAmount();
        this.billFlag = req.getBillFlag();
        this.tradePwd = req.getTradePwd();
        this.applyNote = req.getApplyNote();
        this.channelBank = bankcard.getBankName().concat(bankcard.getSubbranch());
        this.channelAccountInfo = bankcard.getBankUserName();
        this.channelAccountNumber = bankcard.getBankcardNumber();
    }

}
