package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询取现订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawListReq extends BaseListReq {

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 类别（B端账号，C端账号，平台账号）
     */
    @ApiModelProperty(name = "accountType", value = "类别（B端账号，C端账号，平台账号）")
    private String accountType;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 业务类型（withdraw取现 transfer 内部划转）
     */
    @ApiModelProperty(name = "bizType", value = "业务类型（withdraw取现 transfer 内部划转）")
    private String bizType;

    /**
     * 是否开票（0否 1是）
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）")
    private String billFlag;

    /**
     * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
     */
    @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）")
    private String channelType;

    /**
     * 渠道银行
     */
    @ApiModelProperty(name = "channelBank", value = "渠道银行")
    private String channelBank;

    /**
     * 状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）
     */
    @ApiModelProperty(name = "status", value = "状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）")
    private String status;

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUser", value = "申请人")
    private String applyUser;

    /**
     * 用户类型（C端用户 CLINIC诊所用户）
     */
    @ApiModelProperty(name = "applyUserKind", value = "用户类型（C端用户 CLINIC诊所用户）")
    private String applyUserKind;

    /**
     * 支付回录人
     */
    @ApiModelProperty(name = "payUser", value = "支付回录人")
    private String payUser;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetimeStart", value = "申请时间")
    private String applyDatetimeStart;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "申请时间")
    private String applyDatetimeEnd;

    /**
     * 编号
     */
    @ApiModelProperty(name = "idForQuery", value = "编号")
    private Long idForQuery;

}
