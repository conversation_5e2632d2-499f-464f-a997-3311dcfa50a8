package com.std.core.pojo.request;

import javax.validation.constraints.NotNull;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
* 修改取现订单
*
* <AUTHOR> ycj
* @since : 2020-10-20 16:29
*/
@Data
public class WithdrawModifyReq {

      /**
      * 编号
      */
      @NotNull(message = "id不能为空")
      @ApiModelProperty(name = "id", value = "编号", position = 10)
      private Long id;

      /**
      * 账户编号
      */
      @ApiModelProperty(name = "accountNumber", value = "账户编号", position = 20)
      private String accountNumber;

      /**
      * 类别（B端账号，C端账号，平台账号）
      */
      @ApiModelProperty(name = "accountType", value = "类别（B端账号，C端账号，平台账号）", position = 30)
      private String accountType;

      /**
      * 币种
      */
      @ApiModelProperty(name = "currency", value = "币种", position = 40)
      private String currency;

      /**
      * 业务类型（withdraw取现 transfer 内部划转）
      */
      @ApiModelProperty(name = "bizType", value = "业务类型（withdraw取现 transfer 内部划转）", position = 50)
      private String bizType;

      /**
      * 取现金额
      */
      @ApiModelProperty(name = "amount", value = "取现金额", position = 60)
      private BigDecimal amount;

      /**
      * 手续费
      */
      @ApiModelProperty(name = "fee", value = "手续费", position = 70)
      private BigDecimal fee;

      /**
      * 实际到账金额
      */
      @ApiModelProperty(name = "actualAmount", value = "实际到账金额", position = 80)
      private BigDecimal actualAmount;

      /**
      * 提现时账户余额
      */
      @ApiModelProperty(name = "balanceAmount", value = "提现时账户余额", position = 90)
      private BigDecimal balanceAmount;

      /**
      * 是否开票（0否 1是）
      */
      @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）", position = 100)
      private String billFlag;

      /**
      * 支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）
      */
      @ApiModelProperty(name = "channelType", value = "支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）", position = 110)
      private String channelType;

      /**
      * 渠道银行
      */
      @ApiModelProperty(name = "channelBank", value = "渠道银行", position = 120)
      private String channelBank;

      /**
      * 支付渠道账号信息
      */
      @ApiModelProperty(name = "channelAccountInfo", value = "支付渠道账号信息", position = 130)
      private String channelAccountInfo;

      /**
      * 支付渠道账号
      */
      @ApiModelProperty(name = "channelAccountNumber", value = "支付渠道账号", position = 140)
      private String channelAccountNumber;

      /**
      * 支付渠道单号
      */
      @ApiModelProperty(name = "channelOrder", value = "支付渠道单号", position = 150)
      private String channelOrder;

      /**
      * 状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）
      */
      @ApiModelProperty(name = "status", value = "状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）", position = 160)
      private String status;

      /**
      * 申请人
      */
      @ApiModelProperty(name = "applyUser", value = "申请人", position = 170)
      private Long applyUser;

      /**
      * 用户类型（C端用户 CLINIC诊所用户）
      */
      @ApiModelProperty(name = "applyUserKind", value = "用户类型（C端用户 CLINIC诊所用户）", position = 180)
      private String applyUserKind;

      /**
      * 申请说明
      */
      @ApiModelProperty(name = "applyNote", value = "申请说明", position = 190)
      private String applyNote;

      /**
      * 申请时间
      */
      @ApiModelProperty(name = "applyDatetime", value = "申请时间", position = 200)
      private String applyDatetime;

      /**
      * 审批人
      */
      @ApiModelProperty(name = "approveUser", value = "审批人", position = 210)
      private Long approveUser;

      /**
      * 审批说明
      */
      @ApiModelProperty(name = "approveNote", value = "审批说明", position = 220)
      private String approveNote;

      /**
      * 审批时间
      */
      @ApiModelProperty(name = "approveDatetime", value = "审批时间", position = 230)
      private String approveDatetime;

      /**
      * 支付回录人
      */
      @ApiModelProperty(name = "payUser", value = "支付回录人", position = 240)
      private String payUser;

      /**
      * 支付回录说明
      */
      @ApiModelProperty(name = "payNote", value = "支付回录说明", position = 250)
      private String payNote;

      /**
      * 支付渠道手续费
      */
      @ApiModelProperty(name = "payFee", value = "支付渠道手续费", position = 260)
      private BigDecimal payFee;

      /**
      * 支付回录时间
      */
      @ApiModelProperty(name = "payDatetime", value = "支付回录时间", position = 270)
      private String payDatetime;


}
