package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分页查询取现订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawPageReq extends BasePageReq {

    /**
     * 申请人
     */
    @ApiModelProperty(name = "applyUser", value = "申请人")
    private String applyUser;

    @ApiModelProperty(name = "status", value = "状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）")
    private String status;

    @ApiModelProperty(name = "statusList", value = "状态列表")
    private List<String> statusList;
    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;
    /**
     * 用户关键字信息
     */
    @ApiModelProperty(name = "keywords", value = "用户关键字信息")
    private String keywords;

    /**
     * 申请时间起
     */
    @ApiModelProperty(name = "applyDatetimeStart", value = "申请时间起")
    private String applyDatetimeStart;

    /**
     * 申请时间止
     */
    @ApiModelProperty(name = "applyDatetimeEnd", value = "申请时间止")
    private String applyDatetimeEnd;


}
