package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawPayReq {

    /**
     * 编号
     */
    @NotNull
    @ApiModelProperty(name = "id", value = "编号", required = true, position = 10)
    private Long id;

    /**
     * 审核结果
     */
    @NotBlank
    @ApiModelProperty(name = "payResult", value = "支付结果(0不通过 1通过)", required = true, position = 20)
    private String payResult;

//    /**
//     * 支付回录人
//     */
//    @ApiModelProperty(name = "payUser", value = "支付回录人", required = true, position = 30)
//    private String payUser;

    /**
     * 支付回录说明
     */
    @ApiModelProperty(name = "payNote", value = "支付回录说明", position = 40)
    private String payNote;

    /**
     * 支付渠道手续费
     */
    @ApiModelProperty(name = "payFee", value = "支付渠道手续费", position = 50)
    private BigDecimal payFee;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号", position = 60)
    private String channelOrder;

}
