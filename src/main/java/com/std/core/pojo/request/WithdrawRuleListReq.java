package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 列表查询取现规则
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-11 10:35
 */
@Data
public class WithdrawRuleListReq extends BaseListReq {

    /**
     * 针对对象
     */
    @ApiModelProperty(name = "kind", value = "针对对象")
    private String kind;

    /**
     * 类型（转账transfer 取现withdraw）
     */
    @ApiModelProperty(name = "type", value = "类型（转账transfer 取现withdraw）")
    private String type;

    /**
     * 规则名称
     */
    @ApiModelProperty(name = "name", value = "规则名称")
    private String name;

    /**
     * 币种
     */
    @ApiModelProperty(name = "symbol", value = "币种")
    private String symbol;

}
