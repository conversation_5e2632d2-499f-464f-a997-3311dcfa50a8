package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 修改取现规则
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-11 10:35
 */
@Data
public class WithdrawRuleModifyReq {

    /**
     *
     */
    @NotNull(message = "id不能为空")
    @ApiModelProperty(name = "id", value = "")
    private Integer id;

    /**
     * 最小取现金额
     */
    @ApiModelProperty(name = "withdrawMin", value = "最小取现金额")
    private BigDecimal withdrawMin;

    /**
     * 单笔最大提币量
     */
    @ApiModelProperty(name = "withdrawMax", value = "单笔最大提币量")
    private BigDecimal withdrawMax;

    /**
     * 提币步长
     */
    @ApiModelProperty(name = "withdrawStep", value = "提币步长")
    private BigDecimal withdrawStep;

    /**
     * 每人每日提现额度
     */
    @ApiModelProperty(name = "withdrawLimit", value = "每人每日提现额度")
    private BigDecimal withdrawLimit;

    /**
     * 取现手续费扣除位置: 0取现金额中 1余额中
     */
    @ApiModelProperty(name = "withdrawFeeTakeLocation", value = "取现手续费扣除位置: 0取现金额中 1余额中")
    private String withdrawFeeTakeLocation;

    /**
     * 手续费类型 0=绝对值 1=百分比
     */
    @ApiModelProperty(name = "withdrawFeeType", value = "手续费类型 0=绝对值 1=百分比")
    private String withdrawFeeType;

    /**
     * 取现手续费
     */
    @ApiModelProperty(name = "withdrawFee", value = "取现手续费")
    private BigDecimal withdrawFee;

    /**
     * 是否需要审核 0否 1是
     */
    @ApiModelProperty(name = "approveFlag", value = "是否需要审核 0否 1是")
    private String approveFlag;

    /**
     * 提币规则
     */
    @ApiModelProperty(name = "withdrawRule", value = "提币规则")
    private String withdrawRule;

    /**
     * 第一次提现最低金额
     */
    @ApiModelProperty(name = "withdrawMin1", value = "第一次提现最低金额")
    private BigDecimal withdrawMin1;

    /**
     * 第二次提现最低金额
     */
    @ApiModelProperty(name = "withdrawMin2", value = "第二次提现最低金额")
    private BigDecimal withdrawMin2;

    /**
     * 第三次开始提现最低金额
     */
    @ApiModelProperty(name = "withdrawMin3", value = "第三次开始提现最低金额")
    private BigDecimal withdrawMin3;


}
