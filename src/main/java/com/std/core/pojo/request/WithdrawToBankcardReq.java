package com.std.core.pojo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 新增取现订单
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Data
public class WithdrawToBankcardReq {

    /**
     * 账户编号
     */
//    @NotBlank(message = "账户编号")
//    @ApiModelProperty(name = "accountNumber", value = "账户编号", required = true, position = 10)
//    private String accountNumber;

    /**
     * 取现金额
     */
    @NotNull(message = "取现金额不能为空")
    @ApiModelProperty(name = "amount", value = "取现金额", required = true, position = 20)
    private BigDecimal amount;

    /**
     * 是否开票（0否 1是）
     */
    @ApiModelProperty(name = "billFlag", value = "是否开票（0否 1是）", position = 30)
    private String billFlag;

    /**
     * 银行卡编号
     */
    @NotNull(message = "银行卡编号不能为空")
    @ApiModelProperty(name = "bankcardId", value = "银行卡编号", required = true, position = 40)
    private Long bankcardId;

    /**
     * 支付密码
     */
    @NotBlank(message = "支付密码不能为空")
    @ApiModelProperty(name = "tradePwd", value = "支付密码", required = true, position = 71)
    private String tradePwd;

    /**
     * 申请说明
     */
    @ApiModelProperty(name = "applyNote", value = "申请说明", position = 80)
    private String applyNote;

}
