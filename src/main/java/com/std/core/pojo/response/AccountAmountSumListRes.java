package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountAmountSumListRes {

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种: CNY=人民币 DIAMOND=钻石")
    private String currency;

    /**
     * 总资产
     */
    @ApiModelProperty(name = "amount", value = "总资产")
    private BigDecimal amount;

    /**
     * 可用余额
     */
    @ApiModelProperty(name = "availableAmount", value = "可用余额")
    private BigDecimal availableAmount;

    /**
     * 冻结金额
     */
    @ApiModelProperty(name = "frozenAmount", value = "冻结金额")
    private BigDecimal frozenAmount;

}
