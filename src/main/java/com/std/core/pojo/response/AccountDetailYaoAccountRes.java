package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDetailYaoAccountRes {


    @ApiModelProperty(name = "yinYaoAmount", value = "阴爻数量")
    private BigDecimal yinYaoAmount;

    @ApiModelProperty(name = "yangYaoAmount", value = "阳爻数量")
    private BigDecimal yangYaoAmount;

    @ApiModelProperty(name = "yaoMilletConfigList", value = "粟")
    private List<AccountDiamondExchangeMilletRes> yaoMilletConfigList;
}
