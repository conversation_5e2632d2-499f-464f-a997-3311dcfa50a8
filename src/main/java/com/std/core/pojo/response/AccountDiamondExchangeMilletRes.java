package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDiamondExchangeMilletRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 20)
    private String name;

    /**
     * 类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:乾,1:坤,2:巽,3:坎,4:艮,5:震,6:离,7:兑}", position = 20)
    private String type;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种", position = 30)
    private String currency;

    /**
     * 所需爻图
     */
    @ApiModelProperty(name = "yaoPic", value = "所需爻图", position = 30)
    private String yaoPic;

    /**
     * 元粟图
     */
    @ApiModelProperty(name = "milletPic", value = "元粟图", position = 40)
    private String milletPic;

    /**
     * 数量
     */
    @ApiModelProperty(name = "amount", value = "数量", position = 40)
    private String amount;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 90)
    private Integer orderNo;

}
