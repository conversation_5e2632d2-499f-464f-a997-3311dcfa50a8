package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountDiamondExchangeYaoRes {

    @ApiModelProperty(name = "钻石兑换标识", value = "0:未兑换,1:已兑换")
    private String exchangeFlag;

    @ApiModelProperty(name = "diamondAmount", value = "钻石余额")
    private BigDecimal diamondAmount;

    @ApiModelProperty(name = "exchangeYinYaoAmount", value = "可兑阴爻数量")
    private BigDecimal exchangeYinYaoAmount;

    @ApiModelProperty(name = "exchangeYangYaoAmount", value = "可兑阳爻数量")
    private BigDecimal exchangeYangYaoAmount;


    @ApiModelProperty(name = "yinYaoAmount", value = "阴爻数量")
    private BigDecimal yinYaoAmount;

    @ApiModelProperty(name = "yangYaoAmount", value = "阳爻数量")
    private BigDecimal yangYaoAmount;

    @ApiModelProperty(name = "yaoMilletAmount", value = "原粟数量")
    private BigDecimal yaoMilletAmount;

    @ApiModelProperty(name = "yaoMilletConfigList", value = "粟")
    private List<AccountDiamondExchangeMilletRes> yaoMilletConfigList;

    @ApiModelProperty(name = "ruleNote", value = "兑换规则")
    private String ruleNote;
}
