package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountIntegralListRes {

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种: CNY=人民币 DIAMOND=钻石")
    private String currency;

    /**
     * 总资产
     */
    @ApiModelProperty(name = "amount", value = "总资产")
    private BigDecimal amount;

    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;


    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;

    private Long userId;
}
