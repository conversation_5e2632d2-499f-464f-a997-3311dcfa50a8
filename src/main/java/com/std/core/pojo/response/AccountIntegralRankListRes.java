package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 前端列表查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountIntegralRankListRes {


    @ApiModelProperty(name = "integralListRes", value = "排行榜信息")
    private List<AccountIntegralListRes> integralListRes;

    @ApiModelProperty(name = "content", value = "公告信息")
    private String content;

    @ApiModelProperty(name = "prizeFlag", value = "中奖标识")
    private String prizeFlag;

    @ApiModelProperty(name = "rulePic", value = "排行榜解锁规则")
    private String rulePic;
}
