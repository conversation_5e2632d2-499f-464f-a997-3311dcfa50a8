package com.std.core.pojo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.std.core.util.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2021/2/9 下午3:48
 */
@Data
public class AccountProfitLossRes {

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 10)
    private String name;

    /**
     * 金额
     */
    @ApiModelProperty(name = "amount", value = "金额", position = 20)
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    private BigDecimal amount;

}

    
    