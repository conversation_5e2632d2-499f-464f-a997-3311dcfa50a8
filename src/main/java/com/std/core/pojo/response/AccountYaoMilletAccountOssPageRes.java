package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分页查询账户
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Data
public class AccountYaoMilletAccountOssPageRes {

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;

    /**
     * 用户信息
     */
    @ApiModelProperty(name = "user", value = "用户信息")
    private User user;

    /**
     * 阴爻数量
     */
    @ApiModelProperty(name = "yinYao", value = "阴爻数量")
    private BigDecimal yinYao;

    /**
     * 阳爻数量
     */
    @ApiModelProperty(name = "yangYao", value = "阳爻数量")
    private BigDecimal yangYao;

    /**
     * 乾数量
     */
    @ApiModelProperty(name = "qianAmount", value = "乾数量")
    private BigDecimal qianAmount;

    /**
     * 巽数量
     */
    @ApiModelProperty(name = "xunAmount", value = "巽数量")
    private BigDecimal xunAmount;

    /**
     * 坎数量
     */
    @ApiModelProperty(name = "kanAmount", value = "坎数量")
    private BigDecimal kanAmount;

    /**
     * 艮数量
     */
    @ApiModelProperty(name = "genAmount", value = "艮数量")
    private BigDecimal genAmount;

    /**
     * 坤数量
     */
    @ApiModelProperty(name = "kunAmount", value = "坤数量")
    private BigDecimal kunAmount;

    /**
     * 震数量
     */
    @ApiModelProperty(name = "zhenAmount", value = "震数量")
    private BigDecimal zhenAmount;

    /**
     * 离数量
     */
    @ApiModelProperty(name = "liAmount", value = "离数量")
    private BigDecimal liAmount;

    /**
     * 兑数量
     */
    @ApiModelProperty(name = "duiAmount", value = "兑数量")
    private BigDecimal duiAmount;
}
