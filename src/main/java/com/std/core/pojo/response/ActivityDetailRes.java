package com.std.core.pojo.response;

import com.std.core.pojo.domain.ActivityTicketLine;
import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import com.std.core.enums.EActivityLocation;
import com.std.core.enums.EActivityStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@Data
public class ActivityDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 活动标题
     */
    @ApiModelProperty(name = "title", value = "活动标题", position = 30)
    private String title;

    /**
     * 活动封面
     */
    @ApiModelProperty(name = "pic", value = "活动封面", position = 50)
    private String pic;

    /**
     * 活动地址
     */
    @ApiModelProperty(name = "address", value = "活动地址", position = 60)
    private String address;

    /**
     * 售价
     */
    @ApiModelProperty(name = "price", value = "售价", position = 70)
    private BigDecimal price;

    /**
     * 入馆须知
     */
    @ApiModelProperty(name = "visitorInformation", value = "入馆须知", position = 80)
    private String visitorInformation;

    /**
     * 注意事项
     */
    @ApiModelProperty(name = "notice", value = "注意事项", position = 90)
    private String notice;

    private String status;

    /**
     * 最小购买数量
     */
    @ApiModelProperty(name = "minimumBuyNumber", value = "最小购买数量", position = 100)
    private Integer minimumBuyNumber;
    private Integer limit;

    /**
     * 最大购买数量
     */
    @ApiModelProperty(name = "maximumBuyNumber", value = "最大购买数量", position = 110)
    private Integer maximumBuyNumber;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(name = "startTime", value = "活动开始时间", position = 130)
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(name = "endTime", value = "活动结束时间", position = 140)
    private Date endTime;


    /**
     * 票券列表
     */
    @ApiModelProperty(name = "activityTicketLineList", value = "票券列表")
    private List<ActivityTicketLine> activityTicketLineList ;

    /**
     * 省份
     */
    @ApiModelProperty(name = "province", value = "省份", required = true, position = 60)
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(name = "city", value = "城市", required = true, position = 80)
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", required = true, position = 100)
    private String county;


    /**
     * 活动地名
     */
    @ApiModelProperty(name = "addressLocation", value = "活动地名", position = 60)
    private String addressLocation;


    /**
     * 售票开始时间
     */
    @ApiModelProperty(name = "buyStartTime", value = "售票开始时间", required = true, position = 130)
    private Date buyStartTime;

    /**
     * 售票结束时间
     */
    @ApiModelProperty(name = "buyEndTime", value = "售票结束时间", required = true, position = 130)
    private Date buyEndTime;


}
