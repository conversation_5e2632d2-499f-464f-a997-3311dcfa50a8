package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EActivityOrderPayType;
import com.std.core.enums.EActivityOrderStatus;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@Data
public class ActivityOrderPageRes {

    /**
     * 订单id
     */
    @ApiModelProperty(name = "id", value = "订单id", position = 10)
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 20)
    private String orderNumber;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 30)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 40)
    private Long activityId;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;

    /**
     * 票档名称
     */
    @ApiModelProperty(name = "ticketLineName", value = "票档名称", position = 60)
    private String ticketLineName;

    /**
     * 活动图片
     */
    @ApiModelProperty(name = "pic", value = "活动图片", position = 70)
    private String pic;

    /**
     * 姓名
     */
    @ApiModelProperty(name = "name", value = "姓名", position = 80)
    private String name;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "contact", value = "联系方式", position = 90)
    private String contact;

    /**
     * 实付单价
     */
    @ApiModelProperty(name = "price", value = "实付单价", position = 100)
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 110)
    private Integer number;

    /**
     * 是否修改 0:未修改 1:已经修改
     */
    @ApiModelProperty(name = "isModify", value = "是否修改 0:未修改 1:已经修改", position = 120)
    private String isModify;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "date", value = "预约日期", position = 130)
    private Date date;

    /**
     * 修改前日期
     */
    @ApiModelProperty(name = "oldDate", value = "修改前日期", position = 140)
    private Date oldDate;

    /**
     * 二维码
     */
    @ApiModelProperty(name = "codePic", value = "二维码", position = 150)
    private String codePic;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 160)
    private String payType;

    /**
     * 状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}", position = 170)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 180)
    private Date createDatetime;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 190)
    private Date payDatetime;

    /**
     * 取消时间
     */
    @ApiModelProperty(name = "cancleDatetime", value = "取消时间", position = 200)
    private Date cancleDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 210)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 220)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 230)
    private Date updateDatetime;

    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 240)
    private Integer orderNo;

    /**** Properties ****/

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付类型 {0:微信}")
    private String payTypeName;

    /**
     * 状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}")
    private String statusName;

    public String getPayTypeName() {
        if (StringUtils.isNotBlank(payType)) {
            payTypeName = EActivityOrderPayType.getActivityOrderPayType(payType).getValue();
        }

        return payTypeName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EActivityOrderStatus.getActivityOrderStatus(status).getValue();
        }

        return statusName;
    }

}
