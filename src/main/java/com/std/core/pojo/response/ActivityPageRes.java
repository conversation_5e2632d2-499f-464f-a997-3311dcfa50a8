package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EActivityLocation;
import com.std.core.enums.EActivityStatus;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@Data
public class ActivityPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(name = "name", value = "活动名称", position = 20)
    private String name;

    /**
     * 活动标题
     */
    @ApiModelProperty(name = "title", value = "活动标题", position = 30)
    private String title;

    /**
     * 位置{0:普通,1:热门}
     */
    @ApiModelProperty(name = "location", value = "位置{0:普通,1:热门}", position = 40)
    private String location;

    /**
     * 活动封面
     */
    @ApiModelProperty(name = "pic", value = "活动封面", position = 50)
    private String pic;

    /**
     * 活动地址
     */
    @ApiModelProperty(name = "address", value = "活动地址", position = 60)
    private String address;

    /**
     * 售价
     */
    @ApiModelProperty(name = "price", value = "售价", position = 70)
    private BigDecimal price;

    /**
     * 入馆须知
     */
    @ApiModelProperty(name = "visitorInformation", value = "入馆须知", position = 80)
    private String visitorInformation;

    /**
     * 注意事项
     */
    @ApiModelProperty(name = "notice", value = "注意事项", position = 90)
    private String notice;

    /**
     * 最小购买数量
     */
    @ApiModelProperty(name = "minimumBuyNumber", value = "最小购买数量", position = 100)
    private Integer minimumBuyNumber;

    /**
     * 最大购买数量
     */
    @ApiModelProperty(name = "maximumBuyNumber", value = "最大购买数量", position = 110)
    private Integer maximumBuyNumber;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(name = "startTime", value = "活动开始时间", position = 130)
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(name = "endTime", value = "活动结束时间", position = 140)
    private Date endTime;

    /**
     * 状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:未开始,2:售票中,3:已结束,4:已下架}", position = 150)
    private String status;



    /**
     * 售票开始时间
     */
    @ApiModelProperty(name = "buyStartTime", value = "售票开始时间", required = true, position = 130)
    private Date buyStartTime;

    /**
     * 售票结束时间
     */
    @ApiModelProperty(name = "buyEndTime", value = "售票结束时间", required = true, position = 130)
    private Date buyEndTime;

    private Integer limit;


}
