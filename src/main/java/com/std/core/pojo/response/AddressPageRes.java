package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@Data
public class AddressPageRes {

    /**
     * 自增主键
     */
    @ApiModelProperty(name = "id", value = "自增主键", position = 10)
    private Long id;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(name = "name", value = "收货人姓名", position = 30)
    private String name;

    /**
     * 是否默认地址1是，0否,一个用户只能有一个默认地址
     */
    @ApiModelProperty(name = "isDefault", value = "是否默认地址1是，0否,一个用户只能有一个默认地址", position = 40)
    private String isDefault;

    /**
     * 省份ID对应area表中的id
     */
    @ApiModelProperty(name = "provinceId", value = "省份ID对应area表中的id", position = 60)
    private Long provinceId;

    /**
     * 省份名称
     */
    @ApiModelProperty(name = "province", value = "省份名称", position = 50)
    private String province;

    /**
     * 城市ID对应area表中的id
     */
    @ApiModelProperty(name = "cityId", value = "城市ID对应area表中的id", position = 80)
    private Long cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(name = "city", value = "城市名称", position = 70)
    private String city;

    /**
     * 区/县ID对应area表中的id
     */
    @ApiModelProperty(name = "countyId", value = "区/县ID对应area表中的id", position = 100)
    private Long countyId;

    /**
     * 区/县
     */
    @ApiModelProperty(name = "county", value = "区/县", position = 90)
    private String county;

    /**
     * 详细地址
     */
    @ApiModelProperty(name = "address", value = "详细地址", position = 110)
    private String address;

    /**
     * 联系方式
     */
    @ApiModelProperty(name = "phone", value = "联系方式", position = 120)
    private String phone;

}
