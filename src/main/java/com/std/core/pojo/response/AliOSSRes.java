package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AliOSSRes {

    @ApiModelProperty(name = "securityToken", value = "securityToken")
    private String securityToken;

    @ApiModelProperty(name = "accessKeySecret", value = "accessKeySecret")
    private String accessKeySecret;

    @ApiModelProperty(name = "accessKeyId", value = "accessKeyId")
    private String accessKeyId;

    @ApiModelProperty(name = "expiration", value = "expiration")
    private String expiration;

    @ApiModelProperty(name = "bucket", value = "bucket")
    private String bucket;

    @ApiModelProperty(name = "endpoint", value = "endpoint")
    private String endpoint;

    @ApiModelProperty(name = "ossEndpoint", value = "ossEndpoint")
    private String ossEndpoint;

    @ApiModelProperty(name = "filePath", value = "filePath")
    private String filePath;


}
