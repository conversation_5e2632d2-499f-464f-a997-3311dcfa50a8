package com.std.core.pojo.response;

import com.std.core.pojo.domain.Article;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> zhoudong
 * @since : 2020-09-01 14:25
 */
@Data
public class ArticleRes {

    @ApiModelProperty(name = "typeId", value = "文章类型id")
    private Long typeId;

    @ApiModelProperty(name = "typeName", value = "类型name")
    private String typeName;

    @ApiModelProperty(name = "articleList", value = "该分类的文章列表")
    List<Article> articleList;


}

    
    