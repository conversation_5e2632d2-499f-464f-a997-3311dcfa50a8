package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EBigActivityOrderPayType;
import com.std.core.enums.EBigActivityOrderStatus;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
@Data
public class BigActivityOrderPageRes {

    /**
     * 订单id
     */
    @ApiModelProperty(name = "id", value = "订单id", position = 10)
    private Long id;

    /**
     * 流水号
     */
    @ApiModelProperty(name = "serialNumber", value = "流水号", position = 20)
    private String serialNumber;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 30)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 40)
    private Long activityId;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;

    /**
     * 总价
     */
    @ApiModelProperty(name = "totalPrice", value = "总价", position = 60)
    private BigDecimal totalPrice;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 70)
    private Integer number;

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付类型 {0:微信}", position = 80)
    private String payType;

    /**
     * 状态 {0:待支付,1:支付失败,2:支付成功}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待支付,1:支付失败,2:支付成功}", position = 90)
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 110)
    private Date payDatetime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 120)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 130)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 140)
    private Date updateDatetime;

    /**** Properties ****/

    /**
     * 支付类型 {0:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付类型 {0:微信}")
    private String payTypeName;

    /**
     * 状态 {0:待支付,1:支付失败,2:支付成功}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待支付,1:支付失败,2:支付成功}")
    private String statusName;

    public String getPayTypeName() {
        if (StringUtils.isNotBlank(payType)) {
            payTypeName = EBigActivityOrderPayType.getBigActivityOrderPayType(payType).getValue();
        }

        return payTypeName;
    }

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EBigActivityOrderStatus.getBigActivityOrderStatus(status).getValue();
        }

        return statusName;
    }

}
