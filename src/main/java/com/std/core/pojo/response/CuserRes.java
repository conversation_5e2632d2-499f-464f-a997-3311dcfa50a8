package com.std.core.pojo.response;

import com.std.core.enums.ECuserAgentStatus;
import com.std.core.enums.ECuserLevel;
import com.std.core.enums.EUserStatus;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class CuserRes {

    /**
     * 主键ID
     */
    @ApiModelProperty(name = "id", value = "主键ID", position = 10)
    private Long id;

    /**
     * 昵称修改标识
     */
    @ApiModelProperty(name = "nicknameChangeFlag", value = "昵称修改标识", position = 20)
    private String nicknameChangeFlag;


    /**
     * 登录名称
     */
    @ApiModelProperty(name = "loginName", value = "登录名称")
    private String loginName;


    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    /**
     * 头像
     */
    @ApiModelProperty(name = "photo", value = "头像")
    private String photo;


    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 预约状态 0:正常1:限制
     */
    @ApiModelProperty(name = "reservationStatus", value = "预约状态 0:正常1:限制")
    private String reservationStatus;

    /**
     * 预约过期次数
     */
    @ApiModelProperty(name = "reservationExpiredNumber", value = "预约过期次数")
    private Integer reservationExpiredNumber;

    /**
     * 预约限制解封时间
     */
    @ApiModelProperty(name = "reservationUnsealTime", value = "预约限制解封时间")
    private Date reservationUnsealTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 100)
    private Date createDatetime;
}

