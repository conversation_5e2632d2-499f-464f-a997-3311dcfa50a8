package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryDetailRes {

    @ApiModelProperty(value = "活动收入金额")
    private BigDecimal activityIncome;

    @ApiModelProperty(value = "商品收入金额")
    private BigDecimal goodsIncome;

}
