package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Data
public class DailyIncomeSummaryListRes {


    @ApiModelProperty(value = "时间分组（如：2025-04-01 / 2025-04 / 2025-04-14）")
    private String timeGroup;

    @ApiModelProperty(value = "收入金额")
    private BigDecimal income;


}
