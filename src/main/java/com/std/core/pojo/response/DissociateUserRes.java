package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> z<PERSON><PERSON>
 * @since : 2020-09-01 14:25
 */
@Data
public class DissociateUserRes {

    @ApiModelProperty(name = "userId", value = "用户id", position = 10)
    private Long userId;

    @ApiModelProperty(name = "nickname", value = "用户昵称", position = 20)
    private String nickname;

    @ApiModelProperty(name = "realName", value = "真实姓名", position = 25)
    private String realName;
    @ApiModelProperty(name = "mobile", value = "用户手机号", position = 30)
    private String mobile;


    @ApiModelProperty(name = "levelName", value = "等级名称", position = 60)
    private String levelName;

    @ApiModelProperty(name = "isNext", value = "是否可以下拉", position = 90)
    private Integer isNext;


}


