package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EFreshNewsType;
import com.std.core.enums.EFreshNewsStatus;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
@Data
public class FreshNewsDetailRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 封面图片
     */
    @ApiModelProperty(name = "pic", value = "封面图片", position = 20)
    private String pic;

    /**
     * 类型{0:跳转到外部,1:跳转本系统}
     */
    @ApiModelProperty(name = "type", value = "类型{0:跳转到外部,1:跳转本系统}", position = 30)
    private String type;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", position = 50)
    private String content;

    /**
     * 内容类型
     */
    @ApiModelProperty(name = "content", value = "内容类型", position = 50)
    private String contentType;



    /**
     * 发布时间
     */
    @ApiModelProperty(name = "upDatetime", value = "发布时间", position = 70)
    private Date upDatetime;



    /**
     * 顺序
     */
    @ApiModelProperty(name = "orderNo", value = "顺序", position = 140)
    private Integer orderNo;



}
