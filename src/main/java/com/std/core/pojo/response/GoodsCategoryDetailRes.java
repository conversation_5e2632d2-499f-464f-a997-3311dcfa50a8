package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EGoodsCategoryStatus;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
@Data
public class GoodsCategoryDetailRes {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 类型0:大类 1:小类
     */
    @ApiModelProperty(name = "type", value = "类型0:大类 1:小类", position = 20)
    private String type;

    /**
     * 上级类型
     */
    @ApiModelProperty(name = "parentId", value = "上级类型", position = 30)
    private String parentId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 40)
    private String name;

    /**
     * 状态 {0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:待上架,1:上架中,2:已下架}", position = 50)
    private String status;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 60)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 70)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 80)
    private Date updateDatetime;

    /**
     * UI序号
     */
    @ApiModelProperty(name = "orderNo", value = "UI序号", position = 90)
    private Integer orderNo;

    /**** Properties ****/

    /**
     * 状态 {0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待上架,1:上架中,2:已下架}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EGoodsCategoryStatus.getGoodsCategoryStatus(status).getValue();
        }

        return statusName;
    }

}
