package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import org.apache.commons.lang3.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.std.core.enums.EGoodsStatus;

/**
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@Data
public class GoodsPageRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型序号
     */
    @ApiModelProperty(name = "typeId", value = "类型序号", position = 20)
    private Long typeId;

    /**
     * 商品名称
     */
    @ApiModelProperty(name = "name", value = "商品名称", position = 30)
    private String name;

    /**
     * 商品价格
     */
    @ApiModelProperty(name = "price", value = "商品价格", position = 40)
    private BigDecimal price;

    /**
     * 商品图片
     */
    @ApiModelProperty(name = "pic", value = "商品图片", position = 50)
    private String pic;

    /**
     * 商品详情
     */
    @ApiModelProperty(name = "content", value = "商品详情", position = 60)
    private String content;

    /**
     * 状态{0:待上架,1:上架中,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态{0:待上架,1:上架中,2:已下架}", position = 70)
    private String status;

}
