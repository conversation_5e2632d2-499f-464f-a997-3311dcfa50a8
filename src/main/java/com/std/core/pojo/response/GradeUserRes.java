package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> z<PERSON><PERSON>
 * @since : 2020-09-01 14:25
 */
@Data
public class GradeUserRes {

    @ApiModelProperty(name = "userId", value = "用户id", position = 10)
    private Long userId;

    @ApiModelProperty(name = "nickname", value = "用户昵称", position = 20)
    private String nickname;

    @ApiModelProperty(name = "realName", value = "真实姓名", position = 25)
    private String realName;
    @ApiModelProperty(name = "mobile", value = "用户手机号", position = 30)
    private String mobile;

    @ApiModelProperty(name = "levelName", value = "等级名称", position = 60)
    private String levelName;

    @ApiModelProperty(name = "amount", value = "本人业绩", position = 70)
    private BigDecimal amount;

    @ApiModelProperty(name = "isNext", value = "是否可以下拉", position = 75)
    private Integer isNext;

    @ApiModelProperty(name = "teamGeneralCount", value = "团队直推人数量", position = 80)
    private Integer teamGeneralCount;

    @ApiModelProperty(name = "teamGeneralList", value = "团队直推人列表", position = 81)
    private List<User> teamGeneralList;

    @ApiModelProperty(name = "teamCount", value = "团队人数量", position = 90)
    private Integer teamCount;

    @ApiModelProperty(name = "teamCount", value = "团队金额", position = 100)
    private BigDecimal teamAmount;

}


