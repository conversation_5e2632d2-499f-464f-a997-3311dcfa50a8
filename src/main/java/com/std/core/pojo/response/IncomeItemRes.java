package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收入时间段明细响应
 */
@Data
public class IncomeItemRes {

    public IncomeItemRes(){}

    @ApiModelProperty(value = "时间分组（如：2025-04-01 / 2025-04 / 2025-04-14）")
    private String timeGroup;

    @ApiModelProperty(value = "收入金额")
    private BigDecimal income;

    // ✅ 补上这个构造函数
    public IncomeItemRes(String timeGroup, BigDecimal income) {
        this.timeGroup = timeGroup;
        this.income = income;
    }
}
