package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class IncomeStatisticsByDayRes {

    @ApiModelProperty(name = "dayDate", value = "日期")
    private String dayDate;

    @ApiModelProperty(name = "type", value = "类型： 1=推荐收益 2=团队收益")
    private String type;

    @ApiModelProperty(name = "amountType", value = "金额类型：0消费金，1推荐商家金")
    private String amountType;

    @ApiModelProperty(name = "amount", value = "金额数量")
    private BigDecimal amount;


}
