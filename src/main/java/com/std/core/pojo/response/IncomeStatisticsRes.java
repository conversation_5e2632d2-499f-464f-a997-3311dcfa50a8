package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class IncomeStatisticsRes {

    @ApiModelProperty(name = "useSelfIncome", value = "用户自身收益奖")
    private BigDecimal useSelfIncome;

    @ApiModelProperty(name = "userTotalIncome", value = "用户推荐奖")
    private BigDecimal userTotalIncome;

    @ApiModelProperty(name = "userTeamTotalIncome", value = "用户团队奖")
    private BigDecimal userTeamTotalIncome;

    @ApiModelProperty(name = "recommendTotalIncome", value = "推荐商家奖")
    private BigDecimal recommendSellerTotalIncome;

    @ApiModelProperty(name = "recommendAnchorTotalIncome", value = "推荐主播奖")
    private BigDecimal recommendAnchorTotalIncome;


}
