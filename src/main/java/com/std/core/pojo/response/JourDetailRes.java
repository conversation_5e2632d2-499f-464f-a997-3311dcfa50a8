package com.std.core.pojo.response;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class JourDetailRes extends BaseDo {

    @ApiModelProperty(name = "transAmount", value = "变动金额")
    private BigDecimal transAmount;

    @ApiModelProperty(name = "refType", value = "1一口价 2拼单 3乐拍")
    private String refType;

    @ApiModelProperty(name = "thumb", value = "商品图")
    private String thumb;

    @ApiModelProperty(name = "name", value = "商品名称")
    private String name;

    @ApiModelProperty(name = "price", value = "商品价格")
    private BigDecimal price;

    @ApiModelProperty(name = "bizTypeNote", value = "交易种类")
    private String bizTypeNote;

    @ApiModelProperty(name = "refNo", value = "交易号")
    private String refNo;

    @ApiModelProperty(name = "time", value = "时间")
    private Date time;
    

}
