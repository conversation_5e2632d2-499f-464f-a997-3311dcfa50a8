package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/10/28 9:13 下午
 */
@Data
public class JourMyPageRes {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 流水类型（1余额流水 2冻结流水）
     */
    @ApiModelProperty(name = "type", value = "流水类型（1余额流水 2冻结流水）")
    private String type;

    /**
     * 用户编号
     */
    @ApiModelProperty(name = "userId", value = "用户编号")
    private Long userId;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 币种
     */
    @ApiModelProperty(name = "currency", value = "币种")
    private String currency;

    /**
     * 业务小类
     */
    @ApiModelProperty(name = "bizType", value = "业务小类")
    private String bizType;

    /**
     * 业务小类说明
     */
    @ApiModelProperty(name = "bizNote", value = "业务小类说明")
    private String bizNote;

    /**
     * 变动金额
     */
    @ApiModelProperty(name = "transAmount", value = "变动金额")
    private BigDecimal transAmount;

    /**
     * 变动前金额
     */
    @ApiModelProperty(name = "preAmount", value = "变动前金额")
    private BigDecimal preAmount;

    /**
     * 变动后金额
     */
    @ApiModelProperty(name = "postAmount", value = "变动后金额")
    private BigDecimal postAmount;

    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间")
    private Date createDatetime;


}

    
    