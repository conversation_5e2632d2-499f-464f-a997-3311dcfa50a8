package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MyOrderRes {
    /**
     * 订单id
     */
    @ApiModelProperty(name = "id", value = "订单id", position = 10)
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "orderNumber", value = "订单号", position = 20)
    private String orderNumber;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "userId", value = "用户ID", position = 30)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 活动id
     */
    @ApiModelProperty(name = "activityId", value = "活动id", position = 40)
    private Long activityId;


    @ApiModelProperty(name = "activityName", value = "活动名称", position = 40)
    private String activityName;

    /**
     * 票档id
     */
    @ApiModelProperty(name = "ticketLineId", value = "票档id", position = 50)
    private Long ticketLineId;

    /**
     * 票档名称
     */
    @ApiModelProperty(name = "ticketLineName", value = "票档名称", position = 60)
    private String ticketLineName;

    /**
     * 活动图片
     */
    @ApiModelProperty(name = "pic", value = "活动图片", position = 70)
    private String pic;


    /**
     * 实付单价
     */
    @ApiModelProperty(name = "price", value = "实付单价", position = 100)
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(name = "number", value = "数量", position = 110)
    private Integer number;

    /**
     * 预约日期
     */
    @ApiModelProperty(name = "date", value = "预约日期", position = 130)
    private Date date;

    /**
     * 状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}
     */
    @ApiModelProperty(name = "status", value = "状态 {0:预约成功,未使用,1:已使用,2:已过期,3:取消中,4:已取消}", position = 170)
    private String status;

}
