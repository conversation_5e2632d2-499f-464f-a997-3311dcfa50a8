package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/24 16:36
 */
@Data
public class MyTeamUserRes {

    @ApiModelProperty(name = "subUserCount", value = "直推人数")
    private Long subUserCount;

    @ApiModelProperty(name = "teamUserCount", value = "团队人数")
    private Long teamUserCount;

    @ApiModelProperty(name = "teamPerformance", value = "团队总业绩")
    private BigDecimal teamPerformance;

}
