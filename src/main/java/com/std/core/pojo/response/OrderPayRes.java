package com.std.core.pojo.response;

import com.std.core.enums.EBoolean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> silver
 * @since : 2020-03-13 15:15
 */
@Data
public class OrderPayRes {

    @ApiModelProperty(name = "orderId", value = "订单编号")
    Long orderId;


    @ApiModelProperty(name = "wechatAppPayInfo", value = "微信信息")
    WechatAppPayInfo wechatAppPayInfo;



    private String buySuccessFlag = EBoolean.NO.getCode();

    private String isEnough = EBoolean.YES.getCode();

    @ApiModelProperty(name = "isPassword", value = "是否已设置支付密码 0:否，1：是")
    private String isPassword = EBoolean.YES.getCode();

    public OrderPayRes() {
    }

    public OrderPayRes(Long orderId) {
        this.orderId = orderId;
    }


    public OrderPayRes(Long orderId, WechatAppPayInfo wechatAppPayInfo) {
        this.orderId = orderId;
        this.wechatAppPayInfo = wechatAppPayInfo;
    }

    public OrderPayRes(Long orderId, String buySuccessFlag,WechatAppPayInfo wechatAppPayInfo) {
        this.orderId = orderId;
        this.buySuccessFlag = buySuccessFlag;
        this.wechatAppPayInfo = wechatAppPayInfo;
    }

    public OrderPayRes(Long orderId, String buySuccessFlag) {
        this.orderId = orderId;
        this.buySuccessFlag = buySuccessFlag;
    }
}
