package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> zhoudong
 * @since : 2020-09-01 14:25
 */
@Data
public class RecommendUserRes {

    @ApiModelProperty(name = "userId", value = "用户id", position = 10)
    private Long userId;

    @ApiModelProperty(name = "nickname", value = "用户昵称", position = 20)
    private String nickname;

    @ApiModelProperty(name = "realName", value = "真实姓名", position = 25)
    private String realName;
    @ApiModelProperty(name = "mobile", value = "用户手机号", position = 30)
    private String mobile;

    @ApiModelProperty(name = "status", value = "用户状态", position = 40)
    private String status;

    @ApiModelProperty(name = "levelName", value = "等级名称", position = 60)
    private String levelName;

    @ApiModelProperty(name = "referUserCount", value = "推荐人数量", position = 90)
    private Integer referUserCount;

    @ApiModelProperty(name = "amount", value = "个人业绩", position = 90)
    private BigDecimal amount;


}


