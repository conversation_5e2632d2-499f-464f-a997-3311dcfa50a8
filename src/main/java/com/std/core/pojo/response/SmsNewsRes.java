package com.std.core.pojo.response;

import com.std.core.enums.ESmsStatus;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/30 13:25
 */
@Data
public class SmsNewsRes {
    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;
    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 15)
    private Long userId;
    /**
     * 标题
     */
    @ApiModelProperty(name = "title", value = "标题", position = 20)
    private String title;
    /**
     * 内容
     */
    @ApiModelProperty(name = "content", value = "内容", position = 30)
    private String content;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status", value = "状态", position = 40)
    private String status;
    /**
     * 昵称
     */
    @ApiModelProperty(name = "nickname", value = "昵称", position = 50)
    private String nickname;
    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile", value = "手机号", position = 60)
    private String mobile;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "creatorName", value = "创建人", position = 70)
    private String creatorName;
    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 80)
    private Date createDatetime;

    @ApiModelProperty(name = "user", value = "用户信息")
    private User user;
    /*************/
    @ApiModelProperty(name = "statusName", value = "状态名称")
    private String statusName;


    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ESmsStatus.getNoticeStatus(status).getValue();
        }

        return statusName;
    }

}
