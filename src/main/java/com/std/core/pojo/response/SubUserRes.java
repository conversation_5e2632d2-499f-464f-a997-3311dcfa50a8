package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/24 16:36
 */
@Data
public class SubUserRes {

    private Long id;
    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    @ApiModelProperty(name = "mobile", value = "手机号", position = 10)
    private String mobile;

    @ApiModelProperty(name = "level", value = "等级 ", position = 20)
    private String level;

    @ApiModelProperty(name = "teamUserCount", value = "团队人数", position = 30)
    private Long teamUserCount;

    @ApiModelProperty(name = "teamPerformance", value = "团队总业绩", position = 40)
    private BigDecimal teamPerformance;

    @ApiModelProperty(name = "photo", value = "头像", position = 50)
    private String photo;


}
