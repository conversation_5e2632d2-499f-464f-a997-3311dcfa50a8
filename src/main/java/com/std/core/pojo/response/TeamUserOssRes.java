package com.std.core.pojo.response;

import com.std.core.enums.ECuserLevel;
import com.std.core.enums.EUserStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> ycj
 * @since : 2020/11/1 14:10
 */
@Data
public class TeamUserOssRes {

    @ApiModelProperty(name = "id", value = "用户序号")
    private Long id;

    @ApiModelProperty(name = "CuserId", value = "C端用户序号")
    private Long cuserId;

    @ApiModelProperty(name = "nickname", value = "昵称")
    private String nickname;

    @ApiModelProperty(name = "realName", value = "真实姓名")
    private String realName;

    @ApiModelProperty(name = "level", value = "等级")
    private String level;

    @ApiModelProperty(name = "mobile", value = "手机号")
    private String mobile;

    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    /**************/

    private String levelName;

    private String statusName;

    public String getLevelName() {
        if (StringUtils.isNotBlank(level)) {
            levelName = ECuserLevel.getCuserLevel(level).getValue();
        }
        return levelName;
    }

    public String getStatus() {
        if (StringUtils.isNotBlank(status)) {
            statusName = EUserStatus.getUserStatus(status).getValue();
        }

        return statusName;
    }
}
