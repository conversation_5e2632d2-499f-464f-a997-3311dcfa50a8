package com.std.core.pojo.response;

import com.std.common.base.BaseDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> zhou<PERSON>
 * @since : 2020-09-01 14:25
 */
@Data
public class TeamUserRes extends BaseDo {

    @ApiModelProperty(name = "userId", value = "用户id", position = 10)
    private Long userId;

    @ApiModelProperty(name = "nickname", value = "用户昵称", position = 20)
    private String nickname;

    @ApiModelProperty(name = "realName", value = "真实姓名", position = 25)
    private String realName;
    @ApiModelProperty(name = "mobile", value = "用户手机号", position = 30)
    private String mobile;

    @ApiModelProperty(name = "status", value = "用户状态", position = 40)
    private String status;

    @ApiModelProperty(name = "creditScore", value = "信用分", position = 50)
    private BigDecimal creditScore;

    @ApiModelProperty(name = "levelName", value = "等级名称", position = 60)
    private String levelName;

    @ApiModelProperty(name = "amount", value = "交易额", position = 70)
    private BigDecimal amount;

    @ApiModelProperty(name = "nodeLevel", value = "节点等级 0=无星级 1=一星 2=二星 3=三星", position = 80)
    private String nodeLevel;

    @ApiModelProperty(name = "referUserCount", value = "推荐人数量", position = 90)
    private Integer referUserCount;

    @ApiModelProperty(name = "referAmount", value = "推荐销售额", position = 100)
    private BigDecimal referAmount;

    @ApiModelProperty(name = "teamCount", value = "团队人数", position = 110)
    private Integer teamCount;

    @ApiModelProperty(name = "teamAmount", value = "团队销售额", position = 120)
    private BigDecimal teamAmount;

}

    
    