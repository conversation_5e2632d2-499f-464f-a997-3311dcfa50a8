package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> silver
 * @since : 2020-03-17 14:25
 */
@Data
public class TrustAccountIncomeRes {

    @ApiModelProperty(name = "totalAmount", value = "总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(name = "alipayAmount", value = "支付宝金额")
    private BigDecimal alipayAmount;

    @ApiModelProperty(name = "wechatAmount", value = "微信金额")
    private BigDecimal wechatAmount;

    public TrustAccountIncomeRes(BigDecimal alipayAmount, BigDecimal wechatAmount) {
        alipayAmount = alipayAmount == null ? BigDecimal.ZERO : alipayAmount;
        wechatAmount = wechatAmount == null ? BigDecimal.ZERO : wechatAmount;
        this.totalAmount = alipayAmount.add(wechatAmount);
        this.alipayAmount = alipayAmount;
        this.wechatAmount = wechatAmount;
    }
}

