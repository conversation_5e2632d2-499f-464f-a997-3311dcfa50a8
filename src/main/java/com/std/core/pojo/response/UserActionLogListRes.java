package com.std.core.pojo.response;

import com.std.core.enums.EUserActionLogType;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR> ycj
 * @since : 2024-06-17 12:44
 */
@Data
public class UserActionLogListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 类型 {0:微信解绑,1:绑定微信}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:微信解绑,1:绑定微信}", position = 20)
    private String type;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 30)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 原数据
     */
    @ApiModelProperty(name = "oldData", value = "原数据", position = 40)
    private String oldData;

    /**
     * 新数据
     */
    @ApiModelProperty(name = "newData", value = "新数据", position = 50)
    private String newData;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 类型 {0:微信解绑,1:绑定微信}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:微信解绑,1:绑定微信}")
    private String typeName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = EUserActionLogType.getUserActionLogType(type).getValue();
        }

        return typeName;
    }

}
