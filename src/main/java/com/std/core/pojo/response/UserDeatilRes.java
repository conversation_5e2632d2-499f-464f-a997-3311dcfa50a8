package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> ycj
 * @since : 2020/10/25 14:07
 */
@Data
public class UserDeatilRes {
    /**
     * 用户名称
     */
    @ApiModelProperty(name = "userName",value = "用户名称",position = 10)
    private String userName;
    /**
     * 头像
     */
    @ApiModelProperty(name = "photo",value = "头像",position = 20)
    private String photo;
    /**
     * 等级
     */
    @ApiModelProperty(name = "level",value = "等级",position = 30)
    private String level;
    /**
     * 推荐人名称
     */
    @ApiModelProperty(name = "recommenderName",value = "推荐人名称",position = 40)
    private String recommenderName;
    /**
     * 手机号
     */
    @ApiModelProperty(name = "mobile",value = "手机号",position = 50)
    private String mobile;
    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email",value = "邮箱",position = 60)
    private String email;
    /**
     * 状态
     */
    @ApiModelProperty(name = "status",value = "状态 normal：正常，lock：锁定冻结，non_live：禁止开播，permanent_ban：永久封号",position = 70)
    private String status;
    /**
     * 注册时间
     */
    @ApiModelProperty(name = "registerDatetime",value = "注册时间",position = 80)
    private Date registerDatetime;
}
