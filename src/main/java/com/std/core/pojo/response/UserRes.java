package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;


@Data
public class UserRes {

    /**
     * 用户名
     */
    @ApiModelProperty(name = "loginName", value = "用户名")
    private String loginName;

    /**
     * 有效标志
     */
    @ApiModelProperty(name = "effectFlag", value = "有效标志 1有效 0无效")
    private String effectFlag;

    /**
     * 注册时间
     */
    @ApiModelProperty(name = "registerDatetime", value = "注册时间")
    private Date registerDatetime;

    /**
     * 推荐人用户列表
     */
    private List<UserRes> refereeUserList;

}
