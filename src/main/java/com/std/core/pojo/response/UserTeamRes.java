package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;


@Data
public class UserTeamRes {

    @ApiModelProperty(name = "teamCount", value = "团队人数")
    private Integer teamCount;

    @ApiModelProperty(name = "effectCount", value = "有效人数")
    private Integer effectCount;

    @ApiModelProperty(name = "silverCount", value = "白银套餐人数")
    private Integer silverCount;

    @ApiModelProperty(name = "goldCount", value = "黄金套餐人数")
    private Integer goldCount;

    @ApiModelProperty(name = "crownCount", value = "皇冠套餐人数")
    private Integer crownCount;

    @ApiModelProperty(name = "firstResList", value = "第一等级用户列表")
    private List<UserRes> firstResList;

}
