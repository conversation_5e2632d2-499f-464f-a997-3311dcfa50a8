package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * C端用户
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Data
public class WechatAppLoginRes {


    @ApiModelProperty(name = "bindFlag", value = "是否已绑定手机号 0未绑定，1：已绑定，2：老数据单独绑手机号 ", position = 10)
    private String bindMobileFlag;

    @ApiModelProperty(name = "userId", value = "用户id", position = 20)
    private Long userId;

    @ApiModelProperty(name = "token", value = "token", position = 30)
    private String token;

    @ApiModelProperty(name = "expireTime", value = "到期时间", position = 40)
    private Long expireTime;

    @ApiModelProperty(name = "nickName", value = "昵称", position = 30)
    private String nickname;

    @ApiModelProperty(name = "photo", value = "头像", position = 30)
    private String photo;
}
