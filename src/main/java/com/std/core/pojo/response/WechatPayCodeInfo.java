package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: haiqingzheng
 * @since: 2016年12月26日 下午4:46:41
 * @history:
 */
@Data
public class WechatPayCodeInfo {

    @ApiModelProperty(name = "codeUrl", value = "微信支付二维码")
    private String codeUrl;

    @ApiModelProperty(name = "payCode", value = "橙账本流水编号")
    private String payCode;

    @ApiModelProperty(name = "appId", value = "公众号id")
    private String appId;

    @ApiModelProperty(name = "timeStamp", value = "时间戳")
    private String timeStamp;

    @ApiModelProperty(name = "nonceStr", value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(name = "wechatPackage", value = "订单详情扩展字符串")
    private String wechatPackage;

    @ApiModelProperty(name = "signType", value = "签名方式")
    private String signType;

    @ApiModelProperty(name = "paySign", value = "签名")
    private String paySign;
}
