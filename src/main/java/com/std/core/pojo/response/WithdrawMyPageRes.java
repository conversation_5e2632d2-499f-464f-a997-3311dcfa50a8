package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/10/30 1:29 下午
 */
@Data
public class WithdrawMyPageRes {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号")
    private Long id;

    /**
     * 账户编号
     */
    @ApiModelProperty(name = "accountNumber", value = "账户编号")
    private String accountNumber;

    /**
     * 取现金额
     */
    @ApiModelProperty(name = "amount", value = "取现金额")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "fee", value = "手续费")
    private BigDecimal fee;

    /**
     * 实际到账金额
     */
    @ApiModelProperty(name = "actualAmount", value = "实际到账金额")
    private BigDecimal actualAmount;

    /**
     * 渠道银行
     */
    @ApiModelProperty(name = "channelBank", value = "渠道银行")
    private String channelBank;

    /**
     * 支付渠道账号信息
     */
    @ApiModelProperty(name = "channelAccountInfo", value = "支付渠道账号信息")
    private String channelAccountInfo;

    /**
     * 支付渠道账号
     */
    @ApiModelProperty(name = "channelAccountNumber", value = "支付渠道账号")
    private String channelAccountNumber;

    /**
     * 支付渠道单号
     */
    @ApiModelProperty(name = "channelOrder", value = "支付渠道单号")
    private String channelOrder;

    /**
     * 状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）
     */
    @ApiModelProperty(name = "status", value = "状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）")
    private String status;

    /**
     * 申请时间
     */
    @ApiModelProperty(name = "applyDatetime", value = "申请时间")
    private Date applyDatetime;

}

    
    