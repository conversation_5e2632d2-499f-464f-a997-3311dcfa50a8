package com.std.core.pojo.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> ycj
 * @since : 2020/11/24 15:36
 */
@Data
public class WithdrawNoteRes {

    @ApiModelProperty(name = "content", value = "取现手续费", position = 10)
    private String content;
    /**
     * 最小取现金额
     */
    @ApiModelProperty(name = "withdrawMin", value = "最小取现金额")
    private BigDecimal withdrawMin;

    /**
     * 提币步长
     */
    @ApiModelProperty(name = "withdrawStep", value = "提币步长")
    private BigDecimal withdrawStep;
    /**
     * 每人每日提现额度
     */
    @ApiModelProperty(name = "withdrawLimit", value = "每人每日提现额度")
    private BigDecimal withdrawLimit;
    /**
     * 取现手续费扣除位置: 0取现金额中 1余额中
     */
    @ApiModelProperty(name = "withdrawFeeTakeLocation", value = "取现手续费扣除位置: 0取现金额中 1余额中")
    private String withdrawFeeTakeLocation;

    /**
     * 手续费类型 0=绝对值 1=百分比
     */
    @ApiModelProperty(name = "withdrawFeeType", value = "手续费类型 0=绝对值 1=百分比")
    private String withdrawFeeType;

    /**
     * 取现手续费
     */
    @ApiModelProperty(name = "withdrawFee", value = "取现手续费")
    private BigDecimal withdrawFee;
    /**
     * 提币规则
     */
    @ApiModelProperty(name = "withdrawRule", value = "提币规则")
    private String withdrawRule;

}
