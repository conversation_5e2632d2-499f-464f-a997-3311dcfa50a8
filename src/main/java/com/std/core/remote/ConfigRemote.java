package com.std.core.remote;

import com.std.common.exception.BizException;
import com.std.common.exception.FeignException;
import com.std.core.mapper.ConfigMapper;
import com.std.core.pojo.domain.Config;
import com.std.core.service.IConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> qianLei
 * @since : 2019-02-13 11:43
 */
@RestController
@ApiIgnore
@Api(value = "系统配置管理(Remote)", tags = "R5、系统配置管理(Remote)")
@RequestMapping(value = "/remote/config")
public class ConfigRemote {

    @Autowired
    private IConfigService configService;

    @Resource
    private ConfigMapper configMapper;

    @ApiOperation(value = "根据键查询系统配置")
    @PostMapping("/name/{key:.+}")
    public Map<String, String> selectConfigByKey(@PathVariable("key") @Valid String key) {

        try {
            Map<String, String> map = new HashMap<>();

            Config dict = configService.detailByKey(key);
            if (null != dict) {
                map.put(dict.getKey(), dict.getValue());
            }

            return map;
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "列表查询系统配置")
    @PostMapping("/list_by_condition")
    public List<Config> selectConfigByCondition(@RequestBody @Valid Config config) {
        return configMapper.selectByCondition(config);
    }
}
