package com.std.core.remote;

import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.request.DictListReq;
import com.std.core.service.IDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> xieyj
 * @since : 2019-01-22 10:53
 */
@RestController
@ApiIgnore
@Api(value = "数据字典管理(Remote)", tags = "R6、数据字典管理(Remote)")
@RequestMapping(value = "/remote/dict")
public class DictRemote {

    @Resource
    private IDictService dictService;

    @ApiOperation(value = "根据键查询数据字典")
    @PostMapping("/{parentKey}")
    public List<Dict> selectDictByKey(@PathVariable("parentKey") @Valid String key) {
        DictListReq req = new DictListReq();
        req.setParentKey(key);
        return dictService.list(req);
    }
}
