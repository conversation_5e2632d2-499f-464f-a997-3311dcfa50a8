package com.std.core.remote;

import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Group;
import com.std.core.service.IGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> CYL
 * @since : 2019/1/23 10:56
 */
@RestController
@ApiIgnore
@Api(value = "组管理(Remote)", tags = "R4、组管理(Remote)")
@RequestMapping(value = "/remote/group")
public class GroupRemote extends BaseController {

    @Autowired
    private IGroupService groupService;

    @ApiOperation(value = "详细查组")
    @PostMapping("/info/{groupId}")
    public Group selectGroupByGroupId(@PathVariable("groupId") @Valid Long groupId) {
        return groupService.info(groupId);
    }

    @ApiOperation(value = "列表查组")
    @PostMapping("/list")
    public List<Group> listGroup(@RequestBody @Valid Group condition) {
        return groupService.listGroup(condition);
    }
}

    
    