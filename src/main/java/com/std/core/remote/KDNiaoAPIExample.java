package com.std.core.remote;

import com.std.core.pojo.request.SendMessageReq;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;

public class KDNiaoAPIExample {
    public static void main(String[] args) {
        // 快递鸟API接口地址
        String apiUrl = "https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx";

        // 示例请求数据
        String requestData = "{"
                + "\"ShipperCode\": \"YTO\","
                + "\"LogisticCode\": \"YT7517218919335\""
                + "}";

        // 快递鸟的商户ID和AppKey
        String EBusinessID = "1876804";
        String AppKey = "36abcf9f-0575-4a5a-9486-da23b2ca7cbc";

        // 签名计算
        String dataSign = encrypt(requestData, AppKey);

        try {
            // 构建请求参数
            String params = "RequestData=" + encodeURIComponent(requestData)
                    + "&EBusinessID=" + EBusinessID
                    + "&RequestType=8001"
                    + "&DataSign=" + encodeURIComponent(dataSign)
                    + "&DataType=2";

            // 发送POST请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

            // 发送请求数据
            OutputStream os = connection.getOutputStream();
            os.write(params.getBytes(StandardCharsets.UTF_8));
            os.flush();
            os.close();

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String response = readInputStream(connection.getInputStream());
                System.out.println("Response: " + response);
            } else {
                System.out.println("Request failed. Response code: " + responseCode);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static String readInputStream(InputStream inputStream) {
        StringBuilder result = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }




    // 签名加密方法（Base64 + MD5）
    public static String encrypt(String content, String keyValue) {
        try {
            String data = content + keyValue;
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(String.format("%02x", b));
            }
            String md5Str = sb.toString();
            return java.util.Base64.getEncoder().encodeToString(md5Str.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // URL编码方法
    public static String encodeURIComponent(String value) {
        try {
            return java.net.URLEncoder.encode(value, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
