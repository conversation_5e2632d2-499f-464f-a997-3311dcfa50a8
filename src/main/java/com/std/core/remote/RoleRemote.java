package com.std.core.remote;

import com.std.common.exception.BizException;
import com.std.common.exception.FeignException;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.Role;
import com.std.core.service.IRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> cyl
 * @since : 2019-02-21 13:52
 */
@RestController
@ApiIgnore
@Api(value = "角色管理(Remote)", tags = "R2、角色管理(Remote)")
@RequestMapping(value = "/remote/role")
public class RoleRemote extends BaseController {

    @Autowired
    private IRoleService roleService;

    @ApiOperation(value = "创建角色")
    @PostMapping(value = "/create")
    public void createRole(@RequestBody @Valid Role role) {
        try {
            roleService.createRole(role);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "列表查角色")
    @PostMapping("/list")
    public List<Role> selectRoleList(@RequestBody @Valid Role condition) {
        return roleService.listByCondition(condition);
    }
}
