package com.std.core.remote;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.exception.BizException;
import com.std.common.exception.FeignException;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.RemoteUserPageReq;
import com.std.core.pojo.request.UserCheckMobileReq;
import com.std.core.pojo.request.UserModifyMobileOSSReq;
import com.std.core.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> xieyj
 * @since : 2019-01-22 10:48
 */
@RestController
@ApiIgnore
@Api(value = "用户管理(Remote)", tags = "R1、用户管理(Remote)")
@RequestMapping(value = "/remote/user")
public class UserRemote extends BaseController {

    @Resource
    private IUserService userService;

    @ApiOperation(value = "检查token")
    @PostMapping("/check_token")
    public Result<User> checkToken(@RequestHeader(value = "Authorization") @Valid String token) {
        User user = userService.checkToken(token);
        return new Result<>(user);
    }

    @ApiOperation(value = "详细查用户")
    @PostMapping("/info")
    public User detailByToken(@RequestHeader(value = "Authorization") @Valid String token) {
        return getUserByToken(token);
    }

    @ApiOperation(value = "列表查用户")
    @PostMapping("/list")
    public List<User> selectUserList(@RequestBody User user) {
        return userService.list(user);
    }

    @ApiOperation(value = "分页查用户")
    @PostMapping(value = "/page")
    public PageInfo page(@RequestBody @Valid RemoteUserPageReq request) {
        try {
            PageHelper.startPage(
                    request.getPageNum(),
                    request.getPageSize(),
                    SqlUtil.parseSort(request.getSort(), User.class));
            List<User> list = userService.remotePage(request);
            return new PageInfo<>(list);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getMessage());
        }
    }

    @ApiOperation(value = "查询token用户")
    @PostMapping("/check_token/{userId}")
    public User checkToken(@PathVariable("userId") @Valid Long userId) {
        try {
            return userService.tokenDetail(userId);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getErrorMsgs());
        }
    }

    @ApiOperation(value = "详细查用户")
    @PostMapping("/by_user_id/{userId}")
    public User detailByUserId(@PathVariable("userId") @Valid Long userId) {
        try {
            return userService.detail(userId);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getErrorMsgs());
        }
    }

    @ApiOperation(value = "详细查用户")
    @PostMapping("/brief_by_user_id/{userId}")
    public User detailBriefByUserId(@PathVariable("userId") @Valid Long userId) {
        try {
            return userService.detailBrief(userId);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getErrorMsgs());
        }
    }

    @ApiOperation(value = "通过身份证查询用户")
    @PostMapping("/detail_by_id_no/{idNo}")
    public User detailByIdNo(@PathVariable("idNo") @Valid String idNo) {
        return userService.detailByIdNo(idNo);
    }

    @ApiOperation(value = "查询组下用户")
    @PostMapping("/list_group/{groupId}")
    public List<Long> listByGroupId(@PathVariable("groupId") @Valid Long groupId) {
        return userService.listByGroupId(groupId);
    }

    @ApiOperation(value = "查询角色下用户")
    @PostMapping("/list_role/{roleId}")
    public List<Long> selectUserByRoleId(@PathVariable("roleId") Long roleId) {
        return userService.listByRoleId(roleId);
    }

    @ApiOperation(value = "创建用户")
    @PostMapping("/create")
    public void createUser(@RequestBody @Valid User user) {
        try {
            userService.createUser(user);
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getErrorMsgs());
        }
    }

    @ApiOperation(value = "修改用户")
    @PostMapping("/modify")
    public void modifyUser(@RequestBody @Valid User user) {
        userService.modifyUser(user);
    }

    @ApiOperation(value = "删除用户")
    @PostMapping("/remove")
    public void removeUser(@RequestParam("id") Long id, @RequestParam("userId") Long userId) {
        userService.remove(id, userId);
    }

    @ApiOperation(value = "检查手机号")
    @PostMapping("/checkMobile")
    public void checkMobile(@RequestBody @Valid UserCheckMobileReq req) {
        try {
            userService.checkMobileExist(req.getMobile(), req.getKind());
        } catch (BizException e) {
            throw new FeignException(e.getErrorCode(), e.getErrorMsgs());
        }
    }

    @ApiOperation(value = "修改手机号")
    @PostMapping("/modify_mobile")
    public void modifyMobile(
            @RequestBody @Valid UserModifyMobileOSSReq req, @ModelAttribute("ip") String ip) {
        userService.modifyMobileOSS(req, req.getUser(), ip);
    }
}
