package com.std.core.remote;

import com.std.common.utils.JWTUtil;
import com.std.core.pojo.domain.UserRole;
import com.std.core.service.IUserRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR> qianLei
 * @since : 2019-01-22 20:15
 */
@RestController
@ApiIgnore
@Api(value = "用户角色管理(Remote)", tags = "R3、用户角色管理(Remote)")
@RequestMapping(value = "/remote/user_role")
public class UserRoleRemote {

    @Resource
    private IUserRoleService userRoleService;

    @ApiOperation(value = "列表查用户角色")
    @PostMapping(value = "/list")
    public List<UserRole> list(@RequestHeader(value = "Authorization") @Valid String token) {
        return userRoleService.listByUserId(JWTUtil.getUserInfo(token));
    }

    @ApiOperation(value = "创建用户角色")
    @PostMapping(value = "/create")
    public void createUserRole(
            @RequestParam("userId") Long userId, @RequestParam("roleId") Long roleId) {
        userRoleService.allotRole(userId, roleId);
    }
}
