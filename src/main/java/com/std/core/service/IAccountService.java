package com.std.core.service;

import com.std.core.enums.EJourBizTypeUser.AssetBiz;
import com.std.core.enums.EJourCommon;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Jour;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
public interface IAccountService {

    /**
     * 分配账户
     */
    Account distributeAccount(Long userId, String kind, String currency);

    /**
     * 修改金额
     */
    <T> Account changeAmount(Account dbAccount, BigDecimal transAmount,
                             String channelType, String channelOrder, T refNo,
                             EJourCommon bizCategory, EJourCommon bizType, EJourCommon remark, Object... args);

    /**
     * 划转
     */
    <T> void transAmount(Account fromAccount, Account toAccount, BigDecimal transAmount,
                         EJourCommon bizCategory, EJourCommon fromBizType, EJourCommon toBizType,
                         T refNo, EJourCommon remark, Object... args);

    /**
     * 冻结
     */
    <T> Account frozenAmount(Account dbAccount, BigDecimal freezeAmount, EJourCommon bizCategory,
                             EJourCommon bizType, T refNo, EJourCommon remark, Object... args);

    /**
     * 解冻
     */
    <T> Account unfrozenAmount(Account dbAccount, BigDecimal unfreezeAmount,
                               EJourCommon bizCategory, EJourCommon bizType, T refNo, EJourCommon remark,
                               Object... args);

    /**
     * OSS:手动加减账户金额
     */


    void changeAccount(User operator, AccountManualChangeReq req, String ip, AssetBiz bizType);

    void changeCorpAccount(User operator, AccountManualCorpChangeReq req, String ip);

    /**
     *
     */
    void transCreditAmount(Account fromAccount, Account toAccount, BigDecimal creditAmount);

    /**
     *
     */
    Account getAccount(Long userId, String currency);

    /**
     * 锁定账户
     */
    Account getAccountForUpdate(String accountNumber);

    /**
     *
     */
    Account getAccountUnCheck(Long userId, String currency);

    /**
     *
     */
    Account getAccount(String accountNumber);

    Account getAccountById(Long id);

    Account getAccountByUserId(Long id);

    /**
     * 分页查询账户
     */
    List<Account> page(AccountPageReq req);

    /**
     * 列表查询账户
     */
    List<Account> list(AccountListReq req);

    List<Account> list(Account req);

    <T> void changeLockAmountUnJour(Account dbAccount, BigDecimal transAmount);

    <T> void changeFrozenAmountUnJour(Account dbAccount, BigDecimal transAmount);

    <T> void innerFromLockAmountToAmount(Account dbAccount, Jour jour, BigDecimal transAmount, String status);

    Account getAccount(Long userId, String type, String currency);

    Account getAccountFront(Long userId, String currency, String withdrawAmountFlag);

    List<Account> listFront(AccountListFrontReq req, User operator);

    /**
     * 查询用户金额的统计值
     */
    List<AccountAmountSumListRes> amountSumList(AccountAmountSumListReq request);


}