package com.std.core.service;

import com.std.core.pojo.domain.Action;
import com.std.core.pojo.request.ActionCreateReq;
import com.std.core.pojo.request.ActionListReq;
import com.std.core.pojo.request.ActionModifyReq;
import com.std.core.pojo.request.ActionPageReq;
import java.util.List;

public interface IActionService {

    Long create(ActionCreateReq request);

    void modify(ActionModifyReq request);

    void remove(Long id);

    Action detail(Long id);

    List<Action> page(ActionPageReq pageReq);

    List<Action> list(ActionListReq listReq);

    List<Action> listConditionByRole(List<Long> roleList);

    List<Action> selectByConditionByMenu(Long menuId);
}
