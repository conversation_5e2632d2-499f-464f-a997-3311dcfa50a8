package com.std.core.service;

import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.ActivityOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 活动预约单Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
public interface IActivityOrderService {

    /**
     * 新增活动预约单
     *
     * @param req 新增活动预约单入参
     * @param operator 操作人
     */
    OrderPayRes create(ActivityOrderCreateReq req, User operator);

    boolean canOrder(Date date, Activity activity, int number);

    /**
     * 删除活动预约单
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改活动预约单
     *
     * @param req 修改活动预约单入参
     * @param operator 操作人
     */
    void modify(ActivityOrderModifyReq req, User operator);
    void modify(ActivityOrder req);

    /**
     * 详情查询活动预约单
     *
     * @param id 主键ID
     * @return 活动预约单详情数据
     */
     ActivityOrder detail(Long id);
    ActivityOrder detailForUpdate(Long id);

    /**
     * 分页查询活动预约单
     *
     * @param req 分页查询活动预约单入参
     * @return 活动预约单分页数据
     */
     List<ActivityOrder> page(ActivityOrderPageReq req);

    /**
     * 列表查询活动预约单
     *
     * @param req 列表查询活动预约单入参
     * @return 活动预约单列表数据
     */
     List<ActivityOrder> list(ActivityOrderListReq req);

    List<ActivityOrder> list(ActivityOrder req);

    /**
     * 前端详情查询活动预约单
     *
     * @param id 主键ID
     * @return 活动预约单详情数据
     */
    ActivityOrderDetailRes detailFront(Long id);

    /**
     * 前端分页查询活动预约单
     *
     * @param req 分页查询活动预约单入参
     * @return 活动预约单分页数据
     */
     List<ActivityOrderPageRes> pageFront(ActivityOrderPageFrontReq req);

    /**
     * 前端列表查询活动预约单
     *
     * @param req 列表查询活动预约单入参
     * @return 活动预约单列表数据
     */
     List<ActivityOrderListRes> listFront(ActivityOrderListFrontReq req);

    void modifyDate(ActivityOrderModifyDateReq request, User operator);

    @Transactional(rollbackFor = Exception.class)
    void cancleOrder(Long id,User operator);

    @Transactional(rollbackFor = Exception.class)
    void cancleOrderByOss(Long id, User operator);

    List<MyOrderRes> myPageFront(MyOrderReq request, User operator);

    void serveDoCallback(Long bizCode);

    void batchCreate(List<ActivityOrder> activityOrderList);

    List<IncomeItemRes> income();

    List<IncomeItemRes> income(IncomeGroupReq req);

    IncomeRes incomeTotal(IncomeGroupReq req);

    BigDecimal detailIncomeByDate(String startDate, String endDate);
}