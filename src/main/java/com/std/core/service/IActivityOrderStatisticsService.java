package com.std.core.service;

import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.ActivityOrderStatistics;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ActivityOrderStatisticsCreateReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListFrontReq;
import com.std.core.pojo.request.ActivityOrderStatisticsModifyReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageFrontReq;
import com.std.core.pojo.response.ActivityOrderStatisticsDetailRes;
import com.std.core.pojo.response.ActivityOrderStatisticsListRes;
import com.std.core.pojo.response.ActivityOrderStatisticsPageRes;

import java.util.Date;
import java.util.List;

/**
 * 活动预约统计Service
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
public interface IActivityOrderStatisticsService {

    /**
     * 新增活动预约统计
     *
     * @param req 新增活动预约统计入参
     * @param operator 操作人
     */
    void create(ActivityOrderStatisticsCreateReq req, User operator);

    /**
     * 删除活动预约统计
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改活动预约统计
     *
     * @param req 修改活动预约统计入参
     * @param operator 操作人
     */
    void modify(ActivityOrderStatisticsModifyReq req, User operator);

    /**
     * 详情查询活动预约统计
     *
     * @param id 主键ID
     * @return 活动预约统计详情数据
     */
     ActivityOrderStatistics detail(Long id);

    /**
     * 分页查询活动预约统计
     *
     * @param req 分页查询活动预约统计入参
     * @return 活动预约统计分页数据
     */
     List<ActivityOrderStatistics> page(ActivityOrderStatisticsPageReq req);

    /**
     * 列表查询活动预约统计
     *
     * @param req 列表查询活动预约统计入参
     * @return 活动预约统计列表数据
     */
     List<ActivityOrderStatistics> list(ActivityOrderStatisticsListReq req);

    List<ActivityOrderStatistics> list(ActivityOrderStatistics req);
    /**
     * 前端详情查询活动预约统计
     *
     * @param id 主键ID
     * @return 活动预约统计详情数据
     */
    ActivityOrderStatisticsDetailRes detailFront(Long id);

    /**
     * 前端分页查询活动预约统计
     *
     * @param req 分页查询活动预约统计入参
     * @return 活动预约统计分页数据
     */
     List<ActivityOrderStatisticsPageRes> pageFront(ActivityOrderStatisticsPageFrontReq req);

    /**
     * 前端列表查询活动预约统计
     *
     * @param req 列表查询活动预约统计入参
     * @return 活动预约统计列表数据
     */
     List<ActivityOrderStatisticsListRes> listFront(ActivityOrderStatisticsListFrontReq req);

    /**
     * 增加指定活动每日预约数
     * @param activityId
     * @param orderDate
     * @param number
     */
    void addTotalTickets(Long activityId, Date orderDate, Integer number);

    /**
     * 校验所选日期是否还能预约
     * @param orderDate
     * @param activity
     * @param number
     * @return
     */
    boolean canOrder(Date orderDate, Activity activity, Integer number);

    /**
     * 查询已满预约日期
     * @param activityId
     * @param dayLimit
     * @param date
     * @return
     */
    List<Date> findFullyBookedDates(Long activityId, Integer dayLimit, Date date);
}