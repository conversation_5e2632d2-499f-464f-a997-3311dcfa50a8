package com.std.core.service;

import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 活动Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
public interface IActivityService {

    /**
     * 新增活动
     *
     * @param req      新增活动入参
     * @param operator 操作人
     */
    void create(ActivityCreateReq req, User operator);

    /**
     * 删除活动
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改活动
     *
     * @param req      修改活动入参
     * @param operator 操作人
     */
    void modify(ActivityModifyReq req, User operator);

    void modify(Activity req);

    /**
     * 详情查询活动
     *
     * @param id 主键ID
     * @return 活动详情数据
     */
    Activity detail(Long id);

    Activity detailForUpdate(Long id);

    /**
     * 分页查询活动
     *
     * @param req 分页查询活动入参
     * @return 活动分页数据
     */
    List<Activity> page(ActivityPageReq req);

    /**
     * 列表查询活动
     *
     * @param req 列表查询活动入参
     * @return 活动列表数据
     */
    List<Activity> list(ActivityListReq req);

    List<Activity> list(Activity req);

    /**
     * 前端详情查询活动
     *
     * @param id 主键ID
     * @return 活动详情数据
     */
    ActivityDetailRes detailFront(Long id);

    /**
     * 前端分页查询活动
     *
     * @param req 分页查询活动入参
     * @return 活动分页数据
     */
    List<ActivityPageRes> pageFront(ActivityPageFrontReq req);

    /**
     * 前端列表查询活动
     *
     * @param req 列表查询活动入参
     * @return 活动列表数据
     */
    List<ActivityListRes> listFront(ActivityListFrontReq req);

    @Transactional(rollbackFor = Exception.class)
    void batchUpDown(BatchUpDownReq req, User operator);

    List<OrderTimeRes> orderTime(Long id);
    @Transactional(rollbackFor = Exception.class)
    void sort(SortReq request);

    CanOrderRes canOrder(Long id,User operator);
}