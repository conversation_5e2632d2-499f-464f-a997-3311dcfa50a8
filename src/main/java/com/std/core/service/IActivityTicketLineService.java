package com.std.core.service;

import com.std.core.pojo.domain.ActivityTicketLine;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.ActivityTicketLineDetailRes;
import com.std.core.pojo.response.ActivityTicketLineListRes;
import com.std.core.pojo.response.ActivityTicketLinePageRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 票档Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
public interface IActivityTicketLineService {

    /**
     * 新增票档
     *
     * @param req 新增票档入参
     * @param operator 操作人
     */
    ActivityTicketLine create(ActivityTicketLineCreateReq req, User operator);

    void create(ActivityTicketLine req);

    /**
     * 删除票档
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改票档
     *
     * @param req 修改票档入参
     * @param operator 操作人
     */
    void modify(ActivityTicketLineModifyReq req, User operator);
    void modify(ActivityTicketLine req);
    int modifyInventory(Long id, Integer number);
    int addInventory(Long id, Integer number);
    /**
     * 详情查询票档
     *
     * @param id 主键ID
     * @return 票档详情数据
     */
     ActivityTicketLine detail(Long id);
    ActivityTicketLine detailAll(Long id);
    /**
     * 分页查询票档
     *
     * @param req 分页查询票档入参
     * @return 票档分页数据
     */
     List<ActivityTicketLine> page(ActivityTicketLinePageReq req);

    /**
     * 列表查询票档
     *
     * @param req 列表查询票档入参
     * @return 票档列表数据
     */
     List<ActivityTicketLine> list(ActivityTicketLineListReq req);

    List<ActivityTicketLine> list(ActivityTicketLine req);

    /**
     * 前端详情查询票档
     *
     * @param id 主键ID
     * @return 票档详情数据
     */
    ActivityTicketLineDetailRes detailFront(Long id);

    /**
     * 前端分页查询票档
     *
     * @param req 分页查询票档入参
     * @return 票档分页数据
     */
     List<ActivityTicketLinePageRes> pageFront(ActivityTicketLinePageFrontReq req);

    /**
     * 前端列表查询票档
     *
     * @param req 列表查询票档入参
     * @return 票档列表数据
     */
     List<ActivityTicketLineListRes> listFront(ActivityTicketLineListFrontReq req);

    @Transactional(rollbackFor = Exception.class)
    void batchUpDown(BatchUpDownReq req, User operator);

    void batchUpDown(Long id, String status);

    void addStock(ActivityTicketLineAddStockReq request, User operator);


    int sumInventoryByCondition(ActivityTicketLine request);
}