package com.std.core.service;

import com.std.core.pojo.domain.Address;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.AddressDetailRes;
import com.std.core.pojo.response.AddressListRes;
import com.std.core.pojo.response.AddressPageRes;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 收货地址Service
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
public interface IAddressService {

    /**
     * 新增收货地址
     *
     * @param req 新增收货地址入参
     * @param operator 操作人
     */
    void create(AddressCreateReq req, User operator);

    /**
     * 删除收货地址
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改收货地址
     *
     * @param req 修改收货地址入参
     * @param operator 操作人
     */
    void modify(AddressModifyReq req, User operator);

    /**
     * 详情查询收货地址
     *
     * @param id 主键ID
     * @return 收货地址详情数据
     */
     Address detail(Long id);

    /**
     * 分页查询收货地址
     *
     * @param req 分页查询收货地址入参
     * @return 收货地址分页数据
     */
     List<Address> page(AddressPageReq req);

    /**
     * 列表查询收货地址
     *
     * @param req 列表查询收货地址入参
     * @return 收货地址列表数据
     */
     List<Address> list(AddressListReq req);

    List<Address> list(Address req);

    /**
     * 前端详情查询收货地址
     *
     * @param id 主键ID
     * @return 收货地址详情数据
     */
    AddressDetailRes detailFront(Long id);

    /**
     * 前端分页查询收货地址
     *
     * @param req 分页查询收货地址入参
     * @param operator
     * @return 收货地址分页数据
     */
     List<AddressPageRes> pageFront(AddressPageFrontReq req, User operator);

    /**
     * 前端列表查询收货地址
     *
     * @param req 列表查询收货地址入参
     * @return 收货地址列表数据
     */
     List<AddressListRes> listFront(AddressListFrontReq req);

}