package com.std.core.service;

import com.std.core.enums.EJourCommon;
import java.math.BigDecimal;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:00
 */
public interface IAlipayService {

    /**
     *
     */
    void doCallback(String result);

    /**
     * 手机网站支付
     */
    <T> String getTradeWapPaySignedOrder(Long applyUser, String userKind, String toAccountNumber,
            T payGroup, String bizType, String bizNote, BigDecimal transAmount);

    /**
     * 电脑网站支付
     */
    <T> String getTradePagePaySignedOrder(Long applyUser, String userKind, String toAccountNumber,
            T payGroup, String bizType, String bizNote, BigDecimal transAmount);

    /**
     * APP支付
     */
    <T> String getTradeAppPaySignedOrder(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount);

    /**
     * 退款
     */
    <T> String doRefund(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
            Object... bizArgs);

}