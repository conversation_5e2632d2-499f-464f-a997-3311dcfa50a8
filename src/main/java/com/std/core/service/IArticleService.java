package com.std.core.service;

import com.std.core.pojo.domain.Article;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;

import java.util.List;

/**
 * 文章Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
public interface IArticleService {

    /**
     * 新增文章
     *
     * @param req      新增文章入参
     * @param operator 操作人
     */
    Long create(ArticleCreateReq req, User operator);

    /**
     * 删除文章
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改文章
     *
     * @param req      修改文章入参
     * @param operator 操作人
     */
    void modify(ArticleModifyReq req, User operator);

    /**
     * 上下架
     */
    void upDown(ArticleBatchUpDownReq req, User operator);

    /**
     * 详情查询文章
     *
     * @param id 主键ID
     * @return 文章详情数据
     */
    Article detail(Long id, String language);

    /**
     * 分页查询文章
     *
     * @param req 分页查询文章入参
     * @return 文章分页数据
     */
    List<Article> page(ArticlePageReq req);

    /**
     * PC端-文章分页显示
     *
     * @param req 分页查询文章入参
     * @return 文章分页数据
     */
    List<Article> pcPage(ArticlePageReq req);

    /**
     * 分页查询文章
     *
     * @param req 分页查询文章入参
     * @return 文章分页数据
     */
    List<Article> page(ArticlePageFrontReq req);

    /**
     * 列表查询文章
     *
     * @param req 列表查询文章入参
     * @return 文章列表数据
     */
    List<Article> list(ArticleListReq req);

    /**
     *
     */
    List<Article> list(Long typeId, String language, String status);

    /**
     * app学院热门文章
     *
     * @return
     */
    List<Article> hotArticle();
}