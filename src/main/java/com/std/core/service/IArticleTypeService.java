package com.std.core.service;

import com.std.core.pojo.domain.ArticleType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;

import java.util.List;

/**
 * 文章类型Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
public interface IArticleTypeService {

    /**
     * 新增文章类型
     *
     * @param req      新增文章类型入参
     * @param operator 操作人
     */
    Long create(ArticleTypeCreateReq req, User operator);

    /**
     * 删除文章类型
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改文章类型
     *
     * @param req      修改文章类型入参
     * @param operator 操作人
     */
    void modify(ArticleTypeModifyReq req, User operator);

    /**
     * 上下架
     */
    void upDown(ArticleTypeBatchUpDownReq req, User operator);

    /**
     * 详情查询文章类型
     *
     * @param id 主键ID
     * @return 文章类型详情数据
     */
    ArticleType detail(Long id, String language);

    /**
     * 分页查询文章类型
     *
     * @param req 分页查询文章类型入参
     * @return 文章类型分页数据
     */
    List<ArticleType> page(ArticleTypePageReq req);

    /**
     * 列表查询文章类型
     *
     * @param req 列表查询文章类型入参
     * @return 文章类型列表数据
     */
    List<ArticleType> list(ArticleTypeListReq req);


}