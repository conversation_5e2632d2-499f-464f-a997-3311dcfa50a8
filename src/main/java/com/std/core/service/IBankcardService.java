package com.std.core.service;

import com.std.core.pojo.domain.Bankcard;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.BanksListRes;

import java.util.List;

/**
 * 银行卡Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
public interface IBankcardService {

    /**
     * 新增银行卡
     *
     * @param req      新增银行卡入参
     * @param operator 操作人
     */
    void create(BankcardCreateReq req, User operator);

    /**
     * 删除银行卡
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改银行卡
     *
     * @param req      修改银行卡入参
     * @param operator 操作人
     */
    void modify(BankcardModifyReq req, User operator);

    /**
     * 上下架
     */
    void upDown(Long id, User operator);

    /**
     *
     */
    void refreshDefault(String type, Long userId, String defaultFlag);

    /**
     *
     */
    void refreshDefault(Long id, String defaultFlag, User operator);

    /**
     * 详情查询银行卡
     *
     * @param id 主键ID
     * @return 银行卡详情数据
     */
    Bankcard detail(Long id);

    /**
     * 分页查询银行卡
     *
     * @param req 分页查询银行卡入参
     * @return 银行卡分页数据
     */
    List<Bankcard> page(BankcardPageReq req);

    /**
     * 分页查询银行卡
     *
     * @param req 分页查询银行卡入参
     * @return 银行卡分页数据
     */
    List<Bankcard> page(BankcardPageFrontReq req, User operator);

    /**
     * 列表查询银行卡
     *
     * @param req 列表查询银行卡入参
     * @return 银行卡列表数据
     */
    List<Bankcard> list(BankcardListReq req);

    /**
     * 商家端-查询商家的所有银行卡
     * @param req
     * @return
     */
    List<BanksListRes> sellerList(BankcardListReq req);
}