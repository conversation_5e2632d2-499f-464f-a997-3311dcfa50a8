package com.std.core.service;

import com.std.core.pojo.domain.BigActivityOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.BigActivityOrderCreateReq;
import com.std.core.pojo.request.BigActivityOrderListReq;
import com.std.core.pojo.request.BigActivityOrderListFrontReq;
import com.std.core.pojo.request.BigActivityOrderModifyReq;
import com.std.core.pojo.request.BigActivityOrderPageReq;
import com.std.core.pojo.request.BigActivityOrderPageFrontReq;
import com.std.core.pojo.response.BigActivityOrderDetailRes;
import com.std.core.pojo.response.BigActivityOrderListRes;
import com.std.core.pojo.response.BigActivityOrderPageRes;
import java.util.List;

/**
 * 预约单大订单Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
public interface IBigActivityOrderService {

    /**
     * 新增预约单大订单
     *
     * @param req 新增预约单大订单入参
     * @param operator 操作人
     */
    void create(BigActivityOrderCreateReq req, User operator);
    void create(BigActivityOrder req);
    /**
     * 删除预约单大订单
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改预约单大订单
     *
     * @param req 修改预约单大订单入参
     * @param operator 操作人
     */
    void modify(BigActivityOrderModifyReq req, User operator);

    /**
     * 详情查询预约单大订单
     *
     * @param id 主键ID
     * @return 预约单大订单详情数据
     */
     BigActivityOrder detail(Long id);

    /**
     * 分页查询预约单大订单
     *
     * @param req 分页查询预约单大订单入参
     * @return 预约单大订单分页数据
     */
     List<BigActivityOrder> page(BigActivityOrderPageReq req);

    /**
     * 列表查询预约单大订单
     *
     * @param req 列表查询预约单大订单入参
     * @return 预约单大订单列表数据
     */
     List<BigActivityOrder> list(BigActivityOrderListReq req);
     List<BigActivityOrder> list(BigActivityOrder req);

    /**
     * 前端详情查询预约单大订单
     *
     * @param id 主键ID
     * @return 预约单大订单详情数据
     */
    BigActivityOrderDetailRes detailFront(Long id);

    /**
     * 前端分页查询预约单大订单
     *
     * @param req 分页查询预约单大订单入参
     * @return 预约单大订单分页数据
     */
     List<BigActivityOrderPageRes> pageFront(BigActivityOrderPageFrontReq req);

    /**
     * 前端列表查询预约单大订单
     *
     * @param req 列表查询预约单大订单入参
     * @return 预约单大订单列表数据
     */
     List<BigActivityOrderListRes> listFront(BigActivityOrderListFrontReq req);

    /**
     * 预约支付回调
     * @param serialNumber
     */
    void serveDoCallback(String serialNumber);

    /**
     * 查询支付超时的订单
     * @param closeTime
     * @return
     */
    List<BigActivityOrder> listTimeOutOrder(Integer closeTime);

    /**
     * 取消支付
     * @param serialNumber
     */
    void cancelBigOrder(String serialNumber);
}