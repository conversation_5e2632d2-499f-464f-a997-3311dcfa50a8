package com.std.core.service;

import com.std.core.pojo.domain.ChannelBank;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelBankBatchUpDownReq;
import com.std.core.pojo.request.ChannelBankCreateReq;
import com.std.core.pojo.request.ChannelBankListReq;
import com.std.core.pojo.request.ChannelBankModifyReq;
import com.std.core.pojo.request.ChannelBankPageFrontReq;
import com.std.core.pojo.request.ChannelBankPageReq;
import com.std.core.pojo.response.BanksListRes;

import java.util.List;

/**
 * 渠道银行Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
public interface IChannelBankService {

    /**
     * 新增渠道银行
     *
     * @param req 新增渠道银行入参
     * @param operator 操作人
     */
    void create(ChannelBankCreateReq req, User operator);

    /**
     * 删除渠道银行
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改渠道银行
     *
     * @param req 修改渠道银行入参
     * @param operator 操作人
     */
    void modify(ChannelBankModifyReq req, User operator);

    /**
     * 上下架
     */
    void upDown(ChannelBankBatchUpDownReq request, User operator);

    /**
     * 详情查询渠道银行
     *
     * @param id 主键ID
     * @return 渠道银行详情数据
     */
    ChannelBank detail(Long id);

    /**
     * 分页查询渠道银行
     *
     * @param req 分页查询渠道银行入参
     * @return 渠道银行分页数据
     */
    List<ChannelBank> page(ChannelBankPageReq req);

    /**
     * 分页查询渠道银行
     *
     * @param req 分页查询渠道银行入参
     * @return 渠道银行分页数据
     */
    List<ChannelBank> pageFront(ChannelBankPageFrontReq req);

    /**
     * 列表查询渠道银行
     *
     * @param req 列表查询渠道银行入参
     * @return 渠道银行列表数据
     */
    List<ChannelBank> list(ChannelBankListReq req);


}