package com.std.core.service;

import com.std.core.enums.EChannelType;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Charge;
import com.std.core.pojo.domain.ChargeNotice;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChargeApproveReq;
import com.std.core.pojo.request.ChargeListReq;
import com.std.core.pojo.request.ChargeOfflineReq;
import com.std.core.pojo.request.ChargeOnlineAppReq;
import com.std.core.pojo.request.ChargeOnlineWebReq;
import com.std.core.pojo.request.ChargePageReq;
import com.std.core.pojo.request.ChargeToBankcardReq;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 充值订单Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
public interface IChargeService {

    /**
     * 新增充值订单
     *
     * @param req 新增充值订单入参
     * @param operator 操作人
     */
    void create(ChargeOfflineReq req, User operator);

    /**
     *
     */
    void create(ChargeToBankcardReq req, User operator);

    /**
     *
     */
    <T> Long applyOrderOnline(Account account, String userKind, T bizNo, T payGroup,
            String bizType, String bizNote, BigDecimal transAmount,
            EChannelType channelType, Long applyUser);

    <T> Long chargeSuccess(Account account, String userKind, T bizNo, T payGroup,
            String bizType, String bizNote, BigDecimal transAmount,
            EChannelType channelType, Long applyUser);

    /**
     * 审核
     */
    void approve(ChargeApproveReq req, User operator);

    /**
     * 新增充值订单
     *
     * @param req 新增充值订单入参
     * @param operator 操作人
     */
    Object create(ChargeOnlineAppReq req, User operator);

    /**
     * 新增充值订单
     *
     * @param req 新增充值订单入参
     * @param operator 操作人
     */
    Object create(ChargeOnlineWebReq req, User operator);

    /**
     *
     */
    void callback(Long id, boolean success, String channelOrder);

    /**
     *
     */
    <T> void refreshStatusByPayGroup(T payGroup, String status);

    /**
     *
     */
    void refreshStatus(Long id, String status);

    /**
     * 详情查询充值订单
     *
     * @param id 主键ID
     * @return 充值订单详情数据
     */
    Charge detail(Long id);

    Charge detailBrief(Long id);

    /**
     *
     */
    Charge detail(String payGroup);

    /**
     * 分页查询充值订单
     *
     * @param req 分页查询充值订单入参
     * @return 充值订单分页数据
     */
    List<Charge> page(ChargePageReq req, User operator);

    /**
     * 列表查询充值订单
     *
     * @param req 列表查询充值订单入参
     * @return 充值订单列表数据
     */
    List<Charge> list(ChargeListReq req);

    /**
     * 根据用户列表获取有效人数
     */
    List<Long> selectEffectUsers(List<User> userList);

    /**
     *
     */
    List<Charge> list(List<String> channelTypeList, String status, Date applyDatetimeEnd);

    /**
     * 充值回调处理逻辑
     */
    void doChargeCallback(Boolean isSuccess, String channelNo, Long outTradeNo);


}