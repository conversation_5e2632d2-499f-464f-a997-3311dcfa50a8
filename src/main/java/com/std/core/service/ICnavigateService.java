package com.std.core.service;

import com.std.core.pojo.domain.Cnavigate;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;

import java.util.List;

/**
 * 导航Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
public interface ICnavigateService {

    /**
     * 新增导航
     *
     * @param req      新增导航入参
     * @param operator 操作人
     */
    void create(CnavigateCreateReq req, User operator, String ip);

    /**
     * 删除导航
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改导航
     *
     * @param req      修改导航入参
     * @param operator 操作人
     */
    void modify(CnavigateModifyReq req, User operator, String ip);

    /**
     * 上下架
     */
    void upDown(CNavigateBatchUpDownReq request, User operator, String ip);

    /**
     * 详情查询导航
     *
     * @param id 主键ID
     * @return 导航详情数据
     */
    Cnavigate detail(Long id);

    /**
     * 分页查询导航
     *
     * @param req 分页查询导航入参
     * @return 导航分页数据
     */
    List<Cnavigate> page(CnavigatePageReq req);

    /**
     * 列表查询导航
     *
     * @param req 列表查询导航入参
     * @return 导航列表数据
     */
    List<Cnavigate> list(CnavigateListReq req);

    /**
     * 列表查询导航
     *
     * @param req 列表查询导航入参
     * @return 导航列表数据
     */
    List<Cnavigate> list(CnavigateListFrontReq req);

    /**
     * 查询快捷入口
     *
     * @param
     * @return 导航列表数据
     */
    List<Cnavigate> quickEntryList();

}