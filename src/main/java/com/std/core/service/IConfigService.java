package com.std.core.service;

import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ConfigCreateReq;
import com.std.core.pojo.request.ConfigListReq;
import com.std.core.pojo.request.ConfigModifyReq;
import com.std.core.pojo.request.ConfigPageReq;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IConfigService {

    void create(ConfigCreateReq req, User operateUser);

    void modify(ConfigModifyReq req, User operateUser, String ip);

    Config detail(Long id);

    /**
     * 根据key详情查询
     */
    Config detailByKey(String key);

    List<Config> list(String type);

    List<Config> page(ConfigPageReq req);

    List<Config> list(ConfigListReq req);

    Map<String, String> listByType(String type, String key);

    Map<String, String> listByType(String type);

    Integer getIntegerValue(String key);

    Long getLongValue(String key);

    String getStringValue(String key);

    BigDecimal getBigDecimalValue(String key);


    List<Config> aListByType(String type, String key);
}
