package com.std.core.service;

import com.alibaba.fastjson.JSONObject;
import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.Cuser;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * C端用户Service
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
public interface ICuserService {

    /**
     * 新增C端用户
     *
     * @param req      新增C端用户入参
     * @param operator 操作人
     */
    void create(CuserCreateReq req, User operator);

    /**
     * 怀南会产品服务条款
     */
    Config detailRegisterServiceConfig();

    /**
     * 删除C端用户
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改C端用户
     *
     * @param req      修改C端用户入参
     * @param operator 操作人
     */
    void modify(CuserModifyReq req, User operator);

    /**
     * 修改C端用户
     *
     * @param cuser
     */
    void modify(Cuser cuser);

    /**
     * 设置状态
     */
    void modifyStatus(UserStatusReq req, User operator, String ip);

    /**
     * 详情查询C端用户
     *
     * @param id 主键ID
     * @return C端用户详情数据
     */
    Cuser detail(Long id);

    /**
     * 分页查询C端用户
     *
     * @param req 分页查询C端用户入参
     * @return C端用户分页数据
     */
    List<Cuser> page(CuserPageReq req);

    /**
     * 模糊查询
     */
    List<Cuser> vagueDeatil(CuserPageReq req);

    /**
     * 列表查询C端用户
     *
     * @param req 列表查询C端用户入参
     * @return C端用户列表数据
     */
    List<Cuser> list(CuserListReq req);

    List<Cuser> list();

    /**
     * C端用户注册
     */
    JSONObject register(UserRegisterReq request, String ip);

    /**
     * oss端代用户注册
     */
    User ossRegister(UserRegisterByOssReq request);


    /**
     * C端用户登录
     */
    JSONObject login(UserLoginReq request, String ip);
    JSONObject login(CUserLoginReq request, String ip);

    /**
     * 渠道商登录
     */
    JSONObject agentLogin(UserLoginReq request, String ip);

    Cuser detailByUserId(Long userId);

    /**
     * 加盟成为渠道商
     */
    void joinAgent(CuserJoinAgentReq request);

    void removeAgent(CuserRemoveAgentReq request);

    /**
     * 分页条件查询渠道商用户
     */
    List<Cuser> agentPage(CuserAgentPageReq request);

    /**
     * 修改渠道商配置
     */
    void agentSetRate(CuserAgentSetRateReq request, User operator);

    Cuser agentDetail(Long id);

    /**
     * 根据用户序号查询C端用户信息
     */
    Cuser detailByUser(Long userId);

    /**
     * 会员查询
     */
    List<UserDeatilRes> userDeatilRes();

    /**
     * 社区查询
     */
    List<TeamUserRes> teamPage(CuserPageReq request);

    JSONObject registerInstead(UserRegisterOssReq request, String ip);

    /**
     * @param request
     * @return
     */
    List<RecommendUserRes> recommendChain(CuserRecommendReq request);

    /**
     * 等级链查询
     *
     * @param request
     * @return
     */
    List<GradeUserRes> gradeChain(CuserRecommendReq request);



    /**
     * 游离
     *
     * @return
     */
    List<DissociateUserRes> dissociateChain();


    void changeReferUser(UserChangeRefereeReq request);

    @Transactional(rollbackFor = Exception.class)
    WechatAppLoginRes wxAppLogin(UserWxGzhLoginReq request, String ip);

    @Transactional(rollbackFor = Exception.class)
    WechatAppLoginRes cWechatAppLogin(UserWechatAppLoginReq request, String ip);

    void delCuserRedis(Long userId);

    @Transactional(rollbackFor = Exception.class)
    void bindWx(UserBindWxGzhLoginReq request, User operator);

    void unbindWx(User operator);

    void insert(Cuser cuser);
}
