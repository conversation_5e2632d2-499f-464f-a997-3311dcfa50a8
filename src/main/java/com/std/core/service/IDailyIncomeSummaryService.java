package com.std.core.service;

import com.std.core.pojo.domain.DailyIncomeSummary;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.DailyIncomeSummaryDetailRes;
import com.std.core.pojo.response.DailyIncomeSummaryListRes;
import com.std.core.pojo.response.DailyIncomeSummaryPageRes;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 每日收益Service
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
public interface IDailyIncomeSummaryService {

    /**
     * 新增每日收益
     *
     * @param req      新增每日收益入参
     * @param operator 操作人
     */
    void create(DailyIncomeSummaryCreateReq req, User operator);

    void create(String incomeDate, BigDecimal activityIncome, BigDecimal goodsIncome);
    /**
     * 活动收益
     *
     * @param activityAmount
     * @param incomeDate
     */
    void activityIncomeSummary(BigDecimal activityAmount, Date incomeDate);

    /**
     * 商品收益
     * @param goodsAmount
     * @param incomeDate
     */
    void goodsIncomeSummary(BigDecimal goodsAmount, Date incomeDate);

    DailyIncomeSummary detailByDate(String incomeDate);
    /**
     * 删除每日收益
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改每日收益
     *
     * @param req      修改每日收益入参
     * @param operator 操作人
     */
    void modify(DailyIncomeSummaryModifyReq req, User operator);

    /**
     * 详情查询每日收益
     *
     * @param id 主键ID
     * @return 每日收益详情数据
     */
    DailyIncomeSummary detail(Long id);

    /**
     * 分页查询每日收益
     *
     * @param req 分页查询每日收益入参
     * @return 每日收益分页数据
     */
    List<DailyIncomeSummary> page(DailyIncomeSummaryPageReq req);

    /**
     * 收益总计
     * @param request
     * @return
     */
    DailyIncomeSummaryDetailRes incomeTotal(DailyIncomeSummaryTotalReq request);

    /**
     * 列表查询每日收益
     *
     * @param req 列表查询每日收益入参
     * @return 每日收益列表数据
     */
    List<DailyIncomeSummaryListRes> list(DailyIncomeSummaryListReq req);

    /**
     * 前端详情查询每日收益
     *
     * @param id 主键ID
     * @return 每日收益详情数据
     */
    DailyIncomeSummaryDetailRes detailFront(Long id);

    /**
     * 前端分页查询每日收益
     *
     * @param req 分页查询每日收益入参
     * @return 每日收益分页数据
     */
    List<DailyIncomeSummaryPageRes> pageFront(DailyIncomeSummaryPageFrontReq req);

    /**
     * 前端列表查询每日收益
     *
     * @param req 列表查询每日收益入参
     * @return 每日收益列表数据
     */
    List<DailyIncomeSummaryListRes> listFront(DailyIncomeSummaryListFrontReq req);




}