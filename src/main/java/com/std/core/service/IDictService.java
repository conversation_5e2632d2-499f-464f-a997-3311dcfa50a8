package com.std.core.service;

import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;

import java.util.List;

/**
 * 数据字典Service
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:49
 */
public interface IDictService {

    void create(DictCreateReq req, User operateUser);

    void modify(DictModifyReq req, User operateUser, String ip);

    void remove(Integer id, User operateUser);

    /**
     * 详情查询数据字典
     *
     * @param id 主键ID
     * @return 数据字典详情数据
     */
    Dict detail(Integer id);

    /**
     * 分页查询数据字典
     *
     * @param req 分页查询数据字典入参
     * @return 数据字典分页数据
     */
    List<Dict> page(DictPageReq req);

    /**
     * 列表查询数据字典
     *
     * @param req 列表查询数据字典入参
     * @return 数据字典列表数据
     */
    List<Dict> list(DictListReq req);

    List<Dict> list(Dict req);

    List<Dict> publicList(DictPublicListReq request);
}
