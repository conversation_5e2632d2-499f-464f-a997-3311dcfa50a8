package com.std.core.service;

import com.std.core.pojo.domain.FreshNews;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.FreshNewsDetailRes;
import com.std.core.pojo.response.FreshNewsListRes;
import com.std.core.pojo.response.FreshNewsPageRes;
import java.util.List;

/**
 * 新鲜事Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 22:43
 */
public interface IFreshNewsService {

    /**
     * 新增新鲜事
     *
     * @param req 新增新鲜事入参
     * @param operator 操作人
     */
    void create(FreshNewsCreateReq req, User operator);

    /**
     * 删除新鲜事
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改新鲜事
     *
     * @param req 修改新鲜事入参
     * @param operator 操作人
     */
    void modify(FreshNewsModifyReq req, User operator);

    /**
     * 详情查询新鲜事
     *
     * @param id 主键ID
     * @return 新鲜事详情数据
     */
     FreshNews detail(Long id);

    /**
     * 分页查询新鲜事
     *
     * @param req 分页查询新鲜事入参
     * @return 新鲜事分页数据
     */
     List<FreshNews> page(FreshNewsPageReq req);

    /**
     * 列表查询新鲜事
     *
     * @param req 列表查询新鲜事入参
     * @return 新鲜事列表数据
     */
     List<FreshNews> list(FreshNewsListReq req);

    /**
     * 前端详情查询新鲜事
     *
     * @param id 主键ID
     * @return 新鲜事详情数据
     */
    FreshNewsDetailRes detailFront(Long id);

    /**
     * 前端分页查询新鲜事
     *
     * @param req 分页查询新鲜事入参
     * @return 新鲜事分页数据
     */
     List<FreshNewsPageRes> pageFront(FreshNewsPageFrontReq req);

    /**
     * 前端列表查询新鲜事
     *
     * @param req 列表查询新鲜事入参
     * @return 新鲜事列表数据
     */
     List<FreshNewsListRes> listFront(FreshNewsListFrontReq req);

    void batchUpDown(BatchUpDownReq request, User operator);

    void sort(SortReq request);

}