package com.std.core.service;

import com.std.core.pojo.domain.GoodsCategory;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsCategoryDetailRes;
import com.std.core.pojo.response.GoodsCategoryListRes;
import com.std.core.pojo.response.GoodsCategoryPageRes;
import java.util.List;

/**
 * 商品类型Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 15:55
 */
public interface IGoodsCategoryService {

    /**
     * 新增商品类型
     *
     * @param req 新增商品类型入参
     * @param operator 操作人
     */
    void create(GoodsCategoryCreateReq req, User operator);

    /**
     * 删除商品类型
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改商品类型
     *
     * @param req 修改商品类型入参
     * @param operator 操作人
     */
    void modify(GoodsCategoryModifyReq req, User operator);

    void batchUpDown(BatchUpDownReq req, User operator);

    /**
     * 详情查询商品类型
     *
     * @param id 主键ID
     * @return 商品类型详情数据
     */
     GoodsCategory detail(Long id);

    /**
     * 分页查询商品类型
     *
     * @param req 分页查询商品类型入参
     * @return 商品类型分页数据
     */
     List<GoodsCategory> page(GoodsCategoryPageReq req);

    /**
     * 列表查询商品类型
     *
     * @param req 列表查询商品类型入参
     * @return 商品类型列表数据
     */
     List<GoodsCategory> list(GoodsCategoryListReq req);

    List<GoodsCategory> list(GoodsCategory req);

    /**
     * 前端详情查询商品类型
     *
     * @param id 主键ID
     * @return 商品类型详情数据
     */
    GoodsCategoryDetailRes detailFront(Long id);

    /**
     * 前端分页查询商品类型
     *
     * @param req 分页查询商品类型入参
     * @return 商品类型分页数据
     */
     List<GoodsCategoryPageRes> pageFront(GoodsCategoryPageFrontReq req);

    /**
     * 前端列表查询商品类型
     *
     * @param req 列表查询商品类型入参
     * @return 商品类型列表数据
     */
     List<GoodsCategoryListRes> listFront(GoodsCategoryListFrontReq req);

    void sort(SortReq request);
}