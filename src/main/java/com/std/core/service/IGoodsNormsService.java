package com.std.core.service;

import com.std.core.pojo.domain.GoodsNorms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsNormsDetailRes;
import com.std.core.pojo.response.GoodsNormsListRes;
import com.std.core.pojo.response.GoodsNormsPageRes;
import java.util.List;

/**
 * 商品规格Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:59
 */
public interface IGoodsNormsService {

    /**
     * 新增商品规格
     *
     * @param req 新增商品规格入参
     * @param operator 操作人
     */
    void create(GoodsNormsCreateReq req, User operator);

    void create(GoodsNorms req);

    /**
     * 删除商品规格
     *
     * @param id 主键ID
     */
     void remove(Long id,User opertor);

    /**
     * 修改商品规格
     *
     * @param req 修改商品规格入参
     * @param operator 操作人
     */
    void modify(GoodsNormsModifyReq req, User operator);
    void modify(GoodsNorms req, User operator);

    void batchUpDown(BatchUpDownReq req, User operator);

    /**
     * 详情查询商品规格
     *
     * @param id 主键ID
     * @return 商品规格详情数据
     */
     GoodsNorms detail(Long id);

    GoodsNorms detailForUpdate(Long id);

    /**
     * 分页查询商品规格
     *
     * @param req 分页查询商品规格入参
     * @return 商品规格分页数据
     */
     List<GoodsNorms> page(GoodsNormsPageReq req);

    /**
     * 列表查询商品规格
     *
     * @param req 列表查询商品规格入参
     * @return 商品规格列表数据
     */
     List<GoodsNorms> list(GoodsNormsListReq req);

    List<GoodsNorms> list(GoodsNorms req);

    /**
     * 前端详情查询商品规格
     *
     * @param id 主键ID
     * @return 商品规格详情数据
     */
    GoodsNormsDetailRes detailFront(Long id);

    /**
     * 前端分页查询商品规格
     *
     * @param req 分页查询商品规格入参
     * @return 商品规格分页数据
     */
     List<GoodsNormsPageRes> pageFront(GoodsNormsPageFrontReq req);

    /**
     * 前端列表查询商品规格
     *
     * @param req 列表查询商品规格入参
     * @return 商品规格列表数据
     */
     List<GoodsNormsListRes> listFront(GoodsNormsListFrontReq req);

    void addStock(GoodsNormsAddStockReq request, User operator);

    int subtractStock(Long id, Integer number);

    void addInventory(Long id, Integer number);
}