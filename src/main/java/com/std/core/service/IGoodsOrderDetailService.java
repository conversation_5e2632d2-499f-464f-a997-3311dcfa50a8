package com.std.core.service;

import com.std.core.pojo.domain.GoodsOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsOrderDetailCreateReq;
import com.std.core.pojo.request.GoodsOrderDetailListReq;
import com.std.core.pojo.request.GoodsOrderDetailListFrontReq;
import com.std.core.pojo.request.GoodsOrderDetailModifyReq;
import com.std.core.pojo.request.GoodsOrderDetailPageReq;
import com.std.core.pojo.request.GoodsOrderDetailPageFrontReq;
import com.std.core.pojo.response.GoodsOrderDetailDetailRes;
import com.std.core.pojo.response.GoodsOrderDetailListRes;
import com.std.core.pojo.response.GoodsOrderDetailPageRes;
import java.util.List;

/**
 * 商品订单详情Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:53
 */
public interface IGoodsOrderDetailService {

    /**
     * 新增商品订单详情
     *
     * @param req 新增商品订单详情入参
     * @param operator 操作人
     */
    void create(GoodsOrderDetailCreateReq req, User operator);
    void create(GoodsOrderDetail req);

    /**
     * 删除商品订单详情
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改商品订单详情
     *
     * @param req 修改商品订单详情入参
     * @param operator 操作人
     */
    void modify(GoodsOrderDetailModifyReq req, User operator);

    /**
     * 详情查询商品订单详情
     *
     * @param id 主键ID
     * @return 商品订单详情详情数据
     */
     GoodsOrderDetail detail(Long id);

    /**
     * 分页查询商品订单详情
     *
     * @param req 分页查询商品订单详情入参
     * @return 商品订单详情分页数据
     */
     List<GoodsOrderDetail> page(GoodsOrderDetailPageReq req);

    /**
     * 列表查询商品订单详情
     *
     * @param req 列表查询商品订单详情入参
     * @return 商品订单详情列表数据
     */
     List<GoodsOrderDetail> list(GoodsOrderDetailListReq req);
     List<GoodsOrderDetail> list(GoodsOrderDetail req);

    /**
     * 前端详情查询商品订单详情
     *
     * @param id 主键ID
     * @return 商品订单详情详情数据
     */
    GoodsOrderDetailDetailRes detailFront(Long id);

    /**
     * 前端分页查询商品订单详情
     *
     * @param req 分页查询商品订单详情入参
     * @return 商品订单详情分页数据
     */
     List<GoodsOrderDetailPageRes> pageFront(GoodsOrderDetailPageFrontReq req,User operator);

    /**
     * 前端列表查询商品订单详情
     *
     * @param req 列表查询商品订单详情入参
     * @return 商品订单详情列表数据
     */
     List<GoodsOrderDetailListRes> listFront(GoodsOrderDetailListFrontReq req);

    void createBatch(List<GoodsOrderDetail> goodsOrderDetailList);

    List<GoodsOrderDetail> listByOrderId(Long orderId);
}