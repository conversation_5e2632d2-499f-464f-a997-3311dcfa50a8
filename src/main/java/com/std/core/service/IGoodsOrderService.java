package com.std.core.service;

import com.std.core.pojo.domain.GoodsOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品订单Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
public interface IGoodsOrderService {

    /**
     * 新增商品订单
     *
     * @param req 新增商品订单入参
     * @param operator 操作人
     */
    OrderPayRes create(GoodsOrderCreateReq req, User operator);

    @Transactional(rollbackFor = Exception.class)
    GoodsOrder createByTrollery(GoodsOrderTrolleryCreateReq req, User operator);

    /**
     * 购物车订单支付
     * @param goodsOrder
     */
    OrderPayRes payServeOrder(GoodsOrder goodsOrder);

    /**
     * 删除商品订单
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改商品订单
     *
     * @param req 修改商品订单入参
     * @param operator 操作人
     */
    void modify(GoodsOrderModifyReq req, User operator);
    void modify(GoodsOrder req);

    /**
     * 详情查询商品订单
     *
     * @param id 主键ID
     * @return 商品订单详情数据
     */
     GoodsOrder detail(Long id);
    GoodsOrder detailSimple(Long id);
    GoodsOrder detailForUpdate(Long id);
    /**
     * 分页查询商品订单
     *
     * @param req 分页查询商品订单入参
     * @return 商品订单分页数据
     */
     List<GoodsOrder> page(GoodsOrderPageReq req);

    /**
     * 列表查询商品订单
     *
     * @param req 列表查询商品订单入参
     * @return 商品订单列表数据
     */
     List<GoodsOrder> list(GoodsOrderListReq req);

    List<GoodsOrder> list(GoodsOrder req);

    /**
     * 前端详情查询商品订单
     *
     * @param id 主键ID
     * @return 商品订单详情数据
     */
    GoodsOrderDetailRes detailFront(Long id);

    /**
     * 前端分页查询商品订单
     *
     * @param req 分页查询商品订单入参
     * @return 商品订单分页数据
     */
     List<GoodsOrderPageRes> pageFront(GoodsOrderPageFrontReq req,User operator);

    /**
     * 前端列表查询商品订单
     *
     * @param req 列表查询商品订单入参
     * @return 商品订单列表数据
     */
     List<GoodsOrderListRes> listFront(GoodsOrderListFrontReq req);

    List<OrderNumberByStatusRes> orderNumberByStatus(User operator);

    GoodsOrder send(GoodsOrderSendReq request, User operator);

    /**
     * 发货完成通知微信
     * @param goodsOrder
     */
    void sendSuccessNoticeWechat(GoodsOrder goodsOrder);

    void receive(GoodsOrderReceiveReq request, User operator);

    /**
     * 支付回调
     * @param bizCode
     * @param transactionId
     */
    void serveDoCallback(Long bizCode, String transactionId);

    /**
     * 取消支付
     * @param closeTime
     * @return
     */
    List<GoodsOrder> listTimeOutOrder(Integer closeTime);

    /**
     * 取消订单
     * @param id
     */
    void cancelGoodsOrder(Long id);

    @Transactional(rollbackFor = Exception.class)
    void cancelGoodsOrderByOss(Long id);

    List<IncomeItemRes> income(IncomeGroupReq req);

    MinMaxTimeRes minMaxTime();

    BigDecimal total(IncomeGroupReq req);

    BigDecimal detailIncomeByDate(String startDate, String endDate);

}