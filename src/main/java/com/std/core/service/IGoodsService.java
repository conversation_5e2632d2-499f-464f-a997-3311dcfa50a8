package com.std.core.service;

import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsDetailRes;
import com.std.core.pojo.response.GoodsListRes;
import com.std.core.pojo.response.GoodsPageRes;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商品Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
public interface IGoodsService {

    /**
     * 新增商品
     *
     * @param req 新增商品入参
     * @param operator 操作人
     */
    void create(GoodsCreateReq req, User operator);

    /**
     * 删除商品
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改商品
     *
     * @param req 修改商品入参
     * @param operator 操作人
     */
    void modify(GoodsModifyReq req, User operator);
    void modify(Goods req);

    @Transactional(rollbackFor = Exception.class)
    void batchUpDown(BatchUpDownReq req, User operator);

    /**
     * 详情查询商品
     *
     * @param id 主键ID
     * @return 商品详情数据
     */
     Goods detail(Long id);

    /**
     * 分页查询商品
     *
     * @param req 分页查询商品入参
     * @return 商品分页数据
     */
     List<Goods> page(GoodsPageReq req);

    /**
     * 列表查询商品
     *
     * @param req 列表查询商品入参
     * @return 商品列表数据
     */
     List<Goods> list(GoodsListReq req);

    List<Goods> list(Goods req);

    /**
     * 前端详情查询商品
     *
     * @param id 主键ID
     * @return 商品详情数据
     */
    GoodsDetailRes detailFront(Long id);

    /**
     * 前端分页查询商品
     *
     * @param req 分页查询商品入参
     * @return 商品分页数据
     */
     List<GoodsPageRes> pageFront(GoodsPageFrontReq req);

    /**
     * 前端列表查询商品
     *
     * @param req 列表查询商品入参
     * @return 商品列表数据
     */
     List<GoodsListRes> listFront(GoodsListFrontReq req);

    void sort(SortReq request);
}