package com.std.core.service;

import com.std.core.pojo.domain.GoodsTrolley;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsTrolleyDetailRes;
import com.std.core.pojo.response.GoodsTrolleyListRes;
import com.std.core.pojo.response.GoodsTrolleyPageRes;
import java.util.List;

/**
 * 购物车Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
public interface IGoodsTrolleyService {

    /**
     * 新增购物车
     *
     * @param req 新增购物车入参
     * @param operator 操作人
     */
    void create(GoodsTrolleyCreateReq req, User operator);

    /**
     * 删除购物车
     *
     */
     void remove(GoodsTrolleyRemoveReq req,User operator);

    /**
     * 修改购物车
     *
     * @param req 修改购物车入参
     * @param operator 操作人
     */
    void modify(GoodsTrolleyModifyReq req, User operator);
    void modify(GoodsTrolley req);

    void exchange(GoodsTrolleyExchangeReq req, User operator);

    /**
     * 详情查询购物车
     *
     * @param id 主键ID
     * @return 购物车详情数据
     */
     GoodsTrolley detail(Long id);
    GoodsTrolley detailForUpdate(Long id);
    /**
     * 分页查询购物车
     *
     * @param req 分页查询购物车入参
     * @return 购物车分页数据
     */
     List<GoodsTrolley> page(GoodsTrolleyPageReq req);

    /**
     * 列表查询购物车
     *
     * @param req 列表查询购物车入参
     * @return 购物车列表数据
     */
     List<GoodsTrolley> list(GoodsTrolleyListReq req);
     List<GoodsTrolley> list(GoodsTrolley req);

    /**
     * 前端详情查询购物车
     *
     * @param id 主键ID
     * @return 购物车详情数据
     */
    GoodsTrolleyDetailRes detailFront(Long id);

    /**
     * 前端分页查询购物车
     *
     * @param req 分页查询购物车入参
     * @return 购物车分页数据
     */
     List<GoodsTrolleyPageRes> pageFront(GoodsTrolleyPageFrontReq req,User operator);

    /**
     * 前端列表查询购物车
     *
     * @param req 列表查询购物车入参
     * @return 购物车列表数据
     */
     List<GoodsTrolleyListRes> listFront(GoodsTrolleyListFrontReq req,User operator);

}