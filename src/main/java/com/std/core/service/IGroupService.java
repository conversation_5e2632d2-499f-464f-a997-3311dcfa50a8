package com.std.core.service;

import com.std.core.pojo.domain.Group;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GroupCreateReq;
import com.std.core.pojo.request.GroupListReq;
import com.std.core.pojo.request.GroupModifyReq;
import com.std.core.pojo.request.GroupPageReq;
import java.util.List;

/**
 * @Description: 组 @Author: Silver @CreateDate: 2019-01-08 15:33 @Version: 1.0
 */
public interface IGroupService {

    void create(User user, GroupCreateReq req);

    void remove(Long id);

    void modify(User user, GroupModifyReq req);

    Group info(Long id);

    List<Group> list(GroupListReq req, User user);

    List<Group> subOwnerList(GroupListReq req, User user);

    List<Group> page(GroupPageReq req, User user);

    List<Group> listGroup(Group condition);

    /**
     * 通过token查同单位下的组
     *
     * @param user 操作人
     * @return 组
     */
    List<Group> listByToken(User user);

    /**
     * 列表查其余的组
     *
     * @param id 组id
     * @param user 操作人
     * @return 组列表
     */
    List<Group> listOtherGroup(Long id, User user);
}
