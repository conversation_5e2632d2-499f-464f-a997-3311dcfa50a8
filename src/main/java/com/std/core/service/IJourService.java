package com.std.core.service;

import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Jour;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.JourListReq;
import com.std.core.pojo.request.JourMyPageReq;
import com.std.core.pojo.request.JourPageReq;
import com.std.core.pojo.response.JourMyPageRes;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户流水Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
public interface IJourService {

    /**
     *
     */
    <T> String addJour(Account dbAccount, BigDecimal transAmount, String channelType,
            String channelOrder, T refNo, Long refUserId, String bizCategory, String bizCategoryNote,
            String bizType, String bizNote, String remark, String enRemark);

    /**
     *
     */
    String addFrozenJour(Account dbAccount, BigDecimal transAmount, String channelType,
            String refNo, String bizCategory, String bizCategoryNote, String bizType,
            String bizNote, String remark, String enRemark);

    /**
     *
     */
    String addUnFrozenJour(Account dbAccount, BigDecimal transAmount, String channelType,
            String refNo, String bizCategory, String bizCategoryNote, String bizType,
            String bizNote, String remark, String enRemark);

    /**
     * 详情查询账户流水
     *
     * @param id 主键ID
     * @return 账户流水详情数据
     */
    Jour detail(Long id, String language);

    /**
     * 分页查询账户流水
     *
     * @param req 分页查询账户流水入参
     * @return 账户流水分页数据
     */
    List<Jour> page(JourPageReq req, User operator);


    /**
     * 列表查询账户流水
     *
     * @param req 列表查询账户流水入参
     * @return 账户流水列表数据
     */
    List<Jour> list(JourListReq req);

    /**
     *
     */
    BigDecimal selectTotalAmount(String type, String accountType, String currency,
            List<String> bizTypeList);

    List<Jour> selectListDetailByJour(Jour jour);


    /**
     * 更改流水状态
     */
    void updateStatus(Account dbAccount, Jour jour, BigDecimal transAmount, String status);

    List<Jour> selectList(Jour jour);

    /**
     * 得到单条流水
     */
    Jour getJour(Long userId, String refNo, String status, String currency);

    List<Jour> userPage(JourMyPageReq request, User operator);


    void updateJour(Jour jour);
}