package com.std.core.service;

import com.std.common.enums.ELanguage;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.LanguageResource;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 语言资源Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-02 10:59
 */
public interface ILanguageResourceService {

    /**
     * 新增语言资源
     */
    <T> void create(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull String znData, @NotNull String enData);

    /**
     * 替换语言资源
     */
    <T> void replace(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull String znData, @NotNull String enData);

    /**
     * 新增语言资源
     */
    <T> void create(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull ELanguage language, @NotNull String data);

    /**
     * 新增语言资源
     */
    <T> void create(@NotNull T data, @NotNull T refId, @NotNull ELanguage language,
            @NotEmpty List<String> columnList);

    /**
     * 删除语言资源
     */
    <T> void remove(String table, T refId);

    /**
     * 删除语言资源
     *
     * @param id 主键ID
     */
    void remove(Integer id);

    /**
     * 修改语言资源
     */
    <T> void modify(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull ELanguage language, @NotNull String data);

    /**
     * 修改语言资源
     */
    <T> void modify(@NotNull T data, @NotNull T refId, @NotNull ELanguage language,
            @NotEmpty List<String> columnList);

    /**
     *
     */
    <T> void languageParse(T data, String language, T refId);

    /**
     * 详情查询语言资源
     *
     * @param id 主键ID
     * @return 语言资源详情数据
     */
    LanguageResource detail(Integer id);

    /**
     *
     */
    String translate(@NotEmpty List<Dict> dictList, @NotNull String language, @NotNull String key);

    /**
     * 列表查询语言资源
     *
     * @return 语言资源列表数据
     */
    <T> List<LanguageResource> list(String table, T refId);

}