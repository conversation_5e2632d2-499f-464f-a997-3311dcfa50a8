package com.std.core.service;

import com.std.core.pojo.domain.Lock;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LockCreateReq;
import com.std.core.pojo.request.LockListReq;
import com.std.core.pojo.request.LockModifyReq;
import com.std.core.pojo.request.LockPageReq;

import java.util.List;

/**
 * 业务锁Service
 *
 * <AUTHOR> xieyj
 * @since : 2020-06-12 00:27
 */
public interface ILockService {

    /**
     * 新增业务锁
     *
     * @param req      新增业务锁入参
     * @param operator 操作人
     */
    void create(LockCreateReq req, User operator);

    void create(String bizType, String refCode);

    /**
     * 删除业务锁
     *
     * @param id 主键ID
     */
    void remove(Integer id);

    /**
     * 修改业务锁
     *
     * @param req      修改业务锁入参
     * @param operator 操作人
     */
    void modify(LockModifyReq req, User operator);

    /**
     * 详情查询业务锁
     *
     * @param id 主键ID
     * @return 业务锁详情数据
     */
    Lock detail(Integer id);

    /**
     * 分页查询业务锁
     *
     * @param req 分页查询业务锁入参
     * @return 业务锁分页数据
     */
    List<Lock> page(LockPageReq req);

    /**
     * 列表查询业务锁
     *
     * @param req 列表查询业务锁入参
     * @return 业务锁列表数据
     */
    List<Lock> list(LockListReq req);

    
    void insert(Lock lock);
}