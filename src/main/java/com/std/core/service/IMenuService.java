package com.std.core.service;

import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MenuCreateReq;
import com.std.core.pojo.request.MenuListReq;
import com.std.core.pojo.request.MenuModifyReq;
import com.std.core.pojo.request.MenuPageReq;
import java.util.List;

public interface IMenuService {

    Long create(MenuCreateReq req);

    void remove(Long id);

    void modify(MenuModifyReq req);

    Menu detail(Long id);

    List<Menu> list(MenuListReq req);

    List<Menu> page(MenuPageReq req);

    // 查询菜单
    List<Menu> listConditionByRoles(List<Long> roleList, Long menuId, List<String> typeList);

    // 查询菜单
    List<Menu> listMenuByKind(String kind);

    // 根据顶级菜单获取树列表
    List<Menu> listByConditionByTopMenuId(Long menuId);

    // 根据用户类型，获取菜单信息
    List<Menu> listInfoByTopMenuId(User operateUser);
}
