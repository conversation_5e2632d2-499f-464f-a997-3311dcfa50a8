package com.std.core.service;

import com.std.core.enums.EIncomeAmountType;
import com.std.core.pojo.domain.NodeConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NodeConfigCreateReq;
import com.std.core.pojo.request.NodeConfigListReq;
import com.std.core.pojo.request.NodeConfigModifyReq;
import com.std.core.pojo.request.NodeConfigPageReq;
import java.math.BigDecimal;
import java.util.List;

/**
 * 星级节点配置Service
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
public interface INodeConfigService {

    /**
     * 新增星级节点配置
     *
     * @param req 新增星级节点配置入参
     * @param operator 操作人
     */
    void create(NodeConfigCreateReq req, User operator);

    /**
     * 删除星级节点配置
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改星级节点配置
     *
     * @param req 修改星级节点配置入参
     * @param operator 操作人
     */
    void modify(NodeConfigModifyReq req, User operator);

    /**
     * 详情查询星级节点配置
     *
     * @param id 主键ID
     * @return 星级节点配置详情数据
     */
    NodeConfig detail(Long id);

    /**
     * 分页查询星级节点配置
     *
     * @param req 分页查询星级节点配置入参
     * @return 星级节点配置分页数据
     */
    List<NodeConfig> page(NodeConfigPageReq req);

    /**
     * 列表查询星级节点配置
     *
     * @param req 列表查询星级节点配置入参
     * @return 星级节点配置列表数据
     */
    List<NodeConfig> list(NodeConfigListReq req);

    List<NodeConfig> selectConfigList();

    BigDecimal getNodeFenchengRate(Long userId, Integer userLevel, EIncomeAmountType amountType);

}