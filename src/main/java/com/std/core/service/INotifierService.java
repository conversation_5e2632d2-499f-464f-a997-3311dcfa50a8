package com.std.core.service;

import java.util.List;

import com.std.core.pojo.domain.Notifier;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NotifierCreateReq;
import com.std.core.pojo.request.NotifierListReq;
import com.std.core.pojo.request.NotifierModifyReq;
import com.std.core.pojo.request.NotifierPageReq;

/**
 * 通知人Service
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 15:39
 */
public interface INotifierService {

    /**
     * 新增通知人
     *
     * @param req 新增通知人入参
     * @param operator 操作人
     */
    void create(NotifierCreateReq req, User operator);

    /**
     * 删除通知人
     *
     * @param id 主键ID
     */
     void remove(Integer id);

    /**
     * 修改通知人
     *
     * @param req 修改通知人入参
     * @param operator 操作人
     */
    void modify(NotifierModifyReq req, User operator);

    /**
     * 详情查询通知人
     *
     * @param id 主键ID
     * @return 通知人详情数据
     */
     Notifier detail(Integer id);

    /**
     * 分页查询通知人
     *
     * @param req 分页查询通知人入参
     * @return 通知人分页数据
     */
     List<Notifier> page(NotifierPageReq req);

    /**
     * 列表查询通知人
     *
     * @param req 列表查询通知人入参
     * @return 通知人列表数据
     */
     List<Notifier> list(NotifierListReq req);

    /**
     * 提币给管理员消息通知
     */
    void turnOutToAdminMsg(Long id);
}