package com.std.core.service;

import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PayRecordListReq;
import com.std.core.pojo.request.PayRecordModifyReq;
import com.std.core.pojo.request.PayRecordPageReq;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付记录Service
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
public interface IPayRecordService {

    /**
     * 新增支付记录
     */

    PayRecord create(Long id, Long userId, String payType, String payMethod, BigDecimal amount, String bizType, Long bizCode, String request);

    PayRecord create(Long userId, String payType, String payMethod, BigDecimal amount, String bizType, Long bizCode);


    /**
     * 删除支付记录
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改支付记录
     *
     * @param req      修改支付记录入参
     * @param operator 操作人
     */
    void modify(PayRecordModifyReq req, User operator);

    /**
     * 详情查询支付记录
     *
     * @param id 主键ID
     * @return 支付记录详情数据
     */
    PayRecord detail(Long id);

    /**
     * 分页查询支付记录
     *
     * @param req 分页查询支付记录入参
     * @return 支付记录分页数据
     */
    List<PayRecord> page(PayRecordPageReq req);

    /**
     * 列表查询支付记录
     *
     * @param req 列表查询支付记录入参
     * @return 支付记录列表数据
     */
    List<PayRecord> list(PayRecordListReq req);

}