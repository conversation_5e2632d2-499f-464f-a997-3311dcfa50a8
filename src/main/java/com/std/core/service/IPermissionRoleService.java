package com.std.core.service;


import com.std.core.pojo.domain.PermissionRole;
import java.util.List;

/**
 * <AUTHOR> qianLei
 * @since : 2019-03-29 16:10
 */
public interface IPermissionRoleService {

    /**
     * 批量添加
     *
     * @param permissionRoleList
     */
    void batchCreate(List<PermissionRole> permissionRoleList);

    /**
     * 批量分配角色权限 resourceIdList 为空 分配全部（逻辑只试用后端代码中） resourceIdList 不为空 分配部分
     *
     * @param roleId 角色编号
     * @param menuIdList  菜单编号
     */
    void batchCreate(Long roleId, List<Long> menuIdList);

    //根据角色删除
    void removeByRole(Long roleId);

    void removeByCondition(Long roleId, String resourceType);

    List<PermissionRole> list(Long roleId, String... resourceTypes);

    long getTotalCount(Long roleId);

    List<PermissionRole> listByMenu(Long menuId);
}

