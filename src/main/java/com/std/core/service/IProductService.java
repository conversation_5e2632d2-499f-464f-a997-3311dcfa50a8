package com.std.core.service;

import com.std.core.pojo.domain.Product;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ProductCreateReq;
import com.std.core.pojo.request.ProductListReq;
import com.std.core.pojo.request.ProductListFrontReq;
import com.std.core.pojo.request.ProductModifyReq;
import com.std.core.pojo.request.ProductPageReq;
import com.std.core.pojo.request.ProductPageFrontReq;
import com.std.core.pojo.response.ProductDetailRes;
import com.std.core.pojo.response.ProductListRes;
import com.std.core.pojo.response.ProductPageRes;
import java.util.List;

/**
 * 标的Service
 *
 * <AUTHOR> ycj
 * @since : 2024-11-22 14:39
 */
public interface IProductService {

    /**
     * 新增标的
     *
     * @param req 新增标的入参
     * @param operator 操作人
     */
    void create(ProductCreateReq req, User operator);

    /**
     * 删除标的
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改标的
     *
     * @param req 修改标的入参
     * @param operator 操作人
     */
    void modify(ProductModifyReq req, User operator);

    /**
     * 详情查询标的
     *
     * @param id 主键ID
     * @return 标的详情数据
     */
     Product detail(Long id);

    /**
     * 分页查询标的
     *
     * @param req 分页查询标的入参
     * @return 标的分页数据
     */
     List<Product> page(ProductPageReq req);

    /**
     * 列表查询标的
     *
     * @param req 列表查询标的入参
     * @return 标的列表数据
     */
     List<Product> list(ProductListReq req);

    /**
     * 前端详情查询标的
     *
     * @param id 主键ID
     * @return 标的详情数据
     */
    ProductDetailRes detailFront(Long id);

    /**
     * 前端分页查询标的
     *
     * @param req 分页查询标的入参
     * @return 标的分页数据
     */
     List<ProductPageRes> pageFront(ProductPageFrontReq req);

    /**
     * 前端列表查询标的
     *
     * @param req 列表查询标的入参
     * @return 标的列表数据
     */
     List<ProductListRes> listFront(ProductListFrontReq req);

}