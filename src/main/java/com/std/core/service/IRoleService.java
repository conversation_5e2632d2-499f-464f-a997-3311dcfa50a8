package com.std.core.service;

import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.Role;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AllotRolePermissionReq;
import com.std.core.pojo.request.RoleCreateReq;
import com.std.core.pojo.request.RoleListReq;
import com.std.core.pojo.request.RoleModifyReq;
import com.std.core.pojo.request.RolePageReq;
import com.std.core.pojo.request.RoleSubOwnerCreateReq;
import com.std.core.pojo.request.RoleSubOwnerListReq;
import com.std.core.pojo.request.RoleSubOwnerPageReq;
import java.util.List;

/**
 * @Description: 角色 @Author: Silver @CreateDate: 2019-01-07 15:51 @Version: 1.0
 */
public interface IRoleService {

    void create(RoleCreateReq req, User operateUser);

    void subOwnerCreate(RoleSubOwnerCreateReq req, User operateUser);

    void createRole(Role role);

    void remove(Long id, User operateUser);

    void modify(RoleModifyReq req, User operateUser);

    Role detail(Long id);

    List<Role> list(RoleListReq req, User operateUser);

    List<Role> subOwnerList(RoleSubOwnerListReq request, User operateUser);

    List<Role> page(RolePageReq req, User operateUser);

    List<Role> subOwnerPage(RoleSubOwnerPageReq request, User operateUser);

    // 分配角色权限
    void allotRolePermission(AllotRolePermissionReq request, User operateUser);

    List<Menu> listByRole(Long roleId);

    List<Role> listByCondition(Role condition);
}
