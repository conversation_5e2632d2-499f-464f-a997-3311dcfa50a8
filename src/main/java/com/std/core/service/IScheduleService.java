package com.std.core.service;

import java.util.Date;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/6/2 3:35 下午
 */
public interface IScheduleService {

    /**
     * 超时未支付，取消大单
     */
    void doCancelBigOrder();

    /**
     * 商品订单超时未支付，自动取消
     */
    void doGoodsCancelBigOrder();

    void doExpiredActivity();

    void activityExpired();

    void doAutoReceive();

    void doUserReservationRecovery();

    /**
     * 每日收入统计
     * @param incomeDate
     */
    void doDailyIncomeSummary(Date date);
}
