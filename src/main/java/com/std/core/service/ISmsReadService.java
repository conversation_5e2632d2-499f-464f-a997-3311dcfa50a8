package com.std.core.service;

import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.SmsRead;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SmsPageMyReq;
import com.std.core.pojo.request.SmsReadListReq;
import com.std.core.pojo.request.SmsReadPageReq;
import java.util.List;

/**
 * 公告阅读记录Service
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
public interface ISmsReadService {

    /**
     * 新增公告阅读记录
     *
     * @param smsCode 公告编号
     * @param operator 操作人
     */
    void create(Long smsCode, User operator);

    /**
     * 删除公告阅读记录
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 批量删除
     */
    void removeBySmsCode(Long smsCode);

    /**
     * 详情查询公告阅读记录
     *
     * @param id 主键ID
     * @return 公告阅读记录详情数据
     */
    SmsRead detail(Long id);

    /**
     * 分页查询公告阅读记录
     *
     * @param req 分页查询公告阅读记录入参
     * @return 公告阅读记录分页数据
     */
    List<SmsRead> page(SmsReadPageReq req);

    /**
     * 列表查询公告阅读记录
     *
     * @param req 列表查询公告阅读记录入参
     * @return 公告阅读记录列表数据
     */
    List<SmsRead> list(SmsReadListReq req);

    /**
     * 阅读记录是否存在
     *
     * @param userId 用户编号
     * @param smsCode 公告编号
     */
    boolean isExist(Long userId, Long smsCode);

    List<Sms> page(SmsPageMyReq req, User operator);

    /**
     * 获取我未读的系统公告数量
     */
    Integer getMyUnreadCount(User operator);

    /**
     * 获取我未读的消息数量
     */
    Integer getMsgMyUnreadCount(User operator);


}