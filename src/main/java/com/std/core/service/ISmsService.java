package com.std.core.service;

import com.std.common.base.BasePageReq;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.SmsNewsRes;

import java.util.List;

/**
 * 消息记录Service
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
public interface ISmsService {

    /**
     * 新增消息记录
     *
     * @param req      新增消息记录入参
     * @param operator 操作人
     */
    void create(SmsCreateReq req, User operator, String ip);

    /**
     * 群发消息
     *
     * @param req
     * @param operator
     */
    void batchCreate(SmsBatchCreateReq req, User operator, String ip);

    /**
     * 发送系统公告
     */
    void sendSmsRef(String title, String refType, String refNo);

    /**
     * 发送我的消息
     */
    void sendMyMsg(User user, String title, String content, String refType, String refNo);

    /**
     * 删除消息记录
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改消息记录
     *
     * @param req      修改消息记录入参
     * @param operator 操作人
     */
    void modify(SmsModifyReq req, User operator, String ip);

    /**
     * 批量上架
     */
    void batchRelease(SmsPutReq request, User operator, String ip);

    /**
     * 批量下架
     */
    void batchRevoke(SmsPutReq request, User operator, String ip);

    /**
     * 详情查询消息记录
     *
     * @param id 主键ID
     * @return 消息记录详情数据
     */
    Sms detail(Long id);

    /**
     * app详情查询消息记录
     *
     * @param id 主键ID
     * @return 消息记录详情数据
     */
    Sms appDetail(Long id, User operator);

    /**
     * 分页查询消息记录
     *
     * @param req 分页查询消息记录入参
     * @return 消息记录分页数据
     */
    List<Sms> page(SmsPageReq req, User operator);

    /**
     * 列表查询消息记录
     *
     * @param req 列表查询消息记录入参
     * @return 消息记录列表数据
     */
    List<Sms> list(SmsListReq req);

    /**
     * 消息中心
     *
     * @param req 列表查询消息记录入参
     * @return 消息记录列表数据
     */
    List<Sms> messageCenter(SmsListReq req, User operator);

    List<Sms> messageCenterPage(BasePageReq req, User user);

    /**
     * 未读消息数
     *
     * @param
     * @return 消息记录列表数据
     */
    Sms unreadMessages(User operator);

    /**
     * APP未读消息数
     *
     * @param operator
     * @return
     */
    Sms unreadMessagesNumber(User operator);

    /**
     * APP未读公告数
     *
     * @param operator
     * @return
     */
    Sms unreadNoticeNumber(User operator);

    /**
     * 一键已读消息
     *
     * @param operator
     */
    void unifiedReadMessages(User operator);

    /**
     * 一键已读公告
     *
     * @param operator
     */
    void unifiedReadNotice(User operator);

    /**
     * OSS消息管理详情查
     *
     * @param id
     * @return
     */
    SmsNewsRes smsNews(Long id);

    /**
     * APP站内信
     *
     * @return 消息记录列表数据
     */
    List<Sms> userSms(User operator);

}