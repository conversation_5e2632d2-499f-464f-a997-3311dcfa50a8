package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserActionLog;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.UserActionLogDetailRes;
import com.std.core.pojo.response.UserActionLogListRes;
import com.std.core.pojo.response.UserActionLogPageRes;

import java.util.List;

/**
 * 用户业务操作记录Service
 *
 * <AUTHOR> ycj
 * @since : 2024-06-17 12:44
 */
public interface IUserActionLogService {

    /**
     * 新增用户业务操作记录
     *
     * @param req 新增用户业务操作记录入参
     * @param operator 操作人
     */
    void create(UserActionLogCreateReq req, User operator);
    void create(String type, Long userId, String oldData, String newData);
    /**
     * 删除用户业务操作记录
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改用户业务操作记录
     *
     * @param req 修改用户业务操作记录入参
     * @param operator 操作人
     */
    void modify(UserActionLogModifyReq req, User operator);

    /**
     * 详情查询用户业务操作记录
     *
     * @param id 主键ID
     * @return 用户业务操作记录详情数据
     */
     UserActionLog detail(Long id);

    /**
     * 分页查询用户业务操作记录
     *
     * @param req 分页查询用户业务操作记录入参
     * @return 用户业务操作记录分页数据
     */
     List<UserActionLog> page(UserActionLogPageReq req);

    /**
     * 列表查询用户业务操作记录
     *
     * @param req 列表查询用户业务操作记录入参
     * @return 用户业务操作记录列表数据
     */
     List<UserActionLog> list(UserActionLogListReq req);

    /**
     * 前端详情查询用户业务操作记录
     *
     * @param id 主键ID
     * @return 用户业务操作记录详情数据
     */
    UserActionLogDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户业务操作记录
     *
     * @param req 分页查询用户业务操作记录入参
     * @return 用户业务操作记录分页数据
     */
     List<UserActionLogPageRes> pageFront(UserActionLogPageFrontReq req);

    /**
     * 前端列表查询用户业务操作记录
     *
     * @param req 列表查询用户业务操作记录入参
     * @return 用户业务操作记录列表数据
     */
     List<UserActionLogListRes> listFront(UserActionLogListFrontReq req);


}