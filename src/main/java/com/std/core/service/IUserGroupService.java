package com.std.core.service;

import com.std.core.pojo.domain.UserGroup;
import java.util.List;

public interface IUserGroupService {


    /**
     * 批量添加
     */
    void batchCreate(List<UserGroup> userGroupList);

    /**
     * 根据用户删除
     */
    void removeByUser(Long userId);

    /**
     * 根据用户编号获取用户组
     *
     * @param userId 用户编号
     * @return 用户组信息
     */
    List<UserGroup> listByUserId(Long userId);

    /**
     * 根据用户组获取用户组
     *
     * @param groupId 用户组
     * @return 用户组信息
     */
    List<UserGroup> listByGroupId(Long groupId);

    /**
     * 分配用户组
     */
    void allotGroup(Long id, Long groupId);

}
