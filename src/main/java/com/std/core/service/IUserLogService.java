package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserLog;
import com.std.core.pojo.request.UserLogListReq;
import com.std.core.pojo.request.UserLogPageReq;
import java.util.List;

/**
 * 用户日志Service
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
public interface IUserLogService {

    /**
     * 创建
     */
    void create(User user, String type,String typeNote, String ip, String content);

    void batchCreate(List<UserLog> userLogList);
    /**
     * 删除用户日志
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 详情查询用户日志
     *
     * @param id 主键ID
     * @return 用户日志详情数据
     */
    UserLog detail(Long id);

    /**
     * 分页查询用户日志
     *
     * @param req 分页查询用户日志入参
     * @return 用户日志分页数据
     */
    List<UserLog> page(UserLogPageReq req);

    /**
     * 列表查询用户日志
     *
     * @param req 列表查询用户日志入参
     * @return 用户日志列表数据
     */
    List<UserLog> list(UserLogListReq req);

}