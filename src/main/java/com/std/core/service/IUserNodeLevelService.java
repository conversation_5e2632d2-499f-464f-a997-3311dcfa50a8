package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserNodeLevel;
import com.std.core.pojo.request.UserNodeLevelCreateReq;
import com.std.core.pojo.request.UserNodeLevelListReq;
import com.std.core.pojo.request.UserNodeLevelModifyReq;
import com.std.core.pojo.request.UserNodeLevelPageReq;
import java.util.List;

/**
 * 节点用户Service
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
public interface IUserNodeLevelService {

    /**
     * 新增节点用户
     *
     * @param req 新增节点用户入参
     * @param operator 操作人
     */
    void create(UserNodeLevelCreateReq req, User operator);

    /**
     * 删除节点用户
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改节点用户
     *
     * @param req 修改节点用户入参
     * @param operator 操作人
     */
    void modify(UserNodeLevelModifyReq req, User operator);

    /**
     * 详情查询节点用户
     *
     * @param id 主键ID
     * @return 节点用户详情数据
     */
    UserNodeLevel detail(Long id);

    /**
     * 分页查询节点用户
     *
     * @param req 分页查询节点用户入参
     * @return 节点用户分页数据
     */
    List<UserNodeLevel> page(UserNodeLevelPageReq req);

    /**
     * 列表查询节点用户
     *
     * @param req 列表查询节点用户入参
     * @return 节点用户列表数据
     */
    List<UserNodeLevel> list(UserNodeLevelListReq req);

    /**
     * 根据用户编号查询节点等级
     */
    UserNodeLevel selectByUserId(Long userId);

    Integer selectHighPriority(Long userId);

    void upgradeAuto(Long userId, Integer nodeLevel);

    Long selectMySubUserCount(Long id, Integer nodeLevel);


}