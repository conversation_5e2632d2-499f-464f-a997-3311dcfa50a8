package com.std.core.service;

import com.std.core.pojo.domain.UserRole;
import java.util.List;

/**
 * @author: haiqingzheng
 * @since: 2019-01-08 15:14
 * @description:
 */
public interface IUserRoleService {

    void allotRole(Long userId, Long roleId);

    void removeByUserId(Long userId);

    long getTotalCount(Long roleId);

    List<UserRole> list(UserRole userRole);

    List<UserRole> listByUserId(Long id);

    List<UserRole> listByRoleId(Long roleId);
}
