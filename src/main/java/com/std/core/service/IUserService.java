package com.std.core.service;

import com.alibaba.fastjson.JSONObject;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.MyTeamUserRes;
import com.std.core.pojo.response.SubUserRes;
import com.std.core.pojo.response.TeamUserOssRes;
import com.std.core.pojo.response.UserFlow;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户Service
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
public interface IUserService {


    /**
     *
     */
    boolean checkLoginName(String loginName, String kind);

    /**
     *
     */
    void checkLoginPwd(Long id, String loginPwd);

    /**
     *
     */
    void checkMobileExist(String mobile, String kind);

    void isMobileExist(String mobile, String kind);

    void checkMobileExistButOwn(Long id, String mobile, String kind);

    /**
     *
     */
    void checkMobileNotExist(String mobile, String kind);

    /**
     *
     */
    void checkEmail(String email, String kind);

    /**
     * 新增用户
     *
     * @param req      新增用户入参
     * @param operator 操作人
     */
    Long create(UserCreateReq req, User operator, String ip);

    /**
     *
     */
    void createUser(User user);

    /**
     * 删除用户
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     *
     */
    void remove(Long id, Long userId);

    /**
     * 修改用户
     *
     * @param req      修改用户入参
     * @param operator 操作人
     */
    void modify(UserModifyReq req, User operator);

    /**
     * 删除用户缓存
     * @param userId
     * @param userKind
     */
    void delUserRedis(Long userId, String userKind);

    /**
     *
     */
    void modifyUser(User user);

    @Transactional(rollbackFor = Exception.class)
    JSONObject login(PUserLoginReq request, String kind, String ip);

    /**
     * 登录验证
     */
    JSONObject login(UserLoginReq request, String kind, String ip);

    JSONObject login(CUserLoginReq request, String kind, String ip);

    /**
     *
     */
    void bindMobile(UserBindMobileReq request, User operator, String ip);

    /**
     *
     */
    void bindEmail(UserBindEmailReq request, User operator, String ip);

    /**
     *
     */
    void modifyLoginPwd(UserModifyLoginPwdReq request, User operator, String ip);

    void modifyPuserPwd(PUserModifyLoginPwdReq request, User operator, String ip);

    /**
     *
     */
    void modifyLoginPwdOss(UserModifyLoginPwdOSSReq request, User operator, String ip);

    /**
     *
     */
    void modifyTransactionPwdOss(UserModifyTransactionPwdOSSReq request, User operator, String ip);

    /**
     *
     */
    void modifyMobile(UserModifyMobileReq request, User operator, String ip);

    /**
     *
     */
    void modifyEmail(UserModifyEmailReq request, User operator, String ip);

    void lock(UserLockReq req, User operator, String ip);

    void unLock(UserUnLockReq req, User operator, String ip);

    void lock(Long userId);

    void unLock(Long userId);

    /**
     *
     */
    void modifyMobileOSS(UserModifyMobileOSSReq request, User operator, String ip);

    /**
     *
     */
    void forgetLoginPwd(UserForgetLoginPwdReq request);

    /**
     *
     */
    void allotRoles(Long id, List<Long> roleIdList);

    /**
     *
     */
    void allotGroups(Long id, List<Long> groupIdList);

    /**
     *
     */
    List<Menu> listResourceByUser(User operateUser, String... resourceTypes);

    /**
     *
     */
    List<Menu> listResourceByUser(User operateUser, Long parentId);

    /**
     *
     */
    List<Action> listActionByUser(User operateUser);

    /**
     * 递归获取所有下级
     */
    void queryAllInferior(List<User> userList, Long userId, Integer agentNow, Integer agentMax);

    /**
     *
     */
    User checkToken(String token);

    /**
     *
     */
    User tokenDetail(Long id);

    /**
     * 详情查询用户
     *
     * @param id 主键ID
     * @return 用户详情数据
     */
    User detail(Long id);

    User detail(Long userId, EUserKind userKind);

    /**
     *
     */
    User detailBrief(Long id);

    /**
     *
     */
    User detailBrief(String mobile, String userKind);

    /**
     * 分页查询用户
     *
     * @param req 分页查询用户入参
     * @return 用户分页数据
     */
    List<User> page(UserPageReq req);

    /**
     *
     */
    List<User> remotePage(RemoteUserPageReq request);

    /**
     * 列表查询用户
     *
     * @param req 列表查询用户入参
     * @return 用户列表数据
     */
    List<User> list(UserListReq req);

    /**
     *
     */
    List<User> list(User user);

    /**
     * 查询推荐人下的用户
     */
    List<User> listByReferUserId(Long userId);

    /**
     *
     */
    List<Long> listByGroupId(Long groupId);

    /**
     *
     */
    List<Long> listByRoleId(Long roleId);

    /**
     * C端注册
     */
    User register(UserRegisterReq request, String ip);

    /**
     * 修改个人资料
     */
    void editProfile(UserEditProfileReq request, User operator);

    /**
     * 管理端代注册
     */
    void addByOss(UserSysCreateCReq request, User operator, String ip);

    /**
     * 管理端代修改
     */
    void editByOss(UserSysEditCReq request, User operator, String ip);

    /**
     * 导入C端用户
     */
    void importByOss(UserSysImportCReq request, User operator);

    /**
     * 通过身份证查询用户
     */
    User detailByIdNo(String idNo);

    /**
     * 完善用户对象
     */
    void perfect(UserPerfectReq request, User operator);

    /**
     * 根据邀请码查询用户
     */
    User getUserAndCheckByInviteCode(String inviteCode);

    /**
     * 修改支付密码
     */
    void modifyTradePwd(UserModifyTradePwdReq request, User operator, String ip);

    /**
     * 验证支付密码
     */
    void checkTradePwd(Long id, String tradePwd);

    /**
     * 绑定支付密码
     */
    void bindTradePwd(UserBindTradePwdReq request, User operator, String ip);
//
//    @Transactional(rollbackFor = Exception.class)
//    User registerByOss(UserRegisterByOssReq request, String ip);

    @Transactional(rollbackFor = Exception.class)
    User registerByOss(UserRegisterByOssReq request, User operator);

    /**
     * 短信验证码校验
     */
    void doCheckSmsCaptcha(String mobile, String captcha, String bizType);


    List<User> getUsersByUserReferee(Long userReferee);

    List<User> getRichUsersByUserReferee(Long userReferee);

    Integer selectCount(User condition);

    /**
     * 查询用户简要信息
     */
    User selectSummaryInfo(Long userId);

    /**
     * 永久封号
     *
     * @param userId 用户id
     */
    void permanentBan(Long userId);

    /**
     * 根据id更爱状态
     *
     * @param userId 参数
     */
    void updateStatusById(Long userId);

    /**
     * 得到所有账户信息
     */
    List<User> getAllCUser();


    void insertUser(User user);

    /**
     * 拿到所有C端用户除去商家
     */
    List<User> cUserlist();


    /**
     * 商家登陆
     */
    JSONObject merchantLogin(MerchantLoginReq request, String ip);



    /**
     * 查询用户团队
     */
    void queryAllInferior(List<User> userList, Long userId, Integer inviteLevel);

    /**
     * 查询用户团队
     */
    void setLevelCheckAllUser(List<User> userList, Long userId, Long oldUserId, Integer grade);


    /**
     * 查询用户团队
     */
    void queryChannelAllInferior(List<User> userList, Long userId);

    /**
     * oss:查询用户团队
     */
    List<TeamUserOssRes> teamDeatil(Long id);

    /**
     * 团队查询
     */
    MyTeamUserRes teamUser(User operator);

    User selectUserByMobile(String toMobile);

    List<SubUserRes> subUserList(User operator);
    User selectUserByMobile(String mobile, String userKind);

    /**
     * 查询所有正常的C端用户id
     *
     * @return
     */
    List<Long> selectIdList();

    /**
     * 设置等级
     *
     * @param req
     */
    void setLevel(SetUserLevelReq req);


    User registerInstead(UserRegisterOssReq request, String ip);

    /**
     * 设置等级链上级
     *
     * @param req
     */
    void setRefuser(SetUserFidReq req);

    void queryAllInferiorNotInviteLevel(List<User> userList, Long id);

    void queryAllInferiorNotInviteLevelSubChannel(List<User> userList, Long id);

    void batchModifyChannelFlag(List<User> userList, Long channelId);

    @Transactional(rollbackFor = Exception.class)
    JSONObject wxAppletsLogin(UserWxAppletsLoginReq request);

    /**
     * 微信取消支付
     * @param serialNumber
     */
    void cancelOrder(String serialNumber);

    UserFlow userFlow();

    void modifyPuserPwdByOss(PUserModifyLoginPwdByOssReq request, User operator, String ip);

    /**
     * 恢复预约
     * @param user
     */
    void reservationRecovery(User user);
}
