package com.std.core.service;

import com.std.core.pojo.domain.VerificationRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.VerificationRecordCreateReq;
import com.std.core.pojo.request.VerificationRecordListReq;
import com.std.core.pojo.request.VerificationRecordListFrontReq;
import com.std.core.pojo.request.VerificationRecordModifyReq;
import com.std.core.pojo.request.VerificationRecordPageReq;
import com.std.core.pojo.request.VerificationRecordPageFrontReq;
import com.std.core.pojo.response.*;

import java.util.List;

/**
 * 核销Service
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 01:10
 */
public interface IVerificationRecordService {

    /**
     * 新增核销
     *
     * @param req 新增核销入参
     * @param operator 操作人
     */
    VerificationRecordCreateRes create(VerificationRecordCreateReq req, User operator);

    /**
     * 删除核销
     *
     * @param id 主键ID
     */
     void remove(Long id);

    /**
     * 修改核销
     *
     * @param req 修改核销入参
     * @param operator 操作人
     */
    void modify(VerificationRecordModifyReq req, User operator);

    /**
     * 详情查询核销
     *
     * @param id 主键ID
     * @return 核销详情数据
     */
     VerificationRecord detail(Long id);

    /**
     * 分页查询核销
     *
     * @param req 分页查询核销入参
     * @return 核销分页数据
     */
     List<VerificationRecord> page(VerificationRecordPageReq req);

    /**
     * 列表查询核销
     *
     * @param req 列表查询核销入参
     * @return 核销列表数据
     */
     List<VerificationRecord> list(VerificationRecordListReq req);

    List<VerificationRecord> list(VerificationRecord req);

    /**
     * 前端详情查询核销
     *
     * @param id 主键ID
     * @return 核销详情数据
     */
    VerificationRecordDetailRes detailFront(Long id);

    /**
     * 前端分页查询核销
     *
     * @param req 分页查询核销入参
     * @return 核销分页数据
     */
     List<VerificationRecordPageByDays> pageFront(VerificationRecordPageFrontReq req,User operator);

    /**
     * 前端列表查询核销
     *
     * @param req 列表查询核销入参
     * @return 核销列表数据
     */
     List<VerificationRecordListRes> listFront(VerificationRecordListFrontReq req);

}