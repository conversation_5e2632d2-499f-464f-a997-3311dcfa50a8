package com.std.core.service;

import com.std.core.enums.EJourCommon;
import com.std.core.pojo.response.WechatAppPayInfo;
import com.std.core.pojo.response.WechatPayCodeInfo;
import com.std.core.pojo.response.WechatPrepayInfo;
import org.apache.ibatis.transaction.Transaction;

import java.math.BigDecimal;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:01
 */
public interface IWechatService {

    /**
     *
     */
    void doCallback(String result);

    /**
     *
     */
    <T> WechatPrepayInfo getAppPayInfo(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount);

    <T> WechatPrepayInfo getAppPayInfo(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount, String wxAppId
            , Object... args);

    /**
     *
     */
    <T> WechatPrepayInfo getPrepayIdH5(Long applyUser, String userKind, String openId,
                                       String toAccountNumber, T payGroup, String bizType,
                                       String bizNote, BigDecimal transAmount);

    <T> WechatPrepayInfo getPrepayIdH5(Long applyUser, String openId,
                                       Long bizCode, String bizType,
                                       String bizNote, BigDecimal transAmount, String payType, String timeStart, String timeExpire);


    /**
     *
     */
    <T> WechatPayCodeInfo getPayCodeInfo(Long applyUser, String userKind, String toAccountNumber,
                                         T payGroup, String bizType, String bizNote, BigDecimal transAmount);

    /**
     *
     */
    <T> String doRefund(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
                        Object... bizArgs);

    <T> String doRefund2(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
                         Object... bizArgs);

    Transaction queryOrderByOutTradeNo(String serialNumber);

    void sendSubscribeMessage(String openId, String activityDetailId);
}
