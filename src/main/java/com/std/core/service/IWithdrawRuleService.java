package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.WithdrawRule;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawNoteRes;

import java.util.List;

/**
 * 取现规则Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-11 10:35
 */
public interface IWithdrawRuleService {

    /**
     * 新增取现规则
     *
     * @param req      新增取现规则入参
     * @param operator 操作人
     */
    void create(WithdrawRuleCreateReq req, User operator);

    /**
     * 删除取现规则
     *
     * @param id 主键ID
     */
    void remove(Integer id);

    /**
     * 修改取现规则
     *
     * @param req      修改取现规则入参
     * @param operator 操作人
     */
    void modify(WithdrawRuleModifyReq req, User operator);

    /**
     * 详情查询取现规则
     *
     * @param id 主键ID
     * @return 取现规则详情数据
     */
    WithdrawRule detail(Integer id);

    /**
     *
     */
    WithdrawRule detail(String kind, String type, String symbol);

    /**
     * 分页查询取现规则
     *
     * @param req 分页查询取现规则入参
     * @return 取现规则分页数据
     */
    List<WithdrawRule> page(WithdrawRulePageReq req);

    /**
     * 列表查询取现规则
     *
     * @param req 列表查询取现规则入参
     * @return 取现规则列表数据
     */
    List<WithdrawRule> list(WithdrawRuleListReq req);

    /**
     * 提币手续费查询
     *
     * @return
     */
    WithdrawNoteRes detailFee(WithdrawNoteReq request);

    /**
     * OSS：分页条件查询取现规则（除流通NAT）
     *
     * @param request
     * @return
     */
    List<WithdrawRule> pageRule(WithdrawRulePageReq request);
}