package com.std.core.service;

import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.Withdraw;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawSuccessRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * 取现订单Service
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
public interface IWithdrawService {

    /**
     * 新增取现订单
     *
     * @param req 新增取现订单入参
     */
    WithdrawSuccessRes create(WithdrawCreateReq req, User operator);

    /**
     *
     */
    String create(WithdrawToBankcardReq req, User operator);

    /**
     * 审核
     */
    void approve(WithdrawApproveReq request, User operator);

    /**
     *
     */
    void approve(WithdrawBatchApproveNoReq request, User operator);

    /**
     * 支付
     */
    void pay(WithdrawPayReq request, User operator);

    /**
     * 详情查询取现订单
     *
     * @param id 主键ID
     * @return 取现订单详情数据
     */
    Withdraw detail(Long id);

    /**
     * 分页查询取现订单
     *
     * @param req 分页查询取现订单入参
     * @return 取现订单分页数据
     */
    List<Withdraw> page(WithdrawPageReq req, User operator);

    /**
     * 列表查询取现订单
     *
     * @param req 列表查询取现订单入参
     * @return 取现订单列表数据
     */
    List<Withdraw> list(WithdrawListReq req);

    List<Withdraw> myPage(WithdrawMyPageReq request, User operator);

    BigDecimal selectWithdrawingAmount(String accountNumber);

}