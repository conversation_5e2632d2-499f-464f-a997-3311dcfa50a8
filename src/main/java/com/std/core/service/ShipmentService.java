package com.std.core.service;

import com.std.core.config.WechatConfig;

import com.std.core.define.RedisKeyList;
import com.std.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.json.JSONObject;
import org.json.JSONArray;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;


@Service
@Slf4j
public class ShipmentService {
    @Resource
    private WechatConfig wechatConfig;

    @Resource
    private RedisUtil redisUtil;

    public String getAccessToken() {
        String key= RedisKeyList.MSG_WECHAT_ACCESS_TOKEN;
        if (redisUtil.hasKey(key)){
            return redisUtil.getString(key);
        }else {
            String appId = wechatConfig.getAppid();
            String appSecret = wechatConfig.getAppSecret();
            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;

            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.getForObject(url, String.class);

            JSONObject jsonObject = new JSONObject(response);
            redisUtil.set(key, jsonObject.getString("access_token"), 60*60);
            return jsonObject.getString("access_token");
        }
    }

//    public String uploadShippingInfo(String accessToken, String transactionId, String trackingNo, String expressCompany, String itemDesc, String consignorContact, String openid) {
//        String url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=" + accessToken;
//
//        // 创建请求体
//        JSONObject requestBody = new JSONObject();
//        requestBody.put("order_key", new JSONObject()
//                .put("order_number_type", 2)
//                .put("transaction_id", transactionId)
//        );
//        requestBody.put("delivery_mode", 1);
//        requestBody.put("logistics_type", 1);
//
//        // 创建运输项
//        JSONObject shippingItem = new JSONObject();
//        shippingItem.put("tracking_no", trackingNo);
//        shippingItem.put("express_company", expressCompany);
//        shippingItem.put("item_desc", itemDesc);
//
//        // 设置发货人信息
//        JSONObject contact = new JSONObject();
//        contact.put("consignor_contact", consignorContact);
//        shippingItem.put("contact", contact);
//
//        // 将运输项放入列表
//        JSONArray shippingList = new JSONArray();
//        shippingList.put(shippingItem);  // 放入运输项
//
//        // 设置运输列表到请求体
//        requestBody.put("shipping_list", shippingList);
//
//        // 设置上传时间
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
//        String formattedTime = Instant.now().atZone(ZoneId.systemDefault()).format(formatter);
//        requestBody.put("upload_time", formattedTime);
//
//        // 设置支付者的 OpenID
//        JSONObject payer = new JSONObject();
//        payer.put("openid", openid);
//        requestBody.put("payer", payer);
//
//        String utf8EncodedBody = new String(requestBody.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
//
//        RestTemplate restTemplate = new RestTemplate();
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8)); // 设置 UTF-8
//
//// 发送请求
//        HttpEntity<String> request = new HttpEntity<>(utf8EncodedBody, headers);
//        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
//
//// 获取响应头中的 Content-Type
//        HttpHeaders responseHeaders = responseEntity.getHeaders();
//        String contentType = responseHeaders.getContentType().toString();
//        System.out.println("Content-Type: " + contentType);
//
//// 获取响应体内容
//        String responseBody = responseEntity.getBody();
//
//// 手动确保 UTF-8 解码响应体
//        if (responseBody != null) {
//            byte[] responseBytes = responseBody.getBytes(StandardCharsets.ISO_8859_1);  // 先使用 ISO-8859-1 解码
//            responseBody = new String(responseBytes, StandardCharsets.UTF_8);  // 转换为 UTF-8
//        }
//
//        System.out.println("Decoded Response Body: " + responseBody);
//        return responseBody;
//    }


    public String uploadShippingInfo(String accessToken, String transactionId, String trackingNo, String expressCompany, String itemDesc, String consignorContact, String openid) {
        String url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=" + accessToken;

        // 创建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("order_key", new JSONObject()
                .put("order_number_type", 2)
                .put("transaction_id", transactionId)
        );
        requestBody.put("delivery_mode", 1);
        requestBody.put("logistics_type", 1);

        // 创建运输项
        JSONObject shippingItem = new JSONObject();
        shippingItem.put("tracking_no", trackingNo);
        shippingItem.put("express_company", expressCompany);
        shippingItem.put("item_desc", itemDesc);

        // 设置发货人信息
        JSONObject contact = new JSONObject();
        contact.put("consignor_contact", consignorContact);
        shippingItem.put("contact", contact);

        // 将运输项放入列表
        JSONArray shippingList = new JSONArray();
        shippingList.put(shippingItem);  // 放入运输项

        // 设置运输列表到请求体
        requestBody.put("shipping_list", shippingList);

        // 设置上传时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        String formattedTime = Instant.now().atZone(ZoneId.systemDefault()).format(formatter);
        requestBody.put("upload_time", formattedTime);

        // 设置支付者的 OpenID
        JSONObject payer = new JSONObject();
        payer.put("openid", openid);
        requestBody.put("payer", payer);

        // 确保请求体是 UTF-8 编码
        String utf8EncodedBody = new String(requestBody.toString().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8)); // 设置 UTF-8
        headers.setContentType(new MediaType("text", "plain", StandardCharsets.UTF_8));

        // 发送请求
        HttpEntity<String> request = new HttpEntity<>(utf8EncodedBody, headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request, String.class);

        // 获取响应头中的 Content-Type
        HttpHeaders responseHeaders = responseEntity.getHeaders();
        String contentType = responseHeaders.getContentType().toString();
        System.out.println("Content-Type: " + contentType);

        // 获取响应体内容并确保使用 UTF-8 解码
        String responseBody = responseEntity.getBody();
        if (responseBody != null) {
            // 如果响应体内容存在，确保 UTF-8 解码
            responseBody = new String(responseBody.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        }

        System.out.println("Decoded Response Body: " + responseBody);
        log.info("发货微信返回：" + responseBody);
        return responseBody;
    }


//    public String uploadShippingInfo(String accessToken, String transactionId, String trackingNo, String expressCompany, String itemDesc, String consignorContact, String openid) {
//        String url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=" + accessToken;
//
//        // 创建请求体
//        JSONObject requestBody = new JSONObject();
//
//        // 添加订单信息
//        requestBody.put("order_key", new JSONObject()
//                .put("order_number_type", 2)
//                .put("transaction_id", transactionId)
//        );
//        requestBody.put("delivery_mode", 1);
//        requestBody.put("logistics_type", 1);
//
//        // 创建运输项
//        JSONObject shippingItem = new JSONObject();
//        shippingItem.put("tracking_no", trackingNo);
//        shippingItem.put("express_company", expressCompany);
//        String unicodeItemDesc = new String(itemDesc.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
//        unicodeItemDesc = unicodeItemDesc.replace(" ", "-");  // 替换空格
//        shippingItem.put("item_desc", unicodeItemDesc);
//
//
//        // 设置发货人信息
//        JSONObject contact = new JSONObject();
//        contact.put("consignor_contact", consignorContact);
//        shippingItem.put("contact", contact);
//
//        // 将运输项放入列表
//        JSONArray shippingList = new JSONArray();
//        shippingList.put(shippingItem);  // 放入运输项
//
//        // 设置运输列表到请求体
//        requestBody.put("shipping_list", shippingList);
//
//        // 设置上传时间，使用指定格式并带时区偏移
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
//        String formattedTime = Instant.now().atZone(ZoneId.systemDefault()).format(formatter);
//        requestBody.put("upload_time", formattedTime);
//
//        // 设置支付者的 OpenID
//        JSONObject payer = new JSONObject();
//        payer.put("openid", openid);
//        requestBody.put("payer", payer);
//
//        // 打印请求体
//        System.out.println(requestBody.toString()+"111"); // 格式化输出
//
//        // 创建请求头
//        RestTemplate restTemplate = new RestTemplate();
//
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8)); // 设置 UTF-8
//
//
//        // 发送请求
//        HttpEntity<String> request = new HttpEntity<>(requestBody.toString(), headers);
//        String response = restTemplate.postForObject(url, request, String.class);
//
//        return response;
//    }

}
