package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.enums.EJourBizTypeUser.AssetBiz;
import com.std.core.enums.ESystemAccount.SYS_ACCOUNT_PREF;
import com.std.core.mapper.AccountMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Jour;
import com.std.core.pojo.domain.Lock;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.OrderNoGenerater;
import com.std.core.util.RedisLock;
import com.std.core.util.SysConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 账户ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 14:43
 */
@Service
public class AccountServiceImpl implements IAccountService {

    @Resource
    RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    Long metaLockTimeout;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private IJourService jourService;

    //    @Resource
//    private RowLockUtil rowLockUtil;
    @Resource
    private IUserService userService;
    @Resource
    private IUserLogService userLogService;
    @Resource
    private IConfigService configService;
    @Resource
    private ILockService lockService;

    public static void main(String[] args) {
        System.out.println(IdGeneratorUtil.generator());
    }

    /**
     * 分配创建账户
     *
     * @param userId      用户id
     * @param accountType 账户类型
     * @param currency    币种
     * @return 返回账户
     */
    @Override
    public Account distributeAccount(Long userId, String accountType, String currency) {
        return distributeNormalAccount(userId, accountType, currency);
    }

    private Account distributeNormalAccount(Long userId, String accountType, String currency) {
        String accountNumber;
        Account data = null;
        if (null != userId) {
            data = new Account();
            accountNumber = OrderNoGenerater.generate("A");

            data.setAccountNumber(accountNumber);
            data.setUserId(userId);
            data.setCurrency(currency);
            data.setType(accountType);
            data.setStatus(EAccountStatus.NORMAL.getCode());

            data.setAmount(BigDecimal.ZERO);
            data.setFrozenAmount(BigDecimal.ZERO);
            data.setAvailableAmount(BigDecimal.ZERO);
            data.setLockAmount(BigDecimal.ZERO);
            data.setInAmount(BigDecimal.ZERO);
            data.setOutAmount(BigDecimal.ZERO);
            data.setCreateDatetime(new Date());
            accountMapper.insertSelective(data);

//            if (ECurrency.DIAMOND.getCode().equals(currency)) {
//                BigDecimal amount = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_REGISTER)
//                        .setScale(2, BigDecimal.ROUND_DOWN);
//                //用户注册赠送钻石
//                changeAmount(data, amount,
//                        EChannelType.INNER.getCode(),
//                        userId.toString(), userId,
//                        EJourBizTypeUser.UserDiamondIncome.Present,
//                        EJourBizTypeUser.UserDiamondIncome.REGISTER,
//                        EJourBizTypeUser.UserDiamondIncome.REGISTER,
//                        userId, userId);
//
//                //memberConfigService.refreshMemberLevel(userId);
//            }
        }
        return data;
    }

    /**
     * @param dbAccount    账户
     * @param transAmount  变动金额
     * @param channelType
     * @param channelOrder
     * @param refNo
     * @param bizCategory
     * @param bizType
     * @param remark
     * @param args
     * @param <T>
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    //账户 变动金额
    public <T> Account changeAmount(Account dbAccount, BigDecimal transAmount,
                                    String channelType, String channelOrder, T refNo,
                                    EJourCommon bizCategory, EJourCommon bizType, EJourCommon remark, Object... args) {
        // 如果变动金额为0，直接返回
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return dbAccount;
        }

        if (transAmount.compareTo(BigDecimal.ZERO) >= 0 && transAmount.compareTo(new BigDecimal("0.01")) < 0) {
            return dbAccount;
        }

//        // 金额变动之后可用余额, 特定账户余额可为负
        BigDecimal avaliableAmount = dbAccount.getAvailableAmount().add(transAmount);
//        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())
//                && avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
//            throw new BizException(EErrorCode.CORE00001);
//        }

        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
                    && avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00031);
            }
        }

        // 记录流水
        String lastOrder = jourService.addJour(dbAccount, transAmount, channelType, channelOrder,
                refNo, null, bizCategory.getCode(), bizCategory.getValue(), bizType.getCode(),
                bizType.getValue(), String.format(remark.getRemark(), args),
                String.format(remark.getEnRemark(), args));

        // 更改余额
        BigDecimal nowAmount = dbAccount.getAmount().add(transAmount);
        BigDecimal aAmount = dbAccount.getAvailableAmount().add(transAmount);
        dbAccount.setAmount(nowAmount);
        dbAccount.setAvailableAmount(aAmount);
        if (transAmount.compareTo(BigDecimal.ZERO) > 0) {
            dbAccount.setInAmount(dbAccount.getInAmount().add(transAmount));
        }
        dbAccount.setLastOrder(lastOrder);
        accountMapper.updateByPrimaryKeySelective(dbAccount);

//        User user = userService.detail(dbAccount.getUserId());
//
//        if (user.getKind().equals(EUserKind.C.getCode())) {
//            //检查用户是否激活
//            userService.setUpMemberFlag(user);
//        }

//        String title = "账户金额变动提醒";
//        String content = "";
//        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String now = sd.format(new Date());
//        if (BigDecimal.ZERO.compareTo(transAmount) > 0) {
//            content =
//                    "尊敬的用户您好" + now + " " + "您的" + dbAccount.getCurrency() + "账户减少" + transAmount.negate()
//                            .stripTrailingZeros().toPlainString() +
//                            "金额，剩余可用余额为" + dbAccount.getAmount().stripTrailingZeros().toPlainString() + "请注意查收";
//        } else if (BigDecimal.ZERO.compareTo(transAmount) < 0) {
//            content =
//                    "尊敬的用户您好" + now + " " + "您的" + dbAccount.getCurrency() + "账户增加" + transAmount
//                            .stripTrailingZeros().toPlainString() +
//                            "金额，剩余可用余额为" + dbAccount.getAmount().stripTrailingZeros().toPlainString() + "请注意查收";
//        }
//        smsService.sendMyMsg(user, title, content, ESmsRefType.CHANGE_AMOUNT.getCode(), ESmsRefType.CHANGE_AMOUNT.getValue());
        return dbAccount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void transAmount(Account fromAccount, Account toAccount, BigDecimal transAmount,
                                EJourCommon bizCategory, EJourCommon fromBizType, EJourCommon toBizType,
                                T refNo, EJourCommon remark, Object... args) {
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        if (transAmount.compareTo(BigDecimal.ZERO) >= 0 && transAmount.compareTo(new BigDecimal("0.01")) <= 0) {
            return;
        }

        String fromAccountNumber = fromAccount.getAccountNumber();
        String toAccountNumber = toAccount.getAccountNumber();
        if (fromAccountNumber.equals(toAccountNumber)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "来去双方账号一致，无需内部划转");
        }

        this.changeAmount(fromAccount, transAmount.negate(), EChannelType.INNER.getCode(),
                refNo, bizCategory.getCode(), bizCategory.getValue(), fromBizType.getCode(),
                fromBizType.getValue(), String.format(remark.getRemark(), args),
                String.format(remark.getEnRemark(), args));
        this.changeAmount(toAccount, transAmount, EChannelType.INNER.getCode(),
                refNo, bizCategory.getCode(), bizCategory.getValue(), toBizType.getCode(),
                toBizType.getValue(), String.format(remark.getRemark(), args),
                String.format(remark.getEnRemark(), args));
    }

    @Override
    public <T> Account frozenAmount(Account dbAccount, BigDecimal freezeAmount,
                                    EJourCommon bizCategory, EJourCommon bizType, T refNo, EJourCommon remark,
                                    Object... args) {
        if (freezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return dbAccount;
        }

        BigDecimal avaliableAmount = dbAccount.getAvailableAmount().subtract(freezeAmount);
        if (avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(EErrorCode.CORE00001);
        }

        // 记录冻结流水
        String lastOrder = jourService.addFrozenJour(dbAccount, freezeAmount,
                EChannelType.INNER.getCode(), refNo.toString(), bizCategory.getCode(),
                bizCategory.getValue(), bizType.getCode(), bizType.getValue(),
                String.format(remark.getRemark(), args), String.format(remark.getEnRemark(), args));

        BigDecimal nowFrozenAmount = dbAccount.getFrozenAmount().add(freezeAmount);
        BigDecimal aAmount = dbAccount.getAvailableAmount().subtract(freezeAmount);
        dbAccount.setFrozenAmount(nowFrozenAmount);
        dbAccount.setAvailableAmount(aAmount);
        dbAccount.setLastOrder(lastOrder);
        accountMapper.updateByPrimaryKeySelective(dbAccount);
        return dbAccount;
    }

    @Override
    public <T> Account unfrozenAmount(Account dbAccount, BigDecimal unfreezeAmount,
                                      EJourCommon bizCategory, EJourCommon bizType, T refNo, EJourCommon remark,
                                      Object... args) {
        if (unfreezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return dbAccount;
        }

        BigDecimal nowFrozenAmount = dbAccount.getFrozenAmount().subtract(unfreezeAmount);
        if (nowFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(EErrorCode.CORE00007);
        }

        BigDecimal avaliableAmount = dbAccount.getAvailableAmount().add(unfreezeAmount);
        if (avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(EErrorCode.CORE00001);
        }

        // 记录解冻流水
        String lastOrder = jourService.addUnFrozenJour(dbAccount, unfreezeAmount,
                EChannelType.INNER.getCode(), refNo.toString(), bizCategory.getCode(),
                bizCategory.getValue(), bizType.getCode(), bizType.getValue(),
                String.format(remark.getRemark(), args), String.format(remark.getEnRemark(), args));

        dbAccount.setFrozenAmount(nowFrozenAmount);
        dbAccount.setAvailableAmount(avaliableAmount);
        dbAccount.setLastOrder(lastOrder);
        accountMapper.updateByPrimaryKeySelective(dbAccount);
        return dbAccount;
    }

    /**
     * OSS:手动加减账户金额
     */
    @Override
    public void changeAccount(User operator, AccountManualChangeReq req, String ip, AssetBiz bizType) {
        // 查找用户对应账户
        Account account = getAccount(req.getUserId(), req.getCurrency());

        Account corpAccount = new Account();
        // 判断类型
        if (EBoolean.NO.getCode().equals(req.getType())) {
            if (req.getAmount().compareTo(account.getAvailableAmount()) > 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "账户余额不足");
            }

            Long refNo = IdGeneratorUtil.generator();
            transAmount(account, corpAccount, req.getAmount(),
                    AssetBiz.AmountChange, bizType,
                    bizType, refNo, bizType, req.getRemark());


        } else if (EBoolean.YES.getCode().equals(req.getType())) {
            Long refNo = IdGeneratorUtil.generator();

            transAmount(corpAccount, account, req.getAmount(),
                    AssetBiz.AmountChange, bizType, bizType, refNo, bizType, req.getRemark());

        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "类型错误");
        }

        // 生成操作日志
        String content = "账户更新：管理员修改账户金额" + account.getAccountNumber();
        userLogService.create(operator, EUserLogType.ACCOUNT.getCode(), EUserLogType.ACCOUNT.getValue(), ip, content);
    }

    /**
     * OSS:手动加减账户金额
     */
    @Override
    public void changeCorpAccount(User operator, AccountManualCorpChangeReq req, String ip) {
////        req.setAmount(req.getAmount().setScale(2, BigDecimal.ROUND_DOWN));
////        Account account = new Account();
////        // 查找用户对应账户
////        if (ECurrency.CNY.getCode().equals(req.getCurrency())) {
////            account = getAccount(ESystemAccount.CORP.DROP.getAccountNumber());
////        } else if (ECurrency.DEW.getCode().equals(req.getCurrency())) {
////            account = getAccount(ESystemAccount.CORP.DROP_DEW.getAccountNumber());
////        } else {
////            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无该币种的空投账户");
////        }
////
////        // 判断类型
////        if (EBoolean.NO.getCode().equals(req.getType())) {
////            if (req.getAmount().compareTo(account.getAvailableAmount()) > 0) {
////                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "账户余额不足");
////            }
////            changeAmount(account, req.getAmount().negate(), EChannelType.INNER.getCode(), null, "手动变更",
////                    AssetBiz.AmountChange, AssetBiz.ManualChange,
////                    AssetBiz.ManualChange, req.getRemark());
////        } else if (EBoolean.YES.getCode().equals(req.getType())) {
////            changeAmount(account, req.getAmount(), EChannelType.INNER.getCode(), null, "手动变更",
////                    AssetBiz.AmountChange, AssetBiz.ManualChange,
////                    AssetBiz.ManualChange, req.getRemark());
////        } else {
////            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "类型错误");
////        }
//
//        // 生成操作日志
//        String content = "账户更新：管理员修改空投账户金额" + account.getAccountNumber();
//        userLogService.create(operator, EUserLogType.ACCOUNT.getCode(), EUserLogType.ACCOUNT.getValue(), ip, content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transCreditAmount(Account fromAccount, Account toAccount, BigDecimal creditAmount) {
//        refreshCreditAmount(fromAccount.getAccountNumber(),
////                fromAccount.getCreditAmount().subtract(creditAmount));
////        refreshCreditAmount(toAccount.getAccountNumber(),
////                toAccount.getCreditAmount().add(creditAmount));
    }

    private <T> void changeAmount(Account dbAccount, BigDecimal transAmount,
                                  String channelType, T refNo, String bizCategory, String bizCategoryNote,
                                  String bizType, String bizNote, String remark, String enRemark) {

        // 如果变动金额为0，直接返回
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        if (transAmount.compareTo(BigDecimal.ZERO) >= 0 && transAmount.compareTo(new BigDecimal("0.01")) <= 0) {
            return;
        }

        // 金额变动之后可用余额
        BigDecimal avaliableAmount = dbAccount.getAmount()
                .subtract(dbAccount.getFrozenAmount()).add(transAmount);

        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001.getCode(), "账户可用余额不足");
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
//                    || SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP_DIAMOND.getCode().equals(dbAccount.getAccountNumber())
            ) {
                dbAccount.setOutAmount(dbAccount.getOutAmount().add(transAmount.negate()));
            }
//            if ((SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
//                    || SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP_DEW.getCode().equals(dbAccount.getAccountNumber()))
//                    && avaliableAmount.compareTo(BigDecimal.ZERO) < 0) {
//                throw new BizException(EErrorCode.CORE00031.getCode(), "空投账户可用余额不足");
//            }
        }

        // 记录流水
        String lastOrder = jourService.addJour(dbAccount, transAmount, channelType, null,
                refNo, null, bizCategory, bizCategoryNote, bizType, bizNote, remark, enRemark);

        BigDecimal nowAmount = dbAccount.getAmount().add(transAmount);
        BigDecimal aAmount = dbAccount.getAvailableAmount().add(transAmount);
        dbAccount.setAmount(nowAmount);
        dbAccount.setAvailableAmount(aAmount);
        dbAccount.setLastOrder(lastOrder);

        accountMapper.updateByPrimaryKeySelective(dbAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Account getAccountFront(Long userId, String currency, String withdrawAmountFlag) {
        Account account;
        String id = userId.toString().concat(currency);
        long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(id, String.valueOf(time))) {
            throw new  BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请求过于频繁,请稍等");
        }
        try {
            Account condition = new Account();
            condition.setUserId(userId);
            condition.setCurrency(currency);

            List<Account> accountList = accountMapper.selectByCondition(condition);
            if (CollectionUtils.isEmpty(accountList)) {
                User user = userService.detailBrief(userId);
                account = distributeAccount(userId, user.getKind(), currency);
            } else {
                account = accountList.get(0);
                account = accountMapper.selectByPrimaryForUpdate(account.getId());
            }
            account.setAvailableAmount(account.getAvailableAmount().setScale(2, RoundingMode.DOWN));
            account.setFrozenAmount(account.getFrozenAmount().setScale(2, RoundingMode.DOWN));

            if (ECurrency.CNY.getCode().equals(currency) && EBoolean.YES.getCode().equals(withdrawAmountFlag)) {
//                WithdrawListReq req = new WithdrawListReq();
//                req.setAccountNumber(account.getAccountNumber());
//                req.setStatusList(EWithdrawStatus.getWithdrawingStatusList());
//                List<Withdraw> list = withdrawService.list(req);
//                if (CollectionUtils.isNotEmpty(list)) {
//                    account.setWithdrawalAmount(list.get(0).getAmount().setScale(2, RoundingMode.DOWN));
//                } else {
//                    account.setWithdrawalAmount(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN));
//                }
                account.setWithdrawalAmount(BigDecimal.ZERO.setScale(2, RoundingMode.DOWN));
            }

            return account;

        } finally {
//            rowLockUtil.unlock(id);
            redisLock.unlock(id, String.valueOf(time));
        }
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Account getAccount(Long userId, String currency) {
        Account account;
//        String id = userId.toString().concat(currency);
//        long time = System.currentTimeMillis() + metaLockTimeout;
//        if (!redisLock.lock(id, String.valueOf(time))) {
//            throw new com.ais.common.exception.BizException(com.ais.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "账户请求过于频繁,请稍等");
//        }
//        try {
        Account condition = new Account();
        condition.setUserId(userId);
        condition.setCurrency(currency);

        List<Account> accountList = accountMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(accountList)) {
            User user = userService.detailBrief(userId);
            try {
                account = distributeAccount(userId, user.getKind(), currency);
            } catch (Exception e) {
                throw new  BizException( ECommonErrorCode.BIZ_DEFAULT.getCode(), "账户请求过于频繁,请稍等");
            }
        } else {
            account = accountList.get(0);
            account = accountMapper.selectByPrimaryForUpdate(account.getId());
        }

        return account;

//        } finally {
////            rowLockUtil.unlock(id);
//            redisLock.unlock(id, String.valueOf(time));
//        }
    }


    @Override
    public Account getAccountUnCheck(Long userId, String currency) {
        Account condition = new Account();
        condition.setUserId(userId);
        condition.setCurrency(currency);

        List<Account> accountList = accountMapper.selectByCondition(condition);
        return accountList.get(0);
    }

    @Override
    public Account getAccount(String accountNumber) {
        Account condition = new Account();
        condition.setAccountNumber(accountNumber);

        List<Account> accountList = accountMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(accountList)) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(),
                    accountNumber);
        }

        init(accountList.get(0));

        return accountList.get(0);
    }

    @Override
    public Account getAccountForUpdate(String accountNumber) {
        Account condition = new Account();
        condition.setAccountNumber(accountNumber);

        List<Account> accountList = accountMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(accountList)) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(),
                    accountNumber);
        }

        return accountMapper.selectByPrimaryForUpdate(accountList.get(0).getId());
    }

    @Override
    public Account getAccountById(Long id) {
        Account account = accountMapper.selectByPrimaryKey(id);
        if (null == account) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        init(account);
        return account;
    }

    public Account getAccountByUserId(Long id) {
        Account account = accountMapper.selectByUserId(id);
        if (null == account) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        init(account);
        return account;
    }

    /**
     * 分页查询账户
     *
     * @param req 分页查询账户入参
     * @return 分页账户对象
     */
    @Override
    public List<Account> page(AccountPageReq req) {
        Account condition = EntityUtils.copyData(req, Account.class);

        List<Account> accountList = accountMapper.select(condition);

        Map<Long, User> map = new HashMap<>();
        for (Account account : accountList) {
            init(account);
            User user = map.get(account.getUserId());
            if (null == user) {
                user = userService.selectSummaryInfo(account.getUserId());
                map.put(account.getUserId(), user);
            }

            account.setUserInfo(user);
        }

        return accountList;
    }

    /**
     * 列表查询账户
     *
     * @param req 列表查询账户入参
     * @return 列表账户对象
     */
    @Override
    public List<Account> list(AccountListReq req) {
        Account condition = EntityUtils.copyData(req, Account.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Account.class));

        List<Account> list = accountMapper.selectByCondition(condition);

        for (Account account : list) {
            init(account);
        }

        return list;
    }

    @Override
    public List<Account> listFront(AccountListFrontReq req, User operator) {
        Account condition = EntityUtils.copyData(req, Account.class);
        condition.setUserId(operator.getId());
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Account.class));

        return accountMapper.selectByCondition(condition);
    }

    @Override
    public List<AccountAmountSumListRes> amountSumList(AccountAmountSumListReq request) {
        List<AccountAmountSumListRes> resList = new ArrayList<>();
        for (ECurrency currency : ECurrency.values()) {
//            if (StringUtils.isNotBlank(request.getCurrency()) && !request.getCurrency().equals(currency.getCode())) {
//                continue;
//            }
//            AccountAmountSumListRes amountSumListRes = new AccountAmountSumListRes();
//            if (EAccountStatisticsFlag.E_ACCOUNT_STATISTICS_FLAG_0.getCode().equals(request.getStatisticsFlag())) {
//                amountSumListRes = accountMapper.selectamountSumListExcept(currency.getCode());
//            } else if (EAccountStatisticsFlag.E_ACCOUNT_STATISTICS_FLAG_1.getCode().equals(request.getStatisticsFlag())) {
//                amountSumListRes = accountMapper.selectamountSumListNotExcept(currency.getCode());
//            } else {
//                amountSumListRes = accountMapper.selectamountSumList(currency.getCode());
//            }
//            amountSumListRes.setCurrency(currency.getCode());
//
//            resList.add(amountSumListRes);
        }

        return resList;
    }


    @Override
    public List<Account> list(Account req) {

        return accountMapper.selectByCondition(req);
    }

    private void init(Account account) {
        User userInfo = userService.selectSummaryInfo(account.getUserId());
        account.setUserInfo(userInfo);
    }

    /**
     * 余额和锁仓金额转(待结算)
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> void fromAmountToLockAmount(Account fromAccount, Account toAccount, BigDecimal transAmount,
                                           EJourCommon bizCategory, EJourCommon fromBizType, EJourCommon toBizType,
                                           T refNo, EJourCommon remark, Object... args) {

        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        this.changeAmount(fromAccount, transAmount.negate(), EChannelType.INNER.getCode(),
                refNo, bizCategory.getCode(), bizCategory.getValue(), fromBizType.getCode(),
                fromBizType.getValue(), String.format(remark.getRemark(), args),
                String.format(remark.getEnRemark(), args));
        this.changeLockAmount(toAccount, transAmount, EChannelType.INNER.getCode(),
                refNo, bizCategory.getCode(), bizCategory.getValue(), toBizType.getCode(),
                toBizType.getValue(), String.format(remark.getRemark(), args),
                String.format(remark.getEnRemark(), args));
    }


    @Transactional(rollbackFor = Exception.class)
    public <T> void changeLockAmount(Account dbAccount, BigDecimal transAmount,
                                     String channelType, T refNo, String bizCategory, String bizCategoryNote,
                                     String bizType, String bizNote, String remark, String enRemark) {

        // 如果变动金额为0，直接返回
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 金额变动之后可用余额
        BigDecimal avAliAbleAmount = dbAccount.getLockAmount().add(transAmount);

        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (avAliAbleAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
                    && avAliAbleAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00031);
            }
        }

        // 变动之后金额
        BigDecimal nowAmount = dbAccount.getLockAmount().add(transAmount);

        // 记录流水
        String lastOrder = jourService.addJour(dbAccount, transAmount, channelType, null,
                refNo, null, bizCategory, bizCategoryNote, bizType, bizNote, remark, enRemark);

        dbAccount.setLockAmount(nowAmount);
        dbAccount.setLastOrder(lastOrder);

        accountMapper.updateByPrimaryKeySelective(dbAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void changeLockAmountUnJour(Account dbAccount, BigDecimal transAmount) {

        // 如果变动金额为0，直接返回
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 金额变动之后待结算金额
        BigDecimal nowLockAmount = dbAccount.getLockAmount().add(transAmount);

        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (nowLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
                    && nowLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00031);
            }
        }

        dbAccount.setLockAmount(nowLockAmount);

        accountMapper.updateByPrimaryKeySelective(dbAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void changeFrozenAmountUnJour(Account dbAccount, BigDecimal transAmount) {

        // 如果变动金额为0，直接返回
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 金额变动之后冻结金额
        BigDecimal nowFrozenAmount = dbAccount.getFrozenAmount().add(transAmount);
        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (nowFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
                    && nowFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00031);
            }
        }

        dbAccount.setFrozenAmount(nowFrozenAmount);
        dbAccount.setAmount(dbAccount.getAmount().add(transAmount));

        accountMapper.updateByPrimaryKeySelective(dbAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> void innerFromLockAmountToAmount(Account dbAccount, Jour jour, BigDecimal transAmount, String status) {

        //当前变动金额
        BigDecimal nowTransAmount = BigDecimal.ZERO;
        if (transAmount != null) {
            nowTransAmount = transAmount;
        } else {
            nowTransAmount = jour.getTransAmount();
        }

        // 如果变动金额为0，直接返回
        if (nowTransAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 金额变动之后待结算余额
        BigDecimal nowLockAmount = dbAccount.getLockAmount().add(nowTransAmount.negate());

        // 用户账户不能为负数
        if (!dbAccount.getAccountNumber().startsWith(SYS_ACCOUNT_PREF.SYS_ACCOUNT_PREF.getCode())) {
            if (nowLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        } else {
            //系统账户，空投账户不能为负数
            if (SYS_ACCOUNT_PREF.SYS_ACCOUNT_DROP.getCode().equals(dbAccount.getAccountNumber())
                    && nowLockAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new BizException(EErrorCode.CORE00031);
            }
        }

        dbAccount.setLockAmount(nowLockAmount);
        dbAccount.setAmount(dbAccount.getAmount().add(nowTransAmount));
        dbAccount.setAvailableAmount(dbAccount.getAvailableAmount().add(nowTransAmount));
        // 总入金
//        dbAccount.setInAmount(dbAccount.getInAmount().add(nowTransAmount));
        accountMapper.updateByPrimaryKeySelective(dbAccount);

        // 更改流水
        jourService.updateStatus(dbAccount, jour, transAmount, status);
    }

    @Override
    public Account getAccount(Long userId, String type, String currency) {

        Account account = new Account();
        account.setUserId(userId);
        account.setType(type);
        account.setCurrency(currency);
        List<Account> accountList = accountMapper.selectByCondition(account);

        if (CollectionUtils.isNotEmpty(accountList)) {
            return accountList.get(0);
        }

        return null;
    }


}