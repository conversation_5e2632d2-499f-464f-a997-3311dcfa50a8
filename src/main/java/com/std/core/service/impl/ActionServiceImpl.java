package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.mapper.ActionMapper;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.request.ActionCreateReq;
import com.std.core.pojo.request.ActionListReq;
import com.std.core.pojo.request.ActionModifyReq;
import com.std.core.pojo.request.ActionPageReq;
import com.std.core.service.IActionService;
import com.std.core.util.IdGeneratorUtil;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @author: haiqingzheng
 * @since: 2019-01-07 13:53
 * @description:
 */
@Service
public class ActionServiceImpl implements IActionService {

    @Resource
    ActionMapper actionMapper;

    @Override
    public Long create(ActionCreateReq request) {
        actionValidator(request.getUrl());

        Action action = EntityUtils.copyData(request, Action.class);
        action.setId(IdGeneratorUtil.generator());

        actionMapper.insertSelective(action);

        return action.getId();
    }

    @Override
    public void modify(ActionModifyReq request) {
        detail(request.getId());

        Action action = EntityUtils.copyData(request, Action.class);

        actionMapper.updateByPrimaryKeySelective(action);
    }

    @Override
    public void remove(Long id) {
        actionMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Action detail(Long id) {
        Action action = actionMapper.selectByPrimaryKey(id);
        if (action == null) {
            throw new BizException(
                    ECommonErrorCode.E500003.getCode(), ECommonErrorCode.E500003.getValue(), "接口", id);
        }

        return action;
    }

    @Override
    public List<Action> page(ActionPageReq request) {
        Action condition = EntityUtils.copyData(request, Action.class);

        return actionMapper.selectByCondition(condition);
    }

    @Override
    public List<Action> list(ActionListReq request) {
        Action condition = EntityUtils.copyData(request, Action.class);
        condition.setOrderBy(SqlUtil.parseSort(request.getSort(), Action.class));

        return actionMapper.selectByCondition(condition);
    }

    @Override
    public List<Action> listConditionByRole(List<Long> roleList) {
        return actionMapper.selectByConditionByRole(roleList);
    }

    @Override
    public List<Action> selectByConditionByMenu(Long menuId) {
        return actionMapper.selectByConditionByMenu(menuId);
    }

    /**
     * 接口参数验证
     *
     * @param url 接口URL
     */
    private void actionValidator(String url) {
        long count = actionMapper.selectByActionUrl(url);
        if (count != 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "接口URL已存在");
        }
    }
}
