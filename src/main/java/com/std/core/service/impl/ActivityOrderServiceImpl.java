package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.ActivityOrderMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.SysConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.std.common.utils.DateUtil.*;

/**
 * 活动预约单ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 23:20
 */
@Slf4j
@Service
public class ActivityOrderServiceImpl implements IActivityOrderService {

    @Resource
    private ActivityOrderMapper activityOrderMapper;

    @Resource
    private IUserService userService;
    @Resource
    private IWechatService wechatService;

    @Resource
    private IActivityService activityService;

    @Resource
    private ISmsService smsService;

    @Resource
    private IGoodsOrderService goodsOrderService;

    @Resource
    private IActivityTicketLineService activityTicketLineService;

    @Resource
    private IBigActivityOrderService bigActivityOrderService;

    @Resource
    private IActivityOrderStatisticsService activityOrderStatisticsService;

    @Resource
    private IDailyIncomeSummaryService dailyIncomeSummaryService;
    /**
     * 新增活动预约单
     *
     * @param req      新增活动预约单入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(ActivityOrderCreateReq req, User operator) {
        ActivityTicketLine activityTicketLine = activityTicketLineService.detail(req.getTicketLineId());
        Activity activity = activityService.detailForUpdate(activityTicketLine.getActivityId());

        if (EUserReservationStatus.LOCK.getCode().equals(operator.getReservationStatus())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您已有 3 次未到场记录，暂时无法预约活动，限制至：" + operator.getReservationUnsealTime());
        }

        Date date = new Date();
        if (date.before(activity.getBuyStartTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前预约还未开启");
        }else if (date.after(activity.getBuyEndTime())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预约已结束");
        }

        if (DateUtil.strToDate(req.getDate(), DATA_TIME_PATTERN_9).before(activity.getStartTime())
                || DateUtil.strToDate(req.getDate(), DATA_TIME_PATTERN_9).after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预约时间不在活动时间内");
        }
        //上架中能约
        if (!EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode().equals(activityTicketLine.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有上架中的票档可以预约");
        }

        if (activityTicketLine.getInventory() < req.getNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前档位余票不足");
        }

        //约满了不能约
        if (!activityOrderStatisticsService.canOrder(DateUtil.strToDate(req.getDate(), DATA_TIME_PATTERN_9), activity, req.getNumber())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选日期票档已约满");
        }

        //剩下的票不够不能
        if (activityTicketLine.getInventory() < req.getNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "票档剩余票数不足");
        }
        if (req.getNumber() < activity.getMinimumBuyNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您选择的票档最小购买数量为" + activity.getMinimumBuyNumber());
        }

        Integer maximumBuyNumber = activity.getMaximumBuyNumber();
        ActivityOrder condition = new ActivityOrder();
        condition.setUserId(operator.getId());
        List<String> statusList = new ArrayList<>();
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode());
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_1.getCode());
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_2.getCode());
        condition.setStatusList(statusList);
        condition.setDate(DateUtil.strToDate(req.getDate(), DATA_TIME_PATTERN_9));
        condition.setActivityId(activity.getId());
        List<ActivityOrder> list = list(condition);

        condition = new ActivityOrder();
        condition.setUserId(operator.getId());
        condition.setStatusList(statusList);
        condition.setActivityId(activity.getId());
        List<ActivityOrder> list1 = list(condition);
        if (req.getNumber() + list1.size() > activity.getLimit()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您该活动预约已达上限");
        }
        if (req.getNumber() + list.size() > maximumBuyNumber) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您当日预约已达上限");
        }
        int i = activityTicketLineService.modifyInventory(activityTicketLine.getId(), req.getNumber());
        if (i <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前档位余票不足");
        }
        // 先生成大订单
        BigActivityOrder bigActivityOrder = new BigActivityOrder();
        bigActivityOrder.setSerialNumber(IdGeneratorUtil.generator().toString());
        bigActivityOrder.setUserId(operator.getId());
        bigActivityOrder.setActivityId(activity.getId());
        bigActivityOrder.setTicketLineId(activityTicketLine.getId());
        bigActivityOrder.setTicketLineName(activityTicketLine.getName());
        bigActivityOrder.setPic(activity.getPic());
        bigActivityOrder.setName(req.getName());
        bigActivityOrder.setContact(req.getContact());
        bigActivityOrder.setTotalPrice(activityTicketLine.getPrice().multiply(new BigDecimal(req.getNumber())));
        bigActivityOrder.setNumber(req.getNumber());
        bigActivityOrder.setPayType(EBigActivityOrderPayType.BIG_ACTIVITY_ORDER_PAYTYPE_0.getCode());
        bigActivityOrder.setStatus(EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_0.getCode());
        bigActivityOrder.setCreateDatetime(new Date());
        bigActivityOrder.setDate(DateUtil.strToDate(req.getDate(), DATA_TIME_PATTERN_9));
        bigActivityOrderService.create(bigActivityOrder);

//         12.25微信支付
        BigDecimal payAmount = bigActivityOrder.getTotalPrice();
        WechatAppPayInfo wechatAppPayInfo = new WechatAppPayInfo();
//        微信支付，返回签名信息
        if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
            wechatAppPayInfo = wechatService.getAppPayInfo(
                    operator.getId(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_1.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_1.getValue(),
                    Long.valueOf(bigActivityOrder.getSerialNumber()),
                    payAmount, null);
        } else if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付金额不能小于0");
        } else {
            // 金额为 0 直接成功
            bigActivityOrderService.serveDoCallback(bigActivityOrder.getSerialNumber());
        }
        return new OrderPayRes(bigActivityOrder.getId(), EBoolean.YES.getCode(), wechatAppPayInfo);

    }


    @Override
    public boolean canOrder(Date date, Activity activity, int number) {

        int todayOrder = 0;
        ActivityOrder condition = new ActivityOrder();
        List<String> statusList = new ArrayList<>();
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode());
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_1.getCode());
        statusList.add(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_2.getCode());
        condition.setStatusList(statusList);
        condition.setDate(date);
        condition.setActivityId(activity.getId());

//        todayOrder = listSumNumber(condition);

        List<ActivityOrder> list = list(condition);
        for (ActivityOrder activityOrder : list) {
            todayOrder += activityOrder.getNumber();
        }
        // 如果本次的数量加上已预约的数量超出，表示不能再预约
        if (todayOrder + number > activity.getDayLimit()) {
            return false;
        } else {
            return true;
        }
    }

    private int listSumNumber(ActivityOrder condition) {
        return activityOrderMapper.selectSumNumber(condition);
    }


    /**
     * 删除活动预约单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        activityOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改活动预约单
     *
     * @param req      修改活动预约单入参
     * @param operator 操作人
     */
    @Override
    public void modify(ActivityOrderModifyReq req, User operator) {
        ActivityOrder activityOrder = EntityUtils.copyData(req, ActivityOrder.class);
        activityOrderMapper.updateByPrimaryKeySelective(activityOrder);
    }

    @Override
    public void modify(ActivityOrder req) {
        activityOrderMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询活动预约单
     *
     * @param id 主键ID
     * @return 活动预约单对象
     */
    @Override
    public ActivityOrder detail(Long id) {
        ActivityOrder activityOrder = activityOrderMapper.selectByPrimaryKey(id);
        if (null == activityOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        activityOrder.setUser(userService.selectSummaryInfo(activityOrder.getUserId()));

        return activityOrder;
    }

    @Override
    public ActivityOrder detailForUpdate(Long id) {
        ActivityOrder activityOrder = activityOrderMapper.selectForUpdate(id);
        if (null == activityOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return activityOrder;
    }

    /**
     * 分页查询活动预约单
     *
     * @param req 分页查询活动预约单入参
     * @return 分页活动预约单对象
     */
    @Override
    public List<ActivityOrder> page(ActivityOrderPageReq req) {
        ActivityOrder condition = EntityUtils.copyData(req, ActivityOrder.class);

        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(condition);
        // 转译UserId
        activityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            Activity activity = activityService.detail(item.getActivityId());
            item.setActivityName(activity.getName());
        });

        return activityOrderList;
    }

    /**
     * 列表查询活动预约单
     *
     * @param req 列表查询活动预约单入参
     * @return 列表活动预约单对象
     */
    @Override
    public List<ActivityOrder> list(ActivityOrderListReq req) {
        ActivityOrder condition = EntityUtils.copyData(req, ActivityOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ActivityOrder.class));

        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(condition);
        // 转译UserId
        activityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return activityOrderList;
    }

    @Override
    public List<ActivityOrder> list(ActivityOrder req) {

        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(req);
        return activityOrderList;
    }

    /**
     * 前端详情查询活动预约单
     *
     * @param id 主键ID
     * @return 活动预约单对象
     */
    @Override
    public ActivityOrderDetailRes detailFront(Long id) {
        ActivityOrderDetailRes res = new ActivityOrderDetailRes();

        ActivityOrder activityOrder = activityOrderMapper.selectByPrimaryKey(id);
        if (null == activityOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        activityOrder.setUser(userService.selectSummaryInfo(activityOrder.getUserId()));

        BeanUtils.copyProperties(activityOrder, res);
        Activity activity = activityService.detail(activityOrder.getActivityId());
        res.setActivityName(activity.getName());
        res.setAddress(activity.getAddress());
        res.setStartTime(activity.getStartTime());
        res.setEndTime(activity.getEndTime());
        Date date = activityOrder.getDate();
        //过期了
        if (date.before(new Date())) {
            res.setCanModify(EBoolean.NO.getCode());
            res.setCanCancle(EBoolean.NO.getCode());
        }
        //没过期
        else {
            long daysDifference = calculateDaysDifference(DateUtil.getTodayStart(), date);
            if (daysDifference >= 5) {
                if (activityOrder.getIsModify().equals("0")) {
                    res.setCanModify(EBoolean.YES.getCode());
                } else {
                    res.setCanModify(EBoolean.NO.getCode());
                }
            } else {
                res.setCanModify(EBoolean.NO.getCode());
            }

            if (daysDifference < 3) {
                res.setCanCancle(EBoolean.NO.getCode());
            } else {
                res.setCanCancle(EBoolean.YES.getCode());
            }
        }
        res.setLongitude(activity.getLongitude());
        res.setLatitude(activity.getLatitude());
        res.setProvince(activity.getProvince());
        res.setCity(activity.getCity());
        res.setCounty(activity.getCounty());
        res.setExpirationDatetime(DateUtil.getEndDatetimeByDate(activityOrder.getDate()));
        res.setPayDatetime(activityOrder.getUpdateDatetime());
        res.setAddressLocation(activity.getAddressLocation());
        return res;
    }


    public static void main(String[] args) {
//        Date date1 = DateUtil.strToDate("2025-03-24 00:00:00", DATA_TIME_PATTERN_1);
////        Date date2 = DateUtil.strToDate("2025-03-17 00:00:00",DATA_TIME_PATTERN_1);
//        Date date2 = DateUtil.getTodayStart();
//        long diffInMillies = Math.abs(date2.getTime() - date1.getTime());
//        long l = diffInMillies / (24 * 60 * 60 * 1000);
//        System.out.println(l);
//
//        int i = DateUtil.daysBetween(DateUtil.getTodayStart(), date1);
//        System.out.println(i);

        Date date = DateUtils.addMonths(new Date(), -3);
        System.out.println(DateUtil.dateToStr(date, DateUtil.DATA_TIME_PATTERN_1));
    }

    private long calculateDaysDifference(Date date1, Date date2) {
        long diffInMillies = Math.abs(date2.getTime() - date1.getTime());
        return diffInMillies / (24 * 60 * 60 * 1000);
    }

    /**
     * 前端分页查询活动预约单
     *
     * @param req 前端分页查询活动预约单入参
     * @return 分页活动预约单对象
     */
    @Override
    public List<ActivityOrderPageRes> pageFront(ActivityOrderPageFrontReq req) {
        ActivityOrder condition = EntityUtils.copyData(req, ActivityOrder.class);
        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(condition);
        // 转译UserId
        activityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<ActivityOrderPageRes> resList = activityOrderList.stream().map((entity) -> {
            ActivityOrderPageRes res = new ActivityOrderPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(activityOrderList, resList);
    }

    /**
     * 前端列表查询活动预约单
     *
     * @param req 前端列表查询活动预约单入参
     * @return 列表活动预约单对象
     */
    @Override
    public List<ActivityOrderListRes> listFront(ActivityOrderListFrontReq req) {
        ActivityOrder condition = EntityUtils.copyData(req, ActivityOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ActivityOrder.class));

        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(condition);
        // 转译UserId
        activityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<ActivityOrderListRes> resList = activityOrderList.stream().map((entity) -> {
            ActivityOrderListRes res = new ActivityOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyDate(ActivityOrderModifyDateReq request, User operator) {
        ActivityOrder activityOrder = detailForUpdate(request.getId());
        Date date = DateUtil.strToDate(request.getDate(), DATA_TIME_PATTERN_9);
        Activity activity = activityService.detailForUpdate(activityOrder.getActivityId());
        if (EBoolean.YES.getCode().equals(activityOrder.getIsModify())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预约时间只能修改一次");
        }
//        if (!EActivityStatus.ACTIVITY_STATUS_2.getCode().equals(activity.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有售票中的活动可以预约");
//        }
        if (new Date().after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动已结束，无法预约");
        }
        if (date.before(activity.getStartTime()) || date.after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预约时间必须在活动时间范围内");
        }
        int i = DateUtil.daysBetween(new Date(), activityOrder.getDate());
        if (i <= 5) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "距预约时间少于5天不能修改");
        }
        if (!operator.getId().equals(activityOrder.getUserId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能修改自己的预约");
        }

        //约满了不能约
        if (!activityOrderStatisticsService.canOrder(DateUtil.strToDate(request.getDate(), DATA_TIME_PATTERN_9), activity, 1)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选日期票档已约满");
        }
        activityOrder.setIsModify(EBoolean.YES.getCode());
        activityOrder.setOldDate(activityOrder.getDate());
        activityOrder.setDate(date);
        activityOrderMapper.updateByPrimaryKeySelective(activityOrder);

        // 原来的统计减去
        activityOrderStatisticsService.addTotalTickets(activity.getId(), activityOrder.getOldDate(), -1);
        try {
            // 增加每日统计
            activityOrderStatisticsService.addTotalTickets(activity.getId(), DateUtil.strToDate(request.getDate(), DATA_TIME_PATTERN_9), 1);
        } catch (Exception e) {
            log.error("增加每日统计失败，参数:" + activity.getId() + "," + DateUtil.strToDate(request.getDate(), DATA_TIME_PATTERN_9) + "," + 1);
            log.error("增加每日统计失败，原因:" + e.getMessage());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "网络异常，请重试");
        }
        String title = "【西溪蔡志忠美术馆】活动预约时间修改成功通知";
        String conten = "\"亲爱的访客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "我们已经成功为您修改了【西溪蔡志忠美术馆】活动的预约时间。新的预约详情如下：\n" +
                "\n" +
                "修改后的日期：[" + dateToStr(activityOrder.getDate(), DATA_TIME_PATTERN_9) + "]\n" +
                "入场时间：[" + "9:00" + "] - [" + "17:30" + "]\n" +
                " 活动地点： [" + activity.getProvince() + activity.getCity() + activity.getCounty() + activity.getAddress() + "]\n" +
                "\n" +
                "你可以通过打开【个人中心】-【我的预约】-【待使用】打开入场码，通过工作人员核销入场。\"";
        smsService.sendMyMsg(operator, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

    }

    /**
     * 取消预约
     *
     * @param id
     * @param operator
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancleOrder(Long id, User operator) {
        ActivityOrder activityOrder = detailForUpdate(id);

        if (!EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode().equals(activityOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前状态不能取消");
        }

        int i = DateUtil.daysBetween(DateUtil.getTodayStart(), activityOrder.getDate());
        if (i < 3) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "距预约时间少于3天不能取消");
        }
        if (!operator.getId().equals(activityOrder.getUserId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能取消自己的预约");
        }
        activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_4.getCode());
        activityOrder.setCancleDatetime(new Date());
        //todo 12.25微信退款
        if (activityOrder.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            wechatService.doRefund(activityOrder.getSerialNumber(), activityOrder.getPrice().multiply(new BigDecimal(activityOrder.getNumber())), EJourBizTypeUser.AFTER_SALES.AFTER_SALES_REFUND, activityOrder.getId());
        }

        activityOrderMapper.updateByPrimaryKeySelective(activityOrder);

        // 库存加回去，日统计加回去
        activityTicketLineService.addInventory(activityOrder.getTicketLineId(), 1);
        activityOrderStatisticsService.addTotalTickets(activityOrder.getActivityId(), activityOrder.getDate(), -1);

        //  取消减去历史每日收益
        dailyIncomeSummaryService.activityIncomeSummary(activityOrder.getPrice().negate(), activityOrder.getCreateDatetime());

        String title = "【西溪蔡志忠美术馆】活动预约取消及退款通知";
        String conten = "\"亲爱的访客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "我们已收到您关于【西溪蔡志忠美术馆】活动预约的取消请求，并已成功为您取消了该活动的预约。\n" +
                "\n" +
                "取消的活动日期：[" + dateToStr(activityOrder.getDate(), DATA_TIME_PATTERN_9) + "]\n" +
                "订单金额：" + new BigDecimal(activityOrder.getNumber()).multiply(activityOrder.getPrice()) + "元\n" +
                "\n" +
                "关于退款事宜，我们已启动退款流程，您支付的金额将按照原支付路径进行退回。请注意查收您的支付账户，退款通常会在3-7个工作日内到账，具体到账时间可能因银行或支付平台而异。\"";
        smsService.sendMyMsg(operator, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());


    }

    /**
     * 取消预约
     *
     * @param id
     * @param operator
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancleOrderByOss(Long id, User operator) {
        ActivityOrder activityOrder = detailForUpdate(id);

        if (!EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode().equals(activityOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前状态不能取消");
        }

        activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_4.getCode());
        activityOrder.setCancleDatetime(new Date());
        //todo 12.25微信退款
        if (activityOrder.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            wechatService.doRefund(activityOrder.getSerialNumber(), activityOrder.getPrice().multiply(new BigDecimal(activityOrder.getNumber())), EJourBizTypeUser.AFTER_SALES.AFTER_SALES_REFUND, activityOrder.getId());
        }

        activityOrderMapper.updateByPrimaryKeySelective(activityOrder);

        // 库存加回去，日统计加回去
        activityTicketLineService.addInventory(activityOrder.getTicketLineId(), 1);
        activityOrderStatisticsService.addTotalTickets(activityOrder.getActivityId(), activityOrder.getDate(), -1);

        //  取消减去历史每日收益
        dailyIncomeSummaryService.activityIncomeSummary(activityOrder.getPrice().negate(), activityOrder.getCreateDatetime());
        String title = "【西溪蔡志忠美术馆】活动预约取消及退款通知";
        String conten = "\"亲爱的访客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "我们已收到您关于【西溪蔡志忠美术馆】活动预约的取消请求，并已成功为您取消了该活动的预约。\n" +
                "\n" +
                "取消的活动日期：[" + dateToStr(activityOrder.getDate(), DATA_TIME_PATTERN_9) + "]\n" +
                "订单金额：" + new BigDecimal(activityOrder.getNumber()).multiply(activityOrder.getPrice()) + "元\n" +
                "\n" +
                "关于退款事宜，我们已启动退款流程，您支付的金额将按照原支付路径进行退回。请注意查收您的支付账户，退款通常会在3-7个工作日内到账，具体到账时间可能因银行或支付平台而异。\"";
        User detail = userService.detail(activityOrder.getUserId());
        smsService.sendMyMsg(detail, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());


    }

    @Override
    public List<MyOrderRes> myPageFront(MyOrderReq request, User operator) {
        ActivityOrder condition = EntityUtils.copyData(request, ActivityOrder.class);
        condition.setUserId(operator.getId());
        condition.setStatusList(request.getStatusList());
        List<ActivityOrder> activityOrderList = activityOrderMapper.selectByCondition(condition);
        // 转译UserId
        activityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<MyOrderRes> resList = activityOrderList.stream().map((entity) -> {
            MyOrderRes res = new MyOrderRes();
            BeanUtils.copyProperties(entity, res);
            Activity activity = activityService.detail(entity.getActivityId());
            res.setActivityName(activity.getName());
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(activityOrderList, resList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void serveDoCallback(Long bizCode) {
        log.info("回调处理开始");
//        ActivityOrder activityOrder =detail(bizCode);
//        if(!EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode().equals(activityOrder.getStatus())){
//            log.info("订单状态不是待支付");
//            return;
//        }
//        activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_2.getCode());
//        activityOrderMapper.updateByPrimaryKeySelective(activityOrder);
    }

    @Override
    public void batchCreate(List<ActivityOrder> activityOrderList) {
        activityOrderMapper.insertBatchSelective(activityOrderList);
    }

    @Override
    public List<IncomeItemRes> income() {
        return null;
    }


    @Override
    public List<IncomeItemRes> income(IncomeGroupReq req) {
        Date startTime = req.getStartTime();
        Date endTime = req.getEndTime();
        List<IncomeItemRes> dbResult = new ArrayList<>();
        MinMaxTimeRes minMax = new MinMaxTimeRes();
        // 自动补齐时间范围（使用实体类封装返回结果）
        if (startTime == null || endTime == null) {
            if (EBoolean.NO.getCode().equals(req.getType())) {
                minMax = activityOrderMapper.selectMinMaxCreateTime();
            } else {
                minMax = goodsOrderService.minMaxTime();
            }

            if (minMax == null || minMax.getMinTime() == null || minMax.getMaxTime() == null) {
                return Collections.emptyList(); // 没有数据
            }
            if (startTime == null) {
                startTime = minMax.getMinTime();
                req.setStartTime(startTime);
            }
            if (endTime == null) {
                endTime = minMax.getMaxTime();
                req.setEndTime(endTime);
            }
        }

        startTime = Date.from(req.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                .atStartOfDay(ZoneId.systemDefault()).toInstant());
        req.setStartTime(startTime);

        endTime = Date.from(req.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                .atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        req.setEndTime(endTime);

        if (EBoolean.NO.getCode().equals(req.getType())) {
            dbResult = activityOrderMapper.selectActivityIncomeGroupBy(req);
        } else {
            dbResult = goodsOrderService.income(req);
        }

        // 执行查询R

        // 转成 map 以便后续补0
        Map<String, BigDecimal> incomeMap = dbResult.stream()
                .collect(Collectors.toMap(IncomeItemRes::getTimeGroup, IncomeItemRes::getIncome));

        // 时间补全
        List<IncomeItemRes> result = new ArrayList<>();
        LocalDate startDate = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        String period = req.getPeriod();

        if ("day".equalsIgnoreCase(period)) {
            for (LocalDate current = startDate; !current.isAfter(endDate); current = current.plusDays(1)) {
                String key = current.toString(); // yyyy-MM-dd
                result.add(new IncomeItemRes(key, incomeMap.getOrDefault(key, BigDecimal.ZERO)));
            }
        } else if ("month".equalsIgnoreCase(period)) {
            for (YearMonth current = YearMonth.from(startDate); !current.isAfter(YearMonth.from(endDate)); current = current.plusMonths(1)) {
                String key = current.toString(); // yyyy-MM
                result.add(new IncomeItemRes(key, incomeMap.getOrDefault(key, BigDecimal.ZERO)));
            }
        } else if ("week".equalsIgnoreCase(period)) {
            for (LocalDate current = startDate.with(DayOfWeek.MONDAY); !current.isAfter(endDate); current = current.plusWeeks(1)) {
                String key = current.toString(); // yyyy-MM-dd（每周起始日）
                result.add(new IncomeItemRes(key, incomeMap.getOrDefault(key, BigDecimal.ZERO)));
            }
        } else {
            // 默认按日处理
            for (LocalDate current = startDate; !current.isAfter(endDate); current = current.plusDays(1)) {
                String key = current.toString();
                result.add(new IncomeItemRes(key, incomeMap.getOrDefault(key, BigDecimal.ZERO)));
            }
        }

        return result;
    }

    @Override
    public IncomeRes incomeTotal(IncomeGroupReq req) {
        if(null!=req.getStartTime()) {
            Date startTime = Date.from(req.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    .atStartOfDay(ZoneId.systemDefault()).toInstant());
            req.setStartTime(startTime);

            Date endTime = Date.from(req.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    .atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
            req.setEndTime(endTime);
        }
        IncomeRes incomeRes = new IncomeRes();
        incomeRes.setActivityIncome(activityOrderMapper.selectActivityIncomeSum(req));
        incomeRes.setGoodsIncome(goodsOrderService.total(req));
        return incomeRes;
    }

    @Override
    public BigDecimal detailIncomeByDate(String startDate, String endDate) {
        return activityOrderMapper.selectIncomeByDate(startDate,endDate);
    }

}