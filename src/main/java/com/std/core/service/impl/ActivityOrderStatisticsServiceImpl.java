package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.ActivityOrderStatisticsMapper;
import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.ActivityOrderStatistics;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ActivityOrderStatisticsCreateReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListReq;
import com.std.core.pojo.request.ActivityOrderStatisticsListFrontReq;
import com.std.core.pojo.request.ActivityOrderStatisticsModifyReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageReq;
import com.std.core.pojo.request.ActivityOrderStatisticsPageFrontReq;
import com.std.core.pojo.response.ActivityOrderStatisticsDetailRes;
import com.std.core.pojo.response.ActivityOrderStatisticsListRes;
import com.std.core.pojo.response.ActivityOrderStatisticsPageRes;
import com.std.core.service.IActivityOrderStatisticsService;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 活动预约统计ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2025-01-03 15:38
 */
@Service
public class ActivityOrderStatisticsServiceImpl implements IActivityOrderStatisticsService {

    @Resource
    private ActivityOrderStatisticsMapper activityOrderStatisticsMapper;

    /**
     * 新增活动预约统计
     *
     * @param req      新增活动预约统计入参
     * @param operator 操作人
     */
    @Override
    public void create(ActivityOrderStatisticsCreateReq req, User operator) {
        ActivityOrderStatistics activityOrderStatistics = EntityUtils.copyData(req, ActivityOrderStatistics.class);
        activityOrderStatisticsMapper.insertSelective(activityOrderStatistics);
    }

    /**
     * 删除活动预约统计
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        activityOrderStatisticsMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改活动预约统计
     *
     * @param req      修改活动预约统计入参
     * @param operator 操作人
     */
    @Override
    public void modify(ActivityOrderStatisticsModifyReq req, User operator) {
        ActivityOrderStatistics activityOrderStatistics = EntityUtils.copyData(req, ActivityOrderStatistics.class);
        activityOrderStatisticsMapper.updateByPrimaryKeySelective(activityOrderStatistics);
    }

    /**
     * 详情查询活动预约统计
     *
     * @param id 主键ID
     * @return 活动预约统计对象
     */
    @Override
    public ActivityOrderStatistics detail(Long id) {
        ActivityOrderStatistics activityOrderStatistics = activityOrderStatisticsMapper.selectByPrimaryKey(id);
        if (null == activityOrderStatistics) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return activityOrderStatistics;
    }

    /**
     * 分页查询活动预约统计
     *
     * @param req 分页查询活动预约统计入参
     * @return 分页活动预约统计对象
     */
    @Override
    public List<ActivityOrderStatistics> page(ActivityOrderStatisticsPageReq req) {
        ActivityOrderStatistics condition = EntityUtils.copyData(req, ActivityOrderStatistics.class);

        List<ActivityOrderStatistics> activityOrderStatisticsList = activityOrderStatisticsMapper.selectByCondition(condition);

        return activityOrderStatisticsList;
    }

    /**
     * 列表查询活动预约统计
     *
     * @param req 列表查询活动预约统计入参
     * @return 列表活动预约统计对象
     */
    @Override
    public List<ActivityOrderStatistics> list(ActivityOrderStatisticsListReq req) {
        ActivityOrderStatistics condition = EntityUtils.copyData(req, ActivityOrderStatistics.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ActivityOrderStatistics.class));

        List<ActivityOrderStatistics> activityOrderStatisticsList = activityOrderStatisticsMapper.selectByCondition(condition);

        return activityOrderStatisticsList;
    }

    @Override
    public List<ActivityOrderStatistics> list(ActivityOrderStatistics req) {
        List<ActivityOrderStatistics> activityOrderStatisticsList = activityOrderStatisticsMapper.selectByCondition(req);

        return activityOrderStatisticsList;
    }

    /**
     * 前端详情查询活动预约统计
     *
     * @param id 主键ID
     * @return 活动预约统计对象
     */
    @Override
    public ActivityOrderStatisticsDetailRes detailFront(Long id) {
        ActivityOrderStatisticsDetailRes res = new ActivityOrderStatisticsDetailRes();

        ActivityOrderStatistics activityOrderStatistics = activityOrderStatisticsMapper.selectByPrimaryKey(id);
        if (null == activityOrderStatistics) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(activityOrderStatistics, res);

        return res;
    }

    /**
     * 前端分页查询活动预约统计
     *
     * @param req 前端分页查询活动预约统计入参
     * @return 分页活动预约统计对象
     */
    @Override
    public List<ActivityOrderStatisticsPageRes> pageFront(ActivityOrderStatisticsPageFrontReq req) {
        ActivityOrderStatistics condition = EntityUtils.copyData(req, ActivityOrderStatistics.class);
        List<ActivityOrderStatistics> activityOrderStatisticsList = activityOrderStatisticsMapper.selectByCondition(condition);

        List<ActivityOrderStatisticsPageRes> resList = activityOrderStatisticsList.stream().map((entity) -> {
            ActivityOrderStatisticsPageRes res = new ActivityOrderStatisticsPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(activityOrderStatisticsList, resList);
    }

    /**
     * 前端列表查询活动预约统计
     *
     * @param req 前端列表查询活动预约统计入参
     * @return 列表活动预约统计对象
     */
    @Override
    public List<ActivityOrderStatisticsListRes> listFront(ActivityOrderStatisticsListFrontReq req) {
        ActivityOrderStatistics condition = EntityUtils.copyData(req, ActivityOrderStatistics.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ActivityOrderStatistics.class));

        List<ActivityOrderStatistics> activityOrderStatisticsList = activityOrderStatisticsMapper.selectByCondition(condition);

        List<ActivityOrderStatisticsListRes> resList = activityOrderStatisticsList.stream().map((entity) -> {
            ActivityOrderStatisticsListRes res = new ActivityOrderStatisticsListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 增加指定活动每日预约数
     *
     * @param activityId
     * @param orderDate
     * @param totalTickets
     */
    @Override
    public void addTotalTickets(Long activityId, Date orderDate, Integer totalTickets) {
        ActivityOrderStatistics activityOrderStatistics = detail(activityId, orderDate);
        if (null == activityOrderStatistics) {
            activityOrderStatistics = new ActivityOrderStatistics();
            activityOrderStatistics.setActivityId(activityId);
            activityOrderStatistics.setOrderDate(orderDate);
            activityOrderStatistics.setTotalTickets(totalTickets);
            activityOrderStatistics.setUpdateDatetime(new Date());
            activityOrderStatisticsMapper.insertSelective(activityOrderStatistics);
        } else {
            activityOrderStatisticsMapper.updateTotalTickets(activityId, orderDate, totalTickets, new Date());
        }

    }

    @Override
    public boolean canOrder(Date orderDate, Activity activity, Integer number) {
        ActivityOrderStatistics condition = new ActivityOrderStatistics();
        condition.setActivityId(activity.getId());
        condition.setOrderDate(orderDate);
        ActivityOrderStatistics activityOrderStatistics = detail(activity.getId(), orderDate);
        if (null != activityOrderStatistics) {
            if (activityOrderStatistics.getTotalTickets() + number > activity.getDayLimit()) {
                return false;
            } else {
                return true;
            }
        }
        return true;

    }

    /**
     * 查询已满预约日期
     * 查询今天之后的日期
     *
     * @param activityId
     * @param dayLimit
     * @param date
     * @return
     */
    @Override
    public List<Date> findFullyBookedDates(Long activityId, Integer dayLimit, Date date) {
        return activityOrderStatisticsMapper.selectFullyBookedDates(activityId, dayLimit, date);
    }

    private ActivityOrderStatistics detail(Long activityId, Date orderDate) {
        ActivityOrderStatistics condition = new ActivityOrderStatistics();
        condition.setActivityId(activityId);
        condition.setOrderDate(orderDate);
        List<ActivityOrderStatistics> list = list(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            ActivityOrderStatistics activityOrderStatistics = list.get(0);
            return activityOrderStatistics;
        }
        return null;
    }

}