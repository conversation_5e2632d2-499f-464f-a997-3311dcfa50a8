package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.core.enums.EActivityStatus;
import com.std.core.enums.EActivityTicketLineStatus;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.ActivityMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.GaoDeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 16:46
 */
@Service
public class ActivityServiceImpl implements IActivityService {

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private IActivityTicketLineService activityTicketLineService;
    @Resource
    private IActivityOrderService activityOrderService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private ICuserService cuserService;

    @Resource
    private IActivityOrderStatisticsService activityOrderStatisticsService;

    @Resource
    private GaoDeUtil gaoDeUtil;

    /**
     * 新增活动
     *
     * @param req      新增活动入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ActivityCreateReq req, User operator) {
        Date date = new Date();
        Activity activity = EntityUtils.copyData(req, Activity.class);
        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_0.getCode());
        activity.setCreater(operator.getId());
        activity.setCreaterName(operator.getLoginName());
        activity.setBuyStartTime(DateUtil.strToDate(req.getBuyStartTime(), "yyyy-MM-dd HH:mm:ss"));
        activity.setBuyEndTime(DateUtil.strToDate(req.getBuyEndTime(), "yyyy-MM-dd HH:mm:ss"));
        activity.setCreateDatetime(date);
        String address = req.getProvince().concat(req.getCity()).concat(req.getCounty()).concat(req.getAddress());
        // 获取店铺经纬度
        String geocode = gaoDeUtil.geocode(address, req.getCity());

        if (StringUtils.isNotBlank(geocode)) {
            List<String> result = Arrays.asList(geocode.split(","));
            activity.setLongitude(result.get(0));
            activity.setLatitude(result.get(1));
        }

        // 判断下BuyEndTime 不能晚于当天 16:00
        if (dateCheck(activity.getBuyEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动售票结束时间不能晚于16:00");
        }
        if (activity.getBuyStartTime().after(activity.getBuyEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动售票开始不能晚于结束时间");
        }
        if (activity.getStartTime().after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动开始时间不能晚于结束时间");
        }

        if (activity.getBuyStartTime().after(activity.getStartTime()) || activity.getBuyEndTime().after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动售票时间不能晚于活动时间");
        }

        activityMapper.insertSelective(activity);
        BigDecimal minPrice = BigDecimal.ZERO;
        for (ActivityTicketLineCreateReq activityTicketLineCreateReq : req.getActivityTicketLineList()) {
            ActivityTicketLine activityTicketLine = EntityUtils.copyData(activityTicketLineCreateReq, ActivityTicketLine.class);
            activityTicketLine.setActivityId(activity.getId());
            activityTicketLine.setCreater(operator.getId());
            activityTicketLine.setCreaterName(operator.getLoginName());
            activityTicketLine.setCreateDatetime(date);
            activityTicketLine.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_0.getCode());
            activityTicketLine.setInventory(activityTicketLineCreateReq.getNumber());
            activityTicketLineService.create(activityTicketLine);

            if (minPrice.compareTo(BigDecimal.ZERO) == 0 || minPrice.compareTo(activityTicketLine.getPrice()) > 0) {
                minPrice = activityTicketLine.getPrice();
            }
        }
        activity.setPrice(minPrice);

        activityMapper.updateByPrimaryKeySelective(activity);

    }

    public static void main(String[] args) {
        Date date = DateUtil.strToDate("2025-05-30 16:00:00", DateUtil.DATA_TIME_PATTERN_2);
        System.out.println(dateCheck(date));
    }

    private static boolean dateCheck(Date inputDate) {
        // 转为 LocalDateTime（按系统默认时区）
        LocalDateTime inputDateTime = inputDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 构造“inputDateTime 所在日期”的 16:00
        LocalDateTime sameDayAt16 = inputDateTime
                .withHour(16).withMinute(0).withSecond(0).withNano(0);

        // 判断是否晚于当天的 16:00
        if (inputDateTime.isAfter(sameDayAt16)) {
            System.out.println("这个时间晚于当天 16:00");
            return true;
        } else {
            System.out.println("这个时间不晚于当天 16:00");
            return false;
        }
    }

    /**
     * 删除活动
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        Activity activity = activityMapper.selectByPrimaryKey(id);
        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_5.getCode());
        activityMapper.updateByPrimaryKeySelective(activity);
    }

    /**
     * 修改活动
     *
     * @param req      修改活动入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ActivityModifyReq req, User operator) {
        Activity activity = detail(req.getId());
        if (!EActivityStatus.ACTIVITY_STATUS_0.getCode().equals(activity.getStatus())
                && !EActivityStatus.ACTIVITY_STATUS_4.getCode().equals(activity.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能修改待上架的活动");
        }
        Date date = new Date();
        String status = activity.getStatus();
        activity = EntityUtils.copyData(req, Activity.class);
        activity.setUpdater(operator.getId());
        activity.setUpdaterName(operator.getUpdaterName());
        activity.setUpdateDatetime(date);
        activity.setStatus(status);
        activity.setBuyStartTime(DateUtil.strToDate(req.getBuyStartTime(), "yyyy-MM-dd HH:mm:ss"));
        activity.setBuyEndTime(DateUtil.strToDate(req.getBuyEndTime(), "yyyy-MM-dd HH:mm:ss"));
        BigDecimal minPrice = BigDecimal.ZERO;


        if (activity.getBuyStartTime().after(activity.getBuyEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动售票开始不能晚于结束时间");
        }
        if (activity.getStartTime().after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动开始时间不能晚于结束时间");
        }

        if (activity.getBuyStartTime().after(activity.getStartTime()) || activity.getBuyEndTime().after(activity.getEndTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动售票时间不能晚于活动时间");
        }

//        for (ActivityTicketLine activityTicketLine : list) {
//            activityTicketLineService.remove(activityTicketLine.getId());
//        }

        Set<Long> ticketLineIdList = new HashSet<>();
        for (ActivityTicketLineCreateReq activityTicketLineCreateReq : req.getActivityTicketLineList()) {

            ActivityTicketLine activityTicketLine = null;
            if (null != activityTicketLineCreateReq.getId()) {

                activityTicketLine = activityTicketLineService.detailAll(activityTicketLineCreateReq.getId());
                if (null == activityTicketLine) {
                    activityTicketLineCreateReq.setActivityId(activity.getId());
                    activityTicketLine = activityTicketLineService.create(activityTicketLineCreateReq, operator);
                    ticketLineIdList.add(activityTicketLine.getId());
                } else {
                    ticketLineIdList.add(activityTicketLine.getId());
                    if (!EActivityStatus.ACTIVITY_STATUS_0.getCode().equals(activity.getStatus())) {
                        if (!EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_2.getCode().equals(activityTicketLine.getStatus())) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有下架的活动可以修改库存");
                        }
                    }
                    int sellNumber = activityTicketLine.getNumber() - activityTicketLine.getInventory();
                    if (sellNumber > activityTicketLineCreateReq.getNumber()) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "票档:" + activityTicketLineCreateReq.getName() + "的数量不能少于" + sellNumber);
                    }
                    ActivityTicketLine activityTicketLineModify = new ActivityTicketLine();
                    activityTicketLineModify.setId(activityTicketLineCreateReq.getId());
                    activityTicketLineModify.setName(activityTicketLineCreateReq.getName());
                    activityTicketLineModify.setPrice(activityTicketLineCreateReq.getPrice());
                    activityTicketLineModify.setOrderNo(activityTicketLineCreateReq.getOrderNo());
                    activityTicketLineModify.setUpdater(operator.getId());
                    activityTicketLineModify.setUpdaterName(operator.getLoginName());
                    activityTicketLineModify.setUpdateDatetime(date);
                    activityTicketLineService.modify(activityTicketLineModify);
                    // 17-15=2
                    // 13-15=-2
                    // 库存变动
                    int changerNumber = activityTicketLineCreateReq.getNumber() - activityTicketLine.getNumber();
                    ActivityTicketLineAddStockReq addStockReq = new ActivityTicketLineAddStockReq();
                    addStockReq.setId(activityTicketLine.getId());
                    addStockReq.setStock(changerNumber);
                    activityTicketLineService.addStock(addStockReq, operator);
                }
            } else {
                activityTicketLine = activityTicketLineService.create(activityTicketLineCreateReq, operator);
            }


            if (minPrice.compareTo(BigDecimal.ZERO) == 0) {
                minPrice = activityTicketLine.getPrice();
            } else {
                if (minPrice.compareTo(activityTicketLine.getPrice()) > 0) {
                    minPrice = activityTicketLine.getPrice();
                }
            }
        }

        // 删除不存在的票档
        ActivityTicketLine condition = new ActivityTicketLine();
        condition.setActivityId(activity.getId());
        List<ActivityTicketLine> list = activityTicketLineService.list(condition);
        for (ActivityTicketLine activityTicketLine : list) {
            if (!ticketLineIdList.contains(activityTicketLine.getId())) {
                activityTicketLineService.remove(activityTicketLine.getId());
            }
        }


        activity.setPrice(minPrice);
        activityMapper.updateByPrimaryKeySelective(activity);
    }


    @Override
    public void modify(Activity req) {
        activityMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询活动
     *
     * @param id 主键ID
     * @return 活动对象
     */
    @Override
    public Activity detail(Long id) {
        Activity activity = activityMapper.selectByPrimaryKey(id);
        if (null == activity) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        ActivityTicketLine activityTicketLine = new ActivityTicketLine();
        activityTicketLine.setActivityId(id);
        activity.setActivityTicketLineList(activityTicketLineService.list(activityTicketLine));

        return activity;
    }

    @Override
    public Activity detailForUpdate(Long id) {
        Activity activity = activityMapper.selectForUpdate(id);
        if (null == activity) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return activity;
    }

    /**
     * 分页查询活动
     *
     * @param req 分页查询活动入参
     * @return 分页活动对象
     */
    @Override
    public List<Activity> page(ActivityPageReq req) {
        Activity condition = EntityUtils.copyData(req, Activity.class);
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(EActivityStatus.ACTIVITY_STATUS_5.getCode());
        condition.setNoStatusList(noStatusList);
        condition.setOrderBy("t.id desc");
        List<Activity> activityList = activityMapper.selectByCondition(condition);
        String flag = EBoolean.YES.getCode();
        for (Activity activity : activityList) {
            ActivityTicketLine activityTicketLine = new ActivityTicketLine();
            activityTicketLine.setActivityId(activity.getId());
            activityTicketLine.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
            List<ActivityTicketLine> list = activityTicketLineService.list(activityTicketLine);
            for (ActivityTicketLine ticketLine : list) {
                if (ticketLine.getInventory() == 0) {
                    flag = EBoolean.NO.getCode();
                    break;
                }
            }
            activity.setFlag(flag);
        }

        return activityList;
    }

    /**
     * 列表查询活动
     *
     * @param req 列表查询活动入参
     * @return 列表活动对象
     */
    @Override
    public List<Activity> list(ActivityListReq req) {
        Activity condition = EntityUtils.copyData(req, Activity.class);
        condition.setOrderBy("t.order_no desc");
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(EActivityStatus.ACTIVITY_STATUS_5.getCode());
        condition.setNoStatusList(noStatusList);
        List<Activity> activityList = activityMapper.selectByCondition(condition);

        return activityList;
    }

    @Override
    public List<Activity> list(Activity req) {
        List<Activity> activityList = activityMapper.selectByCondition(req);

        return activityList;
    }

    /**
     * 前端详情查询活动
     *
     * @param id 主键ID
     * @return 活动对象
     */
    @Override
    public ActivityDetailRes detailFront(Long id) {
        ActivityDetailRes res = new ActivityDetailRes();

        Activity activity = activityMapper.selectByPrimaryKey(id);
        if (null == activity) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(activity, res);

        ActivityTicketLine activityTicketLine = new ActivityTicketLine();
        activityTicketLine.setActivityId(id);
        List<ActivityTicketLine> list = activityTicketLineService.list(activityTicketLine);
        res.setActivityTicketLineList(list);
        return res;
    }

    /**
     * 前端分页查询活动
     *
     * @param req 前端分页查询活动入参
     * @return 分页活动对象
     */
    @Override
    public List<ActivityPageRes> pageFront(ActivityPageFrontReq req) {
        Activity condition = EntityUtils.copyData(req, Activity.class);
        condition.setOrderBy("t.order_no DESC,t.create_datetime desc");
        List<Activity> activityList = activityMapper.selectByCondition(condition);

        List<ActivityPageRes> resList = activityList.stream().map((entity) -> {
            ActivityPageRes res = new ActivityPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(activityList, resList);
    }

    /**
     * 前端列表查询活动
     *
     * @param req 前端列表查询活动入参
     * @return 列表活动对象
     */
    @Override
    public List<ActivityListRes> listFront(ActivityListFrontReq req) {
        Activity condition = EntityUtils.copyData(req, Activity.class);
//        condition.setOrderBy("t.order_no desc");
        condition.setOrderBy("FIELD(t.status, '1', '2', '6','7','3'), t.order_no DESC");
//        condition.setStatus(EActivityStatus.ACTIVITY_STATUS_2.getCode());
        condition.setStatusList(Arrays.asList(EActivityStatus.ACTIVITY_STATUS_1.getCode(), EActivityStatus.ACTIVITY_STATUS_2.getCode(),
                EActivityStatus.ACTIVITY_STATUS_3.getCode(),
                EActivityStatus.ACTIVITY_STATUS_6.getCode(),EActivityStatus.ACTIVITY_STATUS_7.getCode()));
        List<Activity> activityList = activityMapper.selectByCondition(condition);

        List<ActivityListRes> resList = activityList.stream().map((entity) -> {
            ActivityListRes res = new ActivityListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(BatchUpDownReq req, User operator) {
        Date date = new Date();
        for (Long activityId : req.getIdList()) {
            Activity activity = detail(activityId);

            //下架
            if (EActivityStatus.ACTIVITY_STATUS_4.getCode().equals(req.getStatus())) {
                if (EActivityStatus.ACTIVITY_STATUS_4.getCode().equals(activity.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动已下架");
                }
                activity.setStatus(EActivityStatus.ACTIVITY_STATUS_4.getCode());
                activityTicketLineService.batchUpDown(activityId, EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_2.getCode());
            } else {
                //上架
                if (!EActivityStatus.ACTIVITY_STATUS_0.getCode().equals(activity.getStatus())
                        &&
                        !EActivityStatus.ACTIVITY_STATUS_4.getCode().equals(activity.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待上架或者已下架的活动可以上架");
                }
                if (date.before(activity.getBuyStartTime())) {
                    activity.setStatus(EActivityStatus.ACTIVITY_STATUS_1.getCode());
                    activityTicketLineService.batchUpDown(activityId, EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
                } else if (date.before(activity.getBuyEndTime())) {
                    activity.setStatus(EActivityStatus.ACTIVITY_STATUS_2.getCode());
                    activityTicketLineService.batchUpDown(activityId, EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
                } else if (date.after(activity.getEndTime())) {
                    activity.setStatus(EActivityStatus.ACTIVITY_STATUS_3.getCode());
                    activityTicketLineService.batchUpDown(activityId, EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_2.getCode());
                } else if (date.after(activity.getBuyEndTime())) {
                    activity.setStatus(EActivityStatus.ACTIVITY_STATUS_7.getCode());
                    activityTicketLineService.batchUpDown(activityId, EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_2.getCode());
                }
//                List<Cuser> list = cuserService.list();
//                System.out.println(list.size());
//                for (Cuser cuser : list) {
//                    wechatService.sendSubscribeMessage(cuser.getOpenid(), activity.getId().toString());
//                }
            }

            activity.setUpdater(operator.getId());
            activity.setUpdaterName(operator.getLoginName());
            activity.setUpdateDatetime(date);
            activityMapper.updateByPrimaryKeySelective(activity);
        }
    }


    @Override
    public List<OrderTimeRes> orderTime(Long id) {
        Activity activity = detail(id);
        Date startTime = activity.getStartTime();
        Date endTime = activity.getEndTime();
        List<Date> dateList = generateOrderTimeResList(startTime, endTime);
        List<Date> fullyBookedDates = activityOrderStatisticsService.findFullyBookedDates(activity.getId(), activity.getDayLimit(), new Date());
        // 查询出已经达到预约上限的日期
        List<OrderTimeRes> orderTimeResList = new ArrayList<>();
        for (Date date : dateList) {
            OrderTimeRes orderTimeRes = new OrderTimeRes();
            orderTimeRes.setDate(DateUtil.getDate(date, "yyyy-MM-dd"));
//            boolean flag = activityOrderService.canOrder(date, activity, 0);
//            if (flag) {
//                orderTimeRes.setFlag(EBoolean.YES.getCode());
//            } else {
//                orderTimeRes.setFlag(EBoolean.NO.getCode());
//            }
            if (date.before(DateUtil.getDayStartTime(new Date()))) {
                orderTimeRes.setFlag(EBoolean.NO.getCode());
            } else {
                if (fullyBookedDates.contains(com.std.core.util.DateUtil.strToDate(orderTimeRes.getDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"))) {
                    orderTimeRes.setFlag(EBoolean.NO.getCode());
                } else {
                    orderTimeRes.setFlag(EBoolean.YES.getCode());
                }
            }
            orderTimeResList.add(orderTimeRes);
        }
        return orderTimeResList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(SortReq request) {
        List<Sort> sortList = request.getSortList();
        if (sortList == null || sortList.isEmpty()) {
            return;
        }

        for (Sort sort : sortList) {
            if (sort.getId() != null && sort.getOrderNo() != null) {
                Activity activity = new Activity();
                activity.setId(sort.getId());
                activity.setOrderNo(sort.getOrderNo());
                activityMapper.updateByPrimaryKeySelective(activity); // 使用已有的更新方法
            }
        }
    }

    @Override
    public CanOrderRes canOrder(Long id, User operator) {
        String canOrderFlag = EBoolean.YES.getCode();
        return new CanOrderRes(canOrderFlag);
    }


    private List<Date> generateOrderTimeResList(Date startDate, Date endDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        List<Date> timeList = new ArrayList<>();
        while (calendar.getTime().before(endDate) || calendar.getTime().equals(endDate)) {
            Date currentDate = calendar.getTime();
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            timeList.add(currentDate);
        }

        return timeList;
    }

}

