package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EActivityStatus;
import com.std.core.enums.EActivityTicketLineStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.ActivityTicketLineMapper;
import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.ActivityTicketLine;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.ActivityTicketLineDetailRes;
import com.std.core.pojo.response.ActivityTicketLineListRes;
import com.std.core.pojo.response.ActivityTicketLinePageRes;
import com.std.core.service.IActivityService;
import com.std.core.service.IActivityTicketLineService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 票档ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-25 17:26
 */
@Service
public class ActivityTicketLineServiceImpl implements IActivityTicketLineService {

    @Resource
    private ActivityTicketLineMapper activityTicketLineMapper;
    @Resource
    private IActivityService activityService;

    /**
     * 新增票档
     *
     * @param req      新增票档入参
     * @param operator 操作人
     */
    @Override
    public ActivityTicketLine create(ActivityTicketLineCreateReq req, User operator) {
        ActivityTicketLine activityTicketLine = EntityUtils.copyData(req, ActivityTicketLine.class);
        if (null == req.getActivityId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "活动序号不能为空");
        }
        activityTicketLine.setActivityId(req.getActivityId());
        activityTicketLine.setInventory(activityTicketLine.getNumber());
        activityTicketLine.setCreater(operator.getId());
        activityTicketLine.setCreateDatetime(new Date());
        activityTicketLine.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_0.getCode());
        activityTicketLine.setCreaterName(operator.getLoginName());
        Activity activity = activityService.detail(req.getActivityId());
        if (null != activity.getPrice() && activity.getPrice().compareTo(activityTicketLine.getPrice()) > 0) {
            activity.setPrice(activityTicketLine.getPrice());
            activityService.modify(activity);
        }
        activityTicketLineMapper.insertSelective(activityTicketLine);
        return activityTicketLine;
    }

    @Override
    public void create(ActivityTicketLine req) {
        activityTicketLineMapper.insertSelective(req);
    }

    /**
     * 删除票档
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
//        activityTicketLineMapper.deleteByPrimaryKey(id);
        activityTicketLineMapper.updateDelete(id);
    }

    /**
     * 修改票档
     *
     * @param req      修改票档入参
     * @param operator 操作人
     */
    @Override
    public void modify(ActivityTicketLineModifyReq req, User operator) {
        ActivityTicketLine activityTicketLine = EntityUtils.copyData(req, ActivityTicketLine.class);
        activityTicketLineMapper.updateByPrimaryKeySelective(activityTicketLine);
    }

    @Override
    public void modify(ActivityTicketLine req) {
        activityTicketLineMapper.updateByPrimaryKeySelective(req);
    }


    @Override
    public int modifyInventory(Long id, Integer number) {
        return activityTicketLineMapper.updateInventoryWithVersion(id, number);
    }

    @Override
    public int addInventory(Long id, Integer number) {
        return activityTicketLineMapper.updateAddInventory(id, number);
    }

    /**
     * 详情查询票档
     *
     * @param id 主键ID
     * @return 票档对象
     */
    @Override
    public ActivityTicketLine detail(Long id) {
        ActivityTicketLine activityTicketLine = activityTicketLineMapper.selectByPrimaryKey(id);
        if (null == activityTicketLine) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return activityTicketLine;
    }

    @Override
    public ActivityTicketLine detailAll(Long id) {
        ActivityTicketLine activityTicketLine = activityTicketLineMapper.selectByPrimaryKey(id);

        return activityTicketLine;
    }

    /**
     * 分页查询票档
     *
     * @param req 分页查询票档入参
     * @return 分页票档对象
     */
    @Override
    public List<ActivityTicketLine> page(ActivityTicketLinePageReq req) {
        ActivityTicketLine condition = EntityUtils.copyData(req, ActivityTicketLine.class);

        List<ActivityTicketLine> activityTicketLineList = activityTicketLineMapper.selectByCondition(condition);

        return activityTicketLineList;
    }

    /**
     * 列表查询票档
     *
     * @param req 列表查询票档入参
     * @return 列表票档对象
     */
    @Override
    public List<ActivityTicketLine> list(ActivityTicketLineListReq req) {
        ActivityTicketLine condition = EntityUtils.copyData(req, ActivityTicketLine.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ActivityTicketLine.class));

        List<ActivityTicketLine> activityTicketLineList = activityTicketLineMapper.selectByCondition(condition);

        return activityTicketLineList;
    }

    @Override
    public List<ActivityTicketLine> list(ActivityTicketLine req) {
        req.setOrderBy("t.order_no desc");
        List<ActivityTicketLine> activityTicketLineList = activityTicketLineMapper.selectByCondition(req);
        return activityTicketLineList;
    }

    /**
     * 前端详情查询票档
     *
     * @param id 主键ID
     * @return 票档对象
     */
    @Override
    public ActivityTicketLineDetailRes detailFront(Long id) {
        ActivityTicketLineDetailRes res = new ActivityTicketLineDetailRes();

        ActivityTicketLine activityTicketLine = activityTicketLineMapper.selectByPrimaryKey(id);
        if (null == activityTicketLine) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(activityTicketLine, res);

        return res;
    }

    /**
     * 前端分页查询票档
     *
     * @param req 前端分页查询票档入参
     * @return 分页票档对象
     */
    @Override
    public List<ActivityTicketLinePageRes> pageFront(ActivityTicketLinePageFrontReq req) {
        ActivityTicketLine condition = EntityUtils.copyData(req, ActivityTicketLine.class);
        List<ActivityTicketLine> activityTicketLineList = activityTicketLineMapper.selectByCondition(condition);

        List<ActivityTicketLinePageRes> resList = activityTicketLineList.stream().map((entity) -> {
            ActivityTicketLinePageRes res = new ActivityTicketLinePageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(activityTicketLineList, resList);
    }

    /**
     * 前端列表查询票档
     *
     * @param req 前端列表查询票档入参
     * @return 列表票档对象
     */
    @Override
    public List<ActivityTicketLineListRes> listFront(ActivityTicketLineListFrontReq req) {
        ActivityTicketLine condition = EntityUtils.copyData(req, ActivityTicketLine.class);
        condition.setOrderBy("t.order_no desc, t.price asc");
        condition.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
        List<ActivityTicketLine> activityTicketLineList = activityTicketLineMapper.selectByCondition(condition);

        List<ActivityTicketLineListRes> resList = activityTicketLineList.stream().map((entity) -> {
            ActivityTicketLineListRes res = new ActivityTicketLineListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(BatchUpDownReq req, User operator) {
        List<Long> idList = req.getIdList();
        for (Long activityTicketLineId : idList) {
            ActivityTicketLine activityTicketLine = detail(activityTicketLineId);
            if (EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode().equals(req.getStatus())) {
                Activity activity = activityService.detail(activityTicketLine.getActivityId());
                if (!EActivityStatus.ACTIVITY_STATUS_2.getCode().equals(activity.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有已上架的活动才能上架票档");
                }
            }
            activityTicketLine.setStatus(req.getStatus());
            activityTicketLine.setUpdater(operator.getId());
            activityTicketLine.setUpdateDatetime(new Date());
            activityTicketLine.setUpdaterName(operator.getUserName());
            activityTicketLineMapper.updateByPrimaryKeySelective(activityTicketLine);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(Long id, String status) {
        ActivityTicketLine activityTicketLine = new ActivityTicketLine();
        activityTicketLine.setActivityId(id);
        List<ActivityTicketLine> list = list(activityTicketLine);
        for (ActivityTicketLine ticketLine : list) {
            ticketLine.setStatus(status);
            activityTicketLineMapper.updateByPrimaryKeySelective(ticketLine);
        }
    }

    @Override
    public void addStock(ActivityTicketLineAddStockReq request, User operator) {
        ActivityTicketLine activityTicketLine = detail(request.getId());
        if (EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_2.getCode().equals(activityTicketLine.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "下架不能修改库存，该活动已结束");
        }
        activityTicketLineMapper.addStock(request.getId(), request.getStock());

        Activity detail = activityService.detail(activityTicketLine.getActivityId());
        if (EActivityStatus.ACTIVITY_STATUS_6.getCode().equals(detail.getStatus())) {
            detail.setStatus(EActivityStatus.ACTIVITY_STATUS_2.getCode());
            activityService.modify(detail);
        }
    }

    @Override
    public int sumInventoryByCondition(ActivityTicketLine request) {
        return activityTicketLineMapper.sumInventoryByCondition(request);
    }
}