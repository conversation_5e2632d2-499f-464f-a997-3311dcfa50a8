package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.AddressMapper;
import com.std.core.pojo.domain.Address;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.AddressDetailRes;
import com.std.core.pojo.response.AddressListRes;
import com.std.core.pojo.response.AddressPageRes;
import com.std.core.service.IAddressService;
import com.std.core.service.IAreaService;
import com.std.core.service.IUserService;
import com.vdurmont.emoji.EmojiParser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收货地址ServiceImpl
 *
 * <AUTHOR> yy
 * @since : 2024-03-22 20:44
 */
@Service
public class AddressServiceImpl implements IAddressService {

    @Resource
    private AddressMapper addressMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IAreaService areaService;


    /**
     * 新增收货地址
     *
     * @param req      新增收货地址入参
     * @param operator 操作人
     */
    @Override
    public void create(AddressCreateReq req, User operator) {
        req.setName(EmojiParser.removeAllEmojis(req.getName()));
        Address address = EntityUtils.copyData(req, Address.class);
        address.setUserId(operator.getId());
        address.setProvince(req.getProvince());
        address.setCity(req.getCity());
        address.setCounty(req.getCounty());
        address.setAddress(EmojiParser.removeAllEmojis(req.getAddress()));
        if (StringUtils.isBlank(address.getAddress())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"地址不能为空");
        }
        address.setCreateDatetime(new Date());
        address.setUpdateDatetime(new Date());
        Address condition = new Address();
        condition.setUserId(operator.getId());
        List<Address> list = list(condition);
        if(list.isEmpty()){
            address.setIsDefault(EBoolean.YES.getCode());
        }
        addressMapper.insertSelective(address);
    }

    /**
     * 删除收货地址
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        addressMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改收货地址
     *
     * @param req      修改收货地址入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(AddressModifyReq req, User operator) {
        if (EBoolean.YES.getCode().equals(req.getIsDefault())) {
            addressMapper.updateOtherDefault(operator.getId());
        }
        Address address = EntityUtils.copyData(req, Address.class);
        if (StringUtils.isNotBlank(req.getAddress())){
            address.setAddress(EmojiParser.removeAllEmojis(req.getAddress()));
        }


        address.setUpdateDatetime(new Date());
        addressMapper.updateByPrimaryKeySelective(address);
    }

    /**
     * 详情查询收货地址
     *
     * @param id 主键ID
     * @return 收货地址对象
     */
    @Override
    public Address detail(Long id) {
        Address address = addressMapper.selectByPrimaryKey(id);
        if (null == address) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        address.setUser(userService.selectSummaryInfo(address.getUserId()));

        return address;
    }

    /**
     * 分页查询收货地址
     *
     * @param req 分页查询收货地址入参
     * @return 分页收货地址对象
     */
    @Override
    public List<Address> page(AddressPageReq req) {
        Address condition = EntityUtils.copyData(req, Address.class);

        List<Address> addressList = addressMapper.selectByCondition(condition);
        // 转译UserId
        addressList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return addressList;
    }

    /**
     * 列表查询收货地址
     *
     * @param req 列表查询收货地址入参
     * @return 列表收货地址对象
     */
    @Override
    public List<Address> list(AddressListReq req) {
        Address condition = EntityUtils.copyData(req, Address.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Address.class));

        List<Address> addressList = addressMapper.selectByCondition(condition);
        // 转译UserId
        addressList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return addressList;
    }

    @Override
    public List<Address> list(Address req) {


        List<Address> addressList = addressMapper.selectByCondition(req);


        return addressList;
    }

    /**
     * 前端详情查询收货地址
     *
     * @param id 主键ID
     * @return 收货地址对象
     */
    @Override
    public AddressDetailRes detailFront(Long id) {
        AddressDetailRes res = new AddressDetailRes();

        Address address = addressMapper.selectByPrimaryKey(id);
        if (null == address) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        address.setUser(userService.selectSummaryInfo(address.getUserId()));

        BeanUtils.copyProperties(address, res);

        return res;
    }

    /**
     * 前端分页查询收货地址
     *
     * @param req      前端分页查询收货地址入参
     * @param operator
     * @return 分页收货地址对象
     */
    @Override
    public List<AddressPageRes> pageFront(AddressPageFrontReq req, User operator) {
        Address condition = EntityUtils.copyData(req, Address.class);
        condition.setUserId(operator.getId());
        condition.setOrderBy("t.is_default = '1' desc");
        List<Address> addressList = addressMapper.selectByCondition(condition);

        List<AddressPageRes> resList = addressList.stream().map((entity) -> {
            AddressPageRes res = new AddressPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(addressList, resList);
    }

    /**
     * 前端列表查询收货地址
     *
     * @param req 前端列表查询收货地址入参
     * @return 列表收货地址对象
     */
    @Override
    public List<AddressListRes> listFront(AddressListFrontReq req) {
        Address condition = EntityUtils.copyData(req, Address.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Address.class));

        List<Address> addressList = addressMapper.selectByCondition(condition);
        // 转译UserId
        addressList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<AddressListRes> resList = addressList.stream().map((entity) -> {
            AddressListRes res = new AddressListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}