package com.std.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.domain.ExtendParams;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.WebUtils;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.std.common.exception.BizException;
import com.std.core.config.AlipayConfig;
import com.std.core.enums.*;
import com.std.core.enums.ESystemAccount.TRUST;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Charge;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PayRecordModifyReq;
import com.std.core.service.*;
import com.std.core.util.AmountUtil;
import com.std.core.util.IdGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:01
 */
@Slf4j
@Service
public class AlipayServiceImpl implements IAlipayService {

    @Resource
    private IAccountService accountService;

    @Resource
    private IChargeService chargeService;

    @Resource
    private AlipayConfig alipayConfig;

    @Resource
    private IPayRecordService payRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCallback(String result) {
        boolean isSuccess = false;
        Charge charge;
        // 解析回调结果
        log.info("**** 支付宝支付回调结果： ****：");
        log.info(result);

        // 将异步通知中收到的待验证所有参数都存放到map中
        Map<String, String> paramsMap = split(result);
        try {
            // 拿到签名，调用SDK验证签名
            boolean verifyResult;
            verifyResult = AlipaySignature.rsaCheckV1(paramsMap, alipayConfig.getPublickey(),
                    alipayConfig.getCharset(), alipayConfig.getSigntype());

            log.info("验签结果：" + verifyResult);

            if (verifyResult) {
                // 按照支付结果异步通知中的描述，对支付结果中的业务内容进行1\2\3\4二次校验，校验成功后在response中返回success，校验失败返回failure
                String outTradeNo = paramsMap.get("out_trade_no");
                String totalAmount = paramsMap.get("total_amount");
                String sellerId = paramsMap.get("seller_id");
                String appId = paramsMap.get("app_id");
                String alipayOrderNo = paramsMap.get("trade_no");
                String tradeStatus = paramsMap.get("trade_status");

                // 查询支付记录
                PayRecord payRecord = payRecordService.detail(Long.valueOf(outTradeNo));
                if (!EPayRecordStatus.PAY_RECORD_STATUS_0.getCode().equals(payRecord.getStatus())) {
                    log.error("支付记录不处于待回调状态:{}", outTradeNo);
                    return;
                }

                // 数据正确性校验
                BigDecimal orderAmount = payRecord.getAmount();

                if (orderAmount.compareTo(AmountUtil.mul(totalAmount, 1L)) == 0
                        && sellerId.equals(alipayConfig.getProviderid())
                        && appId.equals(alipayConfig.getAppid())) {

                    PayRecordModifyReq payRecordModifyReq = new PayRecordModifyReq();
                    payRecordModifyReq.setId(payRecord.getId());
                    // 支付成功
                    if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED"
                            .equals(tradeStatus)) {
                        isSuccess = true;
                        payRecordModifyReq.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_1.getCode());
                        payRecordModifyReq.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_1.getCode());
                        if (EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getCode().equals(payRecord.getBizType())) {
                            chargeService.doChargeCallback(isSuccess, alipayOrderNo, payRecord.getBizCode());
                        }
                    } else {// 支付失败
                        // 更新充值订单状态
                        payRecordModifyReq.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_2.getCode());
                        payRecordModifyReq.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_1.getCode());
                    }
                    User user = new User();
                    payRecordModifyReq.setResponse(result);
                    payRecordService.modify(payRecordModifyReq, user);
                } else {
                    throw new BizException(EErrorCode.CORE00000.getCode(), "数据正确性校验失败，非法回调");
                }
            } else {
                throw new BizException(EErrorCode.CORE00000.getCode(), "验签失败，默认为非法回调");
            }
        } catch (AlipayApiException e) {
            throw new BizException(EErrorCode.CORE00000.getCode(), "支付结果通知验签异常");
        }
    }

    @Override
    public <T> String getTradeWapPaySignedOrder(Long applyUser, String userKind,
                                                String toAccountNumber, T payGroup,
                                                String bizType, String bizNote, BigDecimal transAmount) {
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        // 获取收款方账户信息
        Account toAccount = accountService.getAccount(toAccountNumber);

        // 落地此次付款的订单信息
        Long orderCode = chargeService
                .applyOrderOnline(toAccount, userKind, payGroup, payGroup.toString(),
                        bizType, bizNote, transAmount, EChannelType.ALIPAY, applyUser);

        // 商户订单号，商户网站订单系统中唯一订单号
        String outTradeNo = orderCode.toString();

        // 付款金额，必填
        String totalAmount = String.valueOf(AmountUtil.div(transAmount, 1L));

        // 超时时间 可空
        String timeoutExpress = "1m";

        // 销售产品码 必填
        String productCode = "QUICK_WAP_PAY";

        AlipayClient client = new DefaultAlipayClient(alipayConfig.getGateway(),
                alipayConfig.getAppid(), alipayConfig.getPrivatekey(),
                alipayConfig.getFormat(), alipayConfig.getCharset(),
                alipayConfig.getPublickey(), alipayConfig.getSigntype());

        AlipayTradeWapPayRequest alipayRequest = new AlipayTradeWapPayRequest();

        // 封装请求支付信息
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setOutTradeNo(outTradeNo);
        model.setSubject(bizNote);
        model.setTotalAmount(totalAmount);
        model.setBody(bizNote);
        model.setTimeoutExpress(timeoutExpress);
        model.setProductCode(productCode);
        ExtendParams eParams = new ExtendParams();
        eParams.setSysServiceProviderId(alipayConfig.getProviderid());
        model.setExtendParams(eParams);
        alipayRequest.setBizModel(model);

        // 设置异步通知地址
        alipayRequest.setNotifyUrl(alipayConfig.getNotifyurl());
        // 设置同步地址
        alipayRequest.setReturnUrl(alipayConfig.getReturnurl());

        String form;
        try {
            form = client.sdkExecute(alipayRequest).getBody();
            form = alipayConfig.getGateway().concat("?").concat(form);
            // form = client.pageExecute(alipay_request).getBody();
            // logger.info("*********h5支付表单唤醒参数：" + form + "*********");
        } catch (AlipayApiException e) {
            log.error("支付宝支付唤起异常:" + e.getMessage());
            throw new BizException(EErrorCode.AIS00031);
        }
        return form;
    }

    @Override
    public <T> String getTradePagePaySignedOrder(Long applyUser, String userKind,
                                                 String toAccountNumber, T payGroup,
                                                 String bizType, String bizNote, BigDecimal transAmount) {
        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        // 获取收款方账户信息
        Account toAccount = accountService.getAccount(toAccountNumber);

        // 落地此次付款的订单信息
        Long orderCode = chargeService.applyOrderOnline(toAccount, userKind, payGroup, payGroup,
                bizType, bizNote, transAmount, EChannelType.ALIPAY, applyUser);

        // 商户订单号，商户网站订单系统中唯一订单号
        String outTradeNo = orderCode.toString();

        // 付款金额，必填
        String totalAmount = String.valueOf(AmountUtil.div(transAmount, 1L));

        // 超时时间 可空
        String timeoutExpress = "1m";

        // 销售产品码 必填
        String productCode = "FAST_INSTANT_TRADE_PAY";

        AlipayClient client = new DefaultAlipayClient(alipayConfig.getGateway(),
                alipayConfig.getAppid(), alipayConfig.getPrivatekey(),
                alipayConfig.getFormat(), alipayConfig.getCharset(),
                alipayConfig.getPublickey(), alipayConfig.getSigntype());

        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();

        // 封装请求支付信息
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setOutTradeNo(outTradeNo);
        model.setSubject(bizNote);
        model.setTotalAmount(totalAmount);
        model.setBody(bizNote);
        model.setTimeoutExpress(timeoutExpress);
        model.setProductCode(productCode);
        ExtendParams eParams = new ExtendParams();
        eParams.setSysServiceProviderId(alipayConfig.getProviderid());
        model.setExtendParams(eParams);
        alipayRequest.setBizModel(model);

        // 设置异步通知地址
        alipayRequest.setNotifyUrl(alipayConfig.getNotifyurl());
        // 设置同步地址
        alipayRequest.setReturnUrl(alipayConfig.getReturnurl());

        String form;
        try {
            form = client.sdkExecute(alipayRequest).getBody();
            form = alipayConfig.getGateway().concat("?").concat(form);
//            form = client.pageExecute(alipayRequest).getBody();
        } catch (AlipayApiException e) {
            log.error("支付宝支付唤起异常:" + e.getMessage());
            throw new BizException(EErrorCode.AIS00031);
        }
        return form;

    }

    @Override
    public <T> String getTradeAppPaySignedOrder(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
        Long id = IdGeneratorUtil.generator();


        // 商户订单号，商户网站订单系统中唯一订单号
        String outTradeNo = id.toString();

        // 付款金额，必填
        String totalAmount = String.valueOf(AmountUtil.div(amount, 1L));

        // 超时时间 可空
        String timeoutExpress = "1m";

        // 销售产品码 必填
        String productCode = "QUICK_MSECURITY_PAY";

        AlipayClient client = new DefaultAlipayClient(alipayConfig.getGateway(),
                alipayConfig.getAppid(), alipayConfig.getPrivatekey(),
                alipayConfig.getFormat(), alipayConfig.getCharset(),
                alipayConfig.getPublickey(), alipayConfig.getSigntype());

        AlipayTradeAppPayRequest alipayRequest = new AlipayTradeAppPayRequest();

        // 封装请求支付信息
        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
        model.setOutTradeNo(outTradeNo);
        model.setSubject(bizNote);
        model.setTotalAmount(totalAmount);
        model.setBody(bizNote);
        model.setTimeoutExpress(timeoutExpress);
        model.setProductCode(productCode);
        ExtendParams eParams = new ExtendParams();
        eParams.setSysServiceProviderId(alipayConfig.getProviderid());
        model.setExtendParams(eParams);
        alipayRequest.setBizModel(model);

        // 落地支付记录
        PayRecord payRecord = payRecordService
                .create(id, userId, EPayType.ALIPAY.getCode(), EPayMethod.APP.getCode(), amount, bizType, bizCode, JSONObject.toJSONString(model));

        // 设置异步通知地址
        alipayRequest.setNotifyUrl(alipayConfig.getNotifyurl());
        // 设置同步地址
        alipayRequest.setReturnUrl(alipayConfig.getReturnurl());

        String form;
        try {
            form = client.sdkExecute(alipayRequest).getBody();
//            form = alipayConfig.getGateway().concat("?").concat(form);
//            form = client.pageExecute(alipayRequest).getBody();
        } catch (AlipayApiException e) {
            log.error("支付宝支付唤起异常:" + e.getMessage());
            throw new BizException(EErrorCode.AIS00031);
        }
        return form;
    }

    @Override
    public <T> String doRefund(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
                               Object... bizArgs) {

        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        Charge charge = chargeService.detail(payGroup.toString());
        if (null == charge) {
            throw new BizException(EErrorCode.CORE00020);
        }

        AlipayClient client = new DefaultAlipayClient(alipayConfig.getGateway(),
                alipayConfig.getAppid(), alipayConfig.getPrivatekey(),
                alipayConfig.getFormat(), alipayConfig.getCharset(),
                alipayConfig.getPublickey(), alipayConfig.getSigntype());

        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();

        JSONObject contentModel = new JSONObject();
        contentModel.put("out_trade_no", charge.getId());
        contentModel.put("refund_amount", refundAmount);
        contentModel.put("refund_reason", String.format(bizType.getRemark(), bizArgs));
        request.setBizContent(contentModel.toJSONString());

        AlipayTradeRefundResponse response;
        try {
            response = client.execute(request);

            if (response.isSuccess()) {

                // 托管账户扣钱
                Account fromAccount = accountService.getAccount(TRUST.ALIPAY.getAccountNumber());
//                accountService.changeAmount(fromAccount, refundAmount.negate(),
//                        EChannelType.ALIPAY.getCode(), charge.getId().toString(), charge.getId(), charge.getApplyUser(),
//                        bizType, bizType, bizType, bizArgs);

                log.info("支付宝退款成功,支付组号{}", charge.getId());

            } else {
                log.error("支付宝退款失败,支付组号{}", charge.getId());
            }
        } catch (AlipayApiException e) {
            log.error("支付宝退款失败：{}", e);
        }

        return null;
    }

    private Map<String, String> split(String urlparam) {
        Map<String, String> map = new HashMap<String, String>();
        String[] param = urlparam.split("&");
        for (String keyvalue : param) {
            String[] pair = keyvalue.split("=");
            if (pair.length == 2) {
                map.put(pair[0], WebUtils.decode(pair[1]));
            }
        }
        return map;
    }
}

    
    