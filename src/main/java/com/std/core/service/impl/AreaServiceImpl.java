package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EAreaDeep;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.AreaMapper;
import com.std.core.pojo.domain.Area;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.AreaCreateReq;
import com.std.core.pojo.request.AreaListReq;
import com.std.core.pojo.request.AreaModifyReq;
import com.std.core.pojo.request.AreaPageReq;
import com.std.core.service.IAreaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地区表ServiceImpl
 *
 * <AUTHOR> zhoudong
 * @since : 2020-08-10 17:06
 */
@Service
public class AreaServiceImpl implements IAreaService {

    @Resource
    private AreaMapper areaMapper;

    /**
     * 新增地区表
     *
     * @param req      新增地区表入参
     * @param operator 操作人
     */
    @Override
    public void create(AreaCreateReq req, User operator) {
        Area area = EntityUtils.copyData(req, Area.class);

        areaMapper.insertSelective(area);
    }

    /**
     * 删除地区表
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        areaMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改地区表
     *
     * @param req      修改地区表入参
     * @param operator 操作人
     */
    @Override
    public void modify(AreaModifyReq req, User operator) {
        Area area = EntityUtils.copyData(req, Area.class);
        areaMapper.updateByPrimaryKeySelective(area);
    }

    /**
     * 详情查询地区表
     *
     * @param id 主键ID
     * @return 地区表对象
     */
    @Override
    public Area detail(Long id) {
        Area area = areaMapper.selectByPrimaryKey(id);
        if (null == area) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return area;
    }

    /**
     * 分页查询地区表
     *
     * @param req 分页查询地区表入参
     * @return 分页地区表对象
     */
    @Override
    public List<Area> page(AreaPageReq req) {
        Area condition = EntityUtils.copyData(req, Area.class);

        return areaMapper.selectByCondition(condition);
    }

    /**
     * 列表查询地区表
     *
     * @param req 列表查询地区表入参
     * @return 列表地区表对象
     */
    @Override
    public List<Area> list(AreaListReq req) {
        Area condition = EntityUtils.copyData(req, Area.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Area.class));

        return areaMapper.selectByCondition(condition);
    }

    @Override
    public List<Area> getAllProvince() {
        Area condition = new Area();
        condition.setDeep(EAreaDeep.PROVINCE.getCode());
        return areaMapper.selectByCondition(condition);
    }

    @Override
    public List<Area> allCityOrCounty(Long id) {
        Area condition = new Area();
        condition.setPid(id);
        return areaMapper.selectByCondition(condition);
    }

}