package com.std.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.std.common.enums.ELanguage;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.ArticleMapper;
import com.std.core.pojo.domain.Article;
import com.std.core.pojo.domain.ArticleType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IArticleService;
import com.std.core.service.IArticleTypeService;
import com.std.core.service.ILanguageResourceService;
import com.std.core.util.IdGeneratorUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文章ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:46
 */
@Service
public class ArticleServiceImpl implements IArticleService {

    @Resource
    private ArticleMapper articleMapper;

    @Resource
    private IArticleTypeService articleTypeService;

    @Resource
    private ILanguageResourceService languageResourceService;


    /**
     * 新增文章
     *
     * @param req      新增文章入参
     * @param operator 操作人
     */
    @Override
    public Long create(ArticleCreateReq req, User operator) {
        ArticleType articleType = articleTypeService.detail(req.getTypeId(), null);

        Article article = EntityUtils.copyData(req, Article.class);
        Long articleId = IdGeneratorUtil.generator();

        article.setId(articleId);
        article.setTypeId(articleType.getId());
        article.setType(articleType.getName());
        article.setStatus(EUpDownStatus.DOWN.getCode());

        article.setUpdater(operator.getId());
        article.setUpdaterName(operator.getLoginName());
        article.setUpdateDatetime(new Date());

        articleMapper.insertSelective(article);

        //添加英文翻译
        List<String> columnList = new ArrayList<>();
        columnList.add(ELanguageResource.Article.title.getCode());
        columnList.add(ELanguageResource.Article.content.getCode());
        languageResourceService
                .create(article, articleId, ELanguage.match(req.getLanguage()), columnList);

        return articleId;
    }

    /**
     * 删除文章
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        articleMapper.deleteByPrimaryKey(id);
        languageResourceService.remove(Article.class.getSimpleName(), id);
    }

    /**
     * 修改文章
     *
     * @param req      修改文章入参
     * @param operator 操作人
     */
    @Override
    public void modify(ArticleModifyReq req, User operator) {
        Article article = EntityUtils.copyData(req, Article.class);
        article.setUpdater(operator.getId());
        article.setUpdaterName(operator.getLoginName());
        if (article.getTypeId() != null) {
            article.setType(articleTypeService.detail(article.getTypeId(), "").getName());
        }
        article.setUpdateDatetime(new Date());

        articleMapper.updateByPrimaryKeySelective(article);

        //修改英文翻译
        List<String> columnList = new ArrayList<>();
        columnList.add(ELanguageResource.Article.title.getCode());
        columnList.add(ELanguageResource.Article.content.getCode());
        languageResourceService
                .modify(article, req.getId(), ELanguage.match(req.getLanguage()), columnList);
    }

    @Override
    public void upDown(ArticleBatchUpDownReq req, User operator) {
        for (Long id : req.getIds()) {
            Article article = new Article();
            article.setId(id);
            article.setStatus(req.getResult());
            article.setUpdater(operator.getId());
            article.setUpdaterName(operator.getLoginName());
            article.setUpdateDatetime(new Date());

            articleMapper.updateByPrimaryKeySelective(article);
        }
    }

    /**
     * 详情查询文章
     *
     * @param id 主键ID
     * @return 文章对象
     */
    @Override
    public Article detail(Long id, String language) {
        Article article = articleMapper.selectByPrimaryKey(id);
        if (null == article) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        languageResourceService.languageParse(article, language, id);

        return article;
    }

    /**
     * 分页查询文章
     *
     * @param req 分页查询文章入参
     * @return 分页文章对象
     */
    @Override
    public List<Article> page(ArticlePageReq req) {
        Article condition = EntityUtils.copyData(req, Article.class);

        List<Article> list = articleMapper.selectByCondition(condition);

        for (Article article : list) {
            languageResourceService.languageParse(article, req.getLanguage(), article.getId());
        }

        return list;
    }

    /**
     * PC端-文章分页显示
     *
     * @param req 分页查询文章入参
     * @return 分页文章对象
     */
    @Override
    public List<Article> pcPage(ArticlePageReq req) {
        Article condition = EntityUtils.copyData(req, Article.class);

        List<Article> list = articleMapper.selectPCPage(condition);

        for (Article article : list) {
            languageResourceService.languageParse(article, req.getLanguage(), article.getId());
            if (null != article.getReadNumber()) {
                article.setReadNumber(article.getReadNumber() + 0);
            } else {
                article.setReadNumber(0);
            }
        }

        return list;
    }

    @Override
    public List<Article> page(ArticlePageFrontReq req) {
        Article condition = EntityUtils.copyData(req, Article.class);
        condition.setFrontFlag(EBoolean.YES.getCode());
        condition.setStatus(EUpDownStatus.UP.getCode());
        condition.setOrderBy("t.order_no asc, t.id desc");

        List<Article> list = articleMapper.selectPCPage(condition);

        for (Article article : list) {
            languageResourceService.languageParse(article, req.getLanguage(), article.getId());
        }

        return list;
    }

    /**
     * 列表查询文章
     *
     * @param req 列表查询文章入参
     * @return 列表文章对象
     */
    @Override
    public List<Article> list(ArticleListReq req) {
        Article condition = EntityUtils.copyData(req, Article.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Article.class));

        List<Article> list = articleMapper.selectByCondition(condition);

        for (Article article : list) {
            languageResourceService.languageParse(article, req.getLanguage(), article.getId());
        }

        return list;
    }

    @Override
    public List<Article> list(Long typeId, String language, String status) {
        Article condition = new Article();
        condition.setTypeId(typeId);
        condition.setStatus(status);
        List<Article> list = articleMapper.selectTitleByCondition(condition);

        for (Article article : list) {
            languageResourceService.languageParse(article, language, article.getId());
        }

        return list;
    }

    @Override
    public List<Article> hotArticle() {
        Article condition = new Article();
        condition.setFrontFlag(EBoolean.YES.getCode());
        condition.setStatus(EBoolean.YES.getCode());
        PageHelper.startPage(1, 3);
        List<Article> list = articleMapper.selectTitleByCondition(condition);
        return list;
    }

}