package com.std.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.std.common.enums.ELanguage;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ELanguageResource;
import com.std.core.enums.EUpDownStatus;
import com.std.core.mapper.ArticleTypeMapper;
import com.std.core.pojo.domain.Article;
import com.std.core.pojo.domain.ArticleType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IArticleService;
import com.std.core.service.IArticleTypeService;
import com.std.core.service.IDictService;
import com.std.core.service.ILanguageResourceService;
import com.std.core.util.IdGeneratorUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 文章类型ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:22
 */
@Service
public class ArticleTypeServiceImpl implements IArticleTypeService {

    @Resource
    private ArticleTypeMapper articleTypeMapper;

    @Resource
    private ILanguageResourceService languageResourceService;

    @Resource
    private IArticleService articleService;

    @Resource
    private IDictService dictService;

    /**
     * 新增文章类型
     *
     * @param req      新增文章类型入参
     * @param operator 操作人
     */
    @Override
    public Long create(ArticleTypeCreateReq req, User operator) {
        ArticleType articleType = EntityUtils.copyData(req, ArticleType.class);
        Long id = IdGeneratorUtil.generator();

        articleType.setId(id);
        articleType.setStatus(EUpDownStatus.DOWN.getCode());
        articleType.setUpdater(operator.getId());
        articleType.setUpdaterName(operator.getLoginName());
        articleType.setUpdateDatetime(new Date());

        articleTypeMapper.insertSelective(articleType);

        // 添加英文名称
        languageResourceService.create(ELanguageResource.ArticleType.ArticleType.getCode(),
                ELanguageResource.ArticleType.name.getCode(), id,
                ELanguage.match(req.getLanguage()), req.getName());

        return id;
    }

    /**
     * 删除文章类型
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        articleTypeMapper.deleteByPrimaryKey(id);
        languageResourceService.remove(ELanguageResource.ArticleType.ArticleType.getCode(), id);
    }

    /**
     * 修改文章类型
     *
     * @param req      修改文章类型入参
     * @param operator 操作人
     */
    @Override
    public void modify(ArticleTypeModifyReq req, User operator) {
        ArticleType articleType = EntityUtils.copyData(req, ArticleType.class);

        articleType.setUpdater(operator.getId());
        articleType.setUpdaterName(operator.getLoginName());
        articleType.setUpdateDatetime(new Date());

        articleTypeMapper.updateByPrimaryKeySelective(articleType);

        // 修改英文名称
        languageResourceService.modify(ELanguageResource.ArticleType.ArticleType.getCode(),
                ELanguageResource.ArticleType.name.getCode(), req.getId(),
                ELanguage.match(req.getLanguage()), req.getName());
    }

    @Override
    public void upDown(ArticleTypeBatchUpDownReq req, User operator) {

        for (Long id : req.getIds()) {
            ArticleType articleType = new ArticleType();
            articleType.setId(id);
            articleType.setStatus(req.getResult());
            articleType.setUpdater(operator.getId());
            articleType.setUpdaterName(operator.getLoginName());
            articleType.setUpdateDatetime(new Date());
            articleTypeMapper.updateByPrimaryKeySelective(articleType);
        }

    }

    /**
     * 详情查询文章类型
     *
     * @param id 主键ID
     * @return 文章类型对象
     */
    @Override
    public ArticleType detail(Long id, String language) {
        ArticleType articleType = articleTypeMapper.selectByPrimaryKey(id);
        if (null == articleType) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        languageResourceService.languageParse(articleType, language, id);

        return articleType;
    }

    /**
     * 分页查询文章类型
     *
     * @param req 分页查询文章类型入参
     * @return 分页文章类型对象
     */
    @Override
    public List<ArticleType> page(ArticleTypePageReq req) {
        ArticleType condition = EntityUtils.copyData(req, ArticleType.class);

        List<ArticleType> articleTypeList = articleTypeMapper.selectByCondition(condition);

        for (ArticleType articleType : articleTypeList) {
            languageResourceService
                    .languageParse(articleType, "", articleType.getId());
        }
        return articleTypeList;
    }

    /**
     * 列表查询文章类型
     *
     * @param req 列表查询文章类型入参
     * @return 列表文章类型对象
     */
    @Override
    public List<ArticleType> list(ArticleTypeListReq req) {
        ArticleType condition = EntityUtils.copyData(req, ArticleType.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ArticleType.class));

        List<ArticleType> articleTypeList = articleTypeMapper.selectByCondition(condition);
        for (ArticleType articleType : articleTypeList) {
            languageResourceService
                    .languageParse(articleType, req.getLanguage(), articleType.getId());
            DictListReq listReq = new DictListReq();
            listReq.setParentKey("article_location");
            listReq.setKey(articleType.getLocation());
            articleType.setLocationDesc(dictService.list(listReq).get(0).getValue());
        }
        return articleTypeList;
    }


}