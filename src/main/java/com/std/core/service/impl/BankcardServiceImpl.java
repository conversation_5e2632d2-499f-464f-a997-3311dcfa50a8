package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUpDownStatus;
import com.std.core.mapper.BankcardMapper;
import com.std.core.pojo.domain.Bankcard;
import com.std.core.pojo.domain.ChannelBank;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.BanksListRes;
import com.std.core.service.IBankcardService;
import com.std.core.service.IChannelBankService;
import com.std.core.util.IdGeneratorUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 银行卡ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:46
 */
@Service
public class BankcardServiceImpl implements IBankcardService {

    @Resource
    private BankcardMapper bankcardMapper;

    @Resource
    private IChannelBankService channelBankService;

    /**
     * 新增银行卡
     *
     * @param req      新增银行卡入参
     * @param operator 操作人
     */
    @Override
    public void create(BankcardCreateReq req, User operator) {
        ChannelBank channelBank = channelBankService.detail(req.getChannelBankId());

        if (StringUtils.isNotBlank(req.getDefaultFlag()) && EBoolean.YES.getCode()
                .equals(req.getDefaultFlag())) {
            refreshDefault(req.getType(), req.getUserId(), EBoolean.NO.getCode());
        }

        Bankcard bankcard = EntityUtils.copyData(req, Bankcard.class);
        bankcard.setId(IdGeneratorUtil.generator());
        bankcard.setBankCode(channelBank.getBankCode());
        bankcard.setBankName(channelBank.getBankName());
        bankcard.setStatus(EUpDownStatus.UP.getCode());
        bankcard.setCreateDatetime(new Date());
        bankcard.setUpdater(operator.getId());
        bankcard.setUpdaterName(operator.getLoginName());
        bankcard.setUpdateDatetime(new Date());

        bankcardMapper.insertSelective(bankcard);

    }

    /**
     * 删除银行卡
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        bankcardMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改银行卡
     *
     * @param req      修改银行卡入参
     * @param operator 操作人
     */
    @Override
    public void modify(BankcardModifyReq req, User operator) {
        Bankcard bankcard = EntityUtils.copyData(req, Bankcard.class);
        bankcard.setUpdater(operator.getId());
        bankcard.setUpdaterName(operator.getLoginName());
        bankcard.setUpdateDatetime(new Date());

        if (req.getChannelBankId() > 0) {
            ChannelBank channelBank = channelBankService.detail(req.getChannelBankId());
            bankcard.setBankCode(channelBank.getBankCode());
            bankcard.setBankName(channelBank.getBankName());
        }

        bankcardMapper.updateByPrimaryKeySelective(bankcard);

        Bankcard data = detail(req.getId());
        if (StringUtils.isNotBlank(req.getDefaultFlag()) && EBoolean.YES.getCode()
                .equals(req.getDefaultFlag())) {
            refreshDefault(data.getType(), data.getUserId(), EBoolean.NO.getCode());
        }
    }

    @Override
    public void upDown(Long id, User operator) {
        Bankcard bankcard = detail(id);

        String status = EUpDownStatus.UP.getCode();
        if (EUpDownStatus.UP.getCode().equals(bankcard.getStatus())) {
            status = EUpDownStatus.DOWN.getCode();
        }

        bankcard.setStatus(status);
        bankcard.setUpdater(operator.getId());
        bankcard.setUpdaterName(operator.getLoginName());
        bankcard.setUpdateDatetime(new Date());

        bankcardMapper.updateByPrimaryKeySelective(bankcard);
    }

    @Override
    public void refreshDefault(String type, Long userId, String defaultFlag) {
        Bankcard bankcard = new Bankcard();
        bankcard.setType(type);
        bankcard.setUserId(userId);
        bankcard.setDefaultFlag(defaultFlag);

        bankcardMapper.updateByRef(bankcard);
    }

    @Override
    public void refreshDefault(Long id, String defaultFlag, User operator) {
        Bankcard bankcard = new Bankcard();
        bankcard.setId(id);
        bankcard.setDefaultFlag(defaultFlag);

        if (null != operator) {
            bankcard.setUpdater(operator.getId());
            bankcard.setUpdaterName(operator.getLoginName());
            bankcard.setUpdateDatetime(new Date());
        }

        bankcardMapper.updateByPrimaryKeySelective(bankcard);
    }

    /**
     * 详情查询银行卡
     *
     * @param id 主键ID
     * @return 银行卡对象
     */
    @Override
    public Bankcard detail(Long id) {
        Bankcard bankcard = bankcardMapper.selectByPrimaryKey(id);
        if (null == bankcard) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        init(bankcard);
        return bankcard;
    }

    /**
     * 分页查询银行卡
     *
     * @param req 分页查询银行卡入参
     * @return 分页银行卡对象
     */
    @Override
    public List<Bankcard> page(BankcardPageReq req) {
        Bankcard condition = EntityUtils.copyData(req, Bankcard.class);

        return bankcardMapper.selectByCondition(condition);
    }

    @Override
    public List<Bankcard> page(BankcardPageFrontReq req, User operator) {
        Bankcard condition = EntityUtils.copyData(req, Bankcard.class);
        condition.setUserId(operator.getId());
        List<Bankcard> list = bankcardMapper.selectByCondition(condition);
        for (Bankcard bankcard : list) {
            init(bankcard);
        }
        return list;
    }

    /**
     * 列表查询银行卡
     *
     * @param req 列表查询银行卡入参
     * @return 列表银行卡对象
     */
    @Override
    public List<Bankcard> list(BankcardListReq req) {
        Bankcard condition = EntityUtils.copyData(req, Bankcard.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Bankcard.class));
        List<Bankcard> list = bankcardMapper.selectByCondition(condition);
        for (Bankcard bankcard : list) {
            init(bankcard);
        }
        return list;
    }

    private void init(Bankcard bankcard) {
        bankcard.setChannelBank(channelBankService.detail(bankcard.getChannelBankId()));
    }


    /**
     * 商家端-查询商家的所有银行卡
     * @param req
     * @return
     */
    @Override
    public List<BanksListRes> sellerList(BankcardListReq req) {
        List<Bankcard> list = list(req);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }

        List<BanksListRes> banksListResList=new ArrayList<>();
        for (Bankcard bankcard:list){
            BanksListRes banksListRes=new BanksListRes();
            banksListRes.setId(bankcard.getId());

            String bankName=bankcard.getBankName()+"-"+bankcard.getBankcardNumber();
            banksListRes.setBankName(bankName);
            banksListResList.add(banksListRes);
        }
        return banksListResList;
    }

}