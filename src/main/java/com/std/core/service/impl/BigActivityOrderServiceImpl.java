package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.BigActivityOrderMapper;
import com.std.core.pojo.domain.Activity;
import com.std.core.pojo.domain.ActivityOrder;
import com.std.core.pojo.domain.BigActivityOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.BigActivityOrderCreateReq;
import com.std.core.pojo.request.BigActivityOrderListReq;
import com.std.core.pojo.request.BigActivityOrderListFrontReq;
import com.std.core.pojo.request.BigActivityOrderModifyReq;
import com.std.core.pojo.request.BigActivityOrderPageReq;
import com.std.core.pojo.request.BigActivityOrderPageFrontReq;
import com.std.core.pojo.response.BigActivityOrderDetailRes;
import com.std.core.pojo.response.BigActivityOrderListRes;
import com.std.core.pojo.response.BigActivityOrderPageRes;
import com.std.core.service.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9;
import static com.std.common.utils.DateUtil.dateToStr;

/**
 * 预约单大订单ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-30 14:59
 */
@Slf4j
@Service

public class BigActivityOrderServiceImpl implements IBigActivityOrderService {

    @Resource
    private BigActivityOrderMapper bigActivityOrderMapper;

    @Resource
    private IUserService userService;

    @Resource
    private ISmsService smsService;

    @Resource
    private IActivityOrderService activityOrderService;
    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityTicketLineService activityTicketLineService;

    @Resource
    private IActivityOrderStatisticsService activityOrderStatisticsService;

    @Resource
    private IDailyIncomeSummaryService dailyIncomeSummaryService;

    /**
     * 新增预约单大订单
     *
     * @param req      新增预约单大订单入参
     * @param operator 操作人
     */
    @Override
    public void create(BigActivityOrderCreateReq req, User operator) {
        BigActivityOrder bigActivityOrder = EntityUtils.copyData(req, BigActivityOrder.class);
        bigActivityOrderMapper.insertSelective(bigActivityOrder);
    }

    @Override
    public void create(BigActivityOrder req) {
        bigActivityOrderMapper.insertSelective(req);
    }

    /**
     * 删除预约单大订单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        bigActivityOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改预约单大订单
     *
     * @param req      修改预约单大订单入参
     * @param operator 操作人
     */
    @Override
    public void modify(BigActivityOrderModifyReq req, User operator) {
        BigActivityOrder bigActivityOrder = EntityUtils.copyData(req, BigActivityOrder.class);
        bigActivityOrderMapper.updateByPrimaryKeySelective(bigActivityOrder);
    }

    /**
     * 详情查询预约单大订单
     *
     * @param id 主键ID
     * @return 预约单大订单对象
     */
    @Override
    public BigActivityOrder detail(Long id) {
        BigActivityOrder bigActivityOrder = bigActivityOrderMapper.selectByPrimaryKey(id);
        if (null == bigActivityOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        bigActivityOrder.setUser(userService.selectSummaryInfo(bigActivityOrder.getUserId()));

        return bigActivityOrder;
    }

    /**
     * 分页查询预约单大订单
     *
     * @param req 分页查询预约单大订单入参
     * @return 分页预约单大订单对象
     */
    @Override
    public List<BigActivityOrder> page(BigActivityOrderPageReq req) {
        BigActivityOrder condition = EntityUtils.copyData(req, BigActivityOrder.class);

        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderMapper.selectByCondition(condition);
        // 转译UserId
        bigActivityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return bigActivityOrderList;
    }

    /**
     * 列表查询预约单大订单
     *
     * @param req 列表查询预约单大订单入参
     * @return 列表预约单大订单对象
     */
    @Override
    public List<BigActivityOrder> list(BigActivityOrderListReq req) {
        BigActivityOrder condition = EntityUtils.copyData(req, BigActivityOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), BigActivityOrder.class));

        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderMapper.selectByCondition(condition);
        // 转译UserId
        bigActivityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return bigActivityOrderList;
    }

    @Override
    public List<BigActivityOrder> list(BigActivityOrder req) {
        BigActivityOrder condition = EntityUtils.copyData(req, BigActivityOrder.class);
        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderMapper.selectByCondition(condition);
        return bigActivityOrderList;

    }

    /**
     * 前端详情查询预约单大订单
     *
     * @param id 主键ID
     * @return 预约单大订单对象
     */
    @Override
    public BigActivityOrderDetailRes detailFront(Long id) {
        BigActivityOrderDetailRes res = new BigActivityOrderDetailRes();

        BigActivityOrder bigActivityOrder = bigActivityOrderMapper.selectByPrimaryKey(id);
        if (null == bigActivityOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        bigActivityOrder.setUser(userService.selectSummaryInfo(bigActivityOrder.getUserId()));

        BeanUtils.copyProperties(bigActivityOrder, res);

        return res;
    }

    /**
     * 前端分页查询预约单大订单
     *
     * @param req 前端分页查询预约单大订单入参
     * @return 分页预约单大订单对象
     */
    @Override
    public List<BigActivityOrderPageRes> pageFront(BigActivityOrderPageFrontReq req) {
        BigActivityOrder condition = EntityUtils.copyData(req, BigActivityOrder.class);
        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderMapper.selectByCondition(condition);
        // 转译UserId
        bigActivityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<BigActivityOrderPageRes> resList = bigActivityOrderList.stream().map((entity) -> {
            BigActivityOrderPageRes res = new BigActivityOrderPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(bigActivityOrderList, resList);
    }

    /**
     * 前端列表查询预约单大订单
     *
     * @param req 前端列表查询预约单大订单入参
     * @return 列表预约单大订单对象
     */
    @Override
    public List<BigActivityOrderListRes> listFront(BigActivityOrderListFrontReq req) {
        BigActivityOrder condition = EntityUtils.copyData(req, BigActivityOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), BigActivityOrder.class));

        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderMapper.selectByCondition(condition);
        // 转译UserId
        bigActivityOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<BigActivityOrderListRes> resList = bigActivityOrderList.stream().map((entity) -> {
            BigActivityOrderListRes res = new BigActivityOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 预约支付回调
     *
     * @param serialNumber
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void serveDoCallback(String serialNumber) {
        BigActivityOrder bigActivityOrder = detailBySerialNumber(serialNumber);
        if (!EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_0.getCode().equals(bigActivityOrder.getStatus())) {
            return;
        }
        BigActivityOrder bigOrder = new BigActivityOrder();
        bigOrder.setId(bigActivityOrder.getId());
        bigOrder.setStatus(EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_2.getCode());
        bigOrder.setPayDatetime(new Date());
        bigActivityOrderMapper.updateByPrimaryKeySelective(bigOrder);
        Activity activity = activityService.detail(bigActivityOrder.getActivityId());


        // 生成订单明细
        List<ActivityOrder> activityOrderList = new ArrayList<>();
        BigDecimal price = bigActivityOrder.getTotalPrice().divide(new BigDecimal(bigActivityOrder.getNumber()), 8, BigDecimal.ROUND_DOWN);
        for (int i = 0; i < bigActivityOrder.getNumber(); i++) {
            ActivityOrder activityOrder = new ActivityOrder();
            activityOrder.setSerialNumber(serialNumber);
            activityOrder.setOrderNumber(serialNumber + "-" + i);
            activityOrder.setUserId(bigActivityOrder.getUserId());
            activityOrder.setActivityId(bigActivityOrder.getActivityId());
            activityOrder.setTicketLineId(bigActivityOrder.getTicketLineId());
            activityOrder.setTicketLineName(bigActivityOrder.getTicketLineName());
            activityOrder.setPic(bigActivityOrder.getPic());
            activityOrder.setName(bigActivityOrder.getName());
            activityOrder.setContact(bigActivityOrder.getContact());
            activityOrder.setPrice(price);
            activityOrder.setNumber(1);
            activityOrder.setIsModify(EBoolean.NO.getCode());
            activityOrder.setDate(bigActivityOrder.getDate());
            activityOrder.setPayType(bigActivityOrder.getPayType());
            activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode());
            activityOrder.setCreateDatetime(new Date());
            activityOrderList.add(activityOrder);
            try {
                // 增加每日统计
                activityOrderStatisticsService.addTotalTickets(activity.getId(), activityOrder.getDate(), 1);
            } catch (Exception e) {
                log.error("增加每日统计失败，参数:" + activity.getId() + "," + activityOrder.getDate() + "," + 1);
                log.error("增加每日统计失败，原因:" + e.getMessage());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "网络异常，请重试");
            }
        }

        if (CollectionUtils.isNotEmpty(activityOrderList)) {
            activityOrderService.batchCreate(activityOrderList);
        }


        User user = userService.detail(bigActivityOrder.getUserId());
        String title = "【西溪蔡志忠美术馆】活动预约成功通知";
        String conten = "\"恭喜您成功预约了【" + activity.getName() + "】活动！我们非常期待在即将到来的活动中与您相见，共同探索蔡志忠老师的艺术世界。\n" +
                "\n" +
                " 预约日期：" + dateToStr(bigActivityOrder.getDate(), DATA_TIME_PATTERN_9) + "\n" +
                " 入场时间：[" + "9:00" + "] - [" + "17:30" + "]\n" +
                " 活动地点： [" + activity.getProvince() + activity.getCity() + activity.getCounty() + activity.getAddress() + "]\n" +
                "\n" +
                "你可以通过打开【个人中心】-【我的预约】-【待使用】打开入场码，通过工作人员核销入场。\"";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        // 更新日统计
        dailyIncomeSummaryService.activityIncomeSummary(bigActivityOrder.getTotalPrice(), bigActivityOrder.getPayDatetime());
    }

    @Override
    public List<BigActivityOrder> listTimeOutOrder(Integer closeTime) {
        return bigActivityOrderMapper.selectTimeOutOrder(closeTime, new Date());
    }

    /**
     * 订单取消支付
     *
     * @param serialNumber
     */
    @Override
    public void cancelBigOrder(String serialNumber) {
        BigActivityOrder bigActivityOrder = detailBySerialNumber(serialNumber);
        if (!EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_0.getCode().equals(bigActivityOrder.getStatus())) {
            return;
        }
        BigActivityOrder bigOrder = new BigActivityOrder();
        bigOrder.setId(bigActivityOrder.getId());
        bigOrder.setStatus(EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_1.getCode());
        bigOrder.setPayDatetime(new Date());
        bigActivityOrderMapper.updateByPrimaryKeySelective(bigOrder);

        // 库存加回去，日统计加回去
        activityTicketLineService.addInventory(bigActivityOrder.getTicketLineId(), bigActivityOrder.getNumber());
        activityOrderStatisticsService.addTotalTickets(bigActivityOrder.getActivityId(), bigActivityOrder.getDate(), -bigActivityOrder.getNumber());

        // 第三方取消支付
//        userService.cancelOrder(bigActivityOrder.getSerialNumber());
    }

    private BigActivityOrder detailBySerialNumber(String serialNumber) {
        return bigActivityOrderMapper.selectBySerialNumber(serialNumber);
    }

}