package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUpDownStatus;
import com.std.core.mapper.ChannelBankMapper;
import com.std.core.pojo.domain.ChannelBank;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelBankBatchUpDownReq;
import com.std.core.pojo.request.ChannelBankCreateReq;
import com.std.core.pojo.request.ChannelBankListReq;
import com.std.core.pojo.request.ChannelBankModifyReq;
import com.std.core.pojo.request.ChannelBankPageFrontReq;
import com.std.core.pojo.request.ChannelBankPageReq;
import com.std.core.pojo.response.BanksListRes;
import com.std.core.service.IChannelBankService;
import com.std.core.util.IdGeneratorUtil;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 渠道银行ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 12:29
 */
@Service
public class ChannelBankServiceImpl implements IChannelBankService {

    @Resource
    private ChannelBankMapper channelBankMapper;

    /**
     * 新增渠道银行
     *
     * @param req 新增渠道银行入参
     * @param operator 操作人
     */
    @Override
    public void create(ChannelBankCreateReq req, User operator) {
        ChannelBank channelBank = EntityUtils.copyData(req, ChannelBank.class);
        channelBank.setId(IdGeneratorUtil.generator());
        channelBank.setStatus(EUpDownStatus.UP.getCode());
        channelBankMapper.insertSelective(channelBank);
    }

    /**
     * 删除渠道银行
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        channelBankMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改渠道银行
     *
     * @param req 修改渠道银行入参
     * @param operator 操作人
     */
    @Override
    public void modify(ChannelBankModifyReq req, User operator) {
        ChannelBank channelBank = EntityUtils.copyData(req, ChannelBank.class);

        channelBankMapper.updateByPrimaryKeySelective(channelBank);
    }

    @Override
    public void upDown(ChannelBankBatchUpDownReq request, User operator) {
        for (Long id : request.getIds()) {
            ChannelBank channelBank = new ChannelBank();
            channelBank.setId(id);
            channelBank.setStatus(request.getResult());
            channelBankMapper.updateByPrimaryKeySelective(channelBank);
        }
    }

    /**
     * 详情查询渠道银行
     *
     * @param id 主键ID
     * @return 渠道银行对象
     */
    @Override
    public ChannelBank detail(Long id) {
        ChannelBank channelBank = channelBankMapper.selectByPrimaryKey(id);
        if (null == channelBank) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return channelBank;
    }

    /**
     * 分页查询渠道银行
     *
     * @param req 分页查询渠道银行入参
     * @return 分页渠道银行对象
     */
    @Override
    public List<ChannelBank> page(ChannelBankPageReq req) {
        ChannelBank condition = EntityUtils.copyData(req, ChannelBank.class);

        return channelBankMapper.selectByCondition(condition);
    }

    @Override
    public List<ChannelBank> pageFront(ChannelBankPageFrontReq req) {
        ChannelBank condition = EntityUtils.copyData(req, ChannelBank.class);
        condition.setStatus(EUpDownStatus.UP.getCode());

        return channelBankMapper.selectByCondition(condition);
    }

    /**
     * 列表查询渠道银行
     *
     * @param req 列表查询渠道银行入参
     * @return 列表渠道银行对象
     */
    @Override
    public List<ChannelBank> list(ChannelBankListReq req) {
        ChannelBank condition = EntityUtils.copyData(req, ChannelBank.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ChannelBank.class));

        return channelBankMapper.selectByCondition(condition);
    }


}