package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.ChargeMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.AlipayPayOrderRes;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 充值订单ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 10:01
 */
@Service
public class ChargeServiceImpl implements IChargeService {

    @Resource
    private ChargeMapper chargeMapper;

    @Resource
    private IAccountService accountService;

    @Resource
    private IUserService userService;

    @Resource
    private IBankcardService bankcardService;

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private ISmsService smsService;

    /**
     * 新增充值订单
     *
     * @param req      新增充值订单入参
     * @param operator 操作人
     */
    @Override
    public void create(ChargeOfflineReq req, User operator) {

        if (req.getAmount().compareTo(BigDecimal.ZERO) < 1) {
            throw new BizException(EErrorCode.CORE00005);
        }

        Charge charge = EntityUtils.copyData(req, Charge.class);
        Account account = accountService.getAccount(req.getAccountNumber());

        charge.setId(IdGeneratorUtil.generator());
        charge.setAccountType(account.getType());
        charge.setUserId(account.getUserId());
        charge.setCurrency(account.getCurrency());
        charge.setStatus(EChargeStatus.To_Pay.getCode());

        charge.setBizType(EJourBizTypeUser.Charge.Charge.getCode());
        charge.setBizNote(EJourBizTypeUser.Charge.Charge.getValue());
        charge.setApplyUser(operator.getId());
        charge.setApplyDatetime(new Date());
        charge.setChannelType(EChannelType.OFF_LINE.getCode());

        chargeMapper.insertSelective(charge);
    }

    @Override
    public void create(ChargeToBankcardReq req, User operator) {

        Bankcard bankcard = bankcardService.detail(req.getBankcardId());

        create(new ChargeOfflineReq(req, bankcard), operator);
    }

    @Override
    public <T> Long applyOrderOnline(Account account, String userKind, T bizNo, T payGroup,
                                     String bizType, String bizNote, BigDecimal transAmount, EChannelType channelType,
                                     Long applyUser) {
        Charge charge = new Charge();
        Long id = IdGeneratorUtil.generator();

        charge.setId(id);
        charge.setAccountNumber(account.getAccountNumber());
        charge.setAccountType(account.getType());
        charge.setUserId(account.getUserId());
        charge.setUserKind(userKind);

        charge.setCurrency(account.getCurrency());
        charge.setAmount(transAmount);
        charge.setBizType(bizType);
        charge.setBizNote(bizNote);
        charge.setBizNo(null == bizNo ? id.toString() : bizNo.toString());

        charge.setStatus(EChargeStatus.To_Pay.getCode());
        charge.setApplyUser(applyUser);
        charge.setApplyDatetime(new Date());
        charge.setChannelType(channelType.getCode());
        charge.setPayGroup(null == payGroup ? id.toString() : payGroup.toString());

        chargeMapper.insertSelective(charge);

        return id;
    }

    @Override
    public <T> Long chargeSuccess(Account account, String userKind, T bizNo, T payGroup, String bizType, String bizNote,
                                  BigDecimal transAmount, EChannelType channelType, Long applyUser) {
        Charge charge = new Charge();
        Long id = IdGeneratorUtil.generator();

        Date now = Calendar.getInstance().getTime();

        charge.setId(id);
        charge.setAccountNumber(account.getAccountNumber());
        charge.setAccountType(account.getType());
        charge.setUserId(account.getUserId());
        charge.setUserKind(userKind);

        charge.setCurrency(account.getCurrency());
        charge.setAmount(transAmount);
        charge.setBizType(bizType);
        charge.setBizNote(bizNote);
        charge.setBizNo(null == bizNo ? id.toString() : bizNo.toString());

        charge.setStatus(EChargeStatus.Pay_YES.getCode());
        charge.setApplyUser(applyUser);
        charge.setApplyDatetime(now);
        charge.setChannelType(channelType.getCode());
        charge.setPayGroup(null == payGroup ? id.toString() : payGroup.toString());
        charge.setPayDatetime(now);

        chargeMapper.insertSelective(charge);

        return id;
    }

    @Override
    public void approve(ChargeApproveReq req, User operator) {
        Charge charge = detail(req.getId());

        if (!EChargeStatus.To_Pay.getCode().equals(charge.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "订单未处于待审核状态");
        }

        String status = EChargeStatus.Pay_NO.getCode();
        if (EBoolean.YES.getCode().equals(req.getApproveResult())) {
            status = EChargeStatus.Pay_YES.getCode();
            pay(charge, req, operator);
        }

        Charge data = EntityUtils.copyData(req, Charge.class);
        data.setStatus(status);
        data.setPayDatetime(new Date());
        chargeMapper.updateByPrimaryKeySelective(data);
    }

    private void pay(Charge charge, ChargeApproveReq req, User operator) {
        //充值账户加钱
        Account userAccount = accountService.getAccount(charge.getAccountNumber());

        accountService.changeAmount(userAccount, charge.getAmount().setScale(2, BigDecimal.ROUND_DOWN),
                EChannelType.OFF_LINE.getCode(), req.getChannelOrder(), charge.getId(),
                EJourBizTypeUser.Charge.Charge,
                EJourBizTypeUser.Charge.Charge,
                EJourBizTypeUser.Charge.Charge,
                EChannelType.OFF_LINE.getValue());

//        //公账账户加钱
//        Account systemAccount = accountService.getAccount(CORP.CORP.getAccountNumber());
//        accountService.changeAmount(systemAccount, charge.getAmount(),
//                EChannelType.OFF_LINE.getCode(),
//                req.getChannelOrder(), charge.getId(), charge.getApplyUser(),
//                EJourBizTypeSystem.Charge.Charge,
//                EJourBizTypeSystem.Charge.Charge,
//                EJourBizTypeSystem.Charge.Charge,
//                operator.getLoginName(), EChannelType.OFF_LINE.getValue());
    }

    @Override
    public Object create(ChargeOnlineAppReq req, User operator) {
        Object result;
        Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        // 落地此次付款的订单信息
        Long orderCode = applyOrderOnline(account, operator.getKind(), account.getAccountNumber(),
                operator.getId(), EJourBizTypeUser.Charge.Charge.getCode(), EJourBizTypeUser.Charge.Charge.getValue(),
                req.getAmount(), EChannelType.WECHAT, operator.getId());

        if (EPayType.WECHAT.getCode().equals(req.getChannelType())) {
            result = wechatService.getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getValue(), orderCode,
                    req.getAmount());
        } else if (EPayType.ALIPAY.getCode().equals(req.getChannelType())) {
            String signOrder = alipayService.getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getValue(), orderCode,
                    req.getAmount());
            result = new AlipayPayOrderRes(signOrder);
        } else {
            throw new BizException(EErrorCode.AIS00002);
        }

        return result;
    }

    @Override
    public Object create(ChargeOnlineWebReq req, User operator) {
        Object result;
        Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        if (EPayType.WECHAT.getCode().equals(req.getChannelType())) {
            result = wechatService.getPayCodeInfo(operator.getId(), operator.getKind(),
                    account.getAccountNumber(), operator.getId(),
                    EJourBizTypeUser.Charge.Charge.getCode(),
                    EJourBizTypeUser.Charge.Charge.getValue(), req.getAmount());
        } else if (EPayType.ALIPAY.getCode().equals(req.getChannelType())) {
            String signOrder = alipayService.getTradePagePaySignedOrder(operator.getId(),
                    operator.getKind(), account.getAccountNumber(), null,
                    EJourBizTypeUser.Charge.Charge.getCode(),
                    String.format(EJourBizTypeUser.Charge.Charge.getValue(),
                            EPayType.ALIPAY.getValue()), req.getAmount());
            result = new AlipayPayOrderRes(signOrder);
        } else {
            throw new BizException(EErrorCode.AIS00002);
        }

        return result;
    }

    @Override
    public void callback(Long id, boolean success, String channelOrder) {
        Charge charge = new Charge();

//        charge.setId(id);
        if (success) {
            charge.setStatus(EChargeStatus.Pay_YES.getCode());
        } else {
            charge.setStatus(EChargeStatus.Pay_NO.getCode());
        }
        charge.setChannelOrder(channelOrder);
        charge.setPayUser(null);
        charge.setPayNote("在线充值自动回调");
        charge.setPayDatetime(new Date());

        chargeMapper.updateByPrimaryKeySelective(charge);
    }

    @Override
    public <T> void refreshStatusByPayGroup(T payGroup, String status) {
        Charge charge = new Charge();
        charge.setPayGroup(payGroup.toString());
        charge.setStatus(status);
        chargeMapper.updateStatusByPryGroup(charge);
    }

    @Override
    public void refreshStatus(Long id, String status) {
        Charge charge = new Charge();
        charge.setId(id);
        charge.setStatus(status);
        chargeMapper.updateByPrimaryKeySelective(charge);
    }

    /**
     * 详情查询充值订单
     *
     * @param id 主键ID
     * @return 充值订单对象
     */
    @Override
    public Charge detail(Long id) {
        Charge charge = chargeMapper.selectByPrimaryKey(id);
        if (null == charge) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        init(charge);

        return charge;
    }

    @Override
    public Charge detailBrief(Long id) {
        Charge charge = chargeMapper.selectByPrimaryKey(id);
        if (null == charge) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return charge;
    }

    @Override
    public Charge detail(String payGroup) {
        Charge charge = null;

        Charge condition = new Charge();
        condition.setPayGroup(payGroup);
        List<Charge> chargeList = chargeMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(chargeList)) {
            charge = chargeList.get(0);
        }

        return charge;
    }

    /**
     * 分页查询充值订单
     *
     * @param req 分页查询充值订单入参
     * @return 分页充值订单对象
     */
    @Override
    public List<Charge> page(ChargePageReq req, User operator) {
        Charge condition = EntityUtils.copyData(req, Charge.class);
        // 插入支付起始日
        if (null != req.getPayDatetimeEnd()) {
            condition.setPayDatetimeEnd(DateUtil.getEndDatetime(req.getPayDatetimeEnd()));
        }
        if (null != req.getPayDatetimeStart()) {
            condition.setPayDatetimeStart(DateUtil.strToDate(req.getPayDatetimeStart(), DateUtil.DATA_TIME_PATTERN_9));
        }


        condition.setApplyDatetimeStart(
                DateUtil.strToDate(req.getApplyDatetimeStart(), DateUtil.DATA_TIME_PATTERN_9));
        condition.setApplyDatetimeEnd(DateUtil.getEndDatetime(req.getApplyDatetimeEnd()));

        List<Charge> list = chargeMapper.selectByCondition(condition);
        for (Charge charge : list) {
            init(charge);
        }

        return list;
    }

    /**
     * 列表查询充值订单
     *
     * @param req 列表查询充值订单入参
     * @return 列表充值订单对象
     */
    @Override
    public List<Charge> list(ChargeListReq req) {
        Charge condition = EntityUtils.copyData(req, Charge.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Charge.class));

        return chargeMapper.selectByCondition(condition);
    }

    @Override
    public List<Long> selectEffectUsers(List<User> userList) {
        Charge condition = new Charge();
        condition.setUserList(userList);
        condition.setStatus(EChargeStatus.Pay_YES.getCode());

        return chargeMapper.selectEffectUserList(condition);
    }


    @Override
    public List<Charge> list(List<String> channelTypeList, String status, Date applyDatetimeEnd) {
        Charge condition = new Charge();
        condition.setChannelTypeList(channelTypeList);
        condition.setStatus(status);
        condition.setApplyDatetimeEnd(applyDatetimeEnd);
        return chargeMapper.selectByCondition(condition);
    }

    @Override
    public void doChargeCallback(Boolean isSuccess, String channelNo, Long outTradeNo) {
        // 锁定订单信息
        Charge charge = detailBrief(outTradeNo);

        if (!EChargeStatus.To_Pay.getCode().equals(charge.getStatus())) {
            return;
        }

        if (isSuccess) {
            //业务类型
            EJourCommon bizCategory = EJourBizTypeUser.matchCode(charge.getBizType());
            EJourCommon bizType = EJourBizTypeUser.matchCode(charge.getBizType());

            //业务参数
            Object[] bizOrgs = null;
            if (EJourBizTypeUser.Charge.Charge.getCode().equals(charge.getBizType())) {
                bizOrgs = new Object[]{EPayType.matchOnline(charge.getChannelType())};
            }

            // 收款方账户加钱
            Account account = accountService.getAccount(charge.getAccountNumber());
            accountService.changeAmount(account, charge.getAmount().setScale(2, BigDecimal.ROUND_DOWN),
                    charge.getChannelType(), channelNo, charge.getId(),
                    bizCategory, bizType, bizType, bizOrgs);
            charge.setStatus(EChargeStatus.Pay_YES.getCode());
        } else {
            charge.setStatus(EChargeStatus.Pay_NO.getCode());
        }
        charge.setChannelOrder(channelNo);
        charge.setPayUser(null);
        charge.setPayNote("在线充值自动回调");
        charge.setPayDatetime(new Date());

        chargeMapper.updateByPrimaryKeySelective(charge);
    }


    private boolean isExistOfChannelOrder(String refNo) {
        boolean result = false;
        Charge condition = new Charge();
        condition.setChannelOrder(refNo);
        if (chargeMapper.selectTotalCountByCondition(condition) > 0) {
            result = true;
        }
        return result;
    }

    private void init(Charge charge) {

        User userInfo = userService.detailBrief(charge.getUserId());
        charge.setUserInfo(userInfo);
        charge.setApplyUserName(userInfo.getRealName());

    }
}