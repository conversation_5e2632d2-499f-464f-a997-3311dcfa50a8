package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.CnavigateMapper;
import com.std.core.pojo.domain.Cnavigate;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserLog;
import com.std.core.pojo.request.*;
import com.std.core.service.ICnavigateService;
import com.std.core.service.IUserLogService;
import com.std.core.util.IdGeneratorUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 导航ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-24 11:04
 */
@Service
public class CnavigateServiceImpl implements ICnavigateService {

    @Resource
    private CnavigateMapper cnavigateMapper;

    @Resource
    private IUserLogService userLogService;

    /**
     * 新增导航
     *
     * @param req      新增导航入参
     * @param operator 操作人
     */
    @Override
    public void create(CnavigateCreateReq req, User operator, String ip) {
        Cnavigate cnavigate = EntityUtils.copyData(req, Cnavigate.class);
        cnavigate.setId(IdGeneratorUtil.generator());
        cnavigate.setStatus(EUpDownStatus.DOWN.getCode());

        cnavigateMapper.insertSelective(cnavigate);

        String content = "新增轮播图";
        userLogService.create(operator, EUserLogType.BANNER.getCode(), EUserLogType.BANNER.getValue(), ip, content);
    }


    /**
     * 删除导航
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        cnavigateMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改导航
     *
     * @param req      修改导航入参
     * @param operator 操作人
     */
    @Override
    public void modify(CnavigateModifyReq req, User operator, String ip) {
        Cnavigate cnavigate = EntityUtils.copyData(req, Cnavigate.class);

        cnavigateMapper.updateByPrimaryKeySelective(cnavigate);

        String content = "修改轮播图";
        userLogService.create(operator, EUserLogType.BANNER.getCode(), EUserLogType.BANNER.getValue(), ip, content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upDown(CNavigateBatchUpDownReq request, User operator, String ip) {

        List<UserLog> userLogList = new ArrayList<>();
        for (Long id : request.getIds()) {
            Cnavigate detail = detail(id);

//            if (detail.getStatus().equals(request.getResult())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "状态不正确，操作错误");
//            }
            Cnavigate cnavigate = new Cnavigate();
            cnavigate.setId(id);
            cnavigate.setStatus(request.getResult());
            cnavigateMapper.updateByPrimaryKeySelective(cnavigate);

//            String a = "";
//            if (ECnavigateState.wait.getCode().equals(detail.getStatus())) {
//                a = "待发布";
//            } else if (ECnavigateState.down.getCode().equals(detail.getStatus())) {
//                a = "已下架";
//            }
//            String b = "";
//            if (ECnavigateState.up.getCode().equals(request.getResult())) {
//                b = "已发布";
//            } else if (ECnavigateState.down.getCode().equals(request.getResult())) {
//                b = "已下架";
//            }
            String content = "状态更新" + ECnavigateState.getCnavigateStatus(detail.getStatus()).getValue() + "改成" + ECnavigateState.getCnavigateStatus(request.getResult()).getValue();
            UserLog userLog = new UserLog();
            userLog.setUserId(operator.getId().toString());
            userLog.setUserName(operator.getLoginName());
            userLog.setType(EUserLogType.BANNER.getCode());
            userLog.setTypeNote(EUserLogType.BANNER.getValue());
            userLog.setIp(ip);
            userLog.setContent(content);
            userLogList.add(userLog);
        }
        userLogService.batchCreate(userLogList);
    }

    /**
     * 详情查询导航
     *
     * @param id 主键ID
     * @return 导航对象
     */
    @Override
    public Cnavigate detail(Long id) {
        Cnavigate cnavigate = cnavigateMapper.selectByPrimaryKey(id);
        if (null == cnavigate) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return cnavigate;
    }

    /**
     * 分页查询导航
     *
     * @param req 分页查询导航入参
     * @return 分页导航对象
     */
    @Override
    public List<Cnavigate> page(CnavigatePageReq req) {
        Cnavigate condition = EntityUtils.copyData(req, Cnavigate.class);

        return cnavigateMapper.selectByCondition(condition);
    }

    /**
     * 列表查询导航
     *
     * @param req 列表查询导航入参
     * @return 列表导航对象
     */
    @Override
    public List<Cnavigate> list(CnavigateListReq req) {
        Cnavigate condition = EntityUtils.copyData(req, Cnavigate.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Cnavigate.class));

        return cnavigateMapper.selectByCondition(condition);
    }

    @Override
    public List<Cnavigate> list(CnavigateListFrontReq req) {
        Cnavigate condition = EntityUtils.copyData(req, Cnavigate.class);
        condition.setOrderBy("t.order_no desc");
        condition.setStatus(EBoolean.YES.getCode());
        return cnavigateMapper.selectByCondition(condition);
    }

    /**
     * 查询快捷入口
     *
     * @return
     */
    @Override
    public List<Cnavigate> quickEntryList() {
        Cnavigate cnavigate = new Cnavigate();
        cnavigate.setType("app_quick_entry");

        return cnavigateMapper.selectByCondition(cnavigate);
    }

}