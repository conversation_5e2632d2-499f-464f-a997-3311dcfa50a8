package com.std.core.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.qiniu.util.Auth;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.core.config.AliOSSConfig;
import com.std.core.enums.EConfigType;
import com.std.core.enums.EErrorCode;
import com.std.core.pojo.response.AliOSSRes;
import com.std.core.service.ICommonService;
import com.std.core.service.IConfigService;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.KdniaoAPI;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Map;


/**
 * <AUTHOR> silver
 * @since : 2020-03-07 12:31
 */

@Service
public class CommonServiceImpl implements ICommonService {

    @Resource
    private IConfigService configService;

    @Resource
    private AliOSSConfig aliOSSConfig;


    @Resource
    private KdniaoAPI kdniaoAPI;


    @Override
    public String getQiniutoken() {
        Map<String, String> configMap = configService.listByType(EConfigType.QINIU.QINIU.getCode());
        if (configMap.isEmpty()) {
            throw new BizException(EErrorCode.AUTH00008);
        }

        String accessKey = configMap.get(EConfigType.QINIU.QINIU_ACCESS_KEY.getCode());
        String secretKey = configMap.get(EConfigType.QINIU.QINIU_SECRET_KEY.getCode());
        String bucket = configMap.get(EConfigType.QINIU.QINIU_BUCKET.getCode());
        if (StringUtils.isBlank(accessKey) || StringUtils.isBlank(secretKey)
                || StringUtils.isBlank(bucket)) {
            throw new BizException(EErrorCode.AUTH00008);
        }

        Auth auth = Auth.create(accessKey, secretKey);
        return auth.uploadToken(bucket);
    }

    @Override
    public AliOSSRes getAliOssToken() {
        try {
            // 添加endpoint（直接使用STS endpoint，前两个参数留空，无需添加region ID）
            DefaultProfile.addEndpoint("", "", "Sts", aliOSSConfig.getEndpoint());
            // 构造default profile（参数留空，无需添加region ID）
            IClientProfile profile = DefaultProfile.getProfile("", aliOSSConfig.getAccessKeyId(), aliOSSConfig.getAccessKeySecret());
            // 用profile构造client
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setMethod(MethodType.POST);
            request.setRoleArn(aliOSSConfig.getRoleArn());
            request.setRoleSessionName(IdGeneratorUtil.generateShortUuid());
//            request.setPolicy(policy); // 若policy为空，则用户将获得该角色下所有权限
            request.setDurationSeconds(2000L); // 设置凭证有效时间
            final AssumeRoleResponse response = client.getAcsResponse(request);

            AliOSSRes aliOSSRes = EntityUtils.copyData(response.getCredentials(), AliOSSRes.class);
            aliOSSRes.setBucket(aliOSSConfig.getBucket());
            aliOSSRes.setEndpoint(aliOSSConfig.getBucketEndpoint());
            aliOSSRes.setOssEndpoint(aliOSSConfig.getBucketOssEndpoint());
            aliOSSRes.setFilePath(aliOSSConfig.getBucketFilePath());

            return aliOSSRes;
        } catch (Exception e) {
            throw new BizException(EErrorCode.E500003.getCode(), e.getMessage());
        }
    }

    @Override
    public String putObject(String filePath) {
        // Endpoint以华东1（杭州）为例，其它Region请按实际情况填写。
        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
        // 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
        String accessKeyId = aliOSSConfig.getAccessKeyId();
        String accessKeySecret = aliOSSConfig.getAccessKeySecret();
        // 填写Bucket名称，例如examplebucket。
        String bucketName = aliOSSConfig.getBucket();
        // 填写Object完整路径，完整路径中不能包含Bucket名称，例如exampledir/exampleobject.txt。
        long timeMillis = System.currentTimeMillis();
        String objectName = "exampledir/" + timeMillis + ".png";
//        String objectName = "exampledir/result.png";
        // 填写本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
        // 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
//        String filePath= "/Users/<USER>/Desktop/photo-test/1.png";

        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, new File(filePath));
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 上传文件。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
            System.out.println(result.getResponse().getStatusCode());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (com.aliyun.oss.ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }

        objectName = aliOSSConfig.getBucketFilePath().concat("/").concat(objectName);
        return objectName;
    }
}

