package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.enums.EUserLogType;
import com.std.core.mapper.ConfigMapper;
import com.std.core.pojo.domain.Config;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ConfigCreateReq;
import com.std.core.pojo.request.ConfigListReq;
import com.std.core.pojo.request.ConfigModifyReq;
import com.std.core.pojo.request.ConfigPageReq;
import com.std.core.service.IConfigService;
import com.std.core.service.IUserLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:20
 */
@Service
public class ConfigServiceImpl implements IConfigService {

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private IUserLogService userLogService;

    @Override
    public void create(ConfigCreateReq req, User operateUser) {
        Config sysConfig = new Config();
        sysConfig.setType(req.getType());
        sysConfig.setKey(req.getKey());
        sysConfig.setValue(req.getValue());
        sysConfig.setUpdater(operateUser.getLoginName());

        sysConfig.setUpdateTime(new Date());
        sysConfig.setRemark(req.getRemark());

        configMapper.insertSelective(sysConfig);
    }

    @Override
    public void modify(ConfigModifyReq req, User operateUser, String ip) {
        Config config = detail(Long.parseLong(req.getId()));
        Config sysConfig = new Config();
        sysConfig.setId(Long.parseLong(req.getId()));
        sysConfig.setValue(req.getValue());
        sysConfig.setUpdater(operateUser.getLoginName());

        sysConfig.setUpdateTime(new Date());
        sysConfig.setRemark(req.getRemark());
        configMapper.updateByPrimaryKeySelective(sysConfig);

        // 生成操作日志
        String content = "内容修改：修改系统参数" + config.getKey();
        userLogService.create(operateUser, EUserLogType.CONFIG.getCode(), EUserLogType.CONFIG.getValue(), ip, content);
    }

    @Override
    public Config detail(Long id) {
        return configMapper.selectByPrimaryKey(id);
    }

    @Override
    public Config detailByKey(String key) {
        Config condition = new Config();
        condition.setKey(key);
        List<Config> configList = configMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(configList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "系统参数【" + key + "】不存在");
        }
        return configList.get(0);
    }

    @Override
    public List<Config> list(String type) {
        Config sysConfig = new Config();
        sysConfig.setType(type);
        return configMapper.selectByCondition(sysConfig);
    }

    @Override
    public List<Config> page(ConfigPageReq req) {
        Config sysConfig = new Config();
        sysConfig.setType(req.getType());
        sysConfig.setKey(req.getKey());
        sysConfig.setUpdater(req.getUpdater());
        sysConfig.setRemark(req.getRemark());
        return configMapper.selectByCondition(sysConfig);
    }

    @Override
    public List<Config> list(ConfigListReq req) {
        Config sysConfig = new Config();
        sysConfig.setType(req.getType());
        sysConfig.setKey(req.getKey());
        sysConfig.setUpdater(req.getUpdater());
        return configMapper.selectByCondition(sysConfig);
    }

    @Override
    public Map<String, String> listByType(String type, String key) {
        Config condition = new Config();
        condition.setType(type);
        condition.setKey(key);
        List<Config> configList = configMapper.selectByCondition(condition);
        Map<String, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(configList)) {
            for (Config config : configList) {
                map.put(config.getKey(), config.getValue());
            }
        }
        return map;
    }

    @Override
    public Map<String, String> listByType(String type) {
        Config condition = new Config();
        condition.setType(type);
        List<Config> configList = configMapper.selectByCondition(condition);
        Map<String, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(configList)) {
            for (Config config : configList) {
                map.put(config.getKey(), config.getValue());
            }
        }
        return map;
    }

    @Override
    public Integer getIntegerValue(String key) {
        Config config = detailByKey(key);
        try {
            return Integer.valueOf(config.getValue());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "参数名为" + key + "的配置转换成Integer类型发生错误, 原因：" + e.getMessage());
        }
    }

    @Override
    public Long getLongValue(String key) {
        Config config = detailByKey(key);
        try {
            return Long.valueOf(config.getValue());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "参数名为" + key + "的配置转换成Long类型发生错误, 原因：" + e.getMessage());
        }
    }

    @Override
    public String getStringValue(String key) {
        Config config = detailByKey(key);
        return config.getValue();
    }

    @Override
    public BigDecimal getBigDecimalValue(String key) {
        Config config = detailByKey(key);
        try {
            return new BigDecimal(config.getValue());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "参数名为" + key + "的配置转换成BigDecimal类型发生错误, 原因：" + e.getMessage());
        }
    }

    @Override
    public List<Config> aListByType(String type, String key) {
        Config condition = new Config();
        condition.setType(type);
        condition.setKey(key);
        List<Config> configList = configMapper.selectByCondition(condition);
        if (!CollectionUtils.isEmpty(configList)) {
            return configList;
        }
        return null;
    }
}
