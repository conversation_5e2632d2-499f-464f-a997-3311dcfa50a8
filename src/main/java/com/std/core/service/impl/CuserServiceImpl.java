package com.std.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.*;
import com.std.core.config.WechatConfig;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.*;
import com.std.core.mapper.CuserMapper;
import com.std.core.mapper.UserMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.*;
import com.vdurmont.emoji.EmojiParser;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * C端用户ServiceImpl
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@Service
@Slf4j
public class CuserServiceImpl implements ICuserService {


    @Resource
    private WxMpService wxMpService;

    @Resource
    private IUserService userService;

    @Resource
    private CuserMapper cuserMapper;

    @Resource
    private IUserLogService userLogService;

    @Resource
    private IUserActionLogService userActionLogService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private WechatConfig wechatConfig;

    @Resource
    private IConfigService configService;


    @Resource
    private TokenUtil tokenUtil;

    @Resource
    private ISmsService smsService;

   @Resource
    private IUserRoleService userRoleService;

    @Resource
    private SysProperties sysProperties;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 新增C端用户
     *
     * @param req      新增C端用户入参
     * @param operator 操作人
     */
    @Override
    public void create(CuserCreateReq req, User operator) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);

        cuserMapper.insertSelective(cuser);
    }

    /**
     * 怀南会产品服务条款
     */
    @Override
    public Config detailRegisterServiceConfig() {
        String key = "register_service_config";

        return configService.detailByKey(key);
    }

    /**
     * 删除C端用户
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        cuserMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改C端用户
     *
     * @param req      修改C端用户入参
     * @param operator 操作人
     */
    @Override
    public void modify(CuserModifyReq req, User operator) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);

        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    @Override
    public void modify(Cuser cuser) {
        cuserMapper.updateByPrimaryKeySelective(cuser);
    }

    /**
     * 设置状态
     */
    @Override
    public void modifyStatus(UserStatusReq req, User operator, String ip) {
        Cuser cuser = detail(req.getId());

        User user = userService.detail(cuser.getUserId());
        UserModifyReq userModifyReq = new UserModifyReq();
        userModifyReq.setId(cuser.getUserId());
        userModifyReq.setStatus(req.getStatus());
        userService.modify(userModifyReq, operator);

        String status = EUserStatus.getUserStatus(user.getStatus()).getValue();
        String newStatus = EUserStatus.getUserStatus(req.getStatus()).getValue();
        String content = "状态更新：" + status + "改成" + newStatus;
        userLogService.create(operator, EUserLogType.CUSER.getCode(), EUserLogType.CUSER.getValue(), ip, content);

        delCuserRedis(cuser.getUserId());
    }

    /**
     * 详情查询C端用户
     *
     * @param id 主键ID
     * @return C端用户对象
     */
    @Override
    public Cuser detail(Long id) {
        Cuser cuser = cuserMapper.selectByPrimaryKey(id);
        if (null == cuser) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return cuser;
    }

    /**
     * 分页查询C端用户
     *
     * @param req 分页查询C端用户入参
     * @return 分页C端用户对象
     */
    @Override
    public List<Cuser> page(CuserPageReq req) {
        Cuser condition = EntityUtils.copyData(req, Cuser.class);
        condition.setStatus(req.getStatus());
//        condition.setUserId(req.getUserId());
//        condition.setUserReferee(req.getUserReferee());

        List<Cuser> cusers = cuserMapper.select(condition);

        for (Cuser cuser : cusers) {
            cuser.setUser(userService.detailBrief(cuser.getUserId()));
            if (null != cuser.getUserReferee()) {
                cuser.setRefereeUser(userService.detailBrief(cuser.getUserReferee()));
            }
        }

        return cusers;
    }

    /**
     * 模糊查询
     */
    @Override
    public List<Cuser> vagueDeatil(CuserPageReq req) {
        Cuser cuser = EntityUtils.copyData(req, Cuser.class);

        return cuserMapper.selectVague(cuser);
    }

    /**
     * 列表查询C端用户
     *
     * @param req 列表查询C端用户入参
     * @return 列表C端用户对象
     */
    @Override
    public List<Cuser> list(CuserListReq req) {
        Cuser condition = EntityUtils.copyData(req, Cuser.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Cuser.class));

        return cuserMapper.selectByCondition(condition);
    }


    @Override
    public List<Cuser> list() {
        Cuser condition = new Cuser();
        condition.setHaveOpenid(EBoolean.YES.getCode());
        return cuserMapper.selectByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject register(UserRegisterReq request, String ip) {

        User user = userService.register(request, ip);

        cUserInsert(user);

        String title = "感谢注册";
        String content = "尊敬的" + request.getMobile().substring(0, 3) + "****" + request.getMobile().substring(8, 11) + "用户，您好，欢迎注册使用杏福宝产品";
        smsService.sendMyMsg(user, title, content, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        return result;

    }

    private void cUserInsert(User user) {

        Cuser cuser = new Cuser();
        cuser.setUserId(user.getId());
        cuser.setMemberFlag(EBoolean.NO.getCode());
        cuser.setVipFlag(EBoolean.NO.getCode());
        cuser.setLevel(EUserLevel.L0.getCode());
        cuser.setCreateDatetime(user.getRegisterDatetime());
        cuserMapper.insertSelective(cuser);
    }

    @Override
    public User ossRegister(UserRegisterByOssReq request) {
        User user = EntityUtils.copyData(request, User.class);
        Long userId = IdGeneratorUtil.generator();
        Date now = new Date();
        user.setId(userId);
        user.setKind(EUserKind.C.getCode());
        user.setNickname(userId.toString().substring(userId.toString().length() - 8, userId.toString().length()));
        user.setLoginName(user.getMobile());
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
        user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
        user.setRegisterDatetime(now);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setPhoto(configService.getStringValue("user_photo"));
        userMapper.insertSelective(user);
        // 添加默认权限
        userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());
        cUserInsert(user);

        return user;
    }

    @Override
    public JSONObject login(UserLoginReq request, String ip) {
        return userService.login(request, EUserKind.C.getCode(), ip);
    }

    @Override
    public JSONObject login(CUserLoginReq request, String ip) {
        return userService.login(request, EUserKind.C.getCode(), ip);
    }

    @Override
    public JSONObject agentLogin(UserLoginReq request, String ip) {

        JSONObject user = userService.login(request, EUserKind.C.getCode(), ip);
        Cuser cuser = detailByUserId(user.getLongValue("userId"));
        if (!ECuserLevel.CUSER_LEVEL_1.getCode().equals(cuser.getLevel())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您还不是渠道商，请联系商务");
        }
        if (!ECuserAgentStatus.CUSER_AGENTSTATUS_0.getCode().equals(cuser.getAgentStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您已经停止合作");
        }

        return user;
    }

    @Override
    public Cuser detailByUserId(Long userId) {
        Cuser condition = new Cuser();
        condition.setUserId(userId);
        List<Cuser> cuserList = cuserMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(cuserList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "userId不存在");
        }
        return cuserList.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void joinAgent(CuserJoinAgentReq request) {
        Cuser cuser = detailByUserId(request.getUserId());
//        if (EUserLevel.AGENT.getCode().equals(cuser.getLevel())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该用户已经是渠道商");
//        }

        Cuser cuserUpdateDO = new Cuser();
        cuserUpdateDO.setId(cuser.getId());
        cuserUpdateDO.setAgentJoinDatetime(new Date());
        cuserUpdateDO.setAgentStatus(ECuserAgentStatus.CUSER_AGENTSTATUS_0.getCode());
        cuserUpdateDO.setLevel(ECuserLevel.CUSER_LEVEL_1.getCode());
        cuserMapper.updateByPrimaryKeySelective(cuserUpdateDO);

        if (StringUtils.isNotBlank(request.getRemark())) {
            User user = new User();
            user.setId(cuser.getUserId());
            user.setRemark(request.getRemark());
            userMapper.updateByPrimaryKeySelective(user);
        }

    }

    @Override
    public void removeAgent(CuserRemoveAgentReq request) {

        Cuser cuser = detailByUserId(request.getUserId());
//        if (!EUserLevel.AGENT.getCode().equals(cuser.getLevel())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该用户不是渠道商，无法操作");
//        }

        Cuser cuserUpdateDO = new Cuser();
        cuserUpdateDO.setId(cuser.getId());

        if (ECuserAgentStatus.CUSER_AGENTSTATUS_1.getCode().equals(cuser.getAgentStatus())) {
            cuserUpdateDO.setAgentStatus(ECuserAgentStatus.CUSER_AGENTSTATUS_0.getCode());
        } else {
            cuserUpdateDO.setAgentStatus(ECuserAgentStatus.CUSER_AGENTSTATUS_1.getCode());
            cuserUpdateDO.setAgentQuitDatetime(new Date());
        }
        cuserMapper.updateByPrimaryKeySelective(cuserUpdateDO);

        if (StringUtils.isNotBlank(request.getRemark())) {
            User user = new User();
            user.setId(cuser.getUserId());
            user.setRemark(request.getRemark());
            userMapper.updateByPrimaryKeySelective(user);
        }
    }

    @Override
    public List<Cuser> agentPage(CuserAgentPageReq req) {
        Cuser condition = EntityUtils.copyData(req, Cuser.class);
        condition.setLevel(ECuserLevel.CUSER_LEVEL_1.getCode());
        List<Cuser> cusers = cuserMapper.selectRichByCondition(condition);

        return cusers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentSetRate(CuserAgentSetRateReq req, User operator) {
        Date now = new Date();
        Cuser cuser = detailByUserId(req.getId());

    }

    @Override
    public Cuser agentDetail(Long id) {
        Cuser cuser = cuserMapper.selectByPrimaryKey(id);
        if (null == cuser) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return cuser;
    }

    /**
     * 根据用户序号查询C端用户信息
     */
    @Override
    public Cuser detailByUser(Long userId) {
        Cuser cuser = cuserMapper.selectByUserId(userId);
        if (null == cuser) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), userId);
        }
        return cuser;
    }

    /**
     * 会员查询
     */
    @Override
    public List<UserDeatilRes> userDeatilRes() {

//        cuserMapper
        return null;
    }

    @Override
    public List<TeamUserRes> teamPage(CuserPageReq request) {
        List<TeamUserRes> teamUserList = new ArrayList<>();
        List<Cuser> cuserList = page(request);
        for (Cuser cuser : cuserList) {
            TeamUserRes teamUser = new TeamUserRes();
            teamUser.setUserId(cuser.getUserId());
            teamUser.setNickname(cuser.getNickname());
            teamUser.setRealName(cuser.getRealName());
            teamUser.setMobile(cuser.getMobile());
            teamUser.setStatus(cuser.getStatus());
            teamUser.setCreditScore(BigDecimal.ZERO);
            teamUser.setLevelName(cuser.getLevelName());
            teamUser.setNodeLevel("无");
            List<User> userList = new ArrayList<>();
            User user = new User();
            user.setId(cuser.getUserId());
            userList.add(user);
            teamUser.setAmount(BigDecimal.ZERO);
            //直推人数
            user = new User();
            user.setUserReferee(cuser.getUserId());
            user.setKind(EUserKind.C.getCode());
            Integer count = userService.selectCount(user);
            teamUser.setReferUserCount(count);
            //直推销售额
            userList = new ArrayList<>();
            userService.queryAllInferior(userList, cuser.getUserId(), 1);
            teamUser.setReferAmount(BigDecimal.ZERO);
            //团队人数
            userList = new ArrayList<>();
            userService.queryAllInferior(userList, cuser.getUserId(),
                    configService.getIntegerValue(EConfigType.INCOME.NODE_INCOME_LEVEL.getCode()));
            teamUser.setTeamCount(userList.size());
            //团队销售额（需要加上自己的）
            user = new User();
            user.setId(cuser.getUserId());
            userList.add(user);
            teamUser.setTeamAmount(BigDecimal.ZERO);
            teamUserList.add(teamUser);
        }
        return teamUserList;
    }

    @Override
    public List<RecommendUserRes> recommendChain(CuserRecommendReq request) {
        List<RecommendUserRes> teamUserList = new ArrayList<>();
        CuserPageReq req = EntityUtils.copyData(request, CuserPageReq.class);
        List<Cuser> cuserList = page(req);
        for (Cuser cuser : cuserList) {
            RecommendUserRes teamUser = new RecommendUserRes();
            teamUser.setUserId(cuser.getUserId());
            teamUser.setNickname(cuser.getNickname());
            teamUser.setRealName(cuser.getRealName());
            teamUser.setMobile(cuser.getMobile());
            teamUser.setStatus(cuser.getStatus());
            List<User> allUsers = new ArrayList<>();
            User user = new User();
            user.setId(cuser.getUserId());
            allUsers.add(user);

            user = new User();
            user.setUserReferee(cuser.getUserId());
            user.setKind(EUserKind.C.getCode());
            Integer count = userService.selectCount(user);
            teamUser.setReferUserCount(count);

            teamUser.setLevelName(EGradeLevel.getCuserLevel(cuser.getUser().getChannelGrade().toString()).getValue());

            teamUserList.add(teamUser);

        }

        return teamUserList;
    }

    /**
     * 只是拿自己下级有等级的人
     * @param userId
     * @return
     */
    private List<User> channelGradeUserList(Long userId) {
        User condition = new User();
        condition.setChannelId(userId);
        List<Integer> gradeList = new ArrayList<>();
        gradeList.add(1);
        gradeList.add(2);
        gradeList.add(3);
        condition.setGradeList(gradeList);
        return userMapper.selectByCondition(condition);

    }


    /**
     * 查询自己所有等级下级
     * @param userId
     * @return
     */
    private void channelAllGradeUserList(List<User> userList,Long userId) {
        User condition = new User();
        condition.setChannelId(userId);
        List<Integer> gradeList = new ArrayList<>();
        gradeList.add(1);
        gradeList.add(2);
        gradeList.add(3);
        condition.setGradeList(gradeList);
        List<User> users = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(users)){
            userList.addAll(users);
        }

        for (User user : users) {
            channelAllGradeUserList(userList,user.getId());
        }
    }


    private List<User> channelNextUserList(Long userId) {
        User condition = new User();
        condition.setChannelId(userId);
        condition.setChannelGrade(0);

        return userMapper.selectByCondition(condition);

    }


    @Override
    public List<GradeUserRes> gradeChain(CuserRecommendReq request) {

        return null;
    }


    @Override
    public List<DissociateUserRes> dissociateChain() {
        List<DissociateUserRes> resList = new ArrayList<>();
        List<User> userList = userMapper.dissociateChain();
        for (User user : userList) {
            DissociateUserRes res = new DissociateUserRes();
            res.setUserId(user.getId());
            res.setNickname(user.getNickname());
            res.setRealName(user.getRealName());
            res.setMobile(user.getMobile());
            res.setLevelName(EGradeLevel.getCuserLevel(user.getChannelGrade().toString()).getValue());
            //是否可以下拉。
            List<User> list = channelGradeUserList(user.getId());
            res.setIsNext(list.size() > 0 ? 1 : 0);
            resList.add(res);
        }
        return resList;
    }

    @Override
    public void changeReferUser(UserChangeRefereeReq request) {
        User user = userService.detail(request.getUserId());
        if (user.getId().equals(request.getUserReferee())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "上级用户不能为自己!");
        }

        User user2 = userService.detail(request.getUserReferee());
        if (user2.getId() == null) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "推荐人不能为空");
        }

        if (user2.getUserReferee() != null) {
            if (user2.getUserReferee().equals(user.getId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "推荐人不能互为上级用户!");
            }
        }

        if (user.getChannelGrade() > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您选择的用户已有等级,无法更改!");
        }

        //判断推荐人 是否是自己的下级，如果是，不允许调整
        //拿到自己的团队
        List<User> userList = new ArrayList<>();
        userService.queryAllInferiorNotInviteLevel(userList, user.getId());
        if (userList.stream().anyMatch(temp -> temp.getId().equals(request.getUserReferee()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您选择的上级属于自己团队中下级,无法更改!");
        }

        userList = new ArrayList<>();
        userService.queryAllInferiorNotInviteLevelSubChannel(userList, user.getId());
        user.setChannelId(user2.getChannelId());
        user.setUserReferee(request.getUserReferee());
        if (user2.getChannelGrade() > 0) {
            user.setChannelId(user2.getId());
        }

        userMapper.updateByPrimaryKeySelective(user);


        //批量更新用户的渠道标识
        userService.batchModifyChannelFlag(userList, user2.getChannelId());


    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject registerInstead(UserRegisterOssReq request, String ip) {
        User user = userService.registerInstead(request, ip);
        cUserInsert(user);

        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WechatAppLoginRes wxAppLogin(UserWxGzhLoginReq request, String ip) {
        log.info("微信登录入参:+{}", request);
        try {
            JSONObject jsonObject = WxAppUtil.getAppOauth2AccessKey(request.getCode(), wechatConfig.getAppid(), wechatConfig.getAppSecret());
            String openid = jsonObject.getString("openid");
            if (StringUtils.isBlank(openid)) {
                throw new BizException(EErrorCode.CORE00095);
            }
            String unionId = jsonObject.getString("unionid");
            String access_token = jsonObject.getString("access_token");
//            JSONObject appUserInfo = WxAppUtil.getAppUserInfo(access_token, openid);
//            WxMpUser wxUser = WxMpUser.fromJson(appUserInfo.toJSONString());
            WxMpOAuth2AccessToken accessToken = new WxMpOAuth2AccessToken();
            accessToken.setAccessToken(access_token);
            accessToken.setOpenId(openid);
            WxMpUser wxUser = wxMpService.oauth2getUserInfo(accessToken, null);
            return userWxLogin(request, openid, unionId, wxUser);
        } catch (Exception e) {
            log.error("微信app登录失败", e);
            throw new BizException(EErrorCode.CORE00090);
        }
    }


    @NotNull
    private WechatAppLoginRes userWxLogin(UserWxGzhLoginReq request, String openid, String unionId, WxMpUser wxUser) {
        User user;
        WechatAppLoginRes res = new WechatAppLoginRes();
        // 三种情况
        // 微信账号已存在，未绑手机号，先去绑手机号--0
        // 微信账号已存在，且绑定手机号，直接登录--1
        // 微信账号不存在，创建用户，跳转绑定手机号--0
        // 微信账号已存在，未绑手机号老数据，单独绑定手机号--2
        //根据 unionId 查询 user
        user = userMapper.getUserByUnionId(unionId);
        // 兼容老数据
        if (user == null) {
            user = userMapper.getUserByOpenid(openid);
            if (user != null) {
                Cuser cuser = cuserMapper.selectByUserId(user.getId());
                cuser.setUnionId(unionId);
                if (EUserLoginType.APP_LOGIN.getCode().equals(request.getLoginType())) {
                    cuser.setAppOpenid(openid);
                } else {
                    cuser.setOpenid(openid);
                }
                cuserMapper.updateByPrimaryKeySelective(cuser);
                // 老数据没有手机号单独绑手机号
                if (StringUtils.isBlank(user.getMobile())) {
                    res.setUserId(user.getId());
                    res.setBindMobileFlag("2");
                    return res;
                } else {
                    res.setBindMobileFlag(EBoolean.YES.getCode());
                }
            }
        } else {
            Cuser cuser = detailByUser(user.getId());
            if (EUserLoginType.APP_LOGIN.getCode().equals(request.getLoginType()) && StringUtils.isBlank(cuser.getAppOpenid())) {
                cuser.setAppOpenid(openid);
                cuserMapper.updateByPrimaryKeySelective(cuser);
            } else if (EUserLoginType.H5_LOGIN.getCode().equals(request.getLoginType()) && StringUtils.isBlank(cuser.getOpenid())) {
                cuser.setOpenid(openid);
                cuserMapper.updateByPrimaryKeySelective(cuser);
            }
            // 老数据没有手机号单独绑手机号
            if (StringUtils.isBlank(user.getMobile())) {
                res.setUserId(user.getId());
                res.setBindMobileFlag(EBoolean.NO.getCode());
                return res;
            } else {
                res.setBindMobileFlag(EBoolean.YES.getCode());
            }
        }

        if (user == null) {
//                JSONObject appUserInfo = WxAppUtil.getAppUserInfo(access_token, openid);
//                WxMpUser.fromJson(appUserInfo.toJSONString());
            user = new User();
            Date now = new Date();
            if (StringUtils.isNotBlank(request.getInviteCode())) {
                user.setUserReferee(userService.getUserAndCheckByInviteCode(request.getInviteCode()).getId());
            }
            user.setId(IdGeneratorUtil.generator());
            user.setKind(EUserKind.C.getCode());
            user.setLoginName(openid);
//                String nickname = new String(appUserInfo.getString("nickname").getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
//                user.setNickname(nickname);
//                user.setPhoto(appUserInfo.getString("headimgurl"));
//                user.setProvince(appUserInfo.getString("province"));
//                user.setCity(appUserInfo.getString("city"));
//                user.setSex(appUserInfo.getString("sex"));
            String nickname = EmojiParser.removeAllEmojis(wxUser.getNickname());
            // 昵称为空随机生成一个
            if (StringUtils.isBlank(nickname)) {
                nickname = user.getId().toString().substring(user.getId().toString().length() - 8, user.getId().toString().length());
            }
            user.setNickname(nickname);
            user.setPhoto(wxUser.getHeadImgUrl());
            user.setProvince(wxUser.getProvince());
            user.setCity(wxUser.getCity());
            user.setSex(wxUser.getSexDesc());
            user.setRegisterDatetime(now);
            user.setStatus(EUserStatus.TEMPORARILY.getCode());
            user.setCompanyId(-1L);
            // 登录密码
            String psw = CreatePwdUtil.getPsw(8);
            user.setLoginPwd(MD5Util.md5(psw));
            user.setLoginPwdStrength("1");
            userMapper.insertSelective(user);

            Cuser cuser = new Cuser();
            cuser.setUserId(user.getId());
            if (EUserLoginType.APP_LOGIN.getCode().equals(request.getLoginType())) {
                cuser.setAppOpenid(openid);
            } else {
                cuser.setOpenid(openid);
            }
            cuser.setUnionId(unionId);
            cuser.setWxNickname(nickname);
            cuser.setMemberFlag(EBoolean.NO.getCode());
            cuser.setVipFlag(EBoolean.NO.getCode());
            cuser.setLevel(EUserLevel.L0.getCode());
            cuser.setCreateDatetime(user.getRegisterDatetime());
            cuserMapper.insertSelective(cuser);

            // 添加默认权限
            userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());


            // 微信绑定记录
            JSONObject json = new JSONObject();
            json.put("unionId", cuser.getUnionId());
            json.put("openid", cuser.getOpenid());
            json.put("appOpenid", cuser.getAppOpenid());
            json.put("wxNickname", cuser.getNickname());
            String newData = json.toJSONString();
            userActionLogService.create(EUserActionLogType.USER_ACTION_LOG_TYPE_1.getCode(), user.getId(), null, newData);

            res.setUserId(user.getId());
            res.setBindMobileFlag(EBoolean.NO.getCode());
            return res;
//            userActionStatisticsService.rabbitMqSend(EUserActionStatisticsType.USER_ACTION_STATISTICS_TYPE_0.getCode(), user.getId());
        }


        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
//        result.put("nickName", user.getNickname());
//        result.put("photo", user.getPhoto());

        res.setToken(result.getString("token"));
        res.setExpireTime(result.getLong("expireTime"));
        res.setUserId(user.getId());
        res.setNickname(user.getNickname());
        res.setPhoto(user.getPhoto());

        delCuserRedis(user.getId());

        return res;
    }

    public static void main(String[] args) {
        JSONObject jsonObject = JWTUtil.generalToken("904935372541403136", 30 * 24 * 60 * 60 * 1000L);
        System.out.println(jsonObject.toJSONString());
    }

    /**
     * c端微信授权登录绑定手机号
     *
     * @param request
     * @param ip
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WechatAppLoginRes cWechatAppLogin(UserWechatAppLoginReq request, String ip) {
        userService.doCheckSmsCaptcha(request.getMobile(), request.getSmsCode(), "C_REG_WECHAT");

        User user = userService.selectUserByMobile(request.getMobile(), EUserKind.C.getCode());
        User detail = userService.detail(request.getUserId(), EUserKind.C);
        WechatAppLoginRes res = new WechatAppLoginRes();
        if (user == null) {
            if (null != detail) {
                User userModify = new User();
                userModify.setId(detail.getId());
                userModify.setMobile(request.getMobile());
                userModify.setLoginName(request.getMobile());
                String psw = CreatePwdUtil.getPsw(8);
                userModify.setLoginPwd(MD5Util.md5(psw));
                userModify.setUpdateDatetime(new Date());
                userModify.setStatus(EUserStatus.NORMAL.getCode());
                userService.modifyUser(userModify);
                user = new User();
                user.setId(detail.getId());
            } else {
                throw new BizException(EErrorCode.AUTH00011);
            }
        } else {
            Cuser cuser = detailByUser(user.getId());
            if (!EUserStatus.TEMPORARILY.getCode().equals(detail.getStatus())) {
                throw new BizException(EErrorCode.CORE00281);
            } else if (StringUtils.isBlank(cuser.getUnionId())
                    && StringUtils.isBlank(cuser.getOpenid())) {
                // 说明没绑过
                Cuser cuserNew = detailByUser(detail.getId());

                Cuser cuserModify = new Cuser();
                cuserModify.setId(cuser.getId());
                cuserModify.setUnionId(cuserNew.getUnionId());
                cuserModify.setOpenid(cuserNew.getOpenid());
                cuserModify.setAppOpenid(cuserNew.getAppOpenid());
                cuserModify.setWxNickname(cuserNew.getWxNickname());
                modify(cuserModify);

                // 微信绑定记录
                JSONObject json = new JSONObject();
                json.put("unionId", cuserNew.getUnionId());
                json.put("openid", cuserNew.getOpenid());
                json.put("appOpenid", cuserNew.getAppOpenid());
                json.put("wxNickname", cuserNew.getNickname());
                String newData = json.toJSONString();
                userActionLogService.create(EUserActionLogType.USER_ACTION_LOG_TYPE_1.getCode(), user.getId(), null, newData);


                // 删除原来老的账号
                userService.remove(detail.getId());
                removeByUser(detail.getId());
                // 删除权限
                userRoleService.removeByUserId(detail.getId());
            } else {
                throw new BizException(EErrorCode.CORE00281);
            }
        }

        res.setBindMobileFlag(EBoolean.YES.getCode());

        log.info("微信登录，用户序号:" + user.getId());
        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60L);
        res.setUserId(user.getId());
        res.setExpireTime(result.getLong("expireTime"));
        res.setToken(result.getString("token"));
        res.setNickname(user.getNickname());
        res.setPhoto(user.getPhoto());


        return res;
    }


    private void removeByUser(Long userId) {
        cuserMapper.deleteByUser(userId);
    }


    @Override
    public void delCuserRedis(Long userId) {
        // 删除缓存
        String userCacheKey = String.format(RedisKeyList.MSG_USER_INFO_KEY, userId.toString());
        redisUtil.del(userCacheKey);

        String userKindKey = String.format(RedisKeyList.MSG_USER_INFO_KIND_KEY, userId.toString(), EUserKind.C.getCode());
        redisUtil.del(userKindKey);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindWx(UserBindWxGzhLoginReq request, User operator) {
//        log.info("code:{}",request.getCode());
//        if (true){
//            return;
//        }
        if (99L == operator.getCompanyId()) {
            throw new BizException(EErrorCode.CORE00579);
        }

        if (99L == operator.getCompanyId()) {
            throw new BizException(EErrorCode.CORE00579);
        }

        if (StringUtils.isBlank(operator.getMobile())) {
            throw new BizException(EErrorCode.CORE00096);
        }
        Cuser cuser = detailByUserId(operator.getId());
        if (StringUtils.isNotBlank(cuser.getUnionId())) {
            throw new BizException(EErrorCode.CORE00097);
        }

        JSONObject jsonObject = WxAppUtil.getAppOauth2AccessKey(request.getCode(), wechatConfig.getAppid(), wechatConfig.getAppSecret());
        String openid = jsonObject.getString("openid");
        if (StringUtils.isBlank(openid)) {
            throw new BizException(EErrorCode.CORE00095);
        }
        String unionId = jsonObject.getString("unionid");

        // 老用户
        if (StringUtils.isNotBlank(cuser.getOpenid()) && openid.equals(cuser.getOpenid())) {
            Cuser cuserModify = new Cuser();
            cuserModify.setId(cuser.getId());
            cuserModify.setUnionId(unionId);
            cuserMapper.updateByPrimaryKeySelective(cuserModify);
            return;
        }

        User user = userMapper.getUserByUnionId(unionId);

        if (null != user) {
            if (EUserStatus.TEMPORARILY.getCode().equals(user.getStatus())) {
                // 删除原来老的账号
                userService.remove(user.getId());
                removeByUser(user.getId());
                // 删除权限
                userRoleService.removeByUserId(user.getId());
                delCuserRedis(user.getId());
            } else {
                throw new BizException(EErrorCode.CORE00098);
            }
        }

        // 获取用户微信信息
        String access_token = jsonObject.getString("access_token");
        WxMpOAuth2AccessToken accessToken = new WxMpOAuth2AccessToken();
        accessToken.setAccessToken(access_token);
        accessToken.setOpenId(openid);

        String wxNickname = "";
        try {
            WxMpUser wxUser = wxMpService.oauth2getUserInfo(accessToken, null);
            wxNickname = wxUser.getNickname();
            if (StringUtils.isNotBlank(wxNickname)) {
                wxNickname = EmojiParser.removeAllEmojis(wxNickname);
            }
        } catch (WxErrorException e) {
            log.error("获取用户微信信息失败{}", e);
        }

        Cuser cuserModify = new Cuser();
        cuserModify.setId(cuser.getId());
        cuserModify.setUnionId(unionId);
        cuserModify.setAppOpenid(openid);
        cuserModify.setWxNickname(wxNickname);
        cuserMapper.updateByPrimaryKeySelective(cuserModify);

        JSONObject json = new JSONObject();
        json.put("unionId", unionId);
        json.put("openid", cuser.getOpenid());
        json.put("appOpenid", openid);
        json.put("wxNickname", wxNickname);
        String newData = json.toJSONString();
        delCuserRedis(user.getId());
        userActionLogService.create(EUserActionLogType.USER_ACTION_LOG_TYPE_1.getCode(), operator.getId(), null, newData);
    }


    @Override
    public void unbindWx(User operator) {
        if (StringUtils.isBlank(operator.getMobile())) {
            throw new BizException(EErrorCode.CORE00096);
        }
        Cuser cuser = detailByUserId(operator.getId());
        if (StringUtils.isBlank(cuser.getUnionId()) && StringUtils.isBlank(cuser.getOpenid())) {
            throw new BizException(EErrorCode.CORE00555);
        }

        cuserMapper.updateUntieWx(operator.getId());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionId", cuser.getUnionId());
        jsonObject.put("openid", cuser.getOpenid());
        jsonObject.put("appOpenid", cuser.getAppOpenid());
        jsonObject.put("wxNickname", cuser.getWxNickname());
        String oldData = jsonObject.toJSONString();
        delCuserRedis(operator.getId());
        userActionLogService.create(EUserActionLogType.USER_ACTION_LOG_TYPE_0.getCode(), operator.getId(), oldData, null);
    }



    @Override
    public void insert(Cuser cuser) {
        cuserMapper.insertSelective(cuser);
    }

}

