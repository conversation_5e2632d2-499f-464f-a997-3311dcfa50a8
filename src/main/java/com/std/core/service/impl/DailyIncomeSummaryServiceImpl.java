package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.DailyIncomeSummaryMapper;
import com.std.core.pojo.domain.DailyIncomeSummary;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.DailyIncomeSummaryDetailRes;
import com.std.core.pojo.response.DailyIncomeSummaryListRes;
import com.std.core.pojo.response.DailyIncomeSummaryPageRes;
import com.std.core.service.IDailyIncomeSummaryService;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.util.DateRangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 每日收益ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2025-05-29 19:15
 */
@Service
@Slf4j
public class DailyIncomeSummaryServiceImpl implements IDailyIncomeSummaryService {

    @Resource
    private DailyIncomeSummaryMapper dailyIncomeSummaryMapper;

    /**
     * 新增每日收益
     *
     * @param req      新增每日收益入参
     * @param operator 操作人
     */
    @Override
    public void create(DailyIncomeSummaryCreateReq req, User operator) {
        DailyIncomeSummary dailyIncomeSummary = EntityUtils.copyData(req, DailyIncomeSummary.class);
        dailyIncomeSummaryMapper.insertSelective(dailyIncomeSummary);
    }

    @Override
    public void create(String incomeDate, BigDecimal activityIncome, BigDecimal goodsIncome) {
        DailyIncomeSummary dailyIncomeSummary = detailByDate(incomeDate);
        if (null != dailyIncomeSummary) {
            dailyIncomeSummary.setActivityAmount(activityIncome);
            dailyIncomeSummary.setGoodsAmount(goodsIncome);
            dailyIncomeSummaryMapper.updateByPrimaryKeySelective(dailyIncomeSummary);
        }
    }

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    public static String formatIncomeDate(Date date) {
        return DATE_FORMAT.format(date);
    }

    /**
     * 增加活动收益
     *
     * @param activityAmount
     * @param incomeDate
     */
    @Override
    public void activityIncomeSummary(BigDecimal activityAmount, Date incomeDate) {
        if (BigDecimal.ZERO.compareTo(activityAmount) == 0) {
            return;
        }
        try {
            String formatIncomeDate = formatIncomeDate(incomeDate);
            DailyIncomeSummary dailyIncomeSummary = detailByDate(formatIncomeDate);

            dailyIncomeSummaryMapper.updateActivityIncomeSummary(formatIncomeDate, activityAmount);
        } catch (Exception e) {
            log.error("增加每日统计失败，参数{},原因：", incomeDate + "," + activityAmount, e.getMessage());
        }

    }

    @Override
    public void goodsIncomeSummary(BigDecimal goodsAmount, Date incomeDate) {
        if (BigDecimal.ZERO.compareTo(goodsAmount) == 0) {
            return;
        }
        try {
            String formatIncomeDate = formatIncomeDate(incomeDate);
            DailyIncomeSummary dailyIncomeSummary = detailByDate(formatIncomeDate);
            dailyIncomeSummaryMapper.updateGoodsIncomeSummary(formatIncomeDate, goodsAmount);
        } catch (Exception e) {
            log.error("增加每日统计失败，参数{},原因：", incomeDate + "," + goodsAmount, e.getMessage());
        }
    }

    @Override
    public DailyIncomeSummary detailByDate(String incomeDate) {
        DailyIncomeSummary dailyIncomeSummary = dailyIncomeSummaryMapper.selectByDate(incomeDate);
        if (null == dailyIncomeSummary) {
            dailyIncomeSummary = new DailyIncomeSummary();
            dailyIncomeSummary.setOrderCount(0);
            dailyIncomeSummary.setGoodsAmount(BigDecimal.ZERO);
            dailyIncomeSummary.setActivityAmount(BigDecimal.ZERO);
            dailyIncomeSummary.setIncomeDate(DateUtil.strToDate(incomeDate, "yyyy-MM-dd"));
            dailyIncomeSummary.setCreateDatetime(new Date());
            dailyIncomeSummaryMapper.insertSelective(dailyIncomeSummary);
        }
        return dailyIncomeSummary;
    }

    /**
     * 删除每日收益
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        dailyIncomeSummaryMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改每日收益
     *
     * @param req      修改每日收益入参
     * @param operator 操作人
     */
    @Override
    public void modify(DailyIncomeSummaryModifyReq req, User operator) {
        DailyIncomeSummary dailyIncomeSummary = EntityUtils.copyData(req, DailyIncomeSummary.class);
        dailyIncomeSummaryMapper.updateByPrimaryKeySelective(dailyIncomeSummary);
    }

    /**
     * 详情查询每日收益
     *
     * @param id 主键ID
     * @return 每日收益对象
     */
    @Override
    public DailyIncomeSummary detail(Long id) {
        DailyIncomeSummary dailyIncomeSummary = dailyIncomeSummaryMapper.selectByPrimaryKey(id);
        if (null == dailyIncomeSummary) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return dailyIncomeSummary;
    }

    /**
     * 分页查询每日收益
     *
     * @param req 分页查询每日收益入参
     * @return 分页每日收益对象
     */
    @Override
    public List<DailyIncomeSummary> page(DailyIncomeSummaryPageReq req) {
        DailyIncomeSummary condition = EntityUtils.copyData(req, DailyIncomeSummary.class);

        List<DailyIncomeSummary> dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByCondition(condition);

        return dailyIncomeSummaryList;
    }

    @Override
    public DailyIncomeSummaryDetailRes incomeTotal(DailyIncomeSummaryTotalReq request) {

        return dailyIncomeSummaryMapper.selectIncomeTotal(request.getStartTime(), request.getEndTime());
    }

    /**
     * 列表查询每日收益
     *
     * @param req 列表查询每日收益入参
     * @return 列表每日收益对象
     */
    @Override
    public List<DailyIncomeSummaryListRes> list(DailyIncomeSummaryListReq req) {

        Date startDate = new Date();
        Date endDate = new Date();
        if (StringUtils.isNotBlank(req.getStartTime())) {
            try {
                startDate = DateUtil.strToDate(req.getStartTime(), "yyyy-MM-dd");
            } catch (Exception e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "时间入参错误");
            }
        }
        if (StringUtils.isNotBlank(req.getEndTime())) {
            try {
                endDate = DateUtil.strToDate(req.getEndTime(), "yyyy-MM-dd");
            } catch (Exception e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "时间入参错误");
            }
        }
        if ("day".equals(req.getPeriod())) {
            DateRangeUtil.DateRange statisticalRange = DateRangeUtil.getStatisticalRange(new Date(), DateRangeUtil.RangeType.DAILY);
            startDate = statisticalRange.getStartDate();
            endDate = statisticalRange.getEndDate();
        } else if ("week".equals(req.getPeriod())) {
            DateRangeUtil.DateRange statisticalRange = DateRangeUtil.getStatisticalRange(new Date(), DateRangeUtil.RangeType.WEEKLY);
            startDate = statisticalRange.getStartDate();
            endDate = statisticalRange.getEndDate();
        } else if ("month".equals(req.getPeriod())) {
            DateRangeUtil.DateRange statisticalRange = DateRangeUtil.getStatisticalRange(new Date(), DateRangeUtil.RangeType.MONTHLY);
            startDate = statisticalRange.getStartDate();
            endDate = statisticalRange.getEndDate();
        }

        List<DailyIncomeSummaryListRes> dailyIncomeSummaryList = new ArrayList<>();
        if ("week".equals(req.getPeriod())) {
            dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByWeek(startDate, endDate, req.getType().equals(EBoolean.YES.getCode()) ? "t.goods_amount" : "t.activity_amount");
        } else if ("month".equals(req.getPeriod())) {
            dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByMonth(startDate, endDate, req.getType().equals(EBoolean.YES.getCode()) ? "t.goods_amount" : "t.activity_amount");
        } else {
            dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByDay(startDate, endDate, req.getType().equals(EBoolean.YES.getCode()) ? "t.goods_amount" : "t.activity_amount");
        }

        return dailyIncomeSummaryList;
    }

    /**
     * 前端详情查询每日收益
     *
     * @param id 主键ID
     * @return 每日收益对象
     */
    @Override
    public DailyIncomeSummaryDetailRes detailFront(Long id) {
        DailyIncomeSummaryDetailRes res = new DailyIncomeSummaryDetailRes();

        DailyIncomeSummary dailyIncomeSummary = dailyIncomeSummaryMapper.selectByPrimaryKey(id);
        if (null == dailyIncomeSummary) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(dailyIncomeSummary, res);

        return res;
    }

    /**
     * 前端分页查询每日收益
     *
     * @param req 前端分页查询每日收益入参
     * @return 分页每日收益对象
     */
    @Override
    public List<DailyIncomeSummaryPageRes> pageFront(DailyIncomeSummaryPageFrontReq req) {
        DailyIncomeSummary condition = EntityUtils.copyData(req, DailyIncomeSummary.class);
        List<DailyIncomeSummary> dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByCondition(condition);

        List<DailyIncomeSummaryPageRes> resList = dailyIncomeSummaryList.stream().map((entity) -> {
            DailyIncomeSummaryPageRes res = new DailyIncomeSummaryPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(dailyIncomeSummaryList, resList);
    }

    /**
     * 前端列表查询每日收益
     *
     * @param req 前端列表查询每日收益入参
     * @return 列表每日收益对象
     */
    @Override
    public List<DailyIncomeSummaryListRes> listFront(DailyIncomeSummaryListFrontReq req) {
        DailyIncomeSummary condition = EntityUtils.copyData(req, DailyIncomeSummary.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), DailyIncomeSummary.class));

        List<DailyIncomeSummary> dailyIncomeSummaryList = dailyIncomeSummaryMapper.selectByCondition(condition);

        List<DailyIncomeSummaryListRes> resList = dailyIncomeSummaryList.stream().map((entity) -> {
            DailyIncomeSummaryListRes res = new DailyIncomeSummaryListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}