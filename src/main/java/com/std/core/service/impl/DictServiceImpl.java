package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EDictType;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUserLogType;
import com.std.core.mapper.DictMapper;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.service.IDictService;
import com.std.core.service.IUserLogService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 数据字典ServiceImpl
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 16:49
 */
@Service
public class DictServiceImpl implements IDictService {

    @Resource
    private DictMapper dictMapper;

    @Resource
    private IUserLogService userLogService;

    @Override
    public void create(DictCreateReq req, User operateUser) {
        Dict data = EntityUtils.copyData(req, Dict.class);
        data.setType(EDictType.CHILD.getCode());
        data.setUpdater(operateUser.getLoginName());
        data.setUpdateDatetime(new Date());

        dictMapper.insertSelective(data);
    }

    @Override
    public void modify(DictModifyReq req, User operateUser, String ip) {
        Dict dict = detail(req.getId());
        Dict data = EntityUtils.copyData(req, Dict.class);
        data.setUpdater(operateUser.getLoginName());
        data.setUpdateDatetime(new Date());

        dictMapper.updateByPrimaryKeySelective(data);

        String content = "内容修改：修改数据字典：" + dict.getParentKey();
        userLogService.create(operateUser, EUserLogType.DICT.getCode(), EUserLogType.DICT.getValue(), ip, content);
    }

    @Override
    public void remove(Integer id, User operateUser) {
        dictMapper.deleteByPrimaryKey(id);
    }

    /**
     * 详情查询数据字典
     *
     * @param id 主键ID
     * @return 数据字典对象
     */
    @Override
    public Dict detail(Integer id) {
        Dict dict = dictMapper.selectByPrimaryKey(id);
        if (null == dict) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return dict;
    }

    /**
     * 分页查询数据字典
     *
     * @param req 分页查询数据字典入参
     * @return 分页数据字典对象
     */
    @Override
    public List<Dict> page(DictPageReq req) {
        Dict condition = EntityUtils.copyData(req, Dict.class);

        return dictMapper.selectByCondition(condition);
    }

    /**
     * 列表查询数据字典
     *
     * @param req 列表查询数据字典入参
     * @return 列表数据字典对象
     */
    @Override
    public List<Dict> list(DictListReq req) {
        Dict condition = EntityUtils.copyData(req, Dict.class);
        if (StringUtils.isBlank(req.getSort())) {
            req.setSort("id");
        }
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Dict.class));

        return dictMapper.selectByCondition(condition);
    }
   @Override
    public List<Dict> list(Dict req) {
        return dictMapper.selectByCondition(req);
    }

    @Override
    public List<Dict> publicList(DictPublicListReq req) {
        Dict condition = EntityUtils.copyData(req, Dict.class);
        if (StringUtils.isBlank(req.getSort())) {
            req.setSort("id");
        }
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Dict.class));

        return dictMapper.selectByCondition(condition);
    }
}
