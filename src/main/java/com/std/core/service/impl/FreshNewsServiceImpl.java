package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EFreshNewsStatus;
import com.std.core.enums.EGoodsCategoryStatus;
import com.std.core.mapper.FreshNewsMapper;
import com.std.core.pojo.domain.FreshNews;
import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.Sort;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.FreshNewsDetailRes;
import com.std.core.pojo.response.FreshNewsListRes;
import com.std.core.pojo.response.FreshNewsPageRes;
import com.std.core.service.IFreshNewsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* 新鲜事ServiceImpl
*
* <AUTHOR> mjd
* @since : 2024-12-25 22:43
*/
@Service
public class FreshNewsServiceImpl implements IFreshNewsService {

    @Resource
    private FreshNewsMapper freshNewsMapper;

    /**
     * 新增新鲜事
     *
     * @param req 新增新鲜事入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FreshNewsCreateReq req, User operator) {
        FreshNews freshNews = EntityUtils.copyData(req, FreshNews.class);
        freshNews.setStatus(EFreshNewsStatus.FRESH_NEWS_STATUS_0.getCode());
        freshNews.setCreater(operator.getId());
        freshNews.setCreaterName(operator.getLoginName());
        freshNews.setCreateDatetime(new Date());
        freshNewsMapper.insertSelective(freshNews);
    }

    /**
     * 删除新鲜事
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        FreshNews freshNews = freshNewsMapper.selectByPrimaryKey(id);
        freshNews.setStatus(EFreshNewsStatus.FRESH_NEWS_STATUS_3.getCode());
        freshNewsMapper.updateByPrimaryKeySelective(freshNews);
    }

    /**
     * 修改新鲜事
     *
     * @param req 修改新鲜事入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(FreshNewsModifyReq req, User operator) {
        FreshNews freshNews = detail(req.getId());
        freshNews = EntityUtils.copyData(req, FreshNews.class);
        freshNews.setUpdater(operator.getId());
        freshNews.setUpdaterName(operator.getUpdaterName());
        freshNews.setUpdateDatetime(new Date());
        freshNewsMapper.updateByPrimaryKeySelective(freshNews);
    }

    /**
     * 详情查询新鲜事
     *
     * @param id 主键ID
     * @return 新鲜事对象
     */
    @Override
    public FreshNews detail(Long id) {
        FreshNews freshNews = freshNewsMapper.selectByPrimaryKey(id);
        if (null == freshNews) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return freshNews;
    }

    /**
     * 分页查询新鲜事
     *
     * @param req 分页查询新鲜事入参
     * @return 分页新鲜事对象
     */
    @Override
    public List<FreshNews> page(FreshNewsPageReq req) {
        FreshNews condition = EntityUtils.copyData(req, FreshNews.class);
        List<String>noStatusList = new ArrayList<>();
        noStatusList.add(EFreshNewsStatus.FRESH_NEWS_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        List<FreshNews> freshNewsList = freshNewsMapper.selectByCondition(condition);

        return freshNewsList;
    }

    /**
     * 列表查询新鲜事
     *
     * @param req 列表查询新鲜事入参
     * @return 列表新鲜事对象
     */
    @Override
    public List<FreshNews> list(FreshNewsListReq req) {
        FreshNews condition = EntityUtils.copyData(req, FreshNews.class);
        condition.setOrderBy("t.order_no desc");
        List<String>noStatusList = new ArrayList<>();
        noStatusList.add(EFreshNewsStatus.FRESH_NEWS_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        List<FreshNews> freshNewsList = freshNewsMapper.selectByCondition(condition);

        return freshNewsList;
    }

    /**
     * 前端详情查询新鲜事
     *
     * @param id 主键ID
     * @return 新鲜事对象
     */
    @Override
    public FreshNewsDetailRes detailFront(Long id) {
        FreshNewsDetailRes res = new FreshNewsDetailRes();

        FreshNews freshNews = freshNewsMapper.selectByPrimaryKey(id);
        if (null == freshNews) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(freshNews, res);

        return res;
    }

    /**
     * 前端分页查询新鲜事
     *
     * @param req 前端分页查询新鲜事入参
     * @return 分页新鲜事对象
     */
    @Override
    public List< FreshNewsPageRes> pageFront(FreshNewsPageFrontReq req) {
        FreshNews condition = EntityUtils.copyData(req, FreshNews.class);
        condition.setOrderBy("t.order_no desc,t.create_datetime desc");
        if(StringUtils.isBlank(req.getNewsType())){
            condition.setNewsType(EBoolean.NO.getCode());
        }
        condition.setStatus(EFreshNewsStatus.FRESH_NEWS_STATUS_1.getCode());
        List<FreshNews> freshNewsList = freshNewsMapper.selectByCondition(condition);

        List< FreshNewsPageRes> resList = freshNewsList.stream().map((entity) -> {
            FreshNewsPageRes res = new FreshNewsPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(freshNewsList, resList);
    }

    /**
     * 前端列表查询新鲜事
     *
     * @param req 前端列表查询新鲜事入参
     * @return 列表新鲜事对象
     */
    @Override
    public List< FreshNewsListRes> listFront(FreshNewsListFrontReq req) {
        FreshNews condition = EntityUtils.copyData(req, FreshNews.class);
        condition.setOrderBy("t.order_no desc,t.create_datetime desc");
        List<FreshNews> freshNewsList = freshNewsMapper.selectByCondition(condition);

        List< FreshNewsListRes> resList = freshNewsList.stream().map((entity) -> {
            FreshNewsListRes res = new FreshNewsListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(BatchUpDownReq request, User operator) {
        for (Long freshNewsIds : request.getIdList()) {
            FreshNews freshNews = detail(freshNewsIds);
            freshNews.setStatus(request.getStatus());
            if(EFreshNewsStatus.FRESH_NEWS_STATUS_1.getCode().equals(request.getStatus())){
                freshNews.setUpDatetime(new Date());
            }
            freshNews.setUpdater(operator.getId());
            freshNews.setUpdaterName(operator.getLoginName());
            freshNews.setUpdateDatetime(new Date());
            freshNewsMapper.updateByPrimaryKeySelective(freshNews);
        }
    }

    @Override
    public void sort(SortReq request) {
        List<Sort> sortList = request.getSortList();
        if (sortList == null || sortList.isEmpty()) {
            return;
        }

        for (Sort sort : sortList) {
            if (sort.getId() != null && sort.getOrderNo() != null) {
                FreshNews freshNews = new FreshNews();
                freshNews.setId(sort.getId());
                freshNews.setOrderNo(sort.getOrderNo());
                freshNewsMapper.updateByPrimaryKeySelective(freshNews); // 使用已有的更新方法
            }
        }
    }

}