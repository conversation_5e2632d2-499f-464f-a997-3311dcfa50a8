package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EGoodsCategoryStatus;
import com.std.core.enums.EGoodsStatus;
import com.std.core.mapper.GoodsCategoryMapper;
import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.GoodsCategory;
import com.std.core.pojo.domain.Sort;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsCategoryDetailRes;
import com.std.core.pojo.response.GoodsCategoryListRes;
import com.std.core.pojo.response.GoodsCategoryPageRes;
import com.std.core.service.IGoodsCategoryService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.service.IGoodsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* 商品类型ServiceImpl
*
* <AUTHOR> mjd
* @since : 2024-12-26 15:55
*/
@Service
public class GoodsCategoryServiceImpl implements IGoodsCategoryService {

    @Resource
    private GoodsCategoryMapper goodsCategoryMapper;

    @Resource
    private IGoodsService goodsService;

    /**
     * 新增商品类型
     *
     * @param req 新增商品类型入参
     * @param operator 操作人
     */
    @Override
    public void create(GoodsCategoryCreateReq req, User operator) {
        GoodsCategory goodsCategory = new GoodsCategory();
        //小类
        if(EBoolean.YES.getCode().equals(req.getType())){
            if(StringUtils.isBlank(req.getParentId())){
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"大类类别不能为空");
            }
            goodsCategory = EntityUtils.copyData(req, GoodsCategory.class);

        }
        //大类
        else if(EBoolean.NO.getCode().equals(req.getType())){
            goodsCategory = EntityUtils.copyData(req, GoodsCategory.class);
            goodsCategory.setParentId(null);
        }

        goodsCategory.setStatus(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_0.getCode());
        goodsCategoryMapper.insertSelective(goodsCategory);
    }

    /**
     * 删除商品类型
     *
     * @param id 主键ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id) {
        GoodsCategory goodsCategory = goodsCategoryMapper.selectByPrimaryKey(id);
        if (EBoolean.NO.getCode().equals(goodsCategory.getType())){
            GoodsCategory condition = new GoodsCategory();
            condition.setParentId(String.valueOf(goodsCategory.getId()));
            List<GoodsCategory> goodsCategories = goodsCategoryMapper.selectByCondition(condition);
            for (GoodsCategory category : goodsCategories) {
                category.setStatus(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_3.getCode());
                Goods goods = new Goods();
                goods.setTypeId(goodsCategory.getId());
                goods.setStatus(EGoodsStatus.GOODS_STATUS_1.getCode());
                List<Goods> list = goodsService.list(goods);
                if(!list.isEmpty()){
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"该类别下有商品，不能删除");
                }
                for (Goods goods1 : list) {
                    goodsService.modify(goods1);
                }
                goodsCategoryMapper.updateByPrimaryKeySelective(category);
            }
        }
        goodsCategory.setStatus(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_3.getCode());
        Goods goods = new Goods();
        goods.setTypeId(goodsCategory.getId());
        goods.setStatus(EGoodsStatus.GOODS_STATUS_1.getCode());
        List<Goods> list = goodsService.list(goods);
        if(!list.isEmpty()){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"该类别下有商品，不能删除");
        }
        for (Goods goods1 : list) {
            goodsService.modify(goods1);
        }
        goodsCategoryMapper.updateByPrimaryKeySelective(goodsCategory);
    }

    /**
     * 修改商品类型
     *
     * @param req 修改商品类型入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsCategoryModifyReq req, User operator) {
        GoodsCategory goodsCategory = detail(req.getId());
        goodsCategory.setUpdater(operator.getId());
        goodsCategory.setUpdaterName(operator.getLoginName());
        goodsCategory.setUpdateDatetime(new Date());
        goodsCategory.setName(req.getName());
        if(goodsCategory.getType().equals(EBoolean.YES.getCode())) {
            goodsCategory.setParentId(String.valueOf(req.getParentId()));
        }
        goodsCategory.setOrderNo(req.getOrderNo());
        goodsCategoryMapper.updateByPrimaryKeySelective(goodsCategory);
    }

    @Override
    public void batchUpDown(BatchUpDownReq req, User operator) {
        Date date = new Date();
        for (Long goodsId : req.getIdList()) {
            GoodsCategory goodsCategory = detail(goodsId);
            goodsCategory.setStatus(req.getStatus());
            goodsCategory.setUpdater(operator.getId());
            goodsCategory.setUpdaterName(operator.getLoginName());
            goodsCategory.setUpdateDatetime(date);
            goodsCategoryMapper.updateByPrimaryKeySelective(goodsCategory);
            if(goodsCategory.getType().equals(EBoolean.NO.getCode())&&EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_2.getCode().equals(req.getStatus())){
                GoodsCategory condition = new GoodsCategory();
                condition.setParentId(String.valueOf(goodsCategory.getId()));
                condition.setType(EBoolean.YES.getCode());
                List<GoodsCategory> list = list(condition);
                for (GoodsCategory category : list) {
                    category.setStatus(req.getStatus());
                    category.setUpdater(operator.getId());
                    category.setUpdaterName(operator.getLoginName());
                    category.setUpdateDatetime(date);
                    goodsCategoryMapper.updateByPrimaryKeySelective(category);
                    Goods goods = new Goods();
                    goods.setTypeId(category.getId());
                    List<Goods> goodsList = goodsService.list(goods);
                    for (Goods goodsItem : goodsList) {
                        goodsItem.setStatus(EGoodsStatus.GOODS_STATUS_2.getCode());
                        goodsItem.setUpdater(operator.getId());
                        goodsItem.setUpdateDatetime(date);
                        goodsItem.setUpdaterName(operator.getLoginName());
                    }
                }
            }

        }
    }

    /**
     * 详情查询商品类型
     *
     * @param id 主键ID
     * @return 商品类型对象
     */
    @Override
    public GoodsCategory detail(Long id) {
        GoodsCategory goodsCategory = goodsCategoryMapper.selectByPrimaryKey(id);
        if (null == goodsCategory) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        if(StringUtils.isNotBlank(goodsCategory.getParentId())) {
            GoodsCategory category = detail(Long.valueOf(goodsCategory.getParentId()));
            goodsCategory.setParentName(category.getName());
        }

        return goodsCategory;
    }

    /**
     * 分页查询商品类型
     *
     * @param req 分页查询商品类型入参
     * @return 分页商品类型对象
     */
    @Override
    public List<GoodsCategory> page(GoodsCategoryPageReq req) {
        GoodsCategory condition = EntityUtils.copyData(req, GoodsCategory.class);
        List<String>noStatusList = new ArrayList<>();
        noStatusList.add(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        List<GoodsCategory> goodsCategoryList = goodsCategoryMapper.selectByCondition(condition);
        for (GoodsCategory goodsCategory : goodsCategoryList) {
           if(StringUtils.isNotBlank(goodsCategory.getParentId())) {
               GoodsCategory category = detail(Long.valueOf(goodsCategory.getParentId()));
               goodsCategory.setParentName(category.getName());
           }
        }

        return goodsCategoryList;
    }

    /**
     * 列表查询商品类型
     *
     * @param req 列表查询商品类型入参
     * @return 列表商品类型对象
     */
    @Override
    public List<GoodsCategory> list(GoodsCategoryListReq req) {
        GoodsCategory condition = EntityUtils.copyData(req, GoodsCategory.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsCategory.class));
        List<String>noStatusList = new ArrayList<>();
        noStatusList.add(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        condition.setOrderBy("t.order_no desc");
        List<GoodsCategory> goodsCategoryList = goodsCategoryMapper.selectByCondition(condition);
        for (GoodsCategory goodsCategory : goodsCategoryList) {
            if(StringUtils.isNotBlank(goodsCategory.getParentId())) {
                GoodsCategory category = detail(Long.valueOf(goodsCategory.getParentId()));
                goodsCategory.setParentName(category.getName());
            }
        }
        return goodsCategoryList;
    }

    @Override
    public List<GoodsCategory> list(GoodsCategory req) {
        List<GoodsCategory> goodsCategoryList = goodsCategoryMapper.selectByCondition(req);
        return goodsCategoryList;
    }

    /**
     * 前端详情查询商品类型
     *
     * @param id 主键ID
     * @return 商品类型对象
     */
    @Override
    public GoodsCategoryDetailRes detailFront(Long id) {
        GoodsCategoryDetailRes res = new GoodsCategoryDetailRes();

        GoodsCategory goodsCategory = goodsCategoryMapper.selectByPrimaryKey(id);
        if (null == goodsCategory) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goodsCategory, res);

        return res;
    }

    /**
     * 前端分页查询商品类型
     *
     * @param req 前端分页查询商品类型入参
     * @return 分页商品类型对象
     */
    @Override
    public List< GoodsCategoryPageRes> pageFront(GoodsCategoryPageFrontReq req) {
        GoodsCategory condition = EntityUtils.copyData(req, GoodsCategory.class);
        List<GoodsCategory> goodsCategoryList = goodsCategoryMapper.selectByCondition(condition);

        List< GoodsCategoryPageRes> resList = goodsCategoryList.stream().map((entity) -> {
            GoodsCategoryPageRes res = new GoodsCategoryPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsCategoryList, resList);
    }

    /**
     * 前端列表查询商品类型
     *
     * @param req 前端列表查询商品类型入参
     * @return 列表商品类型对象
     */
    @Override
    public List< GoodsCategoryListRes> listFront(GoodsCategoryListFrontReq req) {
        GoodsCategory condition = EntityUtils.copyData(req, GoodsCategory.class);
        condition.setOrderBy("t.order_no desc");
        condition.setStatus(EGoodsCategoryStatus.GOODS_CATEGORY_STATUS_1.getCode());
        List<GoodsCategory> goodsCategoryList = goodsCategoryMapper.selectByCondition(condition);

        List< GoodsCategoryListRes> resList = goodsCategoryList.stream().map((entity) -> {
            GoodsCategoryListRes res = new GoodsCategoryListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void sort(SortReq request) {
        List<Sort> sortList = request.getSortList();
        if (sortList == null || sortList.isEmpty()) {
            return;
        }

        for (Sort sort : sortList) {
            if (sort.getId() != null && sort.getOrderNo() != null) {
                GoodsCategory goods = new GoodsCategory();
                goods.setId(sort.getId());
                goods.setOrderNo(sort.getOrderNo());
                goodsCategoryMapper.updateByPrimaryKeySelective(goods); // 使用已有的更新方法
            }
        }
    }

}