package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EGoodsNormsStatus;
import com.std.core.enums.EGoodsStatus;
import com.std.core.mapper.GoodsNormsMapper;
import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.GoodsNorms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsNormsDetailRes;
import com.std.core.pojo.response.GoodsNormsListRes;
import com.std.core.pojo.response.GoodsNormsPageRes;
import com.std.core.service.IGoodsNormsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.service.IGoodsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
* 商品规格ServiceImpl
*
* <AUTHOR> mjd
* @since : 2024-12-26 20:59
*/
@Service
public class GoodsNormsServiceImpl implements IGoodsNormsService {

    @Resource
    private GoodsNormsMapper goodsNormsMapper;

    @Resource
    private IGoodsService goodsService;

    /**
     * 新增商品规格
     *
     * @param req 新增商品规格入参
     * @param operator 操作人
     */
    @Override
    public void create(GoodsNormsCreateReq req, User operator) {
        GoodsNorms goodsNorms = EntityUtils.copyData(req, GoodsNorms.class);
        if(null==req.getGoodId()){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"商品id不能为空");
        }
        goodsNorms.setInventory(req.getNumber());
        goodsNorms.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_0.getCode());
        goodsNorms.setCreater(operator.getId());
        goodsNorms.setCreateDatetime(new Date());
        goodsNorms.setCreaterName(operator.getLoginName());
        goodsNormsMapper.insertSelective(goodsNorms);
    }




    @Override
    public void create(GoodsNorms req) {
        goodsNormsMapper.insertSelective(req);
    }

    /**
     * 删除商品规格
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id, User operator) {
        GoodsNorms goodsNorms = detail(id);
        goodsNorms.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_3.getCode());
        goodsNorms.setUpdater(operator.getId());
        goodsNorms.setUpdateDatetime(new Date());
        goodsNorms.setUpdaterName(operator.getLoginName());
        goodsNormsMapper.updateByPrimaryKeySelective(goodsNorms);
    }

    /**
     * 修改商品规格
     *
     * @param req 修改商品规格入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsNormsModifyReq req, User operator) {
        GoodsNorms goodsNorms = detail(req.getId());
        if(!EGoodsNormsStatus.GOODS_NORMS_STATUS_0.getCode().equals(goodsNorms.getStatus())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"只有待上架的规格可以修改");
        }
        goodsNorms = EntityUtils.copyData(req, GoodsNorms.class);
        goodsNorms.setUpdater(operator.getId());
        goodsNorms.setUpdateDatetime(new Date());
        goodsNorms.setUpdaterName(operator.getLoginName());
        goodsNormsMapper.updateByPrimaryKeySelective(goodsNorms);
    }


    @Override
    public void modify(GoodsNorms req, User operator) {
        GoodsNorms goodsNorms = detail(req.getId());
        goodsNorms.setStatus(req.getStatus());
        goodsNorms.setUpdater(operator.getId());
        goodsNorms.setUpdateDatetime(new Date());
        goodsNorms.setUpdaterName(operator.getLoginName());
        goodsNormsMapper.updateByPrimaryKeySelective(req);
    }



    @Override
    public void batchUpDown(BatchUpDownReq req, User operator) {
        List<Long> idList = req.getIdList();
        for (Long goodsNormsId : idList) {
            GoodsNorms goodsNorms = detail(goodsNormsId);
            if (EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode().equals(req.getStatus())) {
                Goods goods = goodsService.detail(goodsNorms.getGoodId());
                if (!EGoodsStatus.GOODS_STATUS_1.getCode().equals(goods.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有上架中的商品可以上架规格");
                }
            }
            goodsNorms.setUpdater(operator.getId());
            goodsNorms.setUpdateDatetime(new Date());
            goodsNorms.setUpdaterName(operator.getLoginName());
            goodsNormsMapper.updateByPrimaryKeySelective(goodsNorms);
        }
    }

    /**
     * 详情查询商品规格
     *
     * @param id 主键ID
     * @return 商品规格对象
     */
    @Override
    public GoodsNorms detail(Long id) {
        GoodsNorms goodsNorms = goodsNormsMapper.selectByPrimaryKey(id);
        if (null == goodsNorms) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsNorms;
    }

    @Override
    public GoodsNorms detailForUpdate(Long id) {
        GoodsNorms goodsNorms = goodsNormsMapper.selectForUpdate(id);
        if (null == goodsNorms) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsNorms;
    }

    /**
     * 分页查询商品规格
     *
     * @param req 分页查询商品规格入参
     * @return 分页商品规格对象
     */
    @Override
    public List<GoodsNorms> page(GoodsNormsPageReq req) {
        GoodsNorms condition = EntityUtils.copyData(req, GoodsNorms.class);
        List<String>noStatusList = new ArrayList<>();
        noStatusList.add(EGoodsNormsStatus.GOODS_NORMS_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        List<GoodsNorms> goodsNormsList = goodsNormsMapper.selectByCondition(condition);

        return goodsNormsList;
    }

    /**
     * 列表查询商品规格
     *
     * @param req 列表查询商品规格入参
     * @return 列表商品规格对象
     */
    @Override
    public List<GoodsNorms> list(GoodsNormsListReq req) {
        GoodsNorms condition = EntityUtils.copyData(req, GoodsNorms.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsNorms.class));

        List<GoodsNorms> goodsNormsList = goodsNormsMapper.selectByCondition(condition);

        return goodsNormsList;
    }

    @Override
    public List<GoodsNorms> list(GoodsNorms req) {

        List<GoodsNorms> goodsNormsList = goodsNormsMapper.selectByCondition(req);

        return goodsNormsList;
    }

    /**
     * 前端详情查询商品规格
     *
     * @param id 主键ID
     * @return 商品规格对象
     */
    @Override
    public GoodsNormsDetailRes detailFront(Long id) {
        GoodsNormsDetailRes res = new GoodsNormsDetailRes();

        GoodsNorms goodsNorms = goodsNormsMapper.selectByPrimaryKey(id);
        if (null == goodsNorms) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goodsNorms, res);

        return res;
    }

    /**
     * 前端分页查询商品规格
     *
     * @param req 前端分页查询商品规格入参
     * @return 分页商品规格对象
     */
    @Override
    public List< GoodsNormsPageRes> pageFront(GoodsNormsPageFrontReq req) {
        GoodsNorms condition = EntityUtils.copyData(req, GoodsNorms.class);
        condition.setOrderBy("t.order_no desc,t.create_datetime desc");
        List<GoodsNorms> goodsNormsList = goodsNormsMapper.selectByCondition(condition);

        List< GoodsNormsPageRes> resList = goodsNormsList.stream().map((entity) -> {
            GoodsNormsPageRes res = new GoodsNormsPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsNormsList, resList);
    }

    /**
     * 前端列表查询商品规格
     *
     * @param req 前端列表查询商品规格入参
     * @return 列表商品规格对象
     */
    @Override
    public List< GoodsNormsListRes> listFront(GoodsNormsListFrontReq req) {
        GoodsNorms condition = EntityUtils.copyData(req, GoodsNorms.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsNorms.class));
        condition.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode());
        condition.setOrderBy("t.order_no desc,t.create_datetime desc");
        List<GoodsNorms> goodsNormsList = goodsNormsMapper.selectByCondition(condition);

        List< GoodsNormsListRes> resList = goodsNormsList.stream().map((entity) -> {
            GoodsNormsListRes res = new GoodsNormsListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void addStock(GoodsNormsAddStockReq request, User operator) {
        GoodsNorms goodsNorms = detail(request.getId());
        if (EGoodsNormsStatus.GOODS_NORMS_STATUS_2.getCode().equals(goodsNorms.getStatus())){
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已下架规格不能添加库存");
        }
        goodsNormsMapper.addStock(request.getId(), request.getStock());
    }

    @Override
    public int subtractStock(Long id, Integer number) {
       return goodsNormsMapper.updateSubtractStockSelective(id, number);
    }

    @Override
    public void addInventory(Long id, Integer number) {
        goodsNormsMapper.updateAddInventorySelective(id, number);
    }

}