package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.GoodsOrderDetailMapper;
import com.std.core.pojo.domain.GoodsOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsOrderDetailCreateReq;
import com.std.core.pojo.request.GoodsOrderDetailListReq;
import com.std.core.pojo.request.GoodsOrderDetailListFrontReq;
import com.std.core.pojo.request.GoodsOrderDetailModifyReq;
import com.std.core.pojo.request.GoodsOrderDetailPageReq;
import com.std.core.pojo.request.GoodsOrderDetailPageFrontReq;
import com.std.core.pojo.response.GoodsOrderDetailDetailRes;
import com.std.core.pojo.response.GoodsOrderDetailListRes;
import com.std.core.pojo.response.GoodsOrderDetailPageRes;
import com.std.core.service.IGoodsOrderDetailService;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 商品订单详情ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:53
 */
@Service
public class GoodsOrderDetailServiceImpl implements IGoodsOrderDetailService {

    @Resource
    private GoodsOrderDetailMapper goodsOrderDetailMapper;

    /**
     * 新增商品订单详情
     *
     * @param req      新增商品订单详情入参
     * @param operator 操作人
     */
    @Override
    public void create(GoodsOrderDetailCreateReq req, User operator) {
        GoodsOrderDetail goodsOrderDetail = EntityUtils.copyData(req, GoodsOrderDetail.class);
        goodsOrderDetailMapper.insertSelective(goodsOrderDetail);
    }

    @Override
    public void create(GoodsOrderDetail req) {
        goodsOrderDetailMapper.insertSelective(req);
    }

    /**
     * 删除商品订单详情
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        goodsOrderDetailMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改商品订单详情
     *
     * @param req      修改商品订单详情入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsOrderDetailModifyReq req, User operator) {
        GoodsOrderDetail goodsOrderDetail = EntityUtils.copyData(req, GoodsOrderDetail.class);
        goodsOrderDetailMapper.updateByPrimaryKeySelective(goodsOrderDetail);
    }

    /**
     * 详情查询商品订单详情
     *
     * @param id 主键ID
     * @return 商品订单详情对象
     */
    @Override
    public GoodsOrderDetail detail(Long id) {
        GoodsOrderDetail goodsOrderDetail = goodsOrderDetailMapper.selectByPrimaryKey(id);
        if (null == goodsOrderDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsOrderDetail;
    }

    /**
     * 分页查询商品订单详情
     *
     * @param req 分页查询商品订单详情入参
     * @return 分页商品订单详情对象
     */
    @Override
    public List<GoodsOrderDetail> page(GoodsOrderDetailPageReq req) {
        GoodsOrderDetail condition = EntityUtils.copyData(req, GoodsOrderDetail.class);

        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(condition);

        return goodsOrderDetailList;
    }

    /**
     * 列表查询商品订单详情
     *
     * @param req 列表查询商品订单详情入参
     * @return 列表商品订单详情对象
     */
    @Override
    public List<GoodsOrderDetail> list(GoodsOrderDetailListReq req) {
        GoodsOrderDetail condition = EntityUtils.copyData(req, GoodsOrderDetail.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsOrderDetail.class));

        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(condition);

        return goodsOrderDetailList;
    }

    @Override
    public List<GoodsOrderDetail> list(GoodsOrderDetail req) {
        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(req);
        return goodsOrderDetailList;
    }

    /**
     * 前端详情查询商品订单详情
     *
     * @param id 主键ID
     * @return 商品订单详情对象
     */
    @Override
    public GoodsOrderDetailDetailRes detailFront(Long id) {
        GoodsOrderDetailDetailRes res = new GoodsOrderDetailDetailRes();

        GoodsOrderDetail goodsOrderDetail = goodsOrderDetailMapper.selectByPrimaryKey(id);
        if (null == goodsOrderDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goodsOrderDetail, res);

        return res;
    }

    /**
     * 前端分页查询商品订单详情
     *
     * @param req 前端分页查询商品订单详情入参
     * @return 分页商品订单详情对象
     */
    @Override
    public List<GoodsOrderDetailPageRes> pageFront(GoodsOrderDetailPageFrontReq req, User operator) {
        GoodsOrderDetail condition = EntityUtils.copyData(req, GoodsOrderDetail.class);
        condition.setUserId(operator.getId());
        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(condition);

        List<GoodsOrderDetailPageRes> resList = goodsOrderDetailList.stream().map((entity) -> {
            GoodsOrderDetailPageRes res = new GoodsOrderDetailPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsOrderDetailList, resList);
    }

    /**
     * 前端列表查询商品订单详情
     *
     * @param req 前端列表查询商品订单详情入参
     * @return 列表商品订单详情对象
     */
    @Override
    public List<GoodsOrderDetailListRes> listFront(GoodsOrderDetailListFrontReq req) {
        GoodsOrderDetail condition = EntityUtils.copyData(req, GoodsOrderDetail.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsOrderDetail.class));

        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(condition);

        List<GoodsOrderDetailListRes> resList = goodsOrderDetailList.stream().map((entity) -> {
            GoodsOrderDetailListRes res = new GoodsOrderDetailListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void createBatch(List<GoodsOrderDetail> goodsOrderDetailList) {
        if (CollectionUtils.isEmpty(goodsOrderDetailList)) {
            return;
        }
        goodsOrderDetailMapper.insertBatchSelective(goodsOrderDetailList);
    }

    @Override
    public List<GoodsOrderDetail> listByOrderId(Long orderId) {
        GoodsOrderDetail condition = new GoodsOrderDetail();
        condition.setOrderId(orderId);
        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailMapper.selectByCondition(condition);
        return goodsOrderDetailList;

    }

}