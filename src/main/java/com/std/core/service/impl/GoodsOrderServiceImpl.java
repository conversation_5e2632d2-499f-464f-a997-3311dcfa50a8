package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.GoodsOrderMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.SysConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品订单ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-29 22:43
 */
@Service
@Slf4j
public class GoodsOrderServiceImpl implements IGoodsOrderService {

    @Resource
    private GoodsOrderMapper goodsOrderMapper;

    @Resource
    private IGoodsNormsService goodsNormsService;

    @Resource
    private ShipmentService shipmentService;

    @Resource
    private IGoodsService goodsService;

    @Resource
    private IGoodsOrderDetailService goodsOrderDetailService;

    @Resource
    private IAddressService addressService;

    @Resource
    private IUserService userService;

    @Resource
    private IGoodsTrolleyService goodsTrolleyService;

    @Resource
    private IConfigService configService;

    @Resource
    private ICuserService cuserService;

    @Resource
    private ISmsService smsService;

    @Resource
    private IDictService dictService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private IDailyIncomeSummaryService dailyIncomeSummaryService;

    /**
     * 新增商品订单
     *
     * @param req      新增商品订单入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(GoodsOrderCreateReq req, User operator) {
        Date date = new Date();
        GoodsNorms goodsNorms = goodsNormsService.detail(req.getNormsId());
        if (!EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode().equals(goodsNorms.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请购买上架中的商品");
        }
        if (goodsNorms.getInventory() < req.getNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "库存不足");
        }
        Goods goods = goodsService.detail(goodsNorms.getGoodId());
        GoodsOrder goodsOrder = new GoodsOrder();
        goodsOrder.setId(IdGeneratorUtil.generator());
        goodsOrder.setOrderNumber(goodsOrder.getId().toString());
        goodsOrder.setTotalPrice(new BigDecimal(req.getNumber()).multiply(goodsNorms.getPrice()));
        goodsOrder.setNumber(1);
        goodsOrder.setCreateDatetime(date);
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_0.getCode());
        goodsOrder.setUserId(operator.getId());
        if (goodsOrder.getTotalPrice().compareTo(configService.getBigDecimalValue(SysConstants.SEND_AMOUNT)) >= 0) {
            goodsOrder.setReceiveWay(EGoodsOrderReceiveWay.GOODS_ORDER_RECEIVEWAY_1.getCode());
        } else {
            goodsOrder.setReceiveWay(EGoodsOrderReceiveWay.GOODS_ORDER_RECEIVEWAY_0.getCode());
        }
        goodsOrder.setType(EGoodsOrderType.GOODS_ORDER_TYPE_0.getCode());
        goodsOrder.setRemark(req.getRemark());
        goodsOrder.setPayType(EGoodsOrderPayType.GOODS_ORDER_PAYTYPE_0.getCode());
        Address address = addressService.detail(req.getAddressId());
        goodsOrder.setAddress(address.getProvince().concat(address.getCity().concat(address.getCounty().concat(address.getAddress()))));
        goodsOrder.setUserName(address.getName());
        goodsOrder.setUserMobile(address.getPhone());
        goodsOrderMapper.insertSelective(goodsOrder);

        GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
        goodsOrderDetail.setOrderId(goodsOrder.getId());
        goodsOrderDetail.setGoodsId(goods.getId());
        goodsOrderDetail.setNormsId(goodsNorms.getId());
        goodsOrderDetail.setNumber(req.getNumber());
        goodsOrderDetail.setName(goods.getName());
        goodsOrderDetail.setPrice(goodsNorms.getPrice());
        goodsOrderDetail.setPic(goodsNorms.getPic());
        goodsOrderDetail.setNormsName(goodsNorms.getName());
        goodsOrderDetail.setContent(goods.getContent());
        goodsOrderDetail.setUserId(operator.getId());
        goodsOrderDetailService.create(goodsOrderDetail);

        int i = goodsNormsService.subtractStock(goodsNorms.getId(), req.getNumber());
        if (i <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "库存不足");
        }
//        // 12.25微信支付
        BigDecimal payAmount = goodsOrder.getTotalPrice();
//        微信支付，返回签名信息
        WechatAppPayInfo wechatAppPayInfo = new WechatAppPayInfo();
        if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
            wechatAppPayInfo = wechatService.getAppPayInfo(
                    operator.getId(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getValue(),
                    goodsOrder.getId(),
                    payAmount, null);

        } else if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付金额不能小于0");
        } else {
            // 金额为 0 直接成功
            serveDoCallback(goodsOrder.getId(), null);
        }
        return new OrderPayRes(goodsOrder.getId(), EBoolean.YES.getCode(), wechatAppPayInfo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoodsOrder createByTrollery(GoodsOrderTrolleryCreateReq req, User operator) {
        Date date = new Date();

        GoodsOrder goodsOrder = new GoodsOrder();
        goodsOrder.setId(IdGeneratorUtil.generator());
        goodsOrder.setOrderNumber(goodsOrder.getId().toString());
        BigDecimal totalPrice = BigDecimal.ZERO;
        Integer number = 0;
        List<GoodsOrderDetail> goodsOrderDetailList = new ArrayList<>();
        List<GoodsTrolley> goodsTrolleyList = new ArrayList<>();
        Map<Long, Goods> goodsMap = new HashMap<>();
        int count = 1;
        for (String trollery : req.getGoodsOrderTrolleryList()) {
            GoodsTrolley goodsTrolley = goodsTrolleyService.detailForUpdate(Long.valueOf(trollery));
            if (!EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_1.getCode().equals(goodsTrolley.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选购物车商品已失效");
            }
            GoodsNorms goodsNorms = goodsNormsService.detailForUpdate(goodsTrolley.getNormsId());
            if (!EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode().equals(goodsNorms.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选商品已下架");
            }
            if (goodsNorms.getInventory() < goodsTrolley.getNumber()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), goodsNorms.getName() + "库存不足");
            }
            BigDecimal itemTotalPrice = goodsNorms.getPrice().multiply(new BigDecimal(goodsTrolley.getNumber()));
            totalPrice = totalPrice.add(itemTotalPrice);
            number = number + goodsTrolley.getNumber();

            Goods goods = goodsMap.get(goodsNorms.getGoodId());
            if (goods == null) {
                goods = goodsService.detail(goodsNorms.getGoodId());
                goodsMap.put(goodsNorms.getGoodId(), goods);
            }
            goodsTrolleyList.add(goodsTrolley);
            //生成订单明细
            GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
            goodsOrderDetail.setOrderId(goodsOrder.getId());
            goodsOrderDetail.setOrderNumber(goodsOrder.getOrderNumber() + "-" + count);
            count++;
            goodsOrderDetail.setGoodsId(goodsNorms.getGoodId());
            goodsOrderDetail.setNormsId(goodsNorms.getId());
            goodsOrderDetail.setNumber(goodsTrolley.getNumber());
            goodsOrderDetail.setName(goods.getName());
            goodsOrderDetail.setPrice(goodsNorms.getPrice());
            goodsOrderDetail.setPic(goodsNorms.getPic());
            goodsOrderDetail.setNormsName(goodsNorms.getName());
            goodsOrderDetail.setContent(goods.getContent());
            goodsOrderDetail.setUserId(operator.getId());
            goodsOrderDetailList.add(goodsOrderDetail);
        }


        goodsOrder.setTotalPrice(totalPrice);
        goodsOrder.setNumber(number);
        goodsOrder.setCreateDatetime(date);
        goodsOrder.setType(EGoodsOrderType.GOODS_ORDER_TYPE_1.getCode());
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_0.getCode());
        if (goodsOrder.getTotalPrice().compareTo(configService.getBigDecimalValue(SysConstants.SEND_AMOUNT)) >= 0) {
            goodsOrder.setReceiveWay(EGoodsOrderReceiveWay.GOODS_ORDER_RECEIVEWAY_1.getCode());
        } else {
            goodsOrder.setReceiveWay(EGoodsOrderReceiveWay.GOODS_ORDER_RECEIVEWAY_0.getCode());
        }
        goodsOrder.setPayType(EGoodsOrderPayType.GOODS_ORDER_PAYTYPE_0.getCode());
        Address address = addressService.detail(req.getAddressId());
        goodsOrder.setAddress(address.getProvince().concat(address.getCity().concat(address.getCounty().concat(address.getAddress()))));
        goodsOrder.setUserName(address.getName());
        goodsOrder.setUserMobile(address.getPhone());
        goodsOrder.setUserId(operator.getId());
        goodsOrder.setRemark(req.getRemark());
        goodsOrderMapper.insertSelective(goodsOrder);

        goodsOrderDetailService.createBatch(goodsOrderDetailList);
        String title = "【西溪蔡志忠美术馆】文创商品下单成功通知";
        String conten = "\"亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您在【西溪蔡志忠美术馆】文创商店选购心仪的商品！我们很高兴地通知您，您的订单已经成功下单，并将尽快为您处理和发货。\n" +
                "\n" +
                "订单编号：[" + goodsOrder.getId() + "]\"";

        smsService.sendMyMsg(operator, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        for (GoodsTrolley trollery : goodsTrolleyList) {
            GoodsTrolley goodsTrolley = new GoodsTrolley();
            goodsTrolley.setId(trollery.getId());
            goodsTrolley.setOrderId(goodsOrder.getId());
            goodsTrolley.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_2.getCode());
            goodsTrolley.setUpdateDatetime(date);
            goodsTrolleyService.modify(goodsTrolley);

            int i = goodsNormsService.subtractStock(trollery.getNormsId(), trollery.getNumber());
            if (i <= 0) {
                GoodsNorms goodsNorms = goodsNormsService.detail(goodsTrolley.getNormsId());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), goodsNorms.getName() + "库存不足");
            }
        }


        return goodsOrder;
    }

    /**
     * 购物车订单支付
     *
     * @param goodsOrder
     */
    @Override
    public OrderPayRes payServeOrder(GoodsOrder goodsOrder) {
        GoodsOrder order = detailForUpdate(goodsOrder.getId());
        if (!EGoodsOrderStatus.GOODS_ORDER_STATUS_0.getCode().equals(order.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前状态不能支付");
        }
//        //todo 12.25微信支付
        BigDecimal payAmount = goodsOrder.getTotalPrice();
        WechatAppPayInfo wechatAppPayInfo = new WechatAppPayInfo();
//        微信支付，返回签名信息
        if (payAmount.compareTo(BigDecimal.ZERO) > 0) {
            wechatAppPayInfo = wechatService.getAppPayInfo(
                    order.getUserId(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getValue(),
                    order.getId(),
                    payAmount, null);
        }else if (payAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付金额不能小于0");
        } else {
            // 金额为 0 直接成功
            serveDoCallback(goodsOrder.getId(), null);
        }
        OrderPayRes orderPayRes = new OrderPayRes(goodsOrder.getId(), EBoolean.YES.getCode(), wechatAppPayInfo);
        return orderPayRes;
    }

    /**
     * 删除商品订单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        goodsOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改商品订单
     *
     * @param req      修改商品订单入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsOrderModifyReq req, User operator) {
        GoodsOrder goodsOrder = EntityUtils.copyData(req, GoodsOrder.class);
        goodsOrderMapper.updateByPrimaryKeySelective(goodsOrder);
    }

    @Override
    public void modify(GoodsOrder req) {
        goodsOrderMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询商品订单
     *
     * @param id 主键ID
     * @return 商品订单对象
     */
    @Override
    public GoodsOrder detail(Long id) {
        GoodsOrder goodsOrder = goodsOrderMapper.selectByPrimaryKey(id);
        if (null == goodsOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
        goodsOrderDetail.setOrderId(id);
        List<GoodsOrderDetail> list = goodsOrderDetailService.list(goodsOrderDetail);
        goodsOrder.setGoodsOrderDetailList(list);

        goodsOrder.setSendMessage(sendMessage(goodsOrder.getCompany(), goodsOrder.getCourierNumber(), goodsOrder.getUserMobile()));
        goodsOrder.setUser(userService.detail(goodsOrder.getUserId()));
        return goodsOrder;
    }

    @Override
    public GoodsOrder detailSimple(Long id) {
        GoodsOrder goodsOrder = goodsOrderMapper.selectByPrimaryKey(id);
        if (null == goodsOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return goodsOrder;
    }

    @Override
    public GoodsOrder detailForUpdate(Long id) {
        GoodsOrder goodsOrder = goodsOrderMapper.selectForUpdate(id);
        if (null == goodsOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsOrder;
    }

    /**
     * 分页查询商品订单
     *
     * @param req 分页查询商品订单入参
     * @return 分页商品订单对象
     */
    @Override
    public List<GoodsOrder> page(GoodsOrderPageReq req) {
        GoodsOrder condition = EntityUtils.copyData(req, GoodsOrder.class);
        if (StringUtils.isNotBlank(req.getCreateStartTime())){
            condition.setCreateStartTime(DateUtil.strToDate(req.getCreateStartTime()+" 00:00:00", DateUtil.DATA_TIME_PATTERN_1));
        }
        if (StringUtils.isNotBlank(req.getCreateEndTime())){
            condition.setCreateEndTime(DateUtil.strToDate(req.getCreateEndTime()+" 23:59:59", DateUtil.DATA_TIME_PATTERN_1));
        }

        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(condition);
        for (GoodsOrder goodsOrder : goodsOrderList) {
            User user = userService.detail(goodsOrder.getUserId());
            goodsOrder.setUser(user);
            GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
            goodsOrderDetail.setOrderId(goodsOrder.getId());
            List<GoodsOrderDetail> list = goodsOrderDetailService.list(goodsOrderDetail);
            goodsOrder.setGoodsOrderDetailList(list);
//            goodsOrder.setSendMessage(sendMessage(goodsOrder.getCompany(), goodsOrder.getCourierNumber(), goodsOrder.getUserMobile()));
        }
        return goodsOrderList;
    }

    /**
     * 列表查询商品订单
     *
     * @param req 列表查询商品订单入参
     * @return 列表商品订单对象
     */
    @Override
    public List<GoodsOrder> list(GoodsOrderListReq req) {
        GoodsOrder condition = EntityUtils.copyData(req, GoodsOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsOrder.class));

        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(condition);

        return goodsOrderList;
    }

    @Override
    public List<GoodsOrder> list(GoodsOrder req) {
        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(req);
        return goodsOrderList;
    }

    /**
     * 前端详情查询商品订单
     *
     * @param id 主键ID
     * @return 商品订单对象
     */
    @Override
    public GoodsOrderDetailRes detailFront(Long id) {
        GoodsOrderDetailRes res = new GoodsOrderDetailRes();

        GoodsOrder goodsOrder = goodsOrderMapper.selectByPrimaryKey(id);
        if (null == goodsOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
        goodsOrderDetail.setOrderId(goodsOrder.getId());
        List<GoodsOrderDetail> list = goodsOrderDetailService.list(goodsOrderDetail);
        BeanUtils.copyProperties(goodsOrder, res);
        res.setGoodsOrderDetailList(list);
        Integer expireTimeInMinutes = configService.getIntegerValue(SysConstants.ORDER_EXPIRE_TIME);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(goodsOrder.getCreateDatetime());
        calendar.add(Calendar.MINUTE, expireTimeInMinutes);
        res.setExpireDatetime(calendar.getTime());
        Dict dict = new Dict();
        dict.setParentKey("country.code");
        dict.setKey(goodsOrder.getCompany());
        List<Dict> dictList = dictService.list(dict);
        res.setCompanyName(dictList.get(0).getValue());
        if (EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode().equals(goodsOrder.getStatus())) {
            Date date = new Date();
            Date autoReceiveDatetime = goodsOrder.getAutoReceiveDatetime();
            if (autoReceiveDatetime != null) {
                long leftMillis = autoReceiveDatetime.getTime() - date.getTime();
                Date leftDate = new Date(leftMillis);
                res.setLeftDatetime(leftDate);
            }
        }
        res.setSendMessage(sendMessage(goodsOrder.getCompany(), goodsOrder.getCourierNumber(), goodsOrder.getUserMobile()));
        return res;
    }

    /**
     * 前端分页查询商品订单
     *
     * @param req 前端分页查询商品订单入参
     * @return 分页商品订单对象
     */
    @Override
    public List<GoodsOrderPageRes> pageFront(GoodsOrderPageFrontReq req, User operator) {
        GoodsOrder condition = EntityUtils.copyData(req, GoodsOrder.class);
        condition.setUserId(operator.getId());
        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(condition);

        Integer expireTimeInMinutes = configService.getIntegerValue(SysConstants.ORDER_EXPIRE_TIME);

        List<GoodsOrderPageRes> resList = goodsOrderList.stream().map((entity) -> {
            GoodsOrderPageRes res = new GoodsOrderPageRes();
            BeanUtils.copyProperties(entity, res);
            GoodsOrderDetail goodsOrderDetail = new GoodsOrderDetail();
            goodsOrderDetail.setOrderId(entity.getId());
            List<GoodsOrderDetail> list = goodsOrderDetailService.list(goodsOrderDetail);
            res.setGoodsOrderDetailList(list);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(entity.getCreateDatetime());
            calendar.add(Calendar.MINUTE, expireTimeInMinutes);
            res.setExpireDatetime(calendar.getTime());
            if (EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode().equals(entity.getStatus())) {
                Date date = new Date();
                Date autoReceiveDatetime = entity.getAutoReceiveDatetime();
                if (autoReceiveDatetime != null) {
                    long leftMillis = autoReceiveDatetime.getTime() - date.getTime();
                    Date leftDate = new Date(leftMillis);
                    res.setLeftDatetime(leftDate);
                }
            }
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsOrderList, resList);
    }

    /**
     * 前端列表查询商品订单
     *
     * @param req 前端列表查询商品订单入参
     * @return 列表商品订单对象
     */
    @Override
    public List<GoodsOrderListRes> listFront(GoodsOrderListFrontReq req) {
        GoodsOrder condition = EntityUtils.copyData(req, GoodsOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsOrder.class));

        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(condition);

        List<GoodsOrderListRes> resList = goodsOrderList.stream().map((entity) -> {
            GoodsOrderListRes res = new GoodsOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }


    @Override
    public List<OrderNumberByStatusRes> orderNumberByStatus(User operator) {
        GoodsOrder condition = new GoodsOrder();
        condition.setUserId(operator.getId());
        List<GoodsOrder> goodsOrderList = goodsOrderMapper.selectByCondition(condition);
        OrderNumberByStatusRes orderNumberByStatusRes_0 = new OrderNumberByStatusRes(0, "0");
        OrderNumberByStatusRes orderNumberByStatusRes_1 = new OrderNumberByStatusRes(0, "1");
        OrderNumberByStatusRes orderNumberByStatusRes_2 = new OrderNumberByStatusRes(0, "2");
        OrderNumberByStatusRes orderNumberByStatusRes_3 = new OrderNumberByStatusRes(0, "3");
        for (GoodsOrder goodsOrder : goodsOrderList) {
            switch (goodsOrder.getStatus()) {
                case "0":
                    orderNumberByStatusRes_0.setNumber(orderNumberByStatusRes_0.getNumber() + 1);
                    break;
                case "1":
                    orderNumberByStatusRes_1.setNumber(orderNumberByStatusRes_1.getNumber() + 1);
                    break;
                case "2":
                    orderNumberByStatusRes_2.setNumber(orderNumberByStatusRes_2.getNumber() + 1);
                    break;
                case "3":
                    orderNumberByStatusRes_3.setNumber(orderNumberByStatusRes_3.getNumber() + 1);
                    break;
            }
        }
        List<OrderNumberByStatusRes> resList = new ArrayList<>();
        resList.add(orderNumberByStatusRes_0);
        resList.add(orderNumberByStatusRes_1);
        resList.add(orderNumberByStatusRes_2);
        resList.add(orderNumberByStatusRes_3);
        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoodsOrder send(GoodsOrderSendReq request, User operator) {
        GoodsOrder goodsOrder = detail(request.getId());
        if (!EGoodsOrderStatus.GOODS_ORDER_STATUS_1.getCode().equals(goodsOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择待发货的订单");
        }
        goodsOrder.setCompany(request.getCompany());
        goodsOrder.setCourierNumber(request.getCourierNumber());
        goodsOrder.setUpdateDatetime(DateUtil.strToDate(request.getSendTime(), DateUtil.DATA_TIME_PATTERN_1));
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode());
        // 获取当前日期时间
        LocalDateTime now = goodsOrder.getUpdateDatetime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // 增加7天
        LocalDateTime sevenDaysLater = now.plusDays(configService.getIntegerValue(SysConstants.EXPIRE_DAY));
        // 转换为Date对象
        Date dateSevenDaysLater = Date.from(sevenDaysLater.atZone(ZoneId.systemDefault()).toInstant());
        // 设置到goodsOrder
        goodsOrder.setAutoReceiveDatetime(dateSevenDaysLater);
        goodsOrderMapper.updateByPrimaryKeySelective(goodsOrder);
        User user = userService.detail(goodsOrder.getUserId());
        String title = "【西溪蔡志忠美术馆】文创商品发货通知";
        String conten = "\"亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您在【西溪蔡志忠美术馆】文创商店的选购！我们很高兴地通知您，您购买的文创商品已经成功发货，即将送达您的手中。收到商品后，请务必先检查商品包装是否完好，再拆包验收商品。\n" +
                "\n" +
                "订单编号：[" + goodsOrder.getId() + "]\n" +
                "发货时间：[" + request.getSendTime() + "]\n" +
                "物流单号：[" + request.getCourierNumber() + "]\"";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());


        return goodsOrder;
    }

    @Override
    public void sendSuccessNoticeWechat(GoodsOrder goodsOrder) {
        // 获取 accessToken
        String accessToken = shipmentService.getAccessToken();


        // 从订单中获取相应参数
        Cuser cuser = cuserService.detailByUserId(goodsOrder.getUserId());

        String transactionId = goodsOrder.getTransactionId();
        String courierNumber = goodsOrder.getCourierNumber();
        String expressCompany = goodsOrder.getCompany();
        GoodsOrderDetail condition = new GoodsOrderDetail();
        condition.setOrderId(goodsOrder.getId());
        List<GoodsOrderDetail> list = goodsOrderDetailService.list(condition);
        System.out.println(list.size());
        StringBuilder itemNameBuilder = new StringBuilder();
        for (GoodsOrderDetail goodsOrderDetail : list) {
            if (goodsOrderDetail.getName() != null && goodsOrderDetail.getNumber() != null) {
                itemNameBuilder.append(goodsOrderDetail.getName())
                        .append("*")
                        .append(goodsOrderDetail.getNumber())
                        .append(";");
            }
        }
        String itemDesc = itemNameBuilder.toString().trim();
        itemDesc = StringUtils.removeEnd(itemNameBuilder.toString(), ";");

        // 根据你的需求填写商品描述
        String consignorContact = goodsOrder.getUserMobile(); // 使用订单中的用户手机号作为发货人联系方式
        String openid = cuser.getOpenid(); // 获取用户的OpenID

        // 创建请求数据
        Map<String, Object> requestData = new HashMap<>();
        Map<String, Object> orderKey = new HashMap<>();
        orderKey.put("order_number_type", 2); // 设置订单类型
        orderKey.put("transaction_id",transactionId ); // 设置交易ID
        requestData.put("order_key", orderKey);

        requestData.put("delivery_mode", 1); // 设置配送模式
        requestData.put("logistics_type", 1); // 设置物流类型

        // 创建运输列表
        Map<String, Object> shipping = new HashMap<>();
        shipping.put("tracking_no", courierNumber); // 设置快递单号
        shipping.put("express_company", expressCompany); // 设置快递公司

        String encodedItemDesc = Base64.getEncoder().encodeToString(itemDesc.getBytes(StandardCharsets.UTF_8));
        shipping.put("item_desc", encodedItemDesc); // 设置商品描述

        // 设置发货人信息
        Map<String, String> contact = new HashMap<>();
        contact.put("consignor_contact", consignorContact); // 设置发货人联系方式
        shipping.put("contact", contact);
        List<Map<String, Object>> shippingList = new ArrayList<>();
        shippingList.add(shipping);  // 将运输信息添加到列表

        // 将运输列表添加到请求数据中
        requestData.put("shipping_list", shippingList);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        String uploadTime = Instant.now().atZone(ZoneId.systemDefault()).format(formatter);
        requestData.put("upload_time", uploadTime); // 设置上传时间

        // 设置支付信息
        Map<String, String> payer = new HashMap<>();
        payer.put("openid", openid); // 设置用户的OpenID
        requestData.put("payer", payer);

        // 发送请求
        String response = shipmentService.uploadShippingInfo(
                accessToken,
                transactionId,
                courierNumber,
                expressCompany,
                itemDesc,
                consignorContact,
                openid
        );
    }

    @Override
    public void receive(GoodsOrderReceiveReq request, User operator) {
        GoodsOrder goodsOrder = detail(request.getId());
        if (!EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode().equals(goodsOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择待发货的订单");
        }
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_3.getCode());
        goodsOrder.setFinishDatetime(new Date());
        goodsOrderMapper.updateByPrimaryKeySelective(goodsOrder);

        String title = "【西溪蔡志忠美术馆】文创商品订单完成通知";
        String conten = "\"亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您在【西溪蔡志忠美术馆】文创商店的选购！我们非常高兴地通知您，您的订单[" + goodsOrder.getId() + "]已经顺利完成，商品已成功送达您的手中。\n" +
                "\n" +
                "订单编号：[" + goodsOrder.getId() + "]\n" +
                "完成时间：[" + DateUtil.dateToStr(goodsOrder.getFinishDatetime(), DateUtil.DATA_TIME_PATTERN_9) + "]\"";
        smsService.sendMyMsg(operator, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

    }

    /**
     * 支付回调
     *
     * @param bizCode
     * @param transactionId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void serveDoCallback(Long bizCode, String transactionId) {
        GoodsOrder goodsOrder = detailForUpdate(bizCode);
        if (!EGoodsOrderStatus.GOODS_ORDER_STATUS_0.getCode().equals(goodsOrder.getStatus())) {
            return;
        }

        goodsOrder.setTransactionId(transactionId);
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_1.getCode());
        goodsOrder.setPayDatetime(new Date());
        String title = "【西溪蔡志忠美术馆】文创商品下单成功通知";
        String conten = "\"亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您在【西溪蔡志忠美术馆】文创商店选购心仪的商品！我们很高兴地通知您，您的订单已经成功下单，并将尽快为您处理和发货。\n" +
                "\n" +
                "订单编号：[" + goodsOrder.getId() + "]\"";
        smsService.sendMyMsg(userService.detail(EUserKind.SYS.getAdminUserId()), title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        goodsOrderMapper.updateByPrimaryKeySelective(goodsOrder);

        // 商品收入统计
        dailyIncomeSummaryService.goodsIncomeSummary(goodsOrder.getTotalPrice(), goodsOrder.getPayDatetime());

    }

    @Override
    public List<GoodsOrder> listTimeOutOrder(Integer closeTime) {
        return goodsOrderMapper.selectTimeOutOrder(closeTime, new Date());
    }

    /**
     * 取消订单
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelGoodsOrder(Long id) {
        GoodsOrder goodsOrder = detailForUpdate(id);
        if (!EGoodsOrderStatus.GOODS_ORDER_STATUS_0.getCode().equals(goodsOrder.getStatus())) {
            return;
        }

        GoodsOrder order = new GoodsOrder();
        order.setId(id);
        order.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_4.getCode());
        order.setPayDatetime(new Date());
        order.setCancleDatetime(new Date());
        goodsOrderMapper.updateByPrimaryKeySelective(order);

        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailService.listByOrderId(goodsOrder.getId());
        // 库存加回去
        for (GoodsOrderDetail goodsOrderDetail : goodsOrderDetailList) {
            goodsNormsService.addInventory(goodsOrderDetail.getNormsId(), goodsOrderDetail.getNumber());
        }
        // 第三方取消支付
//        userService.cancelOrder(goodsOrder.getSerialNumber());

        String title = "【西溪蔡志忠美术馆】文创商品订单超时取消通知";
        String conten = "亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您一直以来对【西溪蔡志忠美术馆】文创商店的支持与厚爱。我们非常遗憾地通知您，由于您的订单[" + id + "]在规定时间内未完成支付，根据我们的订单处理规则，该订单已被系统自动取消。\n" +
                "\n" +
                "订单编号：[" + id + "]";
        User user = userService.detail(goodsOrder.getUserId());
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        // 商品收入统计,取消需要减历史的钱
        dailyIncomeSummaryService.goodsIncomeSummary(goodsOrder.getTotalPrice().negate(), goodsOrder.getPayDatetime());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelGoodsOrderByOss(Long id) {
        GoodsOrder goodsOrder = detailForUpdate(id);
        if (!(EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode().equals(goodsOrder.getStatus())||
                EGoodsOrderStatus.GOODS_ORDER_STATUS_3.getCode().equals(goodsOrder.getStatus())
                ||EGoodsOrderStatus.GOODS_ORDER_STATUS_1.getCode().equals(goodsOrder.getStatus()))) {
            return;
        }

        GoodsOrder order = new GoodsOrder();
        order.setId(id);
        order.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_4.getCode());
//        order.setPayDatetime(new Date());
        order.setCancleDatetime(new Date());
        if (StringUtils.isBlank(goodsOrder.getRemark())){
            order.setRemark("取消订单，取消原因：后台取消");
        }else {
            order.setRemark(goodsOrder.getRemark().concat("取消订单，取消原因：后台取消"));
        }

        goodsOrderMapper.updateByPrimaryKeySelective(order);

        List<GoodsOrderDetail> goodsOrderDetailList = goodsOrderDetailService.listByOrderId(goodsOrder.getId());
        // 库存加回去
        for (GoodsOrderDetail goodsOrderDetail : goodsOrderDetailList) {
            goodsNormsService.addInventory(goodsOrderDetail.getNormsId(), goodsOrderDetail.getNumber());
        }
        // 第三方取消支付
//        userService.cancelOrder(goodsOrder.getSerialNumber());
        if (goodsOrder.getTotalPrice().compareTo(BigDecimal.ZERO) > 0) {
            wechatService.doRefund2(order.getId(), goodsOrder.getTotalPrice(), EJourBizTypeUser.AFTER_SALES.AFTER_SALES_REFUND, order.getId());
        }

        String title = "【西溪蔡志忠美术馆】文创商品订单超时取消通知";
        String conten = "亲爱的顾客，\n" +
                "\n" +
                "您好！\n" +
                "\n" +
                "感谢您一直以来对【西溪蔡志忠美术馆】文创商店的支持与厚爱。我们非常遗憾地通知您,该订单已被系统自动取消。\n" +
                "\n" +
                "订单编号：[" + id + "]";
        User user = userService.detail(goodsOrder.getUserId());
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        // 商品收入统计,取消需要减历史的钱
        dailyIncomeSummaryService.goodsIncomeSummary(goodsOrder.getTotalPrice().negate(), goodsOrder.getPayDatetime());

    }

//    @Override
//    public void receive(GoodsOrderSendReq request, User operator) {
//        GoodsOrder goodsOrder = detail(request.getId());
//        if(!EGoodsOrderStatus.GOODS_ORDER_STATUS_1.getCode().equals(goodsOrder.getStatus())){
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"请选择待发货的订单");
//        }
//        goodsOrder.setCompany(request.getCompany());
//        goodsOrder.setCourierNumber(request.getCourierNumber());
//        goodsOrder.setUpdateDatetime(DateUtil.strToDate(request.getSendTime(),DateUtil.DATA_TIME_PATTERN_1));
//        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode());
//        goodsOrderMapper.updateByPrimaryKeySelective(goodsOrder);
//    }


    public String sendMessage(String shipperCode, String logisticCode, String userMobile) {

        // 快递鸟API接口地址
        String apiUrl = "https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx";
        String s = new String();
        // 示例请求数据
        String requestData = "{"
                + "\"ShipperCode\": \"" + shipperCode + "\","
                + "\"LogisticCode\": \"" + logisticCode + "\""
                + "}";
        if ("SF".equals(shipperCode) || "ZTO".equals(shipperCode)) {
            // 获取手机号后 4 位，判断手机号是否长于 4 位
            if (userMobile.length() < 4) {
                return "";
            }
            userMobile = StringUtils.substring(userMobile, -4);
            requestData = "{"
                    + "\"CustomerName\": \"" + userMobile + "\","
                    + "\"ShipperCode\": \"" + shipperCode + "\","
                    + "\"LogisticCode\": \"" + logisticCode + "\""
                    + "}";
        }


        // 快递鸟的商户ID和AppKey
        String EBusinessID = "1882875";
        String AppKey = "9a9de4f4-1837-44c4-85d2-29a68e9935f4";

        // 签名计算
        String dataSign = encrypt(requestData, AppKey);

        try {
            // 构建请求参数
            String params = "RequestData=" + encodeURIComponent(requestData)
                    + "&EBusinessID=" + EBusinessID
                    + "&RequestType=8001"
                    + "&DataSign=" + encodeURIComponent(dataSign)
                    + "&DataType=2";

            // 发送POST请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

            // 发送请求数据
            OutputStream os = connection.getOutputStream();
            os.write(params.getBytes(StandardCharsets.UTF_8));
            os.flush();
            os.close();

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String response = readInputStream(connection.getInputStream());
                s = response;
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }

    public static void main(String[] args) {
//        String message1 = sendMessage1("SF", "SF1551619921481", "***********");
//        System.out.println(message1);
        String remark=null;
        System.out.println(remark.concat("1"));
    }

    public static String sendMessage1(String shipperCode, String logisticCode, String userMobile) {

        // 快递鸟API接口地址
//        String apiUrl = "https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx";
        String apiUrl = "https://api.kdniao.com/api/dist";
        String s = new String();
        // 示例请求数据
        String requestData = "{"
                + "\"ShipperCode\": \"" + shipperCode + "\","
                + "\"LogisticCode\": \"" + logisticCode + "\""
                + "}";
        if ("SF".equals(shipperCode) || "ZTO".equals(shipperCode)) {
            // 获取手机号后 4 位，判断手机号是否长于 4 位
            if (userMobile.length() < 4) {
                return "";
            }
            userMobile = StringUtils.substring(userMobile, -4);
            requestData = "{"
                    + "\"CustomerName\": \"" + userMobile + "\","
                    + "\"ShipperCode\": \"" + shipperCode + "\","
                    + "\"LogisticCode\": \"" + logisticCode + "\""
                    + "}";
        }


        // 快递鸟的商户ID和AppKey
        String EBusinessID = "1882875";
        String AppKey = "9a9de4f4-1837-44c4-85d2-29a68e9935f4";

        // 签名计算
        String dataSign = encrypt(requestData, AppKey);

        try {
            // 构建请求参数
            String params = "RequestData=" + encodeURIComponent(requestData)
                    + "&EBusinessID=" + EBusinessID
                    + "&RequestType=8001"
                    + "&DataSign=" + encodeURIComponent(dataSign)
                    + "&DataType=2";

            // 发送POST请求
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

            // 发送请求数据
            OutputStream os = connection.getOutputStream();
            os.write(params.getBytes(StandardCharsets.UTF_8));
            os.flush();
            os.close();

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String response = readInputStream(connection.getInputStream());
                s = response;
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return s;
    }

    public static String readInputStream(InputStream inputStream) {
        StringBuilder result = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }


    // 签名加密方法（Base64 + MD5）
    public static String encrypt(String content, String keyValue) {
        try {
            String data = content + keyValue;
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(String.format("%02x", b));
            }
            String md5Str = sb.toString();
            return java.util.Base64.getEncoder().encodeToString(md5Str.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // URL编码方法
    public static String encodeURIComponent(String value) {
        try {
            return java.net.URLEncoder.encode(value, "UTF-8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public  List<IncomeItemRes> income(IncomeGroupReq req) {
        return goodsOrderMapper.selectActivityIncomeGroupBy(req);
    }

    @Override
    public MinMaxTimeRes minMaxTime() {
        return goodsOrderMapper.selectMinMaxCreateTime();
    }

    @Override
    public BigDecimal total(IncomeGroupReq req) {
        return goodsOrderMapper.selectActivityIncomeSum(req);
    }

    @Override
    public BigDecimal detailIncomeByDate(String startDate, String endDate) {
        return goodsOrderMapper.selectIncomeByDate(startDate,  endDate);
    }


}