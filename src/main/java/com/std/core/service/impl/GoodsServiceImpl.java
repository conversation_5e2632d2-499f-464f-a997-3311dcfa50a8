package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EGoodsNormsStatus;
import com.std.core.enums.EGoodsStatus;
import com.std.core.mapper.GoodsMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsDetailRes;
import com.std.core.pojo.response.GoodsListRes;
import com.std.core.pojo.response.GoodsPageRes;
import com.std.core.service.IGoodsCategoryService;
import com.std.core.service.IGoodsNormsService;
import com.std.core.service.IGoodsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-26 20:44
 */
@Service
public class GoodsServiceImpl implements IGoodsService {

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private IGoodsNormsService goodsNormsService;

    @Resource
    private IGoodsCategoryService goodsCategoryService;

    /**
     * 新增商品
     *
     * @param req      新增商品入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(GoodsCreateReq req, User operator) {
        Goods goods = EntityUtils.copyData(req, Goods.class);
        goods.setStatus(EGoodsStatus.GOODS_STATUS_0.getCode());
        goods.setCreater(operator.getId());
        goods.setCreaterName(operator.getLoginName());
        goods.setCreateDatetime(new Date());
        List<GoodsNormsCreateReq> goodsNormsList = req.getGoodsNormsList();
        BigDecimal price = BigDecimal.ZERO;
        goodsMapper.insertSelective(goods);
        for (GoodsNormsCreateReq goodsNorms : goodsNormsList) {
            GoodsNorms goodsNormsItem = EntityUtils.copyData(goodsNorms, GoodsNorms.class);
            goodsNormsItem.setGoodId(goods.getId());
            goodsNormsItem.setInventory(goodsNorms.getNumber());
            goodsNormsItem.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_0.getCode());
            goodsNormsItem.setCreater(operator.getId());
            goodsNormsItem.setCreateDatetime(new Date());
            goodsNormsItem.setCreaterName(operator.getLoginName());
            goodsNormsService.create(goodsNormsItem);
            if (price == BigDecimal.ZERO) {
                price = goodsNormsItem.getPrice();
            } else {
                if (price.compareTo(goodsNormsItem.getPrice()) > 0) {
                    price = goodsNormsItem.getPrice();
                }
            }
        }
        goods.setPrice(price);
        goodsMapper.updateByPrimaryKeySelective(goods);
    }

    /**
     * 删除商品
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        Goods goods = detail(id);
        if (EGoodsStatus.GOODS_STATUS_1.getCode().equals(goods.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不能删除上架中的商品");
        }
        goods.setStatus(EGoodsStatus.GOODS_STATUS_3.getCode());
        goodsMapper.updateByPrimaryKeySelective(goods);
    }

    /**
     * 修改商品
     *
     * @param req      修改商品入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(GoodsModifyReq req, User operator) {
        Goods goods = detail(req.getId());
        BigDecimal price = BigDecimal.ZERO;
        if (!EGoodsStatus.GOODS_STATUS_0.getCode().equals(goods.getStatus()) && !EGoodsStatus.GOODS_STATUS_2.getCode().equals(goods.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "上架中商品不可以修改");
        }
        goods = EntityUtils.copyData(req, Goods.class);
        goods.setUpdater(operator.getId());
        goods.setUpdaterName(operator.getLoginName());
        goods.setUpdateDatetime(new Date());
        List<GoodsNormsCreateReq> goodsNormsList = req.getGoodsNormsList();
        GoodsNorms condition = new GoodsNorms();
        condition.setGoodId(goods.getId());
        List<GoodsNorms> list = goodsNormsService.list(condition);
        for (GoodsNorms goodsNorms : list) {
            goodsNormsService.remove(goodsNorms.getId(), operator);
        }
        for (GoodsNormsCreateReq goodsNorms : goodsNormsList) {
            Date date = new Date();
            GoodsNorms goodsNormsItem = EntityUtils.copyData(goodsNorms, GoodsNorms.class);
            goodsNormsItem.setGoodId(goods.getId());
            goodsNormsItem.setInventory(goodsNorms.getNumber());
            goodsNormsItem.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_0.getCode());
            goodsNormsItem.setCreater(operator.getId());
            goodsNormsItem.setCreaterName(operator.getLoginName());
            goodsNormsItem.setCreateDatetime(date);
            goodsNormsItem.setUpdater(operator.getId());
            goodsNormsItem.setUpdateDatetime(date);
            goodsNormsItem.setUpdaterName(operator.getLoginName());
            goodsNormsService.create(goodsNormsItem);
            if (price == BigDecimal.ZERO) {
                price = goodsNormsItem.getPrice();
            } else {
                if (price.compareTo(goodsNormsItem.getPrice()) > 0) {
                    price = goodsNormsItem.getPrice();
                }
            }
        }
        goods.setPrice(price);
        goodsMapper.updateByPrimaryKeySelective(goods);
    }

    @Override
    public void modify(Goods req) {
        goodsMapper.updateByPrimaryKeySelective(req);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(BatchUpDownReq req, User operator) {
        for (Long goodsId : req.getIdList()) {
            Goods goods = detail(goodsId);
            GoodsNorms goodsNorms = new GoodsNorms();
            goodsNorms.setGoodId(goodsId);
            List<String> statusList = new ArrayList<>();
            statusList.add(EGoodsNormsStatus.GOODS_NORMS_STATUS_0.getCode());
            statusList.add(EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode());
            statusList.add(EGoodsNormsStatus.GOODS_NORMS_STATUS_2.getCode());
            goodsNorms.setStatusList(statusList);
            goods.setStatus(req.getStatus());
            goods.setUpdater(operator.getId());
            goods.setUpdaterName(operator.getLoginName());
            goods.setUpdateDatetime(new Date());
            List<GoodsNorms> list = goodsNormsService.list(goodsNorms);

            for (GoodsNorms norms : list) {
                if (EGoodsStatus.GOODS_STATUS_1.getCode().equals(req.getStatus())) {
                    norms.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode());
                    norms.setInventory(norms.getNumber());
                    goodsNormsService.modify(norms, operator);
                } else if (EGoodsStatus.GOODS_STATUS_2.getCode().equals(req.getStatus())) {
                    norms.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_2.getCode());
                    goodsNormsService.modify(norms, operator);
                }
            }
            goodsMapper.updateByPrimaryKeySelective(goods);
        }
    }

    /**
     * 详情查询商品
     *
     * @param id 主键ID
     * @return 商品对象
     */
    @Override
    public Goods detail(Long id) {
        Goods goods = goodsMapper.selectByPrimaryKey(id);
        if (null == goods) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        GoodsCategory goodsCategory = goodsCategoryService.detail(goods.getTypeId());
        if (null != goodsCategory.getParentId()) {
            goods.setParentId(Long.valueOf(goodsCategory.getParentId()));
        }
        GoodsNorms goodsNorms = new GoodsNorms();
        goodsNorms.setGoodId(goods.getId());
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(EGoodsNormsStatus.GOODS_NORMS_STATUS_3.getCode());
        goodsNorms.setNoStatusList(noStatusList);
        List<GoodsNorms> list = goodsNormsService.list(goodsNorms);
        goods.setGoodsNormsList(list);
        return goods;
    }

    /**
     * 分页查询商品
     *
     * @param req 分页查询商品入参
     * @return 分页商品对象
     */
    @Override
    public List<Goods> page(GoodsPageReq req) {
        Goods condition = EntityUtils.copyData(req, Goods.class);
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(EGoodsStatus.GOODS_STATUS_3.getCode());
        condition.setNoStatusList(noStatusList);
        List<Goods> goodsList = goodsMapper.selectByCondition(condition);
        String flag = EBoolean.YES.getCode();
        for (Goods goods : goodsList) {
            GoodsNorms goodsNorms = new GoodsNorms();
            goodsNorms.setGoodId(goods.getId());
            goodsNorms.setStatus(EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode());
            List<GoodsNorms> list = goodsNormsService.list(goodsNorms);
            for (GoodsNorms norms : list) {
                if (norms.getInventory() == 0) {
                    flag = EBoolean.NO.getCode();
                    break;
                }
            }
            goods.setFlag(flag);
        }
        return goodsList;
    }

    /**
     * 列表查询商品
     *
     * @param req 列表查询商品入参
     * @return 列表商品对象
     */
    @Override
    public List<Goods> list(GoodsListReq req) {
        Goods condition = EntityUtils.copyData(req, Goods.class);
        condition.setOrderBy("t.order_no desc");
        List<Goods> goodsList = goodsMapper.selectByCondition(condition);

        return goodsList;
    }

    @Override
    public List<Goods> list(Goods req) {
        List<Goods> goodsList = goodsMapper.selectByCondition(req);
        return goodsList;
    }

    /**
     * 前端详情查询商品
     *
     * @param id 主键ID
     * @return 商品对象
     */
    @Override
    public GoodsDetailRes detailFront(Long id) {
        GoodsDetailRes res = new GoodsDetailRes();

        Goods goods = goodsMapper.selectByPrimaryKey(id);
        if (null == goods) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goods, res);

        return res;
    }

    /**
     * 前端分页查询商品
     *
     * @param req 前端分页查询商品入参
     * @return 分页商品对象
     */
    @Override
    public List<GoodsPageRes> pageFront(GoodsPageFrontReq req) {
        Goods condition = EntityUtils.copyData(req, Goods.class);

        if (EBoolean.YES.getCode().equals(req.getSortFlag())) {
            condition.setOrderBy("t.price desc,t.order_no desc,t.create_datetime desc");
        } else if (EBoolean.NO.getCode().equals(req.getSortFlag())){
            condition.setOrderBy("t.price asc,t.order_no desc,t.create_datetime desc");
        }else {
            condition.setOrderBy("t.order_no desc,t.create_datetime desc");
        }
        condition.setStatus(EGoodsStatus.GOODS_STATUS_1.getCode());
        List<Goods> goodsList = goodsMapper.selectByCondition(condition);

        List<GoodsPageRes> resList = goodsList.stream().map((entity) -> {
            GoodsPageRes res = new GoodsPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsList, resList);
    }

    /**
     * 前端列表查询商品
     *
     * @param req 前端列表查询商品入参
     * @return 列表商品对象
     */
    @Override
    public List<GoodsListRes> listFront(GoodsListFrontReq req) {
        Goods condition = EntityUtils.copyData(req, Goods.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Goods.class));

        List<Goods> goodsList = goodsMapper.selectByCondition(condition);

        List<GoodsListRes> resList = goodsList.stream().map((entity) -> {
            GoodsListRes res = new GoodsListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void sort(SortReq request) {
        List<Sort> sortList = request.getSortList();
        if (sortList == null || sortList.isEmpty()) {
            return;
        }

        for (Sort sort : sortList) {
            if (sort.getId() != null && sort.getOrderNo() != null) {
                Goods goods = new Goods();
                goods.setId(sort.getId());
                goods.setOrderNo(sort.getOrderNo());
                goodsMapper.updateByPrimaryKeySelective(goods); // 使用已有的更新方法
            }
        }
    }


}