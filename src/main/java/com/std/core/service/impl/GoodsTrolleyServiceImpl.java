package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EGoodsNormsStatus;
import com.std.core.enums.EGoodsTrolleyStatus;
import com.std.core.mapper.GoodsTrolleyMapper;
import com.std.core.pojo.domain.Goods;
import com.std.core.pojo.domain.GoodsNorms;
import com.std.core.pojo.domain.GoodsTrolley;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsTrolleyDetailRes;
import com.std.core.pojo.response.GoodsTrolleyListRes;
import com.std.core.pojo.response.GoodsTrolleyPageRes;
import com.std.core.service.IGoodsNormsService;
import com.std.core.service.IGoodsService;
import com.std.core.service.IGoodsTrolleyService;
import com.std.core.service.IUserService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 购物车ServiceImpl
 *
 * <AUTHOR> mjd
 * @since : 2024-12-27 16:53
 */
@Service
public class GoodsTrolleyServiceImpl implements IGoodsTrolleyService {

    @Resource
    private GoodsTrolleyMapper goodsTrolleyMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IGoodsNormsService goodsNormsService;
    @Resource
    private IGoodsService goodsService;

    /**
     * 新增购物车
     *
     * @param req      新增购物车入参
     * @param operator 操作人
     */
    @Override
    public void create(GoodsTrolleyCreateReq req, User operator) {
        GoodsTrolley goodsTrolley = EntityUtils.copyData(req, GoodsTrolley.class);
        GoodsNorms goodsNorms = goodsNormsService.detail(req.getNormsId());

        GoodsTrolley condition =new GoodsTrolley();
        condition.setUserId(operator.getId());
        condition.setNormsId(goodsNorms.getId());
        condition.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_1.getCode());
        List<GoodsTrolley> list = list(condition);
        if (!EGoodsNormsStatus.GOODS_NORMS_STATUS_1.getCode().equals(goodsNorms.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "规格未上架");
        }
        if (goodsNorms.getInventory() < req.getNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "库存不足");
        }
        if(list.isEmpty()) {
            goodsTrolley.setCreateDatetime(new Date());
            goodsTrolley.setGoodsId(goodsNorms.getGoodId());
            goodsTrolley.setUserId(operator.getId());
            goodsTrolley.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_1.getCode());
            goodsTrolleyMapper.insertSelective(goodsTrolley);
        }
        else{
            goodsTrolley=list.get(0);
            if (goodsNorms.getInventory() <goodsTrolley.getNumber()+req.getNumber()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "库存不足");
            }
            goodsTrolley.setNumber(goodsTrolley.getNumber()+req.getNumber());
            goodsTrolley.setUpdateDatetime(new Date());
            goodsTrolleyMapper.updateByPrimaryKeySelective(goodsTrolley);
        }
    }

    /**
     * 删除购物车
     */
    @Override
    public void remove(GoodsTrolleyRemoveReq req, User operator) {
        List<Long> ids = req.getIds();
        for (Long id : ids) {
//            GoodsTrolley goodsTrolley = detail(id);
//            goodsTrolley.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_2.getCode());
//            goodsTrolleyMapper.updateByPrimaryKeySelective(goodsTrolley);
            goodsTrolleyMapper.deleteByPrimaryKey(id);
        }
    }

    /**
     * 修改购物车
     *
     * @param req      修改购物车入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsTrolleyModifyReq req, User operator) {
        GoodsTrolley goodsTrolley = detail(req.getId());
        if (req.getNumber() > goodsTrolley.getNumber()) {
            GoodsNorms goodsNorms = goodsNormsService.detail(goodsTrolley.getNormsId());
            if (goodsNorms.getInventory() < req.getNumber()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "库存不足");
            }
        }

        goodsTrolley.setNumber(req.getNumber());
        goodsTrolleyMapper.updateByPrimaryKeySelective(goodsTrolley);
    }

    @Override
    public void modify(GoodsTrolley req) {
        goodsTrolleyMapper.updateByPrimaryKeySelective(req);
    }


    @Override
    public void exchange(GoodsTrolleyExchangeReq req, User operator) {
        GoodsTrolley goodsTrolley = detail(req.getId());
        GoodsNorms goodsNorms = goodsNormsService.detail(req.getNormsId());
        goodsTrolley.setNormsId(req.getNormsId());
        goodsTrolley.setUpdateDatetime(new Date());
        goodsTrolleyMapper.updateByPrimaryKeySelective(goodsTrolley);
    }

    /**
     * 详情查询购物车
     *
     * @param id 主键ID
     * @return 购物车对象
     */
    @Override
    public GoodsTrolley detail(Long id) {
        GoodsTrolley goodsTrolley = goodsTrolleyMapper.selectByPrimaryKey(id);
        if (null == goodsTrolley) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        goodsTrolley.setUser(userService.selectSummaryInfo(goodsTrolley.getUserId()));

        return goodsTrolley;
    }

    @Override
    public GoodsTrolley detailForUpdate(Long id) {
        GoodsTrolley goodsTrolley = goodsTrolleyMapper.selectForUpdate(id);
        if (null == goodsTrolley) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsTrolley;
    }

    /**
     * 分页查询购物车
     *
     * @param req 分页查询购物车入参
     * @return 分页购物车对象
     */
    @Override
    public List<GoodsTrolley> page(GoodsTrolleyPageReq req) {
        GoodsTrolley condition = EntityUtils.copyData(req, GoodsTrolley.class);

        List<GoodsTrolley> goodsTrolleyList = goodsTrolleyMapper.selectByCondition(condition);
        // 转译UserId
        goodsTrolleyList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return goodsTrolleyList;
    }

    /**
     * 列表查询购物车
     *
     * @param req 列表查询购物车入参
     * @return 列表购物车对象
     */
    @Override
    public List<GoodsTrolley> list(GoodsTrolleyListReq req) {
        GoodsTrolley condition = EntityUtils.copyData(req, GoodsTrolley.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsTrolley.class));

        List<GoodsTrolley> goodsTrolleyList = goodsTrolleyMapper.selectByCondition(condition);
        // 转译UserId
        goodsTrolleyList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return goodsTrolleyList;
    }

    @Override
    public List<GoodsTrolley> list(GoodsTrolley req) {
        List<GoodsTrolley> goodsTrolleyList = goodsTrolleyMapper.selectByCondition(req);
        return goodsTrolleyList;
    }

    /**
     * 前端详情查询购物车
     *
     * @param id 主键ID
     * @return 购物车对象
     */
    @Override
    public GoodsTrolleyDetailRes detailFront(Long id) {
        GoodsTrolleyDetailRes res = new GoodsTrolleyDetailRes();

        GoodsTrolley goodsTrolley = goodsTrolleyMapper.selectByPrimaryKey(id);
        if (null == goodsTrolley) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        goodsTrolley.setUser(userService.selectSummaryInfo(goodsTrolley.getUserId()));

        BeanUtils.copyProperties(goodsTrolley, res);

        return res;
    }

    /**
     * 前端分页查询购物车
     *
     * @param req 前端分页查询购物车入参
     * @return 分页购物车对象
     */
    @Override
    public List<GoodsTrolleyPageRes> pageFront(GoodsTrolleyPageFrontReq req, User operator) {
        GoodsTrolley condition = EntityUtils.copyData(req, GoodsTrolley.class);
        condition.setUserId(operator.getId());
        condition.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_1.getCode());
        condition.setOrderBy("t.order_no desc");
        List<GoodsTrolley> goodsTrolleyList = goodsTrolleyMapper.selectByCondition(condition);
        // 转译UserId
        goodsTrolleyList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<GoodsTrolleyPageRes> resList = goodsTrolleyList.stream().map((entity) -> {
            GoodsTrolleyPageRes res = new GoodsTrolleyPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsTrolleyList, resList);
    }

    /**
     * 前端列表查询购物车
     *
     * @return 列表购物车对象
     */
    @Override
    public List<GoodsTrolleyListRes> listFront(GoodsTrolleyListFrontReq req, User operator) {
        GoodsTrolley condition = EntityUtils.copyData(req, GoodsTrolley.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsTrolley.class));
        condition.setUserId(operator.getId());
        condition.setStatus(EGoodsTrolleyStatus.GOODS_TROLLEY_STATUS_1.getCode());
        List<GoodsTrolley> goodsTrolleyList = goodsTrolleyMapper.selectByCondition(condition);

        List<GoodsTrolleyListRes> resList = goodsTrolleyList.stream().map((entity) -> {
            GoodsTrolleyListRes res = new GoodsTrolleyListRes();
            BeanUtils.copyProperties(entity, res);
            Goods goods = goodsService.detail(entity.getGoodsId());
            try {
                GoodsNorms goodsNorms = goodsNormsService.detail(entity.getNormsId());
                res.setPic(goodsNorms.getPic());
                res.setPrice(goodsNorms.getPrice());
                res.setGoodsName(goods.getName());
                res.setNormsName(goodsNorms.getName());
                res.setNormStatus(goodsNorms.getStatus());
                res.setInventory(goodsNorms.getInventory());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}