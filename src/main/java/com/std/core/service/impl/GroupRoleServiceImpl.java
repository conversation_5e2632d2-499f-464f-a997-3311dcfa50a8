package com.std.core.service.impl;

import com.std.core.service.IRoleService;
import com.std.core.pojo.domain.GroupRole;
import com.std.core.mapper.GroupRoleMapper;
import com.std.core.service.IGroupRoleService;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class GroupRoleServiceImpl implements IGroupRoleService {

    @Resource
    private GroupRoleMapper groupRoleMapper;

    @Autowired
    private IRoleService roleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotRoles(Long groupId, List<Long> roleIdList) {
        //删除之前的数据
        removeByGroup(groupId);

        //添加新数据
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            List<GroupRole> groupRoleList = new ArrayList<GroupRole>();
            for (Long roleId : roleIdList) {
                GroupRole groupRole = new GroupRole(groupId, roleId);
                groupRoleList.add(groupRole);
            }
            groupRoleMapper.batchInsert(groupRoleList);
        }
    }

    @Override
    public void removeByGroup(Long groupId) {
        groupRoleMapper.deleteByGroupId(groupId);
    }

    @Override
    public List<GroupRole> list(Long groupId) {
        GroupRole groupRole = new GroupRole();

        groupRole.setGroupId(groupId);

        List<GroupRole> groupRoleList = groupRoleMapper.selectByCondition(groupRole);

        if (CollectionUtils.isNotEmpty(groupRoleList)) {
            for (GroupRole groupRole1 : groupRoleList) {
                groupRole1.setRole(roleService.detail(groupRole1.getRoleId()));
            }
        }

        return groupRoleList;
    }

    @Override
    public long getTotalCount(Long roleId) {
        GroupRole condition = new GroupRole();
        condition.setRoleId(roleId);
        return groupRoleMapper.selectCountByCondition(condition);
    }
}
