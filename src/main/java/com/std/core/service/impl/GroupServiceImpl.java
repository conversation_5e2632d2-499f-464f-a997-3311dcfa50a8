package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.GroupMapper;
import com.std.core.pojo.domain.Group;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GroupCreateReq;
import com.std.core.pojo.request.GroupListReq;
import com.std.core.pojo.request.GroupModifyReq;
import com.std.core.pojo.request.GroupPageReq;
import com.std.core.service.IGroupService;
import com.std.core.util.IdGeneratorUtil;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class GroupServiceImpl implements IGroupService {

    @Resource
    private GroupMapper groupMapper;

    @Override
    public void create(User user, GroupCreateReq req) {
        groupValidator(req.getParentId(), user.getCompanyId());

        Group group = new Group();
        group.setId(IdGeneratorUtil.generator());
        group.setParentId(req.getParentId());
        group.setKind(user.getKind());
        group.setName(req.getName());
        group.setRemark(req.getRemark());

        group.setOrderNo(req.getOrderNo());
        group.setCreater(user.getLoginName());
        group.setCreateTime(new Date());
        group.setCompanyId(user.getCompanyId());

        groupMapper.insertSelective(group);
    }

    @Override
    public void remove(Long id) {
        groupMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void modify(User user, GroupModifyReq req) {
        if (req.getId().equals(req.getParentId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "父编号不能是自己");
        }

        Group group = info(req.getId());
        groupValidator(req.getParentId(), group.getCompanyId());

        group.setParentId(req.getParentId());
        group.setName(req.getName());
        group.setRemark(req.getRemark());
        group.setUpdater(user.getLoginName());
        group.setUpdateTime(new Date());
        group.setOrderNo(req.getOrderNo());

        groupMapper.updateByPrimaryKeySelective(group);
    }

    private void groupValidator(Long parentId, Long companyId) {
        if (parentId != null) {
            Group parentGroup = info(parentId);
            if (!companyId.equals(parentGroup.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前用户组与父用户组不在同一单位内");
            }
        }
    }

    @Override
    public Group info(Long id) {
        Group group = groupMapper.selectByPrimaryKey(id);
        if (null == group) {
            throw new BizException(
                    ECommonErrorCode.E500003.getCode(), ECommonErrorCode.E500003.getValue(), "用户组", id);
        }
        return group;
    }

    @Override
    public List<Group> list(GroupListReq req, User user) {
        Group condition = new Group();

        condition.setParentId(req.getParentId());
        condition.setKind(req.getKind());
        condition.setName(req.getName());
        condition.setCompanyId(user.getCompanyId());
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Group.class));

        return groupMapper.selectByCondition(condition);
    }

    @Override
    public List<Group> subOwnerList(GroupListReq req, User user) {
        Group condition = new Group();

        condition.setParentId(req.getParentId());
        condition.setKind(req.getKind());
        condition.setName(req.getName());
        if (StringUtils.isNotBlank(req.getCompanyId())) {
            condition.setCompanyId(Long.parseLong(req.getCompanyId()));
        }
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Group.class));

        return groupMapper.selectByCondition(condition);
    }

    @Override
    public List<Group> page(GroupPageReq req, User user) {
        Group condition = new Group();
        if (!EUserKind.OPS.getCode().equals(user.getKind())) {
            condition.setCompanyId(user.getCompanyId());
        }
        condition.setParentId(req.getParentId());
        condition.setKind(req.getKind());
        condition.setName(req.getName());
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Group.class));

        return groupMapper.selectByCondition(condition);
    }

    @Override
    public List<Group> listGroup(Group condition) {
        return groupMapper.selectByCondition(condition);
    }

    @Override
    public List<Group> listByToken(User user) {
        Group condition = new Group();
        condition.setCompanyId(user.getCompanyId());
        return groupMapper.selectByCondition(condition);
    }

    @Override
    public List<Group> listOtherGroup(Long id, User user) {
        Group condition = new Group();
        condition.setId(id);
        condition.setCompanyId(user.getCompanyId());
        condition.setKind(user.getKind());
        return groupMapper.listOtherGroup(condition);
    }
}
