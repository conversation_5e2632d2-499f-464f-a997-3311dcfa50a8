package com.std.core.service.impl;

import com.github.pagehelper.PageHelper;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.enums.ELanguage;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.JourMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Jour;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.JourListReq;
import com.std.core.pojo.request.JourMyPageReq;
import com.std.core.pojo.request.JourPageReq;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 账户流水ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-25 15:52
 */
@Service
public class JourServiceImpl implements IJourService {

    @Resource
    private JourMapper jourMapper;

    @Resource
    private ILanguageResourceService languageResourceService;

    @Resource
    private IUserService userService;

    @Resource
    private IAccountService accountService;

    @Resource
    private IChargeService chargeService;

    @Override
    public <T> String addJour(Account dbAccount, BigDecimal transAmount, String channelType,
                              String channelOrder, T refNo, Long refUserId, String bizCategory, String bizCategoryNote,
                              String bizType, String bizNote, String remark, String enRemark) {
        return doAddJour(dbAccount, EJourType.BALANCE.getCode(), transAmount, channelType,
                channelOrder, refNo, refUserId, bizCategory, bizCategoryNote, bizType, bizNote, remark,
                enRemark);
    }

    @Override
    public String addFrozenJour(Account dbAccount, BigDecimal transAmount, String channelType,
                                String refNo, String bizCategory, String bizCategoryNote, String bizType,
                                String bizNote, String remark, String enRemark) {
        return doAddJour(dbAccount, EJourType.FROZEN.getCode(), transAmount.negate(), channelType,
                null, refNo, null, bizCategory, bizCategoryNote, bizType, bizNote, remark, enRemark);
    }

    @Override
    public String addUnFrozenJour(Account dbAccount, BigDecimal transAmount, String channelType,
                                  String refNo, String bizCategory, String bizCategoryNote, String bizType,
                                  String bizNote, String remark, String enRemark) {
        return doAddJour(dbAccount, EJourType.FROZEN.getCode(), transAmount, channelType,
                null, refNo, null, bizCategory, bizCategoryNote, bizType, bizNote, remark, enRemark);
    }

    private <T> String doAddJour(Account dbAccount, String type, BigDecimal transAmount,
                                 String channelType, String channelOrder, T refNo, Long refUserId, String bizCategory,
                                 String bizCategoryNote, String bizType, String bizNote, String remark,
                                 String enRemark) {
        if (StringUtils.isBlank(refNo.toString())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "系统内部参考订单号不能为空");
        }

        if (transAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "新增流水变动金额不能为0");
        }

        Jour data = new Jour();
        Long id = IdGeneratorUtil.generator();

        data.setId(id);
        data.setType(type);
        data.setUserId(dbAccount.getUserId());
        data.setAccountNumber(dbAccount.getAccountNumber());
        data.setAccountType(dbAccount.getType());

        data.setCurrency(dbAccount.getCurrency());
        data.setBizCategory(bizCategory);
        data.setBizCategoryNote(bizCategoryNote);
        data.setBizType(bizType);
        data.setBizNote(bizNote);
        data.setRefNo(refNo.toString());
        data.setRefUserId(refUserId);

        data.setTransAmount(transAmount);
        data.setPreAmount(dbAccount.getAmount().subtract(dbAccount.getFrozenAmount()));
        data.setPostAmount(data.getPreAmount().add(transAmount));
        data.setPrevJourCode(dbAccount.getLastOrder());
        data.setStatus(EJourStatus.To_Check.getCode());

        data.setRemark(remark);
        data.setCreateDatetime(new Date());
        jourMapper.insertSelective(data);

//        languageResourceService.create(ELanguageResource.Jour.Jour.getCode(),
//                ELanguageResource.Jour.remark.getCode(), id, remark, enRemark);

        return id.toString();
    }

    /**
     * 详情查询账户流水
     *
     * @param id 主键ID
     * @return 账户流水对象
     */
    @Override
    public Jour detail(Long id, String language) {
        Jour jour = jourMapper.selectByPrimaryKey(id);
        if (null == jour) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        languageResourceService.languageParse(jour, language, id);

        return jour;
    }

    /**
     * 分页查询账户流水
     *
     * @param req 分页查询账户流水入参
     * @return 分页账户流水对象
     */
    @Override
    public List<Jour> page(JourPageReq req, User operator) {
        Jour condition = EntityUtils.copyData(req, Jour.class);
//        if (StringUtils.isNotBlank(req.getBizCategory()) && req.getBizCategory().contains(",")) {
//            condition.setBizCategoryList(Arrays.asList(req.getBizCategory().split(",")));
//            condition.setBizCategory(null);
//        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize(),
                SqlUtil.parseSort(req.getSort(), Jour.class));

        List<Jour> list = jourMapper.selectByCondition(condition);

        for (Jour jour : list) {
            jour.setUser(userService.selectSummaryInfo(jour.getUserId()));
        }

        return list;
    }


    /**
     * 列表查询账户流水
     *
     * @param req 列表查询账户流水入参
     * @return 列表账户流水对象
     */
    @Override
    public List<Jour> list(JourListReq req) {
        Jour condition = EntityUtils.copyData(req, Jour.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Jour.class));
        if (StringUtils.isNotBlank(req.getBizCategory()) && req.getBizCategory().contains(",")) {
            condition.setBizCategoryList(Arrays.asList(req.getBizCategory().split(",")));
            condition.setBizCategory(null);
        }

        List<Jour> jours = jourMapper.selectByCondition(condition);
        for (Jour jour : jours) {
            init(jour, req.getLanguage());
            languageResourceService.languageParse(jour, req.getLanguage(), jour.getId());
        }

        return jours;
    }

    @Override
    public BigDecimal selectTotalAmount(String type, String accountType, String currency,
                                        List<String> bizTypeList) {
        Jour jour = new Jour();
        jour.setType(type);
        jour.setAccountType(accountType);
        jour.setCurrency(currency);
        jour.setBizTypeList(bizTypeList);
        return jourMapper.selectTotalAmount(jour);
    }

    @Override
    public List<Jour> selectListDetailByJour(Jour condition) {
        return jourMapper.selectByCondition(condition);
    }

    private void init(Jour jour, String language) {
        if (null != jour.getRefUserId()) {
            jour.setRefUserInfo(userService.selectSummaryInfo(jour.getRefUserId()));
        }
        if (ELanguage.EN.getCode().equals(language)) {
            jour.setBizCategoryNote(jour.getBizCategory());
            jour.setBizNote(jour.getBizType());
        }
    }

    /**
     * 更改账户流水状态
     */
    @Override
    public void updateStatus(Account dbAccount, Jour jour, BigDecimal transAmount, String status) {

        Jour jourDD = new Jour();
        jourDD.setId(jour.getId());
        jourDD.setStatus(status);
        // 如果是入账或者出账成功
        if (EJourStatus.IN_ACCOUNT.getCode().equals(status) || EJourStatus.OUT_ACCOUNT_SUCCESS.getCode().equals(status)) {
            // 更改流水中账户结余和
            jourDD.setPreAmount(dbAccount.getAmount());
            jourDD.setPostAmount(dbAccount.getAmount().add(jour.getTransAmount()));
            jourDD.setArriveAccountTime(new Date());
        } else if (EJourStatus.TO_IN_ACCOUNT.getCode().equals(status) && transAmount != null) {
            BigDecimal serviceCharge = BigDecimal.ZERO;
            if (jour.getServiceCharge() != null && jour.getServiceCharge().compareTo(BigDecimal.ZERO) > 0) {
                serviceCharge = (transAmount.divide(jour.getTransAmount(), 9, BigDecimal.ROUND_HALF_UP)
                        .multiply(jour.getServiceCharge())).setScale(2, BigDecimal.ROUND_HALF_UP);

                serviceCharge = jour.getServiceCharge().subtract(serviceCharge);
                jourDD.setServiceCharge(serviceCharge);
            }
            jourDD.setTransAmount(jour.getTransAmount().subtract(transAmount));
            jourDD.setTotalSettleAmount(jour.getTotalSettleAmount().subtract(transAmount));
        }

        jourMapper.updateByPrimaryKeySelective(jourDD);
    }

    /**
     * 得到单条流水记录
     */
    @Override
    public Jour getJour(Long userId, String refNo, String status, String currency) {
        Jour jour = null;
        Jour condition = new Jour();
        condition.setUserId(userId);
        condition.setCurrency(currency);
        condition.setRefNo(refNo);
        condition.setStatus(status);
        List<Jour> jourList = jourMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(jourList)) {
            jour = jourList.get(0);
        }
        return jour;
    }


    @Override
    public List<Jour> userPage(JourMyPageReq request, User operator) {
        Jour condition = EntityUtils.copyData(request, Jour.class);
        condition.setUserId(operator.getId());
        condition.setType(EJourType.BALANCE.getCode());
        condition.setAccountType(EAccountType.Customer.getCode());
        condition.setOrderBy("t.id desc");

        if (EBoolean.NO.getCode().equals(request.getChangeType())) {
            condition.setIncome("1");
        } else if (EBoolean.YES.getCode().equals(request.getChangeType())) {
            condition.setExpenditure("1");
        }
        List<Jour> jourList = jourMapper.selectByCondition(condition);

        return jourList;
    }


    @Override
    public List<Jour> selectList(Jour jour) {
        return jourMapper.selectByCondition(jour);
    }


    @Override
    public void updateJour(Jour jour) {
        if (jour != null) {
            jourMapper.updateByPrimaryKeySelective(jour);
        }
    }
}