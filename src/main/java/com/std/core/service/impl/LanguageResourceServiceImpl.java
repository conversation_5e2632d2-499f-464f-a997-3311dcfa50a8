package com.std.core.service.impl;

import com.std.common.enums.ELanguage;
import com.std.common.exception.BizException;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.LanguageResourceMapper;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.LanguageResource;
import com.std.core.service.ILanguageResourceService;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 语言资源ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-02 10:59
 */
@Slf4j
@Service
public class LanguageResourceServiceImpl implements ILanguageResourceService {

    @Resource
    private LanguageResourceMapper languageResourceMapper;

    /**
     * 新增语言资源
     */
    @Override
    public <T> void create(String table, String column, T refId, String znData, String enData) {
        if (StringUtils.isBlank(znData) || StringUtils.isBlank(enData)) {
            return;
        }

        LanguageResource languageResource = wapper(table, refId, column, znData, enData);
        languageResourceMapper.insertSelective(languageResource);
    }

    @Override
    public <T> void replace(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull String znData, @NotNull String enData) {
        if (StringUtils.isBlank(znData) || StringUtils.isBlank(enData)) {
            return;
        }

        LanguageResource languageResource = wapper(table, refId, column, znData, enData);
        languageResourceMapper.replaceSelective(languageResource);
    }

    @Override
    public <T> void create(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull ELanguage language, @NotNull String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }

        LanguageResource languageResource = wapper(table, refId, column, language, data);
        languageResourceMapper.insertSelective(languageResource);
    }

    @Override
    public <T> void create(@NotNull T data, @NotNull T refId, @NotNull ELanguage language,
            @NotEmpty List<String> columnList) {
        if (null == refId || CollectionUtils.isEmpty(columnList)) {
            return;
        }

        for (String column : columnList) {
            String getMethod = "get".concat(StringUtils.upperCase(column.substring(0, 1)))
                    .concat(column.substring(1));

            try {
                Method method = data.getClass().getMethod(getMethod);
                String columnData = String.valueOf(method.invoke(data));

                create(data.getClass().getSimpleName(), column, refId, language, columnData);

            } catch (Exception e) {
                log.error("语言数据存储失败:{}:{}:{}", data.getClass().getSimpleName(),
                        refId, column);
            }
        }
    }

    @Override
    public <T> void remove(String table, T refId) {
        LanguageResource languageResource = new LanguageResource();
        languageResource.setTable(table);
        languageResource.setRefId(refId.toString());
        languageResourceMapper.deleteByRef(languageResource);
    }

    /**
     * 删除语言资源
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Integer id) {
        languageResourceMapper.deleteByPrimaryKey(id);
    }

    @Override
    public <T> void modify(@NotNull String table, @NotNull String column, @NotNull T refId,
            @NotNull ELanguage language, @NotNull String data) {
        LanguageResource languageResource = detail(table, refId, column);
        if (null == languageResource) {
            create(table, column, refId, language, data);
        } else {
            if (ELanguage.ZH_CN.getCode().equals(language.getCode())) {
                languageResource.setZnData(data);
            } else if (ELanguage.EN.getCode().equals(language.getCode())) {
                languageResource.setEnData(data);
            }
            languageResourceMapper.updateByPrimaryKeySelective(languageResource);
        }
    }

    @Override
    public <T> void modify(@NotNull T data, @NotNull T refId, @NotNull ELanguage language,
            @NotEmpty List<String> columnList) {
        for (String column : columnList) {
            String getMethod = "get".concat(StringUtils.upperCase(column.substring(0, 1)))
                    .concat(column.substring(1));

            try {
                Method method = data.getClass().getMethod(getMethod);
                String columnData = String.valueOf(method.invoke(data));

                modify(data.getClass().getSimpleName(), column, refId, language, columnData);

            } catch (Exception e) {
                log.error("语言数据存储失败:{}:{}:{}", data.getClass().getSimpleName(),
                        refId, column);
            }
        }
    }

    @Override
    public <T> void languageParse(T data, String language, T refId) {
        if (StringUtils.isBlank(language)) {
            return;
        }

        List<LanguageResource> languageResourceList = list(data.getClass().getSimpleName(), refId);

        if (CollectionUtils.isEmpty(languageResourceList)) {
            return;
        }

        for (LanguageResource languageResource : languageResourceList) {
            String setMethod = "set"
                    .concat(StringUtils.upperCase(languageResource.getColumn().substring(0, 1)))
                    .concat(languageResource.getColumn().substring(1));

            try {
                String columnData = languageResource.getZnData();
                if (ELanguage.EN.getCode().equals(language)) {
                    columnData = languageResource.getEnData();
                }

                if (StringUtils.isBlank(columnData) || "null".equals(columnData)) {
                    continue;
                }

                Method method = data.getClass().getMethod(setMethod, String.class);
                method.invoke(data, columnData);
            } catch (Exception e) {
                log.error("语言数据翻译失败:{}:{}:{}", languageResource.getTable(),
                        languageResource.getColumn(), languageResource.getRefId());
            }
        }
    }

    private <T> LanguageResource wapper(String table, T refId, String column, String znData, String enData) {
        LanguageResource languageResource = new LanguageResource();
        languageResource.setTable(table);
        languageResource.setRefId(refId.toString());
        languageResource.setColumn(column);
        languageResource.setZnData(znData);
        languageResource.setEnData(enData);
        return languageResource;
    }

    private <T> LanguageResource wapper(String table, T refId, String column, ELanguage language,
            String data) {
        LanguageResource languageResource = new LanguageResource();
        languageResource.setTable(table);
        languageResource.setRefId(refId.toString());
        languageResource.setColumn(column);

        String znData = null;
        String enData = null;

        if (ELanguage.ZH_CN.getCode().equals(language.getCode())) {
            znData = data;
        } else if (ELanguage.EN.getCode().equals(language.getCode())) {
            enData = data;
        }

        languageResource.setZnData(znData);
        languageResource.setEnData(enData);
        return languageResource;
    }

    /**
     * 详情查询语言资源
     *
     * @param id 主键ID
     * @return 语言资源对象
     */
    @Override
    public LanguageResource detail(Integer id) {
        LanguageResource languageResource = languageResourceMapper.selectByPrimaryKey(id);
        if (null == languageResource) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return languageResource;
    }

    @Override
    public String translate(@NotEmpty List<Dict> dictList, @NotNull String language,
            @NotNull String key) {
        if (CollectionUtils.isEmpty(dictList) || StringUtils.isBlank(language) || StringUtils
                .isBlank(key)) {
            return key;
        }

        String[] keys = key.split(",");
        List<String> translateList = new ArrayList<>();
        for (String tmpKey : keys) {
            List<Dict> subDict = dictList.stream().filter(t -> t.getKey().equals(tmpKey))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subDict)) {
                if (ELanguage.ZH_CN.getCode().equals(language)) {
                    translateList.add(subDict.get(0).getValue());
                } else {
                    translateList.add(subDict.get(0).getValue());
                }
            }
        }

        return CollectionUtils.isEmpty(translateList) ? key
                : String.join(",", translateList);
    }

    private <T> LanguageResource detail(String table, T refId, String column) {
        LanguageResource condition = new LanguageResource();
        condition.setTable(table);
        condition.setRefId(refId.toString());
        condition.setColumn(column);
        List<LanguageResource> languageResourceList = languageResourceMapper
                .selectByCondition(condition);

        LanguageResource languageResource = null;
        if (CollectionUtils.isNotEmpty(languageResourceList)) {
            languageResource = languageResourceList.get(0);
        }

        return languageResource;
    }

    /**
     * 列表查询语言资源
     *
     * @return 列表语言资源对象
     */
    @Override
    public <T> List<LanguageResource> list(String table, T refId) {
        LanguageResource condition = new LanguageResource();
        condition.setTable(table);
        condition.setRefId(refId.toString());

        return languageResourceMapper.selectByCondition(condition);
    }

}