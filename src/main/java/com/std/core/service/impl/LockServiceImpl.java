package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.LockMapper;
import com.std.core.pojo.domain.Lock;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LockCreateReq;
import com.std.core.pojo.request.LockListReq;
import com.std.core.pojo.request.LockModifyReq;
import com.std.core.pojo.request.LockPageReq;
import com.std.core.service.ILockService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 业务锁ServiceImpl
 *
 * <AUTHOR> xieyj
 * @since : 2020-06-12 00:27
 */
@Service
public class LockServiceImpl implements ILockService {

    @Resource
    private LockMapper lockMapper;

    /**
     * 新增业务锁
     *
     * @param req      新增业务锁入参
     * @param operator 操作人
     */
    @Override
    public void create(LockCreateReq req, User operator) {
        Lock lock = EntityUtils.copyData(req, Lock.class);
        lockMapper.insertSelective(lock);
    }

    @Override
    public void create(String bizType, String refCode) {

        try {
            Lock lock = new Lock();
            lock.setBizType(bizType);
            lock.setRefCode(refCode);
            lock.setCreateDatetime(new Date());
            lockMapper.insertSelective(lock);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), bizType + "重复插入数据" + refCode);
        }

    }

    /**
     * 删除业务锁
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Integer id) {
        lockMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改业务锁
     *
     * @param req      修改业务锁入参
     * @param operator 操作人
     */
    @Override
    public void modify(LockModifyReq req, User operator) {
        Lock lock = EntityUtils.copyData(req, Lock.class);
        lockMapper.updateByPrimaryKeySelective(lock);
    }

    /**
     * 详情查询业务锁
     *
     * @param id 主键ID
     * @return 业务锁对象
     */
    @Override
    public Lock detail(Integer id) {
        Lock lock = lockMapper.selectByPrimaryKey(id);
        if (null == lock) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return lock;
    }

    /**
     * 分页查询业务锁
     *
     * @param req 分页查询业务锁入参
     * @return 分页业务锁对象
     */
    @Override
    public List<Lock> page(LockPageReq req) {
        Lock condition = EntityUtils.copyData(req, Lock.class);

        return lockMapper.selectByCondition(condition);
    }

    /**
     * 列表查询业务锁
     *
     * @param req 列表查询业务锁入参
     * @return 列表业务锁对象
     */
    @Override
    public List<Lock> list(LockListReq req) {
        Lock condition = EntityUtils.copyData(req, Lock.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Lock.class));

        return lockMapper.selectByCondition(condition);
    }

    @Override
    public void insert(Lock lock) {
        lockMapper.insertSelective(lock);
    }

}