package com.std.core.service.impl;

import com.std.core.enums.EResourceType;
import com.std.core.mapper.MenuActionMapper;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.domain.MenuAction;
import com.std.core.pojo.domain.PermissionRole;
import com.std.core.pojo.request.AllotActionReq;
import com.std.core.service.IActionService;
import com.std.core.service.IMenuActionService;
import com.std.core.service.IPermissionRoleService;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MenuActionServiceImpl implements IMenuActionService {

    @Resource
    private MenuActionMapper menuActionMapper;

    @Autowired
    private IActionService actionService;

    @Autowired
    private IPermissionRoleService permissionRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotMenuAction(AllotActionReq req) {
        // 删除之前的数据
        menuActionMapper.deleteByMenuId(req.getId());

        if (CollectionUtils.isEmpty(req.getActionIdList())) {
            return;
        }

        List<MenuAction> menuActionList = new ArrayList<MenuAction>();
        for (Long actionId : req.getActionIdList()) {
            MenuAction menuAction = new MenuAction(req.getId(), actionId);
            menuActionList.add(menuAction);
        }

        // 产生菜单接口
        menuActionMapper.batchInsert(menuActionList);

        // 获取对应角色列表，重新更新角色接口权限
        List<PermissionRole> permissionRoleList = permissionRoleService.listByMenu(req.getId());
        for (PermissionRole permissionRole : permissionRoleList) {
            // 删除角色对应菜单按钮，重新插入
            permissionRoleService.removeByCondition(
                    permissionRole.getRoleId(), EResourceType.ACTION.getCode());

            // 最新角色接口权限
            List<PermissionRole> btnPermissionRoleList = new ArrayList<PermissionRole>();

            // 获取角色下的菜单权限
            List<PermissionRole> menuPermissionRoleList =
                    permissionRoleService.list(
                            permissionRole.getRoleId(),
                            EResourceType.CLIENT.getCode(),
                            EResourceType.MENU.getCode(),
                            EResourceType.BUTTON.getCode(),
                            EResourceType.FUNC_MENU.getCode());

            // 根据菜单和按钮重新排布
            for (PermissionRole menuPermissionRole : menuPermissionRoleList) {
                List<Long> actionList = null;
                if (req.getId().equals(menuPermissionRole.getResourceId())) {
                    actionList = req.getActionIdList();
                } else {
                    actionList = listActionByMenuId(menuPermissionRole.getResourceId());
                }
                for (Long actionId : actionList) {
                    PermissionRole pRole =
                            new PermissionRole(
                                    menuPermissionRole.getRoleId(), actionId,
                                    EResourceType.ACTION.getCode());

                    if (!btnPermissionRoleList.contains(pRole)) {
                        btnPermissionRoleList.add(pRole);
                    }
                }
            }

            // 更新菜单接口权限
            permissionRoleService.batchCreate(btnPermissionRoleList);
        }
    }

    @Override
    public MenuAction info(Long menuId, Long actionId) {
        MenuAction condition = new MenuAction(menuId, actionId);

        List<MenuAction> menuActionList = menuActionMapper.selectByCondition(condition);
        MenuAction menuAction = null;

        if (CollectionUtils.isNotEmpty(menuActionList)) {
            menuAction = menuActionList.get(0);
            Action action = actionService.detail(menuAction.getActionId());
            menuAction.setAction(action);
        }

        return menuAction;
    }

    @Override
    public List<Long> listActionByMenuId(Long menuId) {
        MenuAction menuAction = new MenuAction();
        menuAction.setMenuId(menuId);
        List<MenuAction> menuActionList = menuActionMapper.selectByCondition(menuAction);

        List<Long> actionIdList = new ArrayList<Long>();
        if (CollectionUtils.isNotEmpty(menuActionList)) {
            for (MenuAction domain : menuActionList) {
                actionIdList.add(domain.getActionId());
            }
        }

        return actionIdList;
    }

    @Override
    public List<MenuAction> list(Long menuId) {
        MenuAction menuAction = new MenuAction();
        menuAction.setMenuId(menuId);
        return menuActionMapper.selectByCondition(menuAction);
    }
}
