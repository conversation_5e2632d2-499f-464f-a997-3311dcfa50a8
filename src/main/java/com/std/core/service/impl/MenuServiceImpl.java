package com.std.core.service.impl;

import static java.util.stream.Collectors.toList;

import com.std.common.utils.SqlUtil;
import com.std.common.utils.StringValidator;
import com.std.core.enums.EClientMenu;
import com.std.core.mapper.ActionMapper;
import com.std.core.mapper.MenuMapper;
import com.std.core.pojo.domain.Action;
import com.std.core.pojo.domain.Menu;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MenuCreateReq;
import com.std.core.pojo.request.MenuListReq;
import com.std.core.pojo.request.MenuModifyReq;
import com.std.core.pojo.request.MenuPageReq;
import com.std.core.service.IMenuService;
import com.std.core.util.IdGeneratorUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class MenuServiceImpl implements IMenuService {

    @Resource
    private MenuMapper menuMapper;

    @Resource
    private ActionMapper actionMapper;

    @Override
    public Long create(MenuCreateReq req) {
        Menu menu = new Menu();

        menu.setId(IdGeneratorUtil.generator());
        menu.setParentId(req.getParentId());
        menu.setType(req.getType());
        Menu parentMenu = detail(req.getParentId());
        menu.setKind(parentMenu.getKind());
        menu.setName(req.getName());

        menu.setLogo(req.getLogo());
        menu.setUrl(req.getUrl());
        menu.setOrderNo(Long.parseLong(req.getOrderNo()));
        menu.setLocation(req.getLocation());
        menu.setRemark(req.getRemark());
        menuMapper.insertSelective(menu);

        return menu.getId();
    }

    @Override
    public void remove(Long id) {
        menuMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void modify(MenuModifyReq req) {
        Menu menu = new Menu();

        menu.setId(req.getId());
        menu.setType(req.getType());
        menu.setName(req.getName());
        menu.setLogo(req.getLogo());
        menu.setUrl(req.getUrl());
        menu.setRemark(req.getRemark());

        if (null != req.getParentId()) {
            menu.setParentId(req.getParentId());
        }
        menu.setOrderNo(StringValidator.toLong(req.getOrderNo()));
        menu.setLocation(req.getLocation());
        menuMapper.updateByPrimaryKeySelective(menu);
    }

    @Override
    public Menu detail(Long id) {
        return menuMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<Menu> list(MenuListReq req) {
        Menu condition = new Menu();

        condition.setParentId(req.getParentId());
        condition.setTypeList(req.getTypeList());
        condition.setKind(req.getKind());
        condition.setName(req.getName());
        condition.setUrl(req.getUrl());
        condition.setLocation(req.getLocation());
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Menu.class));

        return menuMapper.selectByCondition(condition);
    }

    @Override
    public List<Menu> page(MenuPageReq req) {
        Menu menu = new Menu();

        menu.setParentId(req.getParentId());
        menu.setTypeList(req.getTypeList());
        menu.setKind(req.getKind());
        menu.setName(req.getName());
        menu.setUrl(req.getUrl());
        menu.setLocation(req.getLocation());

        return menuMapper.selectByCondition(menu);
    }

    @Override
    public List<Menu> listConditionByRoles(List<Long> roleList, Long menuId, List<String> typeList) {

        List<Menu> menuList = listByConditionByTopMenuId(menuId);

        Menu condition = new Menu();
        condition.setRoleList(roleList);
        condition.setTypeList(typeList);
        List<Menu> roleMenuList = menuMapper.selectByConditionByRoleType(condition);

        menuList.removeIf(
                menu ->
                        CollectionUtils.isEmpty(
                                roleMenuList
                                        .parallelStream()
                                        .filter(tmp -> tmp.getId().equals(menu.getId()))
                                        .collect(toList())));

        return menuList;
    }

    @Override
    public List<Menu> listMenuByKind(String kind) {
        Menu menu = new Menu();
        menu.setKind(kind);
        return menuMapper.selectByCondition(menu);
    }

    @Override
    public List<Menu> listByConditionByTopMenuId(Long menuId) {
        List<Menu> menuList = new ArrayList<>();
        List<String> path = new ArrayList<>();

        Menu menu = menuMapper.selectByPrimaryKey(menuId);
        menu.setDepth("1");
        path.add(menu.getId().toString());
        menu.setPath(StringUtils.join(path).replace("[", "{").replace("]", "}").replace(" ", ""));
        menuList.add(menu);

        selectByConditionByTopMenuId(menuList, menuId, 2, path);

        menuList =
                menuList
                        .parallelStream()
                        .sorted(Comparator.comparing(Menu::getDepth).thenComparing(Menu::getOrderNo))
                        .collect(toList());

        return menuList;
    }

    @Override
    public List<Menu> listInfoByTopMenuId(User operateUser) {
        List<Menu> menuList =
                listByConditionByTopMenuId(EClientMenu.topMenuIdByUser(operateUser.getKind()));
        for (Menu menu : menuList) {
            List<Action> actionList = actionMapper.selectByConditionByMenu(menu.getId());
            menu.setActionList(actionList);
        }

        return menuList;
    }

    private void selectByConditionByTopMenuId(
            List<Menu> menuList, Long menuId, Integer depth, List<String> path) {
        Menu condition = new Menu();
        condition.setParentId(menuId);
        List<Menu> childMenu = menuMapper.selectByCondition(condition);

        for (Menu menu : childMenu) {
            menu.setDepth(depth.toString());

            List<String> currectPath = new ArrayList<>(path);
            currectPath.add(menu.getId().toString());

            menu.setPath(
                    StringUtils.join(currectPath).replace("[", "{").replace("]", "}").replace(" ", ""));
            selectByConditionByTopMenuId(menuList, menu.getId(), depth + 1, currectPath);
        }

        if (childMenu.size() > 0) {
            menuList.addAll(childMenu);
        }
    }
}
