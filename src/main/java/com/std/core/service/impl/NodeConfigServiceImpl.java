package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EIncomeAmountType;
import com.std.core.mapper.NodeConfigMapper;
import com.std.core.pojo.domain.NodeConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NodeConfigCreateReq;
import com.std.core.pojo.request.NodeConfigListReq;
import com.std.core.pojo.request.NodeConfigModifyReq;
import com.std.core.pojo.request.NodeConfigPageReq;
import com.std.core.service.INodeConfigService;
import java.math.BigDecimal;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 星级节点配置ServiceImpl
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 15:24
 */
@Service
public class NodeConfigServiceImpl implements INodeConfigService {

    @Resource
    private NodeConfigMapper nodeConfigMapper;



    /**
     * 新增星级节点配置
     *
     * @param req 新增星级节点配置入参
     * @param operator 操作人
     */
    @Override
    public void create(NodeConfigCreateReq req, User operator) {
        NodeConfig nodeConfig = EntityUtils.copyData(req, NodeConfig.class);
        nodeConfigMapper.insertSelective(nodeConfig);
    }

    /**
     * 删除星级节点配置
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        nodeConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改星级节点配置
     *
     * @param req 修改星级节点配置入参
     * @param operator 操作人
     */
    @Override
    public void modify(NodeConfigModifyReq req, User operator) {
        NodeConfig nodeConfig = EntityUtils.copyData(req, NodeConfig.class);
        nodeConfigMapper.updateByPrimaryKeySelective(nodeConfig);
    }

    /**
     * 详情查询星级节点配置
     *
     * @param id 主键ID
     * @return 星级节点配置对象
     */
    @Override
    public NodeConfig detail(Long id) {
        NodeConfig nodeConfig = nodeConfigMapper.selectByPrimaryKey(id);
        if (null == nodeConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return nodeConfig;
    }

    /**
     * 分页查询星级节点配置
     *
     * @param req 分页查询星级节点配置入参
     * @return 分页星级节点配置对象
     */
    @Override
    public List<NodeConfig> page(NodeConfigPageReq req) {
        NodeConfig condition = EntityUtils.copyData(req, NodeConfig.class);

        return nodeConfigMapper.selectByCondition(condition);
    }

    /**
     * 列表查询星级节点配置
     *
     * @param req 列表查询星级节点配置入参
     * @return 列表星级节点配置对象
     */
    @Override
    public List<NodeConfig> list(NodeConfigListReq req) {
        NodeConfig condition = EntityUtils.copyData(req, NodeConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), NodeConfig.class));

        return nodeConfigMapper.selectByCondition(condition);
    }

    @Override
    public List<NodeConfig> selectConfigList() {
        NodeConfig condition = new NodeConfig();
        condition.setOrderBy("t.level desc");
        return nodeConfigMapper.selectByCondition(condition);
    }

    @Override
    public BigDecimal getNodeFenchengRate(Long userId, Integer userLevel, EIncomeAmountType amountType) {
        BigDecimal rate = BigDecimal.ZERO;

        NodeConfig condition = new NodeConfig();
        condition.setLevel(userLevel);
        List<NodeConfig> list = nodeConfigMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            rate = list.get(0).getRate();
        }

        return rate;
    }

}