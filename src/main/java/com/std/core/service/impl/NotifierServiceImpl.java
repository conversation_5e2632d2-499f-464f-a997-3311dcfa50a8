package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.EmailUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.NotifierMapper;
import com.std.core.pojo.domain.Notifier;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.NotifierCreateReq;
import com.std.core.pojo.request.NotifierListReq;
import com.std.core.pojo.request.NotifierModifyReq;
import com.std.core.pojo.request.NotifierPageReq;
import com.std.core.service.INotifierService;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 通知人ServiceImpl
 *
 * <AUTHOR> LEO
 * @since : 2020-10-31 15:39
 */
@Service
public class NotifierServiceImpl implements INotifierService {

    @Resource
    private NotifierMapper notifierMapper;

    @Resource
    private ISmsOutService smsOutService;

    /**
     * 新增通知人
     *
     * @param req 新增通知人入参
     * @param operator 操作人
     */
    @Override
    public void create(NotifierCreateReq req, User operator) {
        Notifier notifier = EntityUtils.copyData(req, Notifier.class);

        if (req.getEndDate() == 0) {
            req.setEndDate(24);
        }
        // 结束时间必须大于开始时间
        if (req.getEndDate() <= req.getStartDate()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "结束时间必须大于开始时间");
        }

        //邮箱格式校验
        EmailUtil.checkEmail(req.getEmail());

        notifierMapper.insertSelective(notifier);
    }

    /**
     * 删除通知人
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Integer id) {
        notifierMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改通知人
     *
     * @param req 修改通知人入参
     * @param operator 操作人
     */
    @Override
    public void modify(NotifierModifyReq req, User operator) {
        Notifier notifier = EntityUtils.copyData(req, Notifier.class);

        if (req.getEndDate() == 0) {
            req.setEndDate(24);
        }
        // 结束时间必须大于开始时间
        if (req.getEndDate() <= req.getStartDate()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "结束时间必须大于开始时间");
        }

        //邮箱格式校验
        EmailUtil.checkEmail(req.getEmail());

        notifierMapper.updateByPrimaryKeySelective(notifier);
    }

    /**
     * 详情查询通知人
     *
     * @param id 主键ID
     * @return 通知人对象
     */
    @Override
    public Notifier detail(Integer id) {
        Notifier notifier = notifierMapper.selectByPrimaryKey(id);
        if (null == notifier) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return notifier;
    }

    /**
     * 分页查询通知人
     *
     * @param req 分页查询通知人入参
     * @return 分页通知人对象
     */
    @Override
    public List<Notifier> page(NotifierPageReq req) {
        Notifier condition = EntityUtils.copyData(req, Notifier.class);

        return notifierMapper.selectByCondition(condition);
    }

    /**
     * 列表查询通知人
     *
     * @param req 列表查询通知人入参
     * @return 列表通知人对象
     */
    @Override
    public List<Notifier> list(NotifierListReq req) {
        Notifier condition = EntityUtils.copyData(req, Notifier.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Notifier.class));

        return notifierMapper.selectByCondition(condition);
    }

    @Override
    public void turnOutToAdminMsg(Long id) {
        Calendar calendar=Calendar.getInstance();
        //24小时制
        int hour24=calendar.get(Calendar.HOUR_OF_DAY);
        Notifier condition =new Notifier();
        condition.setHour(hour24);
        //获取规定时间的通知人
        List<Notifier> notifierList = notifierMapper.selectByCondition(condition);

        for (Notifier notifier : notifierList) {
             //开始通知人
            if (StringUtils.isNotBlank(notifier.getPhone())){
                Map map = new HashMap();
                map.put("remark", "散取订单号:"+id);
                smsOutService.sendSmsOut(notifier.getPhone(), "SMS_205880604", JSON.toJSON(map).toString(), "CL-XFB", "CL-XFB");
            }else{
              //todo
            }
        }
    }
}