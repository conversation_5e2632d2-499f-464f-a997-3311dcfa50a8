package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EPayRecordBizStatus;
import com.std.core.enums.EPayRecordStatus;
import com.std.core.mapper.PayRecordMapper;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PayRecordListReq;
import com.std.core.pojo.request.PayRecordModifyReq;
import com.std.core.pojo.request.PayRecordPageReq;
import com.std.core.service.IPayRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 支付记录ServiceImpl
 *
 * <AUTHOR> LEO
 * @since : 2020-09-12 16:58
 */
@Service
public class PayRecordServiceImpl implements IPayRecordService {

    @Resource
    private PayRecordMapper payRecordMapper;

    /**
     * 新增支付记录
     */
    @Override
    public PayRecord create(Long id, Long userId, String payType, String payMethod, BigDecimal amount, String bizType, Long bizCode, String request) {
        PayRecord payRecord = new PayRecord();
        payRecord.setId(id);
        payRecord.setUserId(userId);
        payRecord.setPayType(payType);
        payRecord.setPayMethod(payMethod);
        payRecord.setAmount(amount);
        payRecord.setCreateTime(new Date());
        payRecord.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_0.getCode());
        payRecord.setBizType(bizType);
        payRecord.setBizCode(bizCode);
        payRecord.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_0.getCode());
        payRecord.setRequest(request);
        payRecordMapper.insertSelective(payRecord);
        return payRecord;
    }


    @Override
    public PayRecord create(Long userId, String payType, String payMethod, BigDecimal amount, String bizType, Long bizCode) {
        PayRecord payRecord = new PayRecord();
        payRecord.setId(bizCode);
        payRecord.setUserId(userId);
        payRecord.setPayType(payType);
        payRecord.setPayMethod(payMethod);
        payRecord.setAmount(amount);
        payRecord.setCreateTime(new Date());
        payRecord.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_0.getCode());
        payRecord.setBizType(bizType);
        payRecord.setBizCode(bizCode);
        payRecord.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_0.getCode());
        payRecordMapper.insertSelective(payRecord);
        return payRecord;
    }

    /**
     * 删除支付记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        payRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改支付记录
     *
     * @param req      修改支付记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(PayRecordModifyReq req, User operator) {
        PayRecord payRecord = EntityUtils.copyData(req, PayRecord.class);
        payRecordMapper.updateByPrimaryKeySelective(payRecord);
    }

    /**
     * 详情查询支付记录
     *
     * @param id 主键ID
     * @return 支付记录对象
     */
    @Override
    public PayRecord detail(Long id) {
        PayRecord payRecord = payRecordMapper.selectByPrimaryKey(id);


        return payRecord;
    }

    /**
     * 分页查询支付记录
     *
     * @param req 分页查询支付记录入参
     * @return 分页支付记录对象
     */
    @Override
    public List<PayRecord> page(PayRecordPageReq req) {
        PayRecord condition = EntityUtils.copyData(req, PayRecord.class);

        return payRecordMapper.selectByCondition(condition);
    }

    /**
     * 列表查询支付记录
     *
     * @param req 列表查询支付记录入参
     * @return 列表支付记录对象
     */
    @Override
    public List<PayRecord> list(PayRecordListReq req) {
        PayRecord condition = EntityUtils.copyData(req, PayRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), PayRecord.class));

        return payRecordMapper.selectByCondition(condition);
    }

}