package com.std.core.service.impl;

import com.std.core.enums.EResourceType;
import com.std.core.mapper.PermissionRoleMapper;
import com.std.core.pojo.domain.PermissionRole;
import com.std.core.service.IPermissionRoleService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-01-18 16:25
 */
@Service
public class PermissionRoleServiceImpl implements IPermissionRoleService {

    @Resource
    private PermissionRoleMapper permissionRoleMapper;

    @Override
    public void batchCreate(List<PermissionRole> permissionRoleList) {
        permissionRoleMapper.batchInsert(permissionRoleList);
    }

    /**
     * 分配角色权限 resourceIdList 为空 分配全部（此逻辑只试用后端代码中） resourceIdList 不为空 分配部分
     *
     * @param roleId 角色编号
     * @param menuIdList 菜单编号
     */
    @Override
    public void batchCreate(Long roleId, List<Long> menuIdList) {
        PermissionRole permissionRole = new PermissionRole();
        permissionRole.setRoleId(roleId);
        permissionRole.setMenuIdList(menuIdList);

        permissionRoleMapper.batchCreate(permissionRole);
    }

    @Override
    public void removeByRole(Long roleId) {
        permissionRoleMapper.deleteByRole(roleId);
    }

    @Override
    public void removeByCondition(Long roleId, String resourceType) {
        PermissionRole condition = new PermissionRole();
        condition.setRoleId(roleId);
        condition.setResourceType(resourceType);

        permissionRoleMapper.deleteByCondition(condition);
    }

    @Override
    public List<PermissionRole> list(Long roleId, String... resourceTypes) {
        PermissionRole condition = new PermissionRole();
        condition.setRoleId(roleId);
        condition.setResourceTypeList(Arrays.asList(resourceTypes));
        return permissionRoleMapper.selectByCondition(condition);
    }

    @Override
    public long getTotalCount(Long roleId) {
        PermissionRole condition = new PermissionRole();
        condition.setRoleId(roleId);
        return permissionRoleMapper.selectCountByCondition(condition);
    }

    @Override
    public List<PermissionRole> listByMenu(Long menuId) {
        PermissionRole condition = new PermissionRole();
        condition.setResourceId(menuId);
        List<String> resourceTypeList = new ArrayList<>();
        resourceTypeList.add(EResourceType.CLIENT.getCode());
        resourceTypeList.add(EResourceType.MENU.getCode());
        resourceTypeList.add(EResourceType.FUNC_MENU.getCode());
        resourceTypeList.add(EResourceType.BUTTON.getCode());
        condition.setResourceTypeList(resourceTypeList);
        return permissionRoleMapper.selectByCondition(condition);
    }
}
