package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.ProductMapper;
import com.std.core.pojo.domain.Product;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ProductCreateReq;
import com.std.core.pojo.request.ProductListReq;
import com.std.core.pojo.request.ProductListFrontReq;
import com.std.core.pojo.request.ProductModifyReq;
import com.std.core.pojo.request.ProductPageReq;
import com.std.core.pojo.request.ProductPageFrontReq;
import com.std.core.pojo.response.ProductDetailRes;
import com.std.core.pojo.response.ProductListRes;
import com.std.core.pojo.response.ProductPageRes;
import com.std.core.service.IProductService;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* 标的ServiceImpl
*
* <AUTHOR> ycj
* @since : 2024-11-22 14:39
*/
@Service
public class ProductServiceImpl implements IProductService {

    @Resource
    private ProductMapper productMapper;

    /**
     * 新增标的
     *
     * @param req 新增标的入参
     * @param operator 操作人
     */
    @Override
    public void create(ProductCreateReq req, User operator) {
        Product product = EntityUtils.copyData(req, Product.class);
        productMapper.insertSelective(product);
    }

    /**
     * 删除标的
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        productMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改标的
     *
     * @param req 修改标的入参
     * @param operator 操作人
     */
    @Override
    public void modify(ProductModifyReq req, User operator) {
        Product product = EntityUtils.copyData(req, Product.class);
        productMapper.updateByPrimaryKeySelective(product);
    }

    /**
     * 详情查询标的
     *
     * @param id 主键ID
     * @return 标的对象
     */
    @Override
    public Product detail(Long id) {
        Product product = productMapper.selectByPrimaryKey(id);
        if (null == product) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return product;
    }

    /**
     * 分页查询标的
     *
     * @param req 分页查询标的入参
     * @return 分页标的对象
     */
    @Override
    public List<Product> page(ProductPageReq req) {
        Product condition = EntityUtils.copyData(req, Product.class);

        List<Product> productList = productMapper.selectByCondition(condition);

        return productList;
    }

    /**
     * 列表查询标的
     *
     * @param req 列表查询标的入参
     * @return 列表标的对象
     */
    @Override
    public List<Product> list(ProductListReq req) {
        Product condition = EntityUtils.copyData(req, Product.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Product.class));

        List<Product> productList = productMapper.selectByCondition(condition);

        return productList;
    }

    /**
     * 前端详情查询标的
     *
     * @param id 主键ID
     * @return 标的对象
     */
    @Override
    public ProductDetailRes detailFront(Long id) {
        ProductDetailRes res = new ProductDetailRes();

        Product product = productMapper.selectByPrimaryKey(id);
        if (null == product) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(product, res);

        return res;
    }

    /**
     * 前端分页查询标的
     *
     * @param req 前端分页查询标的入参
     * @return 分页标的对象
     */
    @Override
    public List< ProductPageRes> pageFront(ProductPageFrontReq req) {
        Product condition = EntityUtils.copyData(req, Product.class);
        List<Product> productList = productMapper.selectByCondition(condition);

        List< ProductPageRes> resList = productList.stream().map((entity) -> {
            ProductPageRes res = new ProductPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(productList, resList);
    }

    /**
     * 前端列表查询标的
     *
     * @param req 前端列表查询标的入参
     * @return 列表标的对象
     */
    @Override
    public List< ProductListRes> listFront(ProductListFrontReq req) {
        Product condition = EntityUtils.copyData(req, Product.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Product.class));

        List<Product> productList = productMapper.selectByCondition(condition);

        List< ProductListRes> resList = productList.stream().map((entity) -> {
            ProductListRes res = new ProductListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}