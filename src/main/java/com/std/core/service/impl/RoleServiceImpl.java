package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.RoleMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-03-26 23:47
 */
@Service
public class RoleServiceImpl implements IRoleService {

    @Resource
    private RoleMapper roleMapper;

    @Autowired
    private IPermissionRoleService permissionRoleService;

    @Autowired
    private IMenuService menuService;

    @Autowired
    private IMenuActionService menuActionService;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private IGroupRoleService groupRoleService;

    @Override
    public void create(RoleCreateReq req, User operateUser) {
        Role role = new Role();

        role.setId(IdGeneratorUtil.generator());
        role.setName(req.getName());
        role.setKind(operateUser.getKind());
        role.setCreator(operateUser.getLoginName());
        role.setCreateTime(new Date());

        role.setRemark(req.getRemark());
        role.setCompanyId(operateUser.getCompanyId());
        roleMapper.insertSelective(role);
    }

    @Override
    public void subOwnerCreate(RoleSubOwnerCreateReq req, User operateUser) {
        Role role = new Role();

        EUserKind.getUserKind(req.getKind());

        role.setId(IdGeneratorUtil.generator());
        role.setName(req.getName());
        role.setKind(req.getKind());
        role.setCreator(operateUser.getLoginName());
        role.setCreateTime(new Date());

        role.setRemark(req.getRemark());
        role.setCompanyId(req.getCompanyId());
        roleMapper.insertSelective(role);
    }

    @Override
    public void createRole(Role role) {
        // 分配菜单权限
        //    List<Menu> menuList = menuService.listMenuByKind(role.getKind());
        //    ArrayList<PermissionRole> permissionRoleList = new ArrayList<>();
        //    if (CollectionUtils.isNotEmpty(menuList)) {
        //      for (Menu menu : menuList) {
        //        PermissionRole permissionRole = new PermissionRole();
        //        permissionRole.setRoleId(role.getId());
        //        permissionRole.setResourceId(menu.getId());
        //        permissionRole.setResourceType(menu.getType());
        //        permissionRoleList.add(permissionRole);
        //      }
        //    }
        //
        //    // 添加接口权限
        //    for (Menu menu : menuList) {
        //      List<MenuAction> menuActionList = menuActionService.list(menu.getId());
        //      if (CollectionUtils.isNotEmpty(menuActionList)) {
        //        for (MenuAction menuAction : menuActionList) {
        //          PermissionRole permissionRoleAction =
        //                  new PermissionRole(
        //                          role.getId(), menuAction.getActionId(),
        //                          EResourceType.ACTION.getCode());
        //          if (!permissionRoleList.contains(permissionRoleAction)) {
        //            permissionRoleList.add(permissionRoleAction);
        //          }
        //        }
        //      }
        //    }
        //
        //    permissionRoleService.batchCreate(permissionRoleList);

        roleMapper.insertSelective(role);
        permissionRoleService.batchCreate(role.getId(), null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id, User operateUser) {

        Role dbRole = detail(id);
        if (dbRole == null) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色编号不存在");
        }

        if (!EUserKind.OPS.getCode().equals(operateUser.getKind())) {
            if (EBoolean.YES.getCode().equals(dbRole.getIsDefault())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "系统默认角色，无权删除");
            }
        }

        long count = userRoleService.getTotalCount(id);
        if (count > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色已被关联，不能删除");
        }

        long count2 = groupRoleService.getTotalCount(id);
        if (count2 > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色已被关联，不能删除");
        }

        long count3 = permissionRoleService.getTotalCount(id);
        if (count3 > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色已被关联，不能删除");
        }

        roleMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void modify(RoleModifyReq req, User operateUser) {

        Role dbRole = detail(req.getId());
        if (dbRole == null) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色编号不存在");
        }

        if (!EUserKind.OPS.getCode().equals(operateUser.getKind())) {
            if (EBoolean.YES.getCode().equals(dbRole.getIsDefault())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "系统默认角色，无权修改");
            }
        }

        Role role = new Role();

        role.setId(req.getId());
        role.setName(req.getName());
        role.setUpdater(operateUser.getLoginName());
        role.setUpdateTime(new Date());
        role.setRemark(req.getRemark());

        roleMapper.updateByPrimaryKeySelective(role);
    }

    @Override
    public Role detail(Long id) {
        return roleMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotRolePermission(AllotRolePermissionReq request, User operateUser) {

        Role dbRole = detail(request.getRoleId());
        if (dbRole == null) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "角色编号不存在");
        }

        if (!EUserKind.OPS.getCode().equals(operateUser.getKind())) {
            if (EBoolean.YES.getCode().equals(dbRole.getIsDefault())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "系统默认角色，无权配置");
            }
        }

        // 删除角色下的所有权限
        permissionRoleService.removeByRole(request.getRoleId());

        //        if (CollectionUtils.isNotEmpty(request.getMenuIdList())) {
        //            permissionRoleService.batchCreate(request.getRoleId(), request.getMenuIdList());
        //        }

        if (CollectionUtils.isEmpty(request.getMenuIdList())) {
            return;
        }

        // 重新添加菜单权限
        List<PermissionRole> permissionRoleList = new ArrayList<PermissionRole>();
        for (Long resourceId : request.getMenuIdList()) {
            Menu menu = menuService.detail(resourceId);
            if (menu == null) {
                continue;
            }
            PermissionRole permissionRole =
                    new PermissionRole(request.getRoleId(), menu.getId(), menu.getType());
            if (!permissionRoleList.contains(permissionRole)) {
                permissionRoleList.add(permissionRole);
            } else {
                continue;
            }

            // 添加接口权限
            List<MenuAction> menuActionList = menuActionService.list(resourceId);
            if (CollectionUtils.isNotEmpty(menuActionList)) {
                for (MenuAction menuAction : menuActionList) {
                    PermissionRole permissionRoleAction =
                            new PermissionRole(
                                    request.getRoleId(), menuAction.getActionId(), EResourceType.ACTION.getCode());
                    if (!permissionRoleList.contains(permissionRoleAction)) {
                        permissionRoleList.add(permissionRoleAction);
                    }
                }
            }
        }

        permissionRoleService.batchCreate(permissionRoleList);
    }

    @Override
    public List<Menu> listByRole(Long roleId) {
        Role role = detail(roleId);

        List<Menu> resultList = null;
        if (EUserKind.OPS.getCode().equals(role.getKind())) {
            resultList = menuService.listByConditionByTopMenuId(EClientMenu.OPS.getCode());
        } else if (EUserKind.SYS.getCode().equals(role.getKind())) {
            resultList = menuService.listByConditionByTopMenuId(EClientMenu.SYS.getCode());
        } else if (EUserKind.C.getCode().equals(role.getKind())) {
            resultList = menuService.listByConditionByTopMenuId(EClientMenu.C.getCode());
        } else if (EUserKind.P.getCode().equals(role.getKind())) {
            resultList = menuService.listByConditionByTopMenuId(EClientMenu.P.getCode());
        } else {
            throw new BizException(EBizErrorCode.UNDONE.getCode(), EBizErrorCode.UNDONE.getValue());
        }

        return resultList;
    }

    @Override
    public List<Role> listByCondition(Role condition) {
        return roleMapper.selectByCondition(condition);
    }

    @Override
    public List<Role> list(RoleListReq req, User operateUser) {
        Role condition = new Role();
        condition.setName(req.getName());
        condition.setCompanyIdAndDefault(operateUser.getCompanyId());
        condition.setKind(operateUser.getKind());
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Role.class));

        return roleMapper.selectByCondition(condition);
    }

    @Override
    public List<Role> subOwnerList(RoleSubOwnerListReq request, User operateUser) {
        Role condition = new Role();
        condition.setName(request.getName());
        condition.setCompanyIdAndDefault(request.getCompanyId());
        condition.setKind(request.getKind());
        condition.setOrderBy(SqlUtil.parseSort(request.getSort(), Role.class));

        return roleMapper.selectByCondition(condition);
    }

    @Override
    public List<Role> page(RolePageReq req, User operateUser) {

        Role condition = new Role();

        if (EUserKind.OPS.getCode().equals(operateUser.getKind())) {
            condition.setKind(req.getKind());
            condition.setName(req.getName());
        } else {
            condition.setKind(operateUser.getKind());
            condition.setName(req.getName());
        }

        return roleMapper.selectByCondition(condition);
    }

    @Override
    public List<Role> subOwnerPage(RoleSubOwnerPageReq request, User operateUser) {
        Role condition = new Role();
        condition.setName(request.getName());
        condition.setCompanyIdAndDefault(request.getCompanyId());
        condition.setKind(request.getKind());
        return roleMapper.selectByCondition(condition);
    }
}
