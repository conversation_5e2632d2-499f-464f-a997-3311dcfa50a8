package com.std.core.service.impl;

import com.std.core.enums.*;
import com.std.core.pojo.domain.*;
import com.std.core.service.*;
import com.std.core.util.DateUtil;
import com.std.core.util.SysConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/6/2 3:35 下午
 */
@Slf4j
@Service
@PropertySource(value = "classpath:schedule.properties")
public class ScheduleServiceImpl implements IScheduleService {
    @Resource
    private IConfigService configService;

    @Resource
    private IActivityService activityService;

    @Resource
    private IActivityOrderService activityOrderService;

    @Resource
    private IBigActivityOrderService bigActivityOrderService;

    @Resource
    private IGoodsOrderService goodsOrderService;
    @Resource
    private IActivityTicketLineService activityTicketLineService;

    @Resource
    private IUserService userService;

    @Resource
    private IDailyIncomeSummaryService dailyIncomeSummaryService;

    /**
     * 订单超时未支付取消
     */
    @Override
    public void doCancelBigOrder() {
        Integer closeTime = configService.getIntegerValue(SysConstants.ORDER_EXPIRE_TIME);
        List<BigActivityOrder> bigActivityOrderList = bigActivityOrderService.listTimeOutOrder(closeTime);

        for (BigActivityOrder bigActivityOrder : bigActivityOrderList) {
            try {
                bigActivityOrderService.cancelBigOrder(bigActivityOrder.getSerialNumber());
            } catch (Exception e) {
                log.error("活动预约超时支付失败处理报错，序号：" + bigActivityOrder.getId() + ",原因：" + e);
            }
        }
    }

    /**
     * 商品订单超时未支付，自动取消
     */
    @Override
    public void doGoodsCancelBigOrder() {
        Integer closeTime = configService.getIntegerValue(SysConstants.ORDER_EXPIRE_TIME);

        List<GoodsOrder> goodsOrderList = goodsOrderService.listTimeOutOrder(closeTime);

        for (GoodsOrder goodsOrder : goodsOrderList) {
            try {
                goodsOrderService.cancelGoodsOrder(goodsOrder.getId());
            } catch (Exception e) {
                log.error("商品订单超时支付失败处理报错，序号：" + goodsOrder.getId() + ",原因：" + e);
            }
        }
    }


    /**
     * 活动过期
     */
    @Override
    public void doExpiredActivity() {
        ActivityOrder condition = new ActivityOrder();
        condition.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_0.getCode());
        condition.setExpiredTime(new Date());
        List<ActivityOrder> activityOrders = activityOrderService.list(condition);

        Integer reservationExpiredMaxNumber = 0;
        Integer reservationExpiredUnsealTime = 0;
        if (CollectionUtils.isNotEmpty(activityOrders)) {
            reservationExpiredMaxNumber = configService.getIntegerValue(SysConstants.RESERVATION_EXPIRED_MAX_NUMBER);
            reservationExpiredUnsealTime = configService.getIntegerValue(SysConstants.RESERVATION_EXPIRED_UNSEAL_TIME);
        }
        for (ActivityOrder activityOrder : activityOrders) {
            try {
                activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_2.getCode());
                activityOrderService.modify(activityOrder);

                User user = userService.detailBrief(activityOrder.getUserId());
                User userModify = new User();
                userModify.setId(user.getId());
                userModify.setReservationExpiredNumber(user.getReservationExpiredNumber() + 1);
                if (userModify.getReservationExpiredNumber() >= reservationExpiredMaxNumber) {
                    userModify.setReservationStatus(EUserReservationStatus.LOCK.getCode());
                    userModify.setReservationUnsealTime(DateUtils.addDays(new Date(), reservationExpiredUnsealTime));
                }
                userService.modifyUser(userModify);
            } catch (Exception e) {
                log.error("活动状态更新失败：" + activityOrder.getId() + ",原因：" + e);
            }
        }
    }

    @Override
    public void activityExpired() {
        Activity condition = new Activity();
        List<String> statusList = new ArrayList<>();
        statusList.add(EActivityStatus.ACTIVITY_STATUS_1.getCode());
        statusList.add(EActivityStatus.ACTIVITY_STATUS_2.getCode());
        statusList.add(EActivityStatus.ACTIVITY_STATUS_6.getCode());
        statusList.add(EActivityStatus.ACTIVITY_STATUS_7.getCode());
        condition.setStatusList(statusList);
        List<Activity> activitys = activityService.list(condition);
        for (Activity activity : activitys) {
            try {
                Date date = new Date();
                //未开始变成售票中
                if (EActivityStatus.ACTIVITY_STATUS_1.getCode().equals(activity.getStatus())) {
                    if (activity.getBuyStartTime().getTime() < date.getTime()) {
                        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_2.getCode());
                        activityService.modify(activity);
                    }
                } else if (EActivityStatus.ACTIVITY_STATUS_2.getCode().equals(activity.getStatus())) {
                    ActivityTicketLine activityTicketLine = new ActivityTicketLine();
                    activityTicketLine.setActivityId(activity.getId());
                    activityTicketLine.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
                    int i = activityTicketLineService.sumInventoryByCondition(activityTicketLine);
                    if (i == 0) {
                        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_6.getCode());
                        activityService.modify(activity);
                    } else if (activity.getEndTime().getTime() < date.getTime()) {
                        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_3.getCode());
                        activityService.modify(activity);
                    } else if (activity.getBuyEndTime().getTime() < date.getTime()) {
                        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_7.getCode());
                        activityService.modify(activity);
                    }
                }else if (EActivityStatus.ACTIVITY_STATUS_6.getCode().equals(activity.getStatus())) {
                    ActivityTicketLine activityTicketLine = new ActivityTicketLine();
                    activityTicketLine.setActivityId(activity.getId());
                    activityTicketLine.setStatus(EActivityTicketLineStatus.ACTIVITY_TICKET_LINE_STATUS_1.getCode());
                    int i = activityTicketLineService.sumInventoryByCondition(activityTicketLine);
                    if (i > 0) {
                        if (activity.getEndTime().getTime() < date.getTime()) {
                            activity.setStatus(EActivityStatus.ACTIVITY_STATUS_3.getCode());
                            activityService.modify(activity);
                        } else if (activity.getBuyEndTime().getTime() < date.getTime()) {
                            activity.setStatus(EActivityStatus.ACTIVITY_STATUS_7.getCode());
                            activityService.modify(activity);
                        }else {
                            activity.setStatus(EActivityStatus.ACTIVITY_STATUS_2.getCode());
                            activityService.modify(activity);
                        }
                    }
                } else if (EActivityStatus.ACTIVITY_STATUS_7.getCode().equals(activity.getStatus())) {
                    if (activity.getEndTime().getTime() < date.getTime()) {
                        activity.setStatus(EActivityStatus.ACTIVITY_STATUS_3.getCode());
                        activityService.modify(activity);
                    }
                }

            } catch (Exception e) {
                log.error("活动状态更新失败：" + activity.getId() + ",原因：" + e);
            }
        }
    }

    @Override
    public void doAutoReceive() {
        GoodsOrder goodsOrder = new GoodsOrder();
        goodsOrder.setAutoReceiveDatetime(new Date());
        goodsOrder.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_2.getCode());
        List<GoodsOrder> list = goodsOrderService.list(goodsOrder);
        for (GoodsOrder order : list) {
            try {
                order.setStatus(EGoodsOrderStatus.GOODS_ORDER_STATUS_3.getCode());
                order.setFinishDatetime(new Date());
                goodsOrderService.modify(order);
            } catch (Exception e) {
                log.error("自动收货失败：" + order.getId() + ",原因：" + e);
            }
        }
    }

    @Override
    public void doUserReservationRecovery() {
        User condition = new User();
        condition.setReservationStatus(EUserReservationStatus.LOCK.getCode());
        condition.setReservationUnsealTimeEnd(new Date());
        List<User> list = userService.list(condition);

        for (User user : list) {
            try {
                userService.reservationRecovery(user);
            } catch (Exception e) {
                log.error("用户预约恢复失败：" + user.getId() + ",原因：" + e);
            }

        }
    }

    @Override
    public void doDailyIncomeSummary(Date dateTime) {
        log.info("每日收益统计开始,时间：" + DateUtil.dateToStr(dateTime, DateUtil.DATA_TIME_PATTERN_9));
        String incomeDate = DateUtil.dateToStr(dateTime, DateUtil.DATA_TIME_PATTERN_9);
        String startDate = incomeDate.concat(" 00:00:00");
        String endDate = incomeDate.concat(" 23:59:59");
        BigDecimal activityIncome = activityOrderService.detailIncomeByDate(startDate, endDate);
        BigDecimal goodsIncome = goodsOrderService.detailIncomeByDate(startDate, endDate);
        dailyIncomeSummaryService.create(incomeDate, activityIncome, goodsIncome);

        Date date = DateUtil.strToDate(incomeDate, DateUtil.DATA_TIME_PATTERN_9);
        for (int i = 0; i < 7; i++) {
            date = DateUtils.addDays(date, 1);
            String incomeNextDate = DateUtil.dateToStr(date, DateUtil.DATA_TIME_PATTERN_9);
            // 查询后面 7 天的记录
            dailyIncomeSummaryService.detailByDate(incomeNextDate);
        }
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.getDayEndTime(new Date()));
    }
}


