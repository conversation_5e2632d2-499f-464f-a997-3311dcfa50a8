package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.SmsReadMapper;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.SmsRead;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SmsPageMyReq;
import com.std.core.pojo.request.SmsReadListReq;
import com.std.core.pojo.request.SmsReadPageReq;
import com.std.core.service.ISmsReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 公告阅读记录ServiceImpl
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
@Service
public class SmsReadServiceImpl implements ISmsReadService {

    @Resource
    private SmsReadMapper smsReadMapper;

    /**
     * 新增公告阅读记录
     *
     * @param smsCode  新增公告阅读记录入参
     * @param operator 操作人
     */
    @Override
    public void create(Long smsCode, User operator) {
        boolean result = isExist(smsCode, operator.getId());
        if (result) {
            return;
        }

        SmsRead smsRead = new SmsRead();
        smsRead.setSmsCode(smsCode);
        smsRead.setUserId(operator.getId());
        smsRead.setStatus(ESmsReadStatus.READ.getCode());
        smsRead.setCreateDatetime(new Date());
        smsRead.setReadDatetime(new Date());

        smsReadMapper.insertSelective(smsRead);
    }

    /**
     * 删除公告阅读记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        smsReadMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void removeBySmsCode(Long smsCode) {
        smsReadMapper.deleteBySmsCode(smsCode);
    }

    /**
     * 详情查询公告阅读记录
     *
     * @param id 主键ID
     * @return 公告阅读记录对象
     */
    @Override
    public SmsRead detail(Long id) {
        SmsRead smsRead = smsReadMapper.selectByPrimaryKey(id);
        if (null == smsRead) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return smsRead;
    }

    /**
     * 分页查询公告阅读记录
     *
     * @param req 分页查询公告阅读记录入参
     * @return 分页公告阅读记录对象
     */
    @Override
    public List<SmsRead> page(SmsReadPageReq req) {
        SmsRead condition = EntityUtils.copyData(req, SmsRead.class);

        return smsReadMapper.selectByCondition(condition);
    }

    /**
     * 列表查询公告阅读记录
     *
     * @param req 列表查询公告阅读记录入参
     * @return 列表公告阅读记录对象
     */
    @Override
    public List<SmsRead> list(SmsReadListReq req) {
        SmsRead condition = EntityUtils.copyData(req, SmsRead.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), SmsRead.class));

        return smsReadMapper.selectByCondition(condition);
    }

    @Override
    public boolean isExist(Long userId, Long smsCode) {
        SmsRead condition = new SmsRead();
        condition.setUserId(userId);
        condition.setSmsCode(smsCode);

        Integer count = smsReadMapper.selectCount(condition);
        if (count > 0) {
            return true;
        }

        return false;
    }

    @Override
    public List<Sms> page(SmsPageMyReq req, User operator) {
        Sms condition = EntityUtils.copyData(req, Sms.class);
        condition.setTarget(ESmsTarget.CUSTOMER.getCode());
        condition.setStatus(ESmsStatus.SENDED.getCode());
        condition.setUserId(operator.getId());
        return smsReadMapper.selectMySmsByCondition(condition);
    }

    @Override
    public Integer getMyUnreadCount(User operator) {
        Sms condition = new Sms();
        condition.setUserId(operator.getId());
        condition.setTarget(operator.getKind());
        condition.setType(ESmsType.SYSTEM.getCode());
        condition.setStatus(ESmsStatus.SENDED.getCode());

        return smsReadMapper.selectMyUnreadCount(condition);
    }

    @Override
    public Integer getMsgMyUnreadCount(User operator) {
        Sms condition = new Sms();
        condition.setUserId(operator.getId());
        condition.setTarget(operator.getKind());
        condition.setType(ESmsType.MY.getCode());
        condition.setStatus(ESmsStatus.SENDED.getCode());

        return smsReadMapper.selectMsgMyUnreadCount(condition);
    }
}
