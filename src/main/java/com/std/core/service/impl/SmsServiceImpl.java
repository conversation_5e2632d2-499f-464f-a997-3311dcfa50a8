package com.std.core.service.impl;

import com.std.common.base.BasePageReq;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.SmsMapper;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.SmsNewsRes;
import com.std.core.service.ISmsReadService;
import com.std.core.service.ISmsService;
import com.std.core.service.IUserLogService;
import com.std.core.service.IUserService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 消息记录ServiceImpl
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 19:57
 */
@Service
public class SmsServiceImpl implements ISmsService {

    @Resource
    private SmsMapper smsMapper;

    @Resource
    private IUserService userService;

    @Resource
    private ISmsReadService smsReadService;

    @Resource
    private IUserLogService userLogService;

    /**
     * 新增消息记录
     *
     * @param req      新增消息记录入参
     * @param operator 操作人
     */
    @Override
    public void create(SmsCreateReq req, User operator, String ip) {
        Sms sms = EntityUtils.copyData(req, Sms.class);
        sms.setStatus(ESmsStatus.DRAFT.getCode());
        sms.setRefType(ESmsRefType.SYSTEM_SMS.getCode());
        sms.setRefNo(ESmsRefType.SYSTEM_SMS.getValue());

        sms.setCreator(operator.getId());
        sms.setCreatorName(operator.getLoginName());
        sms.setCreateDatetime(new Date());

        smsMapper.insertSelective(sms);

        String content = "管理员新增公告成功";
        userLogService.create(operator, EUserLogType.NOTICE_CREATE.getCode(), EUserLogType.NOTICE_CREATE.getValue(), ip, content);
    }

    /**
     * 群发消息
     *
     * @param req
     * @param operator
     */
    @Override
    public void batchCreate(SmsBatchCreateReq req, User operator, String ip) {

        List<Sms> smsList = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getIdList())) {
            idList = req.getIdList();
        } else if (CollectionUtils.isEmpty(req.getIdList())) {
            idList = userService.selectIdList();
        }

        for (Long id : idList) {
            Sms sms = new Sms();
            userService.detail(id);
            sms.setUserId(id);
            sms.setRefType(ESmsRefType.SYSTEM_MESSAGE.getCode());
            sms.setRefNo(ESmsRefType.SYSTEM_MESSAGE.getValue());

//            sms.setTarget(EUserKind.C.getCode());
            sms.setTitle(req.getTitle());
            sms.setType(ESmsType.MY.getCode());
            sms.setContent(req.getContent());
            sms.setCreator(operator.getId());
            sms.setCreatorName(operator.getLoginName());
            sms.setCreateDatetime(new Date());
            sms.setStatus(ESmsStatus.SENDED.getCode());
            sms.setType(ESmsType.MY.getCode());

            smsList.add(sms);
        }
        smsMapper.batchInsertSelective(smsList);

        String content = "管理员群发消息成功";
        userLogService.create(operator, EUserLogType.MESSAGE_SENDING.getCode(), EUserLogType.MESSAGE_SENDING.getValue(), ip, content);
    }

    @Override
    public void sendSmsRef(String title, String refType, String refNo) {
        Sms sms = new Sms();
        sms.setTarget(ESmsTarget.CUSTOMER.getCode());
        sms.setType(ESmsType.SYSTEM.getCode());
        sms.setTitle(title);

        sms.setStatus(ESmsStatus.SENDED.getCode());
        sms.setRefType(refType);
        sms.setRefNo(refNo);
        sms.setCreator(ESystemUser.SYS.getCode());
        sms.setCreatorName(ESystemUser.SYS.getValue());
        sms.setCreateDatetime(new Date());
        sms.setUpdater(ESystemUser.SYS.getCode());
        sms.setUpdaterName(ESystemUser.SYS.getValue());
        sms.setUpdateDatetime(sms.getCreateDatetime());

        smsMapper.insertSelective(sms);
    }

    @Override
    public void sendMyMsg(User user, String title, String content, String refType, String refNo) {
        Sms sms = new Sms();

        sms.setTarget(ESmsTarget.CUSTOMER.getCode());
        sms.setType(ESmsType.MY.getCode());
        sms.setTitle(title);
        sms.setContent(content);
        sms.setUserId(user.getId());
        sms.setRefType(refType);
        sms.setRefNo(refNo);

        sms.setStatus(ESmsStatus.SENDED.getCode());
        sms.setCreator(ESystemUser.SYS.getCode());
        sms.setCreatorName(ESystemUser.SYS.getValue());
        sms.setCreateDatetime(new Date());
        sms.setUpdater(ESystemUser.SYS.getCode());
        sms.setUpdaterName(ESystemUser.SYS.getValue());
        sms.setUpdateDatetime(sms.getCreateDatetime());

        smsMapper.insertSelective(sms);
    }

    /**
     * 删除消息记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        smsMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改消息记录
     *
     * @param req      修改消息记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(SmsModifyReq req, User operator, String ip) {
        Sms sms = EntityUtils.copyData(req, Sms.class);
        Sms data = detail(sms.getId());

        if (ESmsType.MY.getCode().equals(data.getType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "消息不能修改");
        }

        if (ESmsStatus.SENDED.getCode().equals(data.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "公告已发布不能修改，请先撤回");
        }
        sms.setUpdater(operator.getId());
        sms.setUpdaterName(operator.getLoginName());
        sms.setUpdateDatetime(new Date());
        smsMapper.updateByPrimaryKeySelective(sms);

        String content = "公告修改";
        userLogService.create(operator, EUserLogType.NOTICE_MODIFY.getCode(), EUserLogType.NOTICE_MODIFY.getValue(), ip, content);
    }

    @Override
    public void batchRelease(SmsPutReq request, User operator, String ip) {
        for (Long id : request.getIds()) {
            refreshStatus(id, ESmsStatus.SENDED.getCode(), operator, ip);
        }
    }

    @Override
    public void batchRevoke(SmsPutReq request, User operator, String ip) {
        for (Long id : request.getIds()) {
            refreshStatus(id, ESmsStatus.REVOKED.getCode(), operator, ip);
        }
    }

    private void refreshStatus(Long id, String status, User operator, String ip) {
        Sms detail = detail(id);
        Sms condition = new Sms();
        condition.setId(id);
        condition.setStatus(status);
        condition.setUpdater(operator.getId());
        condition.setUpdaterName(operator.getLoginName());
        condition.setUpdateDatetime(new Date());
        smsMapper.updateByPrimaryKeySelective(condition);

        if (ESmsStatus.SENDED.getCode().equals(status)) {
            String content = "状态更新：" + ESmsStatus.getNoticeStatus(detail.getStatus()).getValue() + "改成已发布";
            userLogService.create(operator, EUserLogType.NOTICE_ON.getCode(), EUserLogType.NOTICE_ON.getValue(), ip, content);
        } else if (ESmsStatus.REVOKED.getCode().equals(status)) {
            String content = "状态更新：" + ESmsStatus.getNoticeStatus(detail.getStatus()).getValue() + "状态改成已撤回";
            userLogService.create(operator, EUserLogType.NOTICE_DOWN.getCode(), EUserLogType.NOTICE_DOWN.getValue(), ip, content);
        }

    }

    /**
     * 详情查询消息记录
     *
     * @param id 主键ID
     * @return 消息记录对象
     */
    @Override
    public Sms detail(Long id) {
        Sms sms = smsMapper.selectByPrimaryKey(id);
        if (null == sms) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return sms;
    }

    /**
     * app详情查询消息记录
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public Sms appDetail(Long id, User operator) {
        Sms sms = smsMapper.selectByPrimaryKey(id);
        if (null == sms) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        smsReadService.create(sms.getId(), operator);
        return sms;
    }

    /**
     * oss:分页查询消息记录
     *
     * @param req 分页查询消息记录入参
     * @return 分页消息记录对象
     */
    @Override
    public List<Sms> page(SmsPageReq req, User operator) {

        Sms condition = EntityUtils.copyData(req, Sms.class);
        List<Sms> smsList = smsMapper.select(condition);
        if (null != operator) {
            for (Sms sms1 : smsList) {
                sms1.setIsRead(smsReadService.isExist(operator.getId(), sms1.getId()) ? EBoolean.YES.getCode() : EBoolean.NO.getCode());

                if (ESmsType.MY.getCode().equals(req.getType())) {
                    sms1.setUser(userService.detail(sms1.getUserId()));
                }
            }
        }

        return smsList;
    }

    /**
     * 列表查询消息记录
     *
     * @param req 列表查询消息记录入参
     * @return 列表消息记录对象
     */
    @Override
    public List<Sms> list(SmsListReq req) {
        Sms condition = EntityUtils.copyData(req, Sms.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Sms.class));

        return smsMapper.selectByCondition(condition);
    }

    /**
     * 消息中心
     *
     * @param req      列表查询消息记录入参
     * @param operator
     * @return
     */
    @Override
    public List<Sms> messageCenter(SmsListReq req, User operator) {

        Sms sms = EntityUtils.copyData(req, Sms.class);
        sms.setUserId(operator.getId());
        sms.setStatus(ESmsStatus.SENDED.getCode());
        sms.setTsrStatus(ESmsReadStatus.DELETE.getCode());
        return smsMapper.messageCenter(sms);
    }

    @Override
    public List<Sms> messageCenterPage(BasePageReq req, User user) {
        Sms sms = new Sms();
        if (null != user) {
            sms.setUserId(user.getId());
        }
        sms.setStatus(ESmsStatus.SENDED.getCode());

        List<Sms> smsList = smsMapper.messageCenterPage(sms);

        for (Sms sms1 : smsList) {
            sms1.setIsRead(smsReadService.isExist(user.getId(), sms1.getId()) ? EBoolean.YES.getCode() : EBoolean.NO.getCode());
        }

        return smsList;
    }

    /**
     * 未读消息数
     *
     * @param
     * @param operator
     * @return
     */
    @Override
    public Sms unreadMessages(User operator) {
        Sms sms = new Sms();
        sms.setUserId(operator.getId());
        return smsMapper.unreadMessages(sms);
    }

    /**
     * 我的未读消息数量
     *
     * @param operator
     * @return
     */
    @Override
    public Sms unreadMessagesNumber(User operator) {
        Sms sms = new Sms();
        sms.setUserId(operator.getId());
        return smsMapper.unreadMessagesNumber(sms);
    }

    /**
     * 我的未读公告数量
     *
     * @param operator
     * @return
     */
    @Override
    public Sms unreadNoticeNumber(User operator) {
        Sms sms = new Sms();
        sms.setUserId(operator.getId());
        return smsMapper.unreadNoticeNumber(sms);
    }

    /**
     * 一键已读消息
     *
     * @param operator
     */
    @Override
    public void unifiedReadMessages(User operator) {
        Sms sms = new Sms();
        sms.setUserId(operator.getId());
        sms.setType(ESmsType.MY.getCode());
        List<Sms> smsList = smsMapper.selectByCondition(sms);
        for (Sms sms1 : smsList) {
            appDetail(sms1.getId(), operator);
        }
    }

    /**
     * 一键已读公告
     *
     * @param operator
     */
    @Override
    public void unifiedReadNotice(User operator) {
        Sms sms = new Sms();
        sms.setType(ESmsType.SYSTEM.getCode());
        List<Sms> smsList = smsMapper.selectByCondition(sms);
        for (Sms sms1 : smsList) {
            appDetail(sms1.getId(), operator);
        }
    }

    /**
     * OSS消息管理详情查
     *
     * @param id
     * @return
     */
    @Override
    public SmsNewsRes smsNews(Long id) {
        Sms sms = new Sms();
        sms.setId(id);
        SmsNewsRes smsNewsRes = smsMapper.selectNewsDetail(sms);
        smsNewsRes.setUser(userService.detail(smsNewsRes.getUserId()));
        return smsNewsRes;
    }

    /**
     * APP站内信
     *
     * @param operator
     * @return
     */
    @Override
    public List<Sms> userSms(User operator) {
        Sms sms = new Sms();
        sms.setUserId(operator.getId());
        sms.setType(ESmsType.MY.getCode());
        sms.setStatus(ESmsStatus.SENDED.getCode());
        List<Sms> smsList = smsMapper.selectByCondition(sms);

        for (Sms sms1 : smsList) {
            sms1.setIsRead(smsReadService.isExist(operator.getId(), sms1.getId()) ? EBoolean.YES.getCode() : EBoolean.NO.getCode());
        }
        return smsList;
    }


}
