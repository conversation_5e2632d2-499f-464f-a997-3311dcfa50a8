package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.UserActionLogMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserActionLog;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.UserActionLogDetailRes;
import com.std.core.pojo.response.UserActionLogListRes;
import com.std.core.pojo.response.UserActionLogPageRes;
import com.std.core.service.IUserActionLogService;
import com.std.core.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* 用户业务操作记录ServiceImpl
*
* <AUTHOR> ycj
* @since : 2024-06-17 12:44
*/
@Service
public class UserActionLogServiceImpl implements IUserActionLogService {

    @Resource
    private UserActionLogMapper userActionLogMapper;

    @Resource
    private IUserService userService;

    /**
     * 新增用户业务操作记录
     *
     * @param req 新增用户业务操作记录入参
     * @param operator 操作人
     */
    @Override
    public void create(UserActionLogCreateReq req, User operator) {
        UserActionLog userActionLog = EntityUtils.copyData(req, UserActionLog.class);
        userActionLogMapper.insertSelective(userActionLog);
    }

    @Override
    public void create(String type, Long userId, String oldData, String newData) {
        UserActionLog userActionLog = new UserActionLog();
        userActionLog.setType(type);
        userActionLog.setUserId(userId);
        userActionLog.setOldData(oldData);
        userActionLog.setNewData(newData);
        userActionLog.setCreateDatetime(new Date());
        userActionLogMapper.insertSelective(userActionLog);
    }

    /**
     * 删除用户业务操作记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        userActionLogMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改用户业务操作记录
     *
     * @param req 修改用户业务操作记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(UserActionLogModifyReq req, User operator) {
        UserActionLog userActionLog = EntityUtils.copyData(req, UserActionLog.class);
        userActionLogMapper.updateByPrimaryKeySelective(userActionLog);
    }

    /**
     * 详情查询用户业务操作记录
     *
     * @param id 主键ID
     * @return 用户业务操作记录对象
     */
    @Override
    public UserActionLog detail(Long id) {
        UserActionLog userActionLog = userActionLogMapper.selectByPrimaryKey(id);
        if (null == userActionLog) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userActionLog.setUser(userService.selectSummaryInfo(userActionLog.getUserId()));

        return userActionLog;
    }

    /**
     * 分页查询用户业务操作记录
     *
     * @param req 分页查询用户业务操作记录入参
     * @return 分页用户业务操作记录对象
     */
    @Override
    public List<UserActionLog> page(UserActionLogPageReq req) {
        UserActionLog condition = EntityUtils.copyData(req, UserActionLog.class);

        List<UserActionLog> userActionLogList = userActionLogMapper.selectByCondition(condition);
        // 转译UserId
        userActionLogList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userActionLogList;
    }

    /**
     * 列表查询用户业务操作记录
     *
     * @param req 列表查询用户业务操作记录入参
     * @return 列表用户业务操作记录对象
     */
    @Override
    public List<UserActionLog> list(UserActionLogListReq req) {
        UserActionLog condition = EntityUtils.copyData(req, UserActionLog.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserActionLog.class));

        List<UserActionLog> userActionLogList = userActionLogMapper.selectByCondition(condition);
        // 转译UserId
        userActionLogList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userActionLogList;
    }

    /**
     * 前端详情查询用户业务操作记录
     *
     * @param id 主键ID
     * @return 用户业务操作记录对象
     */
    @Override
    public UserActionLogDetailRes detailFront(Long id) {
        UserActionLogDetailRes res = new UserActionLogDetailRes();

        UserActionLog userActionLog = userActionLogMapper.selectByPrimaryKey(id);
        if (null == userActionLog) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userActionLog.setUser(userService.selectSummaryInfo(userActionLog.getUserId()));

        BeanUtils.copyProperties(userActionLog, res);

        return res;
    }

    /**
     * 前端分页查询用户业务操作记录
     *
     * @param req 前端分页查询用户业务操作记录入参
     * @return 分页用户业务操作记录对象
     */
    @Override
    public List< UserActionLogPageRes> pageFront(UserActionLogPageFrontReq req) {
        UserActionLog condition = EntityUtils.copyData(req, UserActionLog.class);
        List<UserActionLog> userActionLogList = userActionLogMapper.selectByCondition(condition);
            // 转译UserId
            userActionLogList.forEach(item -> {
                item.setUser(userService.selectSummaryInfo(item.getUserId()));
            });

        List< UserActionLogPageRes> resList = userActionLogList.stream().map((entity) -> {
            UserActionLogPageRes res = new UserActionLogPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(userActionLogList, resList);
    }

    /**
     * 前端列表查询用户业务操作记录
     *
     * @param req 前端列表查询用户业务操作记录入参
     * @return 列表用户业务操作记录对象
     */
    @Override
    public List< UserActionLogListRes> listFront(UserActionLogListFrontReq req) {
        UserActionLog condition = EntityUtils.copyData(req, UserActionLog.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserActionLog.class));

        List<UserActionLog> userActionLogList = userActionLogMapper.selectByCondition(condition);
          // 转译UserId
          userActionLogList.forEach(item -> {
              item.setUser(userService.selectSummaryInfo(item.getUserId()));
          });

        List< UserActionLogListRes> resList = userActionLogList.stream().map((entity) -> {
            UserActionLogListRes res = new UserActionLogListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

}