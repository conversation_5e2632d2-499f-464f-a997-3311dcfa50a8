package com.std.core.service.impl;

import com.std.core.mapper.UserGroupMapper;
import com.std.core.pojo.domain.UserGroup;
import com.std.core.service.IUserGroupService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class UserGroupServiceImpl implements IUserGroupService {

    @Resource
    private UserGroupMapper userGroupMapper;

    @Override
    public void batchCreate(List<UserGroup> userGroupList) {
        userGroupMapper.batchInsert(userGroupList);
    }

    @Override
    public void removeByUser(Long userId) {
        userGroupMapper.deleteByUserId(userId);
    }

    @Override
    public List<UserGroup> listByUserId(Long userId) {
        UserGroup userGroup = new UserGroup();
        userGroup.setUserId(userId);
        return userGroupMapper.selectByCondition(userGroup);
    }

    @Override
    public List<UserGroup> listByGroupId(Long groupId) {
        UserGroup userGroup = new UserGroup();
        userGroup.setGroupId(groupId);
        return userGroupMapper.selectByCondition(userGroup);
    }


    @Override
    public void allotGroup(Long id, Long groupId) {
        UserGroup userGroup = new UserGroup();
        userGroup.setUserId(id);
        userGroup.setGroupId(groupId);

        userGroupMapper.insert(userGroup);
    }
}
