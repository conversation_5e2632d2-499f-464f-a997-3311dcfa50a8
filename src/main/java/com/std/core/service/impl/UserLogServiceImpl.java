package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.UserLogMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserLog;
import com.std.core.pojo.request.UserLogListReq;
import com.std.core.pojo.request.UserLogPageReq;
import com.std.core.service.IUserLogService;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 用户日志ServiceImpl
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-02-25 14:07
 */
@Service
public class UserLogServiceImpl implements IUserLogService {

    @Resource
    private UserLogMapper userLogMapper;

    @Override
    public void create(User user, String type,String typeNote, String ip, String content) {

        UserLog userLog = new UserLog();
        userLog.setUserId(user.getId().toString());
        userLog.setUserName(user.getLoginName());
        userLog.setType(type);
        userLog.setTypeNote(typeNote);
        userLog.setContent(content);
        userLog.setIp(ip);
        userLog.setCreateDatetime(new Date());

        userLogMapper.insertSelective(userLog);
    }

    /**
     * 批量新增用户日志
     * @param userLogList
     */
    @Override
    public void batchCreate(List<UserLog> userLogList) {

        for(UserLog userLog:userLogList){
            userLog.setCreateDatetime(new Date());
        }
        userLogMapper.batchInsert(userLogList);
    }

    /**
     * 删除用户日志
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        userLogMapper.deleteByPrimaryKey(id);
    }

    /**
     * 详情查询用户日志
     *
     * @param id 主键ID
     * @return 用户日志对象
     */
    @Override
    public UserLog detail(Long id) {
        UserLog userLog = userLogMapper.selectByPrimaryKey(id);
        if (null == userLog) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return userLog;
    }

    /**
     * 分页查询用户日志
     *
     * @param req 分页查询用户日志入参
     * @return 分页用户日志对象
     */
    @Override
    public List<UserLog> page(UserLogPageReq req) {
        UserLog condition = EntityUtils.copyData(req, UserLog.class);

        return userLogMapper.selectByCondition(condition);
    }

    /**
     * 列表查询用户日志
     *
     * @param req 列表查询用户日志入参
     * @return 列表用户日志对象
     */
    @Override
    public List<UserLog> list(UserLogListReq req) {
        UserLog condition = EntityUtils.copyData(req, UserLog.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserLog.class));

        return userLogMapper.selectByCondition(condition);
    }
}
