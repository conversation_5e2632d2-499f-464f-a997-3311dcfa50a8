package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUserNodeLevel;
import com.std.core.enums.EUserNodeLevelWay;
import com.std.core.mapper.UserNodeLevelMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserNodeLevel;
import com.std.core.pojo.request.UserNodeLevelCreateReq;
import com.std.core.pojo.request.UserNodeLevelListReq;
import com.std.core.pojo.request.UserNodeLevelModifyReq;
import com.std.core.pojo.request.UserNodeLevelPageReq;
import com.std.core.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 节点用户ServiceImpl
 *
 * <AUTHOR> Leo
 * @since : 2020-06-07 14:42
 */
@Service
public class UserNodeLevelServiceImpl implements IUserNodeLevelService {

    @Resource
    private UserNodeLevelMapper userNodeLevelMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IConfigService configService;

    @Resource
    private INodeConfigService nodeConfigService;

    @Resource
    private IUserNodeLevelService userNodeLevelService;

    /**
     * 新增节点用户
     *
     * @param req      新增节点用户入参
     * @param operator 操作人
     */
    @Override
    public void create(UserNodeLevelCreateReq req, User operator) {
        userService.detail(req.getUserId());
        UserNodeLevel userNodeLevel = selectByUserId(req.getUserId());
        if (userNodeLevel == null) {
            UserNodeLevel data = EntityUtils.copyData(req, UserNodeLevel.class);
            data.setWay(EUserNodeLevelWay.MANUAL.getCode());
            data.setCreateTime(new Date());
            userNodeLevelMapper.insertSelective(data);
        } else {
            UserNodeLevel data = new UserNodeLevel();
            data.setId(userNodeLevel.getId());
            data.setWay(EUserNodeLevelWay.MANUAL.getCode());
            data.setNodeLevelManual(req.getNodeLevelManual());
            data.setUpdateTime(new Date());
            userNodeLevelMapper.updateByPrimaryKeySelective(data);
        }
    }

    @Override
    public UserNodeLevel selectByUserId(Long userId) {
        UserNodeLevel condition = new UserNodeLevel();
        condition.setUserId(userId);
        List<UserNodeLevel> resultList = userNodeLevelMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(resultList)) {
            return resultList.get(0);
        } else {
            return null;
        }
    }

    /**
     * 删除节点用户
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        UserNodeLevel userNodeLevel = detail(id);
        if (null != userNodeLevel.getNodeLevelAuto() && 0 != userNodeLevel.getNodeLevelAuto()) {
            UserNodeLevel data = new UserNodeLevel();
            data.setId(userNodeLevel.getId());
            data.setWay(EUserNodeLevelWay.AUTO.getCode());
            data.setUpdateTime(new Date());
            userNodeLevelMapper.updateByPrimaryKeySelective(data);
        } else {
            userNodeLevelMapper.deleteByPrimaryKey(id);
        }
    }

    /**
     * 修改节点用户
     *
     * @param req      修改节点用户入参
     * @param operator 操作人
     */
    @Override
    public void modify(UserNodeLevelModifyReq req, User operator) {
        UserNodeLevel userNodeLevel = EntityUtils.copyData(req, UserNodeLevel.class);
        userNodeLevelMapper.updateByPrimaryKeySelective(userNodeLevel);
    }

    /**
     * 详情查询节点用户
     *
     * @param id 主键ID
     * @return 节点用户对象
     */
    @Override
    public UserNodeLevel detail(Long id) {
        UserNodeLevel userNodeLevel = userNodeLevelMapper.selectByPrimaryKey(id);
        if (null == userNodeLevel) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return userNodeLevel;
    }

    /**
     * 分页查询节点用户
     *
     * @param req 分页查询节点用户入参
     * @return 分页节点用户对象
     */
    @Override
    public List<UserNodeLevel> page(UserNodeLevelPageReq req) {
        UserNodeLevel condition = EntityUtils.copyData(req, UserNodeLevel.class);

        List<UserNodeLevel> resultList = userNodeLevelMapper.selectByCondition(condition);

        for (UserNodeLevel userNodeLevel : resultList) {
            User user = userService.detail(userNodeLevel.getUserId());
            userNodeLevel.setUser(user);

            Integer nodeLevel = userNodeLevel.getWay().equals(EUserNodeLevelWay.AUTO.getCode()) ? userNodeLevel.getNodeLevelAuto()
                    : userNodeLevel.getNodeLevelManual();

            userNodeLevel.setNodeLevel(nodeLevel);
        }

        return resultList;

    }

    /**
     * 列表查询节点用户
     *
     * @param req 列表查询节点用户入参
     * @return 列表节点用户对象
     */
    @Override
    public List<UserNodeLevel> list(UserNodeLevelListReq req) {
        UserNodeLevel condition = EntityUtils.copyData(req, UserNodeLevel.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserNodeLevel.class));

        return userNodeLevelMapper.selectByCondition(condition);
    }

    @Override
    public Integer selectHighPriority(Long userId) {
        UserNodeLevel userNodeLevel = selectByUserId(userId);
        if (userNodeLevel == null) {
            return 0;
        }
        if (EUserNodeLevelWay.MANUAL.getCode().equals(userNodeLevel.getWay())) {
            return userNodeLevel.getNodeLevelManual();
        } else {
            return userNodeLevel.getNodeLevelAuto();
        }
    }

    @Override
    public void upgradeAuto(Long userId, Integer nodeLevel) {

        UserNodeLevel userNodeLevel = selectByUserId(userId);
        if (userNodeLevel == null) {
            if (EUserNodeLevel.LEVEL_0.getCode().equals(nodeLevel.toString())) {
                return;
            }
            UserNodeLevel data = new UserNodeLevel();
            data.setUserId(userId);
            data.setWay(EUserNodeLevelWay.AUTO.getCode());
            data.setNodeLevelAuto(nodeLevel);
            data.setCreateTime(new Date());
            userNodeLevelMapper.insertSelective(data);
        } else {
            if (EUserNodeLevel.LEVEL_0.getCode().equals(nodeLevel.toString()) && EUserNodeLevelWay.AUTO.getCode()
                    .equals(userNodeLevel.getWay())) {
                userNodeLevelMapper.deleteByPrimaryKey(userNodeLevel.getId());
            } else {
                UserNodeLevel data = new UserNodeLevel();
                data.setId(userNodeLevel.getId());
                data.setNodeLevelAuto(nodeLevel);
                data.setUpdateTime(new Date());
                userNodeLevelMapper.updateByPrimaryKeySelective(data);
            }
        }

    }

    @Override
    public Long selectMySubUserCount(Long id, Integer nodeLevel) {
        UserNodeLevel condition = new UserNodeLevel();
        condition.setUserId(id);
        condition.setNodeLevel(nodeLevel);
        return userNodeLevelMapper.selectMySubUserCount(condition);
    }


}