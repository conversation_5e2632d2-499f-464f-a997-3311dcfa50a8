package com.std.core.service.impl;

import com.std.core.pojo.domain.UserRole;
import com.std.core.mapper.UserRoleMapper;
import com.std.core.service.IUserRoleService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @author: haiqingzheng
 * @since: 2019-01-08 15:14
 */
@Service
public class UserRoleServiceImpl implements IUserRoleService {

    @Resource
    private UserRoleMapper userRoleMapper;

    @Override
    public void allotRole(Long userId, Long roleId) {
        UserRole data = new UserRole();
        data.setUserId(userId);
        data.setRoleId(roleId);
        userRoleMapper.insert(data);
    }

    @Override
    public void removeByUserId(Long userId) {
        userRoleMapper.deleteByUserId(userId);
    }

    @Override
    public long getTotalCount(Long roleId) {
        UserRole condition = new UserRole();
        condition.setRoleId(roleId);
        return userRoleMapper.selectCountByCondition(condition);
    }

    @Override
    public List<UserRole> list(UserRole userRole) {
        return userRoleMapper.selectByCondition(userRole);
    }

    @Override
    public List<UserRole> listByUserId(Long id) {
        UserRole condition = new UserRole();
        condition.setUserId(id);
        return userRoleMapper.selectByCondition(condition);
    }

    @Override
    public List<UserRole> listByRoleId(Long roleId) {
        UserRole condition = new UserRole();
        condition.setRoleId(roleId);
        return userRoleMapper.selectByCondition(condition);
    }


}
