package com.std.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.enums.ESmsOutBizType;
import com.std.common.exception.AuthException;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.*;
import com.std.core.config.WxAppletsConfig;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.*;
import com.std.core.mapper.RoleMapper;
import com.std.core.mapper.UserMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.MyTeamUserRes;
import com.std.core.pojo.response.SubUserRes;
import com.std.core.pojo.response.TeamUserOssRes;
import com.std.core.pojo.response.UserFlow;
import com.std.core.service.*;
import com.std.core.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

// import com.std.common.enums.ESmsOutBizType;
// import com.std.common.service.ISmsOutService;

/**
 * 用户ServiceImpl
 *
 * <AUTHOR> haiqingzheng
 * @since : 2020-03-02 14:09
 */
@Service
public class UserServiceImpl implements IUserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    RoleMapper roleMapper;

    @Resource
    IMenuService menuService;

    @Resource
    IActionService actionService;

    @Resource
    private IUserRoleService userRoleService;

    @Resource
    private IUserGroupService userGroupService;

    @Resource
    private IRoleService roleService;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Resource
    private IGroupService groupService;

    @Resource
    private IUserLogService userLogService;

    @Resource
    private ISmsOutService smsOutService;

    @Resource
    private SysProperties sysProperties;


    @Resource
    private TokenUtil tokenUtil;

    @Resource
    private IVerificationRecordService verificationRecordService;


    @Resource
    private IConfigService configService;
    @Resource
    private IWechatService wechatService;


    @Resource
    private ICuserService cuserService;

    @Resource
    private ISmsService smsService;


    @Resource
    private WxAppletsConfig wxAppletsConfig;

    @Resource
    private RedisUtil redisUtil;


    @Override
    public boolean checkLoginName(String loginName, String kind) {
        boolean result = false;

        User condition = new User();
        condition.setLoginName(loginName);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            result = true;
        }

        return result;
    }

    @Override
    public void checkLoginPwd(Long id, String loginPwd) {
        User condition = new User();
        condition.setId(id);
        condition.setLoginPwd(MD5Util.md5(loginPwd));

        List<User> list = userMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(EErrorCode.AUTH00001);
        }
    }

    @Override
    public void checkMobileExist(String mobile, String kind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            throw new BizException(EErrorCode.AUTH00005, ":" + mobile);
        }
    }

    @Override
    public void isMobileExist(String mobile, String kind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该手机号不存在");
        }
    }

    @Override
    public void checkMobileExistButOwn(Long id, String mobile, String kind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            for (User user : list) {
                if (!user.getId().equals(id)) {
                    throw new BizException(EErrorCode.AUTH00005);
                }
            }
        }
    }

    private void checkIdNo(String idNo, String kind) {
        User condition = new User();
        condition.setIdNo(idNo);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "身份证号码已经被使用");
        }
    }

    private void checkIdNoButOwn(Long id, String idNo, String kind) {
        User condition = new User();
        condition.setIdNo(idNo);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            for (User user : list) {
                if (!user.getId().equals(id)) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "身份证号码已经被使用");
                }
            }
        }
    }

    @Override
    public void checkMobileNotExist(String mobile, String kind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(EErrorCode.AUTH00010);
        }
    }

    @Override
    public void checkEmail(String email, String kind) {
        User condition = new User();
        condition.setEmail(email);
        condition.setKind(kind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            throw new BizException(EErrorCode.AUTH00006);
        }
    }

    /**
     * 新增用户
     *
     * @param req      新增用户入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(UserCreateReq req, User operator, String ip) {
        // 验证登录名是否含有表情符号
        StringValidator.validateEmoji(req.getLoginName());

        // 验证密码
        PwdUtil.checkPassword(req.getLoginPwd());

        // 验证登录名称是否重复
        if (checkLoginName(req.getLoginName(), req.getKind())) {
            throw new BizException(EErrorCode.AUTH00002);
        }

        if (StringUtils.isNotBlank(req.getMobile())) {
            User user = new User();
            user.setMobile(req.getMobile());
            user.setKind(req.getKind());
            List<User> userList = userMapper.selectByCondition(user);

            if (CollectionUtils.isNotEmpty(userList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该手机号已存在，请选用其他手机号");
            }
        }

        // 添加用户
        User user = wapperUsr(req, operator, ip);
        userMapper.insertSelective(user);

        // 添加角色
        userRoleService.allotRole(user.getId(), req.getRoleCode());

        String content = "管理员新增用户，用户登录名：" + req.getLoginName();
        userLogService.create(operator, EUserLogType.USER_OSS.getCode(), EUserLogType.USER_OSS.getValue(), ip, content);
        return user.getId();
    }

    private User wapperUsr(UserCreateReq req, User operator, String ip) {
        User user = EntityUtils.copyData(req, User.class);
        Long userId = IdGeneratorUtil.generator();

        user.setId(userId);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setLoginPwd(MD5Util.md5(req.getLoginPwd()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(req.getLoginPwd()));
        user.setRegisterDatetime(new Date());
        user.setRegisterIp(ip);
        user.setUpdater(operator.getId());
        user.setUpdaterName(operator.getLoginName());
        user.setUpdateDatetime(new Date());

        return user;
    }

    @Override
    public void createUser(User user) {
        // 验证登录名称是否重复
        if (checkLoginName(user.getLoginName(), user.getKind())) {
            throw new BizException(EErrorCode.AUTH00002);
        }

        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setRegisterDatetime(new Date());
        user.setUpdater(user.getId());
        user.setUpdaterName(user.getLoginName());
        user.setUpdateDatetime(new Date());

        userMapper.insertSelective(user);
    }

    /**
     * 删除用户
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        userMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void remove(Long id, Long userId) {
        User dbUser = detail(id);
        User user = new User();
        user.setId(id);
        if (EUserStatus.NORMAL.getCode().equals(dbUser.getStatus())) {
            user.setStatus(EUserStatus.LOCK.getCode());
        } else {
            user.setStatus(EUserStatus.NORMAL.getCode());
        }
        user.setUpdater(userId);
        user.setUpdateDatetime(new Date());
        userMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 修改用户
     *
     * @param req      修改用户入参
     * @param operator 操作人
     */
    @Override
    public void modify(UserModifyReq req, User operator) {
        User user = EntityUtils.copyData(req, User.class);

        user.setUpdater(operator.getId());
        user.setUpdaterName(operator.getLoginName());
        user.setUpdateDatetime(new Date());

        userMapper.updateByPrimaryKeySelective(user);

        // 删除用户缓存
        delUserRedis(operator.getId(), operator.getKind());
    }

    @Override
    public void delUserRedis(Long userId, String userKind) {
        // 删除缓存
        String userCacheKey = String.format(RedisKeyList.MSG_USER_INFO_KEY, userId.toString());
        redisUtil.del(userCacheKey);

        String userKindKey = String.format(RedisKeyList.MSG_USER_INFO_KIND_KEY, userId.toString(), userKind);
        redisUtil.del(userKindKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void perfect(UserPerfectReq request, User operator) {
        // 检查身份证号是否重复
        checkIdNo(request.getIdNo(), EUserKind.C.getCode());

        User user = EntityUtils.copyData(request, User.class);
        user.setId(operator.getId());
        user.setSex(IdCardUtil.getGenderByIdCard(user.getIdNo()));
        user.setAge(IdCardUtil.getAgeByIdCard(user.getIdNo()));
        userMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    public void modifyUser(User user) {
        userMapper.updateByPrimaryKeySelective(user);
        delUserRedis(user.getId(), StringUtils.isBlank(user.getKind()) ? EUserKind.C.getCode() : user.getKind());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject login(PUserLoginReq request, String kind, String ip) {
        // 验证登录名称
        isMobileExist(request.getMobile(), kind);

        // 验证登录密码
        List<User> userList = listP(request.getMobile(), request.getLoginPwd(), kind);
        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(EErrorCode.AUTH00001);
        }

        // 获取用户信息，并返回token
        User user = userList.get(0);
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(EErrorCode.AUTH00004);
        }

        refreshLastLoginTime(user.getId());
        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject login(UserLoginReq request, String kind, String ip) {
        // 验证登录名称
        if (!checkLoginName(request.getLoginName(), kind)) {
            throw new BizException(EErrorCode.AUTH00003);
        }

        // 验证登录密码
        List<User> userList = list(request.getLoginName(), request.getLoginPwd(), kind);
        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(EErrorCode.AUTH00001);
        }

        // 获取用户信息，并返回token
        User user = userList.get(0);
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(EErrorCode.AUTH00004);
        }

        refreshLastLoginTime(user.getId());
        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        return result;
    }

    @Override
    public JSONObject login(CUserLoginReq request, String kind, String ip) {
        // 验证登录名称
        checkMobileExist(request.getMobile(), kind);


        // 原手机验证码校验
        try {
            smsOutService.checkCaptcha(
                    request.getMobile(), request.getSmsCaptcha(), ESmsOutBizType.C_REG_MOBILE.getCode());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "手机短信验证码不正确");
        }

        // 验证登录密码
        User condition = new User();
        condition.setMobile(request.getMobile());
        condition.setKind(EUserKind.C.getCode());
        List<User> userList = list(condition);

        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(EErrorCode.AUTH00001);
        }

        // 获取用户信息，并返回token
        User user = userList.get(0);
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(EErrorCode.AUTH00004);
        }

        refreshLastLoginTime(user.getId());

        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        return result;
    }

    private void refreshLastLoginTime(Long id) {
        User user = new User();
        user.setId(id);
        user.setLastLoginDatetime(new Date());
        userMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    public void bindMobile(UserBindMobileReq request, User operator, String ip) {
        PhoneUtil.checkMobile(request.getMobile());
        checkMobileExist(request.getMobile(), operator.getKind());

        User user = new User();
        user.setId(request.getId());
        user.setMobile(request.getMobile());
        userMapper.updateByPrimaryKeySelective(user);

        User userNew = detail(request.getId());
//        String content = "状态更新：绑定手机号";
//        userLogService.create(userNew, EUserLogType.BIND_MOBILE.getCode(), EUserLogType.BIND_MOBILE.getValue(), ip, content);

        String title = "手机号绑定成功";
        String conten = "尊敬的用户您好，您的手机号码绑定成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void bindEmail(UserBindEmailReq request, User operator, String ip) {
        EmailUtil.checkEmail(request.getEmail());
        checkEmail(request.getEmail(), operator.getKind());

        User user = new User();
        user.setId(request.getId());
        user.setEmail(request.getEmail());
        userMapper.updateByPrimaryKeySelective(user);

//        User userNew = detail(request.getId());
//        String content = "状态更新：绑定邮箱";
//        userLogService.create(userNew, EUserLogType.BIND_EMAIL.getCode(), EUserLogType.BIND_EMAIL.getValue(), ip, content);

        String title = "邮箱绑定成功";
        String conten = "尊敬的用户您好，您的邮箱绑定成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void modifyLoginPwd(UserModifyLoginPwdReq request, User operator, String ip) {
        checkLoginPwd(operator.getId(), request.getOldLoginPwd());

        User user = new User();
        user.setId(operator.getId());
        user.setLoginPwd(MD5Util.md5(request.getNewLoginPwd()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getNewLoginPwd()));
        userMapper.updateByPrimaryKeySelective(user);

//        String content = "状态更新：修改登录密码";
//        userLogService.create(operator, EUserLogType.MODIFY_LOGINPWD.getCode(), EUserLogType.MODIFY_LOGINPWD.getValue(), ip, content);

        String title = "登录密码修改成功";
        String conten = "尊敬的用户您好，您的登录密码修改成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void modifyPuserPwd(PUserModifyLoginPwdReq request, User operator, String ip) {


        doCheckSmsCaptcha(request.getMobile(), request.getSmsCode(), ESmsOutBizType.FORGET_LOGINPWD.getCode());
        // 检查手机号是否已经存在
        isMobileExist(request.getMobile(), EUserKind.P.getCode());

        User user = new User();
        user.setId(operator.getId());
        user.setLoginPwd(MD5Util.md5(request.getNewLoginPwd()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getNewLoginPwd()));
        userMapper.updateByPrimaryKeySelective(user);

//        String content = "状态更新：修改登录密码";
//        userLogService.create(operator, EUserLogType.MODIFY_LOGINPWD.getCode(), EUserLogType.MODIFY_LOGINPWD.getValue(), ip, content);

        String title = "登录密码修改成功";
        String conten = "尊敬的用户您好，您的登录密码修改成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void modifyLoginPwdOss(UserModifyLoginPwdOSSReq request, User operator, String ip) {
        User user = new User();
        user.setId(request.getId());
        user.setLoginPwd(MD5Util.md5(request.getLoginPwd()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getLoginPwd()));
        userMapper.updateByPrimaryKeySelective(user);

        User userNew = detail(request.getId());
        String content = "状态更新：管理员修改登录密码";
        userLogService
                .create(userNew, EUserLogType.MODIFY_LOGINPWD_OSS.getCode(), EUserLogType.MODIFY_LOGINPWD_OSS.getValue(), ip, content);

        String title = "登录密码更改";
        String conten = "尊敬的用户您好，您的登录密码已被管理员更改，请您知晓";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    /**
     * 管理员修改用户支付密码
     */
    @Override
    public void modifyTransactionPwdOss(UserModifyTransactionPwdOSSReq request, User operator, String ip) {
        User user = new User();
        user.setId(request.getId());
        user.setTradePwd(MD5Util.md5(request.getTransactionPwd()));
        user.setTradePwdStrength(PwdUtil.calculateSecurityLevel(request.getTransactionPwd()));
        userMapper.updateByPrimaryKeySelective(user);

        User userNew = detail(request.getId());

        String content = "状态更新：管理员修改用户支付密码";
        userLogService.create(userNew, EUserLogType.MODIFY_TRANSACTION_OSS.getCode(), EUserLogType.MODIFY_TRANSACTION_OSS.getValue(), ip,
                content);

        String title = "支付密码更改";
        String conten = "尊敬的用户您好，您的支付密码已被管理员更改，请您知晓";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        // 删除用户缓存
        delUserRedis(request.getId(), EUserKind.C.getCode());
    }

    @Override
    public void modifyMobile(UserModifyMobileReq request, User operator, String ip) {


        // 原手机验证码校验
        try {
            smsOutService.checkCaptcha(
                    operator.getMobile(), request.getSmsCaptchaOld(), ESmsOutBizType.MODIFY_MOBILE.getCode());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "原手机短信验证码不正确");
        }

        // 新手机验证码校验
        try {
            smsOutService.checkCaptcha(
                    request.getNewMobile(),
                    request.getSmsCaptchaNew(),
                    ESmsOutBizType.MODIFY_MOBILE.getCode());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "新手机短信验证码不正确");
        }


        checkMobileExist(request.getNewMobile(), operator.getKind());

        User condition = new User();
        condition.setId(operator.getId());
        condition.setMobile(request.getNewMobile());
        condition.setLoginName(request.getNewMobile());

        userMapper.updateByPrimaryKeySelective(condition);

        String content = "状态更新：修改手机号";
        userLogService.create(operator, EUserLogType.MODIFY_MOBILE.getCode(), EUserLogType.MODIFY_MOBILE.getValue(), ip, content);

        String title = "手机号修改成功";
        String conten = "尊敬的用户您好，您的手机号码修改成功";
        smsService.sendMyMsg(condition, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void modifyEmail(UserModifyEmailReq request, User operator, String ip) {
        EmailUtil.checkEmail(request.getNewEmail());
        checkEmail(request.getNewEmail(), operator.getKind());

        User user = new User();
        user.setId(request.getId());
        user.setEmail(request.getNewEmail());
        userMapper.updateByPrimaryKeySelective(user);

        User userNew = detail(request.getId());
//        String content = "状态更新：修改邮箱";
//        userLogService.create(userNew, EUserLogType.MODIFY_EMAIL.getCode(), EUserLogType.MODIFY_EMAIL.getValue(), ip, content);

        String title = "邮箱修改成功";
        String conten = "尊敬的用户您好，您的邮箱修改成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void lock(UserLockReq req, User operator, String ip) {
        for (Long id : req.getIds()) {
            User user = new User();
            user.setId(id);
            user.setStatus(EUserStatus.LOCK.getCode());
            user.setUpdater(operator.getId());
            user.setUpdaterName(operator.getLoginName());
            user.setUpdateDatetime(new Date());
            user.setRemark(req.getRemark());
            userMapper.updateByPrimaryKeySelective(user);

            User userNew = detail(id);
            String content = "状态更新：锁定账号,用户手机号：" + userNew.getMobile();
            userLogService.create(operator, EUserLogType.LOCK.getCode(), EUserLogType.LOCK.getValue(), ip, content);

            User user1 = detailBrief(id);
            delUserRedis(id, user1.getKind());
        }
    }

    @Override
    public void unLock(UserUnLockReq req, User operator, String ip) {
        for (Long id : req.getIds()) {
            User user = new User();
            user.setId(id);
            user.setStatus(EUserStatus.NORMAL.getCode());
            user.setUpdater(operator.getId());
            user.setUpdaterName(operator.getLoginName());
            user.setUpdateDatetime(new Date());
            userMapper.updateByPrimaryKeySelective(user);

            User userNew = detail(id);
            String content = "状态更新：解锁账号。用户手机号：" + userNew.getMobile();
            userLogService.create(operator, EUserLogType.UNLOCK.getCode(), EUserLogType.UNLOCK.getValue(), ip, content);

            String title = "账号已解锁";
            String conten = "尊敬的用户您好，您的已被解锁成功";
            smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

            User user1 = detailBrief(id);
            delUserRedis(id, user1.getKind());
        }
    }

    @Override
    public void lock(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setStatus(EUserStatus.LOCK.getCode());
        userMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    public void unLock(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setStatus(EUserStatus.NORMAL.getCode());
        userMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    public void modifyMobileOSS(UserModifyMobileOSSReq request, User operator, String ip) {

        if (EUserKind.SYS.getCode().equals(operator.getKind())) {

        }
        PhoneUtil.checkMobile(request.getNewMobile());

        User user = detail(request.getId());
        if (!user.getMobile().equals(request.getNewMobile())) {
            checkMobileExist(request.getNewMobile(), user.getKind());

            User condition = new User();
            if (user.getLoginName().equals(user.getMobile())) {
                condition.setLoginName(request.getNewMobile());
            }
            condition.setId(request.getId());
            condition.setMobile(request.getNewMobile());

            userMapper.updateByPrimaryKeySelective(condition);
            delUserRedis(user.getId(), user.getKind());
            String content = "状态更新：管理员修改手机号";
            userLogService.create(operator, EUserLogType.MODIFY_MOBILE_OSS.getCode(), EUserLogType.MODIFY_MOBILE_OSS.getValue(), ip, content);

            String title = "手机号更改";
            String conten = "尊敬的用户您好，您的手机号已被管理员更改，请您知晓";
            smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
        }

        if (StringUtils.isNotBlank(request.getRealName())) {
            User condition = new User();
            condition.setId(request.getId());
            condition.setRealName(request.getRealName());
            userMapper.updateByPrimaryKeySelective(condition);

            delUserRedis(user.getId(), user.getKind());
        }

    }

    @Override
    public void forgetLoginPwd(UserForgetLoginPwdReq request) {
        checkMobileNotExist(request.getMobile(), request.getUserKind());

        // 验证码校验
        doCheckSmsCaptcha(request.getMobile(), request.getSmsCaptcha(), ESmsOutBizType.FORGET_LOGINPWD.getCode());

        User user = detailBrief(request.getMobile(), request.getUserKind());

        if (user != null) {
            User condition = new User();
            condition.setId(user.getId());
            condition.setLoginPwd(MD5Util.md5(request.getLoginPwd()));
            condition.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getLoginPwd()));
            userMapper.updateByPrimaryKeySelective(condition);

            delUserRedis(user.getId(), user.getKind());
        }

        String title = "登录密码已重置";
        String conten = "尊敬的用户您好，您的登录密码已重置成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void allotRoles(Long id, List<Long> roleIdList) {
        User user = detail(id);
        userRoleService.removeByUserId(id);
        for (Long roleId : roleIdList) {
            userRoleService.allotRole(user.getId(), roleId);
        }
    }

    @Override
    public void allotGroups(Long id, List<Long> groupIdList) {
        userGroupService.removeByUser(id);
        User user = detail(id);
        for (Long groupId : groupIdList) {
            userGroupService.allotGroup(user.getId(), groupId);
        }
    }

    @Override
    public List<Menu> listResourceByUser(User operateUser, String... resourceTypes) {
        // 设置对应的menuId
        Long parentId = EClientMenu.topMenuIdByUser(operateUser.getKind());

        // 查询拥有的角色列表
        List<Long> roleList = roleMapper.selectByConditionByUser(operateUser.getId());
        if (CollectionUtils.isEmpty(roleList)) {
            return new ArrayList<>();
        }

        List<String> resourceTypeList = Arrays.asList(resourceTypes);
        return menuService.listConditionByRoles(roleList, parentId, resourceTypeList);
    }

    @Override
    public List<Menu> listResourceByUser(User operateUser, Long parentId) {
        // 查询拥有的角色列表
        List<Long> roleList = roleMapper.selectByConditionByUser(operateUser.getId());
        List<String> resourceTypeList = new ArrayList<>();
        resourceTypeList.add(EResourceType.MENU.getCode());
        resourceTypeList.add(EResourceType.BUTTON.getCode());

        return menuService.listConditionByRoles(roleList, parentId, resourceTypeList);
    }

    @Override
    public List<Action> listActionByUser(User operateUser) {
        // 查询拥有的角色列表
        List<Long> roleList = roleMapper.selectByConditionByUser(operateUser.getId());

        return actionService.listConditionByRole(roleList);
    }

    @Override
    public void queryAllInferior(List<User> userList, Long userId, Integer agentNow, Integer agentMax) {
        if (0 != agentMax && agentNow > agentMax) {
            return;
        }

        User condition = new User();
        condition.setUserReferee(userId);
        List<User> refrereeList = userMapper.selectByCondition(condition);
        for (User user : refrereeList) {
            user.setAgentNo(agentNow);
        }

        if (refrereeList.size() > 0) {
            userList.addAll(refrereeList);
        }

        for (User user : refrereeList) {
            agentNow++;
            queryAllInferior(userList, user.getId(), agentNow, agentMax);
        }

    }

    @Override
    public User checkToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new AuthException(
                    ECommonErrorCode.TOKEN_EMPTY.getCode(), ECommonErrorCode.TOKEN_EMPTY.getValue());
        }
        Long userId = JWTUtil.getUserInfo(token);
        return userMapper.selectByPrimaryKey(userId);
    }

    @Override
    public User tokenDetail(Long id) {
        User user = userMapper.selectByPrimaryKey(id);
        if (null == user) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return user;
    }

    /**
     * 详情查询用户
     *
     * @param id 主键ID
     * @return 用户对象
     */
    @Override
    public User detail(Long id) {
        User user = userMapper.selectByPrimaryKey(id);
        if (null == user) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        if (EUserKind.SYS.getCode().equals(user.getKind()) || EUserKind.OPS.getCode().equals(user.getKind())
        ) {
            initUser(user);
        } else if (EUserKind.C.getCode().equals(user.getKind())) {
            user.setInviteCode(InviteCodeUtil.toSerialCode(user.getInviteNo()));
        }
        if (StringUtils.isNotBlank(user.getTradePwdStrength())) {
            user.setTradePwdFlag(EBoolean.YES.getCode());
        } else {
            user.setTradePwdFlag(EBoolean.NO.getCode());
        }
        return user;
    }

    @Override
    public User detail(Long userId, EUserKind userKind) {

        User condition = new User();
        condition.setId(userId);
        condition.setKind(userKind.getCode());
        List<User> userList = userMapper.selectByCondition(condition);

        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), userId);
        }

        User user = userList.get(0);

        if (EUserKind.SYS.getCode().equals(user.getKind()) || EUserKind.OPS.getCode().equals(user.getKind())) {
            initUser(user);
        }

        return user;
    }

    @Override
    public User detailBrief(Long id) {
        User user = userMapper.selectByPrimaryKey(id);
        if (null == user) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return user;
    }

    @Override
    public User detailBrief(String mobile, String userKind) {
        User user = null;
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(userKind);
        List<User> list = userMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(list)) {
            user = list.get(0);
        }

        return user;
    }

    /**
     * 分页查询用户
     *
     * @param req 分页查询用户入参
     * @return 分页用户对象
     */
    @Override
    public List<User> page(UserPageReq req) {
        User condition = EntityUtils.copyData(req, User.class);

        List<User> list = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            for (User user : list) {
                initUser(user);
            }
        }
        return list;
    }

    @Override
    public List<User> remotePage(RemoteUserPageReq request) {
        User condition = EntityUtils.copyData(request, User.class);
        return userMapper.selectByCondition(condition);
    }

    /**
     * 列表查询用户
     *
     * @param req 列表查询用户入参
     * @return 列表用户对象
     */
    @Override
    public List<User> list(UserListReq req) {
        User condition = EntityUtils.copyData(req, User.class);

        if (StringUtils.isNotBlank(req.getMobile())) {
            condition.setMobileForQuery(req.getMobile());
            condition.setMobile(null);
        }
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), User.class));
        return userMapper.selectByCondition(condition);
    }

    @Override
    public List<User> list(User user) {
        User condition = EntityUtils.copyData(user, User.class);

        return userMapper.selectByCondition(condition);
    }

    @Override
    public List<User> listByReferUserId(Long userId) {
        User condition = new User();
        condition.setUserReferee(userId);

        return userMapper.selectByCondition(condition);
    }

    @Override
    public List<Long> listByGroupId(Long groupId) {
        List<Long> list = new ArrayList<>();
        List<UserGroup> userGroups = userGroupService.listByGroupId(groupId);
        if (CollectionUtils.isNotEmpty(userGroups)) {
            for (UserGroup uGroup : userGroups) {
                list.add(uGroup.getUserId());
            }
        }
        return list;
    }

    @Override
    public List<Long> listByRoleId(Long roleId) {
        List<Long> list = new ArrayList<>();
        List<UserRole> userRoleList = userRoleService.listByRoleId(roleId);
        if (CollectionUtils.isNotEmpty(userRoleList)) {
            for (UserRole userRole : userRoleList) {
                list.add(userRole.getUserId());
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User register(UserRegisterReq request, String ip) {
        doCheckSmsCaptcha(request.getMobile(), request.getSmsCode(), ESmsOutBizType.C_REG_MOBILE.getCode());

        // 检查手机号是否已经存在
        checkMobileExist(request.getMobile(), EUserKind.C.getCode());

        User user = EntityUtils.copyData(request, User.class);
        Long userId = IdGeneratorUtil.generator();

        Date now = new Date();
        user.setId(userId);
        user.setKind(EUserKind.C.getCode());
        user.setNickname(userId.toString().substring(userId.toString().length() - 8, userId.toString().length()));
        user.setLoginName(user.getMobile());
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
        user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
        user.setRegisterDatetime(now);
        user.setRegisterIp(ip);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setPhoto(configService.getStringValue("user_photo"));

        // 注册获得愿力值
//        user.setWillingValue(willingAction.getValue());

        // 推荐人逻辑处理
        if (StringUtils.isNotBlank(request.getInviteCode())) {
            User userReferee = getUserAndCheckByInviteCode(request.getInviteCode());
            user.setUserReferee(userReferee.getId());
            if (userReferee.getChannelGrade() > 0) {
                user.setChannelId(userReferee.getId());
            } else {
                user.setChannelId(userReferee.getChannelId());
            }
        }

        int a = userMapper.insertSelective(user);

        // 分配账户
        if (0 < a) {
            // 添加默认权限
            userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());

//            accountService.distributeAccount(user.getId(), EUserKind.C.getCode());
        }

        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User registerByOss(UserRegisterByOssReq request, User operator) {

        // 检查手机号是否已经存在
        checkMobileExist(request.getMobile(), EUserKind.P.getCode());

        User user = EntityUtils.copyData(request, User.class);
        Long userId = IdGeneratorUtil.generator();

        Date now = new Date();
        user.setId(userId);
        user.setKind(EUserKind.P.getCode());
        user.setNickname(request.getNickname());
        user.setLoginName(user.getMobile());
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
        user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
        user.setRegisterDatetime(now);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setPhoto(configService.getStringValue("user_photo"));
        user.setUpdater(operator.getId());
        user.setUpdaterName(operator.getNickname());
        user.setUpdateDatetime(now);

        int a = userMapper.insertSelective(user);

        // 分配账户
        if (0 < a) {
            // 添加默认权限
            userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleP());

        }

        return user;
    }

    @Override
    public void doCheckSmsCaptcha(String mobile, String captcha, String bizType) {
        // 验证码校验
        try {
            smsOutService.checkCaptcha(mobile, captcha, bizType);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "短信验证码不正确");
        }
    }

    @Override
    public List<User> getUsersByUserReferee(Long userReferee) {
        List<User> userList = new ArrayList<User>();
        if (null != userReferee) {
            User condition = new User();
            condition.setUserReferee(userReferee);
            userList = userMapper.selectByCondition(condition);
        }
        return userList;
    }

    @Override
    public List<User> getRichUsersByUserReferee(Long userReferee) {
        List<User> userList = new ArrayList<User>();
        if (null != userReferee) {
            User condition = new User();
            condition.setUserReferee(userReferee);
            userList = userMapper.selectUserRefereeByCondition(condition);
        }
        return userList;
    }

    @Override
    public Integer selectCount(User condition) {
        return userMapper.selectCount(condition);
    }

    @Override
    public User selectSummaryInfo(Long userId) {
        User user = new User();
        User dbUser = userMapper.selectByPrimaryKey(userId);
        if (null != dbUser) {
            user.setId(dbUser.getId());
            user.setLoginName(dbUser.getLoginName());
            user.setNickname(dbUser.getNickname());
            user.setPhoto(dbUser.getPhoto());
            user.setMobile(dbUser.getMobile());
            user.setRealName(dbUser.getRealName());
            user.setInviteCode(dbUser.getInviteCode());
        }

        return user;
    }


    @Override
    public void editProfile(UserEditProfileReq request, User operator) {

        User user = EntityUtils.copyData(request, User.class);
        user.setId(operator.getId());
        user.setNicknameChangeFlag(EBoolean.YES.getCode());
        userMapper.updateByPrimaryKeySelective(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addByOss(UserSysCreateCReq request, User operator, String ip) {

        // 检查手机号是否已经存在
        checkMobileExist(request.getMobile(), EUserKind.C.getCode());

        // 检查身份证号是否重复
        checkIdNo(request.getIdNo(), EUserKind.C.getCode());

        User user = EntityUtils.copyData(request, User.class);
        Date now = new Date();
        user.setId(IdGeneratorUtil.generator());
        user.setKind(EUserKind.C.getCode());
        user.setLoginName(user.getMobile());
        user.setSex(IdCardUtil.getGenderByIdCard(user.getIdNo()));
        user.setAge(IdCardUtil.getAgeByIdCard(user.getIdNo()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
        user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
        user.setRegisterDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setCompanyId(-1L);

        userMapper.insertSelective(user);

        // 添加默认权限
        userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());
        String content = "状态更新：管理员代用户注册";
        userLogService.create(operator, EUserLogType.REGISTER_OSS.getCode(), EUserLogType.REGISTER_OSS.getValue(), ip, content);

        String title = "欢迎注册使用杏福宝";
        String conten = "尊敬的用户您好，欢迎您注册并使用杏福宝。本次注册方式为管理员代注册";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editByOss(UserSysEditCReq request, User operator, String ip) {

        // 检查手机号是否已经存在
        checkMobileExistButOwn(request.getId(), request.getMobile(), EUserKind.C.getCode());

        // 检查身份证号是否重复
        checkIdNoButOwn(request.getId(), request.getIdNo(), EUserKind.C.getCode());

        User user = EntityUtils.copyData(request, User.class);
        Date now = new Date();
        user.setLoginName(user.getMobile());
        user.setSex(IdCardUtil.getGenderByIdCard(user.getIdNo()));
        user.setAge(IdCardUtil.getAgeByIdCard(user.getIdNo()));
        if (StringUtils.isNotBlank(user.getLoginPwd())) {
            user.setLoginPwd(MD5Util.md5(request.getLoginPwd()));
            user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getLoginPwd()));
        }
        user.setUpdater(operator.getId());
        user.setUpdaterName(operator.getLoginName());
        user.setUpdateDatetime(now);

        userMapper.updateByPrimaryKeySelective(user);

        String content = "状态更新：修改用户个人信息";
        userLogService
                .create(operator, EUserLogType.PERSONAL_INFORMATION_OSS.getCode(), EUserLogType.PERSONAL_INFORMATION_OSS.getValue(), ip,
                        content);
        String title = "个人资料更改";
        String conten = "尊敬的用户您好，您的个人资料已被管理员更改，请您知晓";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        delUserRedis(request.getId(), EUserKind.C.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importByOss(UserSysImportCReq request, User operator) {
        List<User> userList = new ArrayList<User>();
        for (UserSysCreateCReq req : request.getUserList()) {
            // 检查手机号是否已经存在
            checkMobileExist(req.getMobile(), EUserKind.C.getCode());

            // 检查身份证号是否重复
            checkIdNo(req.getIdNo(), EUserKind.C.getCode());

            User user = EntityUtils.copyData(req, User.class);
            Date now = new Date();
            user.setId(IdGeneratorUtil.generator());
            user.setKind(EUserKind.C.getCode());
            user.setLoginName(user.getMobile());
            user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getLoginPwd()));
            user.setLoginPwd(MD5Util.md5(user.getLoginPwd()));
            user.setRegisterDatetime(now);
            user.setStatus(EUserStatus.NORMAL.getCode());
            user.setCompanyId(-1L);

            userList.add(user);
        }
        userMapper.insertBatch(userList);
    }

    @Override
    public User detailByIdNo(String idNo) {
        User condition = new User();
        condition.setIdNo(idNo);
        condition.setKind(EUserKind.C.getCode());
        List<User> list = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    public List<User> list(String loginName, String loginPwd, String kind) {
        User condition = new User();
        condition.setLoginName(loginName);
        condition.setLoginPwd(MD5Util.md5(loginPwd));
        condition.setKind(kind);
        return userMapper.selectByCondition(condition);
    }

    public List<User> listP(String mobile, String loginPwd, String kind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setLoginPwd(MD5Util.md5(loginPwd));
        condition.setKind(kind);
        return userMapper.selectByCondition(condition);
    }

    private void initUser(User user) {
        // 用户角色列表
        List<UserRole> userRoleList = userRoleService.listByUserId(user.getId());

        List<Role> roleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userRoleList)) {
            for (UserRole userRole : userRoleList) {
                Role role = roleService.detail(userRole.getRoleId());
                roleList.add(role);
            }
        }
        user.setRoleList(roleList);

        user.setGroupList(initUserGroup(user.getId()));
    }

    private List<Group> initUserGroup(Long userId) {
        // 用户组列表
        List<UserGroup> userGroupList = userGroupService.listByUserId(userId);
        List<Group> groupList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userGroupList)) {
            for (UserGroup userGroup : userGroupList) {
                Group group = groupService.info(userGroup.getGroupId());
                groupList.add(group);
            }
        }

        return groupList;
    }

    @Override
    public User getUserAndCheckByInviteCode(String inviteCode) {
        // 解析邀请码
        Long inviteNo = InviteCodeUtil.codeToId(inviteCode);
        User condition = new User();
        condition.setInviteNo(inviteNo);
        condition.setKind(EUserKind.C.getCode());
        List<User> userList = userMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无效的邀请码");
        }
        return userList.get(0);
    }

    @Override
    public void modifyTradePwd(UserModifyTradePwdReq request, User operator, String ip) {
        checkTradePwd(operator.getId(), request.getOldTradePwd());

        User user = new User();
        user.setId(operator.getId());
        user.setTradePwd(MD5Util.md5(request.getNewTradePwd()));
        user.setTradePwdStrength(PwdUtil.calculateSecurityLevel(request.getNewTradePwd()));
        userMapper.updateByPrimaryKeySelective(user);

//        String content = "状态更新：修改支付密码";
//        userLogService.create(operator, EUserLogType.MODIFY_TRADEPWD.getCode(), EUserLogType.MODIFY_TRADEPWD.getValue(), ip, content);

        String title = "支付密码修改成功";
        String conten = "尊敬的用户您好，您的支付密码修改成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    @Override
    public void checkTradePwd(Long id, String tradePwd) {
        User user = detailBrief(id);
        if (StringUtils.isBlank(user.getTradePwdStrength())) {
            throw new BizException(EErrorCode.CORE00008);
        }

        User condition = new User();
        condition.setId(id);
        condition.setTradePwd(MD5Util.md5(tradePwd));

        List<User> list = userMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(EErrorCode.CORE00009);
        }
    }

    @Override
    public void bindTradePwd(UserBindTradePwdReq request, User operator, String ip) {
        User user = detailBrief(operator.getId());
        doCheckSmsCaptcha(user.getMobile(), request.getSmsCaptcha(), ESmsOutBizType.BIND_TRADEPWD.getCode());

        User condition = new User();
        condition.setId(operator.getId());
        condition.setTradePwd(MD5Util.md5(request.getTradePwd()));
        condition.setTradePwdStrength(PwdUtil.calculateSecurityLevel(request.getTradePwd()));
        userMapper.updateByPrimaryKeySelective(condition);

//        String content = "状态更新：绑定支付密码";
//        userLogService.create(operator, EUserLogType.BIND_TRADEPWD.getCode(), EUserLogType.BIND_TRADEPWD.getValue(), ip, content);

        String title = "支付密码绑定成功";
        String conten = "尊敬的用户您好，您的支付密码绑定成功";
        smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());
    }

    /**
     * 永久封号
     *
     * @param userId 用户id
     */
    @Override
    public void permanentBan(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setStatus(EUserStatus.PERMANENT_BAN.getCode());
        userMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 根据id更改状态
     *
     * @param userId 参数
     */
    @Override
    public void updateStatusById(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setStatus(EUserStatus.NORMAL.getCode());
        userMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 得到所有账户信息
     */
    @Override
    public List<User> getAllCUser() {
        List<User> userList = userMapper.getAllCUser(EUserKind.C.getCode(), EUserStatus.NORMAL.getCode());
        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户为空");
        }

        return userList;
    }


    @Override
    public void insertUser(User user) {
        userMapper.insertSelective(user);
    }

    @Override
    public List<User> cUserlist() {

        return userMapper.cUserlist();
    }

    @Override
    public JSONObject merchantLogin(MerchantLoginReq request, String ip) {
        // 验证登录名称
        if (!checkLoginName(request.getLoginName(), EUserKind.C.getCode())) {
            throw new BizException(EErrorCode.AUTH00003);
        }

        // 验证登录密码
        List<User> userList = list(request.getLoginName(), request.getLoginPwd(), EUserKind.C.getCode());
        if (CollectionUtils.isEmpty(userList)) {
            throw new BizException(EErrorCode.AUTH00001);
        }

        // 获取用户信息，并返回token
        User user = userList.get(0);
        if (EUserStatus.LOCK.getCode().equals(user.getStatus())) {
            throw new BizException(EErrorCode.AUTH00004);
        }

        return null;
    }

    @Override
    public void queryAllInferior(List<User> userList, Long userId, Integer inviteLevel) {
        if (inviteLevel == 0) {
            return;
        }
        User condition = new User();
        condition.setUserReferee(userId);
        List<User> refrereeList = userMapper.selectByCondition(condition);
        if (refrereeList.size() > 0) {
            userList.addAll(refrereeList);
        }
        for (User user : refrereeList) {
            queryAllInferior(userList, user.getId(), inviteLevel - 1);
        }

    }

    @Override
    public void setLevelCheckAllUser(List<User> userList, Long userId, Long oldUserId, Integer grade) {
        User condition = new User();
        condition.setUserReferee(userId);
        List<User> refrereeList = userMapper.selectByCondition(condition);

        for (User user : refrereeList) {
            if (user.getChannelGrade() > 0) {
                if (user.getChannelGrade() >= grade) {
                    //如果下级比你等级大，吧下级拉出新的链
                    if (user.getChannelId() != null && user.getChannelId().equals(oldUserId)) {
                        user.setChannelId(-1L);
                        userList.add(user);
                    }
                    continue;
                } else if (user.getChannelId() != null && user.getChannelGrade() < grade) {
                    continue;
                }
            }
            user.setChannelId(oldUserId);
            userList.add(user);
            setLevelCheckAllUser(userList, user.getId(), oldUserId, grade);
        }

    }

    @Override
    public void queryChannelAllInferior(List<User> userList, Long userId) {
        User condition = new User();
        condition.setChannelId(userId);
        List<User> refrereeList = userMapper.selectByCondition(condition);
        if (refrereeList.size() > 0) {
            userList.addAll(refrereeList);
        }
        for (User user : refrereeList) {
            queryChannelAllInferior(userList, user.getId());
        }

    }

    /**
     * oss:查询用户团队
     */
    @Override
    public List<TeamUserOssRes> teamDeatil(Long id) {
        User user = new User();
        user.setUserReferee(id);
        return userMapper.ossTeamDeatil(user);
    }

    /**
     * 团队查询
     */
    @Override
    public MyTeamUserRes teamUser(User operator) {
        MyTeamUserRes res = new MyTeamUserRes();
        List<SubUserRes> userResList = subUserList(operator);
        Integer a = userResList.size();

        List<User> userList = new ArrayList<>();
        queryAllInferior(userList, operator.getId(), configService.getIntegerValue(EConfigType.INCOME.NODE_INCOME_LEVEL.getCode()));

        res.setSubUserCount(a.longValue());
        res.setTeamPerformance(BigDecimal.ZERO);
        res.setTeamUserCount(Long.valueOf(userList.size()));
        return res;
    }

    public void add(User operator) {
        User user = new User();
        if (null != operator.getUserReferee()) {
            user.setId(operator.getUserReferee());
        }
        user.setUserReferee(operator.getId());
        // 直推人数
        List<User> userList = userMapper.selectByCondition(user);

        for (User user1 : userList) {
            add(user1);
        }
    }

    @Override
    public User selectUserByMobile(String mobile) {
        User condition = new User();
        condition.setMobile(mobile);
        List<User> userList = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(userList)) {
            return userList.get(0);
        }
        return null;
    }

    @Override
    public List<SubUserRes> subUserList(User operator) {
        List<SubUserRes> subUserRes = userMapper.selectSubUserList(operator.getId());
        subUserRes.forEach(item -> {
            item.setMobile(PhoneUtil.hideMobile(item.getMobile()));
            item.setLevel(EUserLevel.getEnum(item.getLevel()).getValue());
//            item.setTeamPerformance(BigDecimal.ZERO);
//            List<User> userList = new ArrayList<>();
//            queryAllInferior(userList, item.getId(), configService.getIntegerValue(EConfigType.INCOME.NODE_INCOME_LEVEL.getCode()));
//            item.setTeamUserCount(Long.valueOf(userList.size()));
        });
        return subUserRes;
    }

    /**
     * 查询所有正常的C端用户id
     */
    @Override
    public List<Long> selectIdList() {
        return userMapper.selectIdList();
    }


    @Override
    public void setLevel(SetUserLevelReq req) {
        User user = detailBrief(req.getUserId());
        //判断上级是否存在身份
        if (user.getChannelId() != null) {
            User refUser = detailBrief(user.getChannelId());
            if (refUser.getChannelGrade() <= req.getLevel()) {
                //需要单独拉出来一条链
                user.setChannelId(-1L);
                user.setChannelGrade(req.getLevel());
            } else {
                //     无等级链上级
                user.setChannelGrade(req.getLevel());
            }
        } else {
//          无等级链上级
            user.setChannelGrade(req.getLevel());
        }
        //同时需要检查下级是否是先设置的等级，如果是，则判断下级是否低于自己的下级，如果是，则吧上级id改为自己的
        List<User> userList = new ArrayList<>();
        userList.add(user);
        setLevelCheckAllUser(userList, user.getId(), user.getId(), user.getChannelGrade());

        userMapper.batchUpdate(userList);

    }

    @Override
    public User registerInstead(UserRegisterOssReq request, String ip) {
        // 检查手机号是否已经存在
        checkMobileExist(request.getMobile(), EUserKind.C.getCode());

        User user = EntityUtils.copyData(request, User.class);
        Long userId = IdGeneratorUtil.generator();

        Date now = new Date();
        user.setId(userId);
        user.setKind(EUserKind.C.getCode());
        user.setNickname(user.getMobile().substring(0, 3).concat("****").concat(user.getMobile().substring(7)));
        user.setLoginName(user.getMobile());
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(user.getMobile().substring(user.getMobile().length() - 6)));
        user.setLoginPwd(MD5Util.md5(user.getMobile().substring(user.getMobile().length() - 6)));
        user.setRegisterDatetime(now);
        user.setRegisterIp(ip);
        user.setLastLoginDatetime(now);
        user.setStatus(EUserStatus.NORMAL.getCode());
        user.setPhoto(configService.getStringValue("user_photo"));

        // 推荐人逻辑处理
        if (StringUtils.isNotBlank(request.getInviteCode())) {
            User userReferee = getUserAndCheckByInviteCode(request.getInviteCode());
            user.setUserReferee(userReferee.getId());
            if (userReferee.getChannelGrade() > 0) {
                user.setChannelId(userReferee.getId());
            } else {
                user.setChannelId(userReferee.getChannelId());
            }

        }

        userMapper.insertSelective(user);

        // 添加默认权限
        userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());

        return user;
    }

    @Override
    public void setRefuser(SetUserFidReq req) {
        User user = detailBrief(req.getUserId());
        User fUser = detailBrief(req.getFid());
        if (fUser.getChannelGrade() <= user.getChannelGrade()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "上级用户等级不能小于等于该用户!");
        }

        user.setChannelId(fUser.getId());
        modifyUser(user);

    }

    @Override
    public void queryAllInferiorNotInviteLevel(List<User> userList, Long userId) {
        User condition = new User();
        condition.setUserReferee(userId);
        List<User> refrereeList = userMapper.selectByCondition(condition);
        if (refrereeList.size() > 0) {
            userList.addAll(refrereeList);
        }
        for (User user : refrereeList) {
            queryAllInferiorNotInviteLevel(userList, user.getId());
        }
    }

    @Override
    public void queryAllInferiorNotInviteLevelSubChannel(List<User> userList, Long userId) {
        User condition = new User();
        condition.setUserReferee(userId);
        condition.setChannelGrade(0);
        List<User> refrereeList = userMapper.selectByCondition(condition);
        if (refrereeList.size() > 0) {
            userList.addAll(refrereeList);
        }
        for (User user : refrereeList) {
            queryAllInferiorNotInviteLevelSubChannel(userList, user.getId());
        }
    }

    @Override
    public void batchModifyChannelFlag(List<User> userList, Long channelId) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        userMapper.batchModifyChannelFlag(userList, channelId);
    }


    @Override
    public User selectUserByMobile(String mobile, String userKind) {
        User condition = new User();
        condition.setMobile(mobile);
        condition.setKind(userKind);
        List<User> userList = userMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(userList)) {
            return userList.get(0);
        }
//        throw new BizException(EErrorCode.CORE00300);
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject wxAppletsLogin(UserWxAppletsLoginReq request) {
        // 获取appid和session_key
        JSONObject jsonObject = WxAppletsUtil.getAccessKey(request.getCode(), wxAppletsConfig.getAppId(), wxAppletsConfig.getAppSecret());
        String sessionKey = jsonObject.getString("session_key");
        String openid = jsonObject.getString("openid");
        User user = userMapper.getUserByOpenid(openid);
        if (user == null) {
            //拿手机号
            JSONObject infoByPhone = WxAppletsUtil.getUserInfo(request.getEncryptedDataByPhone(), sessionKey, request.getIvByPhone());
            String mobile = infoByPhone.getString("phoneNumber");
            if (StringUtils.isBlank(mobile)) {
                return null;
            }
            user = detailBrief(mobile, EUserKind.C.getCode());
            Long refUserId = null;

            if (StringUtils.isNotBlank(request.getInviteNo())) {
                try {
                    refUserId = getUserAndCheckByInviteCode(request.getInviteNo()).getId();
                } catch (Exception e) {
                }
            }

            if (user == null) {
                //拿用户信息

                user = new User();
                Date now = new Date();
                user.setId(IdGeneratorUtil.generator());
                user.setKind(EUserKind.C.getCode());
                user.setMobile(mobile);
                user.setLoginName(mobile);
                user.setUserReferee(refUserId);
                user.setNickname("微信用户");
                user.setPhoto(configService.getStringValue(SysConstants.USER_PHOTO));
                user.setRegisterDatetime(now);
                user.setStatus(EUserStatus.NORMAL.getCode());
                user.setCompanyId(-1L);
                user.setLoginPwd(MD5Util.md5("888888"));
                user.setLoginPwdStrength("1");
                user.setLastLoginDatetime(now);
                userMapper.insertSelective(user);

                Cuser cuser = new Cuser();
                cuser.setUserId(user.getId());
                cuser.setOpenid(openid);
                cuser.setCreateDatetime(user.getRegisterDatetime());
                cuserService.insert(cuser);
                // 添加默认权限
                userRoleService.allotRole(user.getId(), sysProperties.getDefaultRoleC());
            } else {
                Cuser cuser = cuserService.detailByUserId(user.getId());
                CuserModifyReq data = new CuserModifyReq();
                data.setId(cuser.getId());
                data.setOpenid(openid);
                User userUpdate = new User();
                userUpdate.setId(user.getId());
                userUpdate.setLastLoginDatetime(new Date());
                modifyUser(userUpdate);
                cuserService.modify(data, null);
            }

            String title = "欢迎加入【西溪蔡志忠美术馆】";
            String conten = "欢迎您踏入这片充满创意与灵感的殿堂——西溪蔡志忠美术馆。在这里，您不仅将领略到蔡志忠大师卓越的艺术成就，还能通过我们精心设计的丰富功能，与他笔下那些经典角色和故事进行一场跨越时空的对话。";
            smsService.sendMyMsg(user, title, conten, ESmsRefType.SYSTEM_MESSAGE.getCode(), ESmsRefType.SYSTEM_MESSAGE.getValue());

        }

        JSONObject result = tokenUtil.generalToken(user.getId(), 30 * 24 * 60 * 60 * 1000L);
        result.put("nickName", user.getNickname());
        result.put("photo", user.getPhoto());
        delUserRedis(user.getId(), user.getKind());
        return result;
    }

    @Override
    public UserFlow userFlow() {
        VerificationRecord verificationRecord = new VerificationRecord();
        UserFlow userFlow = new UserFlow();
        userFlow.setNumber(verificationRecordService.list(verificationRecord).size());
        return userFlow;
    }

    @Override
    public void modifyPuserPwdByOss(PUserModifyLoginPwdByOssReq request, User operator, String ip) {
        User user = detail(request.getUserId());
        if (!EUserKind.P.getCode().equals(user.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能修改平台用户密码");
        }
        user.setLoginPwd(MD5Util.md5(request.getNewLoginPwd()));
        user.setLoginPwdStrength(PwdUtil.calculateSecurityLevel(request.getNewLoginPwd()));
        userMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 恢复预约
     * @param user
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reservationRecovery(User user) {
        user = detailBrief(user.getId());
        if (!EUserReservationStatus.LOCK.getCode().equals(user.getReservationStatus())){
            return;
        }
        if (new Date().before(user.getReservationUnsealTime())){
            return;
        }

        User userModify = new User();
        userModify.setId(user.getId());
        userModify.setReservationStatus(EUserReservationStatus.NORMAL.getCode());
        userModify.setReservationExpiredNumber(0);
        modifyUser(userModify);
    }

    @Override
    public void cancelOrder(String serialNumber) {

//        // 查看微信端的订单是否已支付
//        // 未支付取消上一笔
//        Transaction transaction = wechatService.queryOrderByOutTradeNo(serialNumber);
//        if (null == transaction || "CLOSED".equals(transaction.getTradeState())) {
//            return;
//        } else if (null == transaction || !"SUCCESS".equals(transaction.getTradeState())) {
//            boolean closeFlag = wechatService.closeOrder(serialNumber);
//            if (!closeFlag) {
//                throw new BizException(EErrorCode.CORE00264);
//            }
//        } else {
//            throw new BizException(EErrorCode.CORE00264);
//        }

    }


    public static void main(String[] args) {
        System.out.println(JWTUtil.generalToken("928902300859375616", 30 * 24 * 60 * 60 * 1000L)
        );
    }
}
