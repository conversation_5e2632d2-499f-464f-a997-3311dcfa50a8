package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.VerificationRecordMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.VerificationRecordCreateReq;
import com.std.core.pojo.request.VerificationRecordListReq;
import com.std.core.pojo.request.VerificationRecordListFrontReq;
import com.std.core.pojo.request.VerificationRecordModifyReq;
import com.std.core.pojo.request.VerificationRecordPageReq;
import com.std.core.pojo.request.VerificationRecordPageFrontReq;
import com.std.core.pojo.response.*;
import com.std.core.service.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
* 核销ServiceImpl
*
* <AUTHOR> mjd
* @since : 2024-12-30 01:10
*/
@Service
public class VerificationRecordServiceImpl implements IVerificationRecordService {

    @Resource
    private VerificationRecordMapper verificationRecordMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IActivityOrderService activityOrderService;
    @Resource
    private IActivityService activityService;
    @Resource
    private IActivityTicketLineService activityTicketLineService;
    private String errorMessage;

    /**
     * 新增核销
     *
     * @param req 新增核销入参
     * @param operator 操作人
     */
    @Override
    public VerificationRecordCreateRes create(VerificationRecordCreateReq req, User operator) {
        VerificationRecordCreateRes res = new VerificationRecordCreateRes();
        errorMessage = new String();
        String flag = new String();
        VerificationRecord verificationRecord = EntityUtils.copyData(req, VerificationRecord.class);
        ActivityOrder activityOrder = activityOrderService.detail(req.getActivityOrderId());
        if(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_1.getCode().equals(activityOrder.getStatus())){
            errorMessage ="该预约单已核销，请勿重复使用";
            flag= EBoolean.NO.getCode();
        }
        Date date = activityOrder.getDate();
        Activity activity = activityService.detail(activityOrder.getActivityId());

        VerificationRecord resRecord = new VerificationRecord();
        if(activityOrder.getDate().after(activity.getEndTime())){
             errorMessage ="门票已过期,请提示访客出示有效二维码。";
             flag= EBoolean.NO.getCode();
        }
        Date today = DateUtil.getTodayStart();
        if(today.compareTo(date)!=0){
             errorMessage ="非当天门票,请提示访客核对预约日期。";
            flag= EBoolean.NO.getCode();
        }
        if(StringUtils.isBlank(errorMessage)) {
            ActivityTicketLine activityTicketLine = activityTicketLineService.detail(activityOrder.getTicketLineId());
            verificationRecord.setActivityId(activityOrder.getActivityId());
            verificationRecord.setTicketLineId(activityOrder.getTicketLineId());
            verificationRecord.setUserId(activityOrder.getUserId());
            verificationRecord.setPrice(activityOrder.getPrice());
            verificationRecord.setTicketLineName(activityTicketLine.getName());
            verificationRecord.setCode(activityOrder.getCodePic());
            verificationRecord.setPic(activity.getPic());
            verificationRecord.setName(activityOrder.getName());
            verificationRecord.setContract(activityOrder.getContact());
            verificationRecord.setDate(DateUtil.getDayStartTime(activityOrder.getDate()));
            verificationRecord.setStatus(EVerificationRecordStatus.VERIFICATION_RECORD_STATUS_0.getCode());
            verificationRecord.setCreater(operator.getId());
            verificationRecord.setCreateTime(new Date());
            verificationRecord.setCreaterName(operator.getLoginName());

            verificationRecordMapper.insertSelective(verificationRecord);
            flag= EBoolean.YES.getCode();
            resRecord=verificationRecord;

            activityOrder.setStatus(EActivityOrderStatus.ACTIVITY_ORDER_STATUS_1.getCode());
            activityOrder.setUpdater(operator.getId());
            activityOrder.setUpdaterName(operator.getLoginName());
            activityOrder.setUpdateDatetime(new Date());
            activityOrderService.modify(activityOrder);
        }
        res.setErrorMessage(errorMessage);
        res.setFlag(flag);
        res.setVerificationRecord(resRecord);

        return res;
    }

    /**
     * 删除核销
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        verificationRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改核销
     *
     * @param req 修改核销入参
     * @param operator 操作人
     */
    @Override
    public void modify(VerificationRecordModifyReq req, User operator) {
        VerificationRecord verificationRecord = EntityUtils.copyData(req, VerificationRecord.class);
        verificationRecordMapper.updateByPrimaryKeySelective(verificationRecord);
    }

    /**
     * 详情查询核销
     *
     * @param id 主键ID
     * @return 核销对象
     */
    @Override
    public VerificationRecord detail(Long id) {
        VerificationRecord verificationRecord = verificationRecordMapper.selectByPrimaryKey(id);
        if (null == verificationRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        verificationRecord.setUser(userService.selectSummaryInfo(verificationRecord.getUserId()));

        return verificationRecord;
    }

    /**
     * 分页查询核销
     *
     * @param req 分页查询核销入参
     * @return 分页核销对象
     */
    @Override
    public List<VerificationRecord> page(VerificationRecordPageReq req) {
        VerificationRecord condition = EntityUtils.copyData(req, VerificationRecord.class);

        List<VerificationRecord> verificationRecordList = verificationRecordMapper.selectByCondition(condition);
        // 转译UserId
        verificationRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            item.setActivityName(activityService.detail(item.getActivityId()).getName());
            User user = userService.detail(item.getCreater());
            item.setMobile(user.getMobile());
            item.setNickname(user.getNickname());
        });

        return verificationRecordList;
    }

    /**
     * 列表查询核销
     *
     * @param req 列表查询核销入参
     * @return 列表核销对象
     */
    @Override
    public List<VerificationRecord> list(VerificationRecordListReq req) {
        VerificationRecord condition = EntityUtils.copyData(req, VerificationRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), VerificationRecord.class));

        List<VerificationRecord> verificationRecordList = verificationRecordMapper.selectByCondition(condition);
        // 转译UserId
        verificationRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return verificationRecordList;
    }

    @Override
    public List<VerificationRecord> list(VerificationRecord req) {
        List<VerificationRecord> verificationRecordList = verificationRecordMapper.selectByCondition(req);
        return verificationRecordList;
    }

    /**
     * 前端详情查询核销
     *
     * @param id 主键ID
     * @return 核销对象
     */
    @Override
    public VerificationRecordDetailRes detailFront(Long id) {
        VerificationRecordDetailRes res = new VerificationRecordDetailRes();

        VerificationRecord verificationRecord = verificationRecordMapper.selectByPrimaryKey(id);
        if (null == verificationRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        verificationRecord.setUser(userService.selectSummaryInfo(verificationRecord.getUserId()));

        BeanUtils.copyProperties(verificationRecord, res);

        return res;
    }

    /**
     * 前端分页查询核销
     *
     * @param req 前端分页查询核销入参
     * @return 分页核销对象
     */
    @Override
    public List<VerificationRecordPageByDays> pageFront(VerificationRecordPageFrontReq req,User operator) {
        List<VerificationRecordPageByDays> daysResList = new ArrayList<>();
        if(StringUtils.isBlank(req.getStartDate())&&StringUtils.isBlank(req.getEndDate())){
            String date = DateUtil.getDate(new Date(), DateUtil.DATA_TIME_PATTERN_9);
            req.setStartDate(date);
            req.setEndDate(date);
        }
        VerificationRecord condition = EntityUtils.copyData(req, VerificationRecord.class);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATA_TIME_PATTERN_9);
        LocalDate start = LocalDate.parse(req.getStartDate(), formatter);
        LocalDate end = LocalDate.parse(req.getEndDate(), formatter);
        List<String> dates = new ArrayList<>();

        while (!start.isAfter(end)) {
            dates.add(start.format(formatter));
            start = start.plusDays(1);
        }
        for (String date : dates) {
            VerificationRecordPageByDays verificationRecordPageByDays = new VerificationRecordPageByDays();
            verificationRecordPageByDays.setDate(date);
            condition.setCreater(operator.getId());
            condition.setStartDate(DateUtil.strToDate(date,DateUtil.DATA_TIME_PATTERN_9));
            condition.setEndDate(DateUtil.strToDate(date,DateUtil.DATA_TIME_PATTERN_9));
            List<VerificationRecord> verificationRecordList = verificationRecordMapper.selectByCondition(condition);
            // 转译UserId
            verificationRecordList.forEach(item -> {
                item.setUser(userService.selectSummaryInfo(item.getUserId()));
            });

            List< VerificationRecordPageRes> resList = verificationRecordList.stream().map((entity) -> {
                VerificationRecordPageRes res = new VerificationRecordPageRes();
                BeanUtils.copyProperties(entity, res);
                return res;
            }).collect(Collectors.toList());
            verificationRecordPageByDays.setVerificationRecordPageResList(resList);
            verificationRecordPageByDays.setNumber(resList.size());
            verificationRecordPageByDays.setPrice(resList.stream().map(VerificationRecordPageRes::getPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            if(!resList.isEmpty()) {
                daysResList.add(verificationRecordPageByDays);
            }
        }


        return  daysResList;
    }

    /**
     * 前端列表查询核销
     *
     * @param req 前端列表查询核销入参
     * @return 列表核销对象
     */
    @Override
    public List< VerificationRecordListRes> listFront(VerificationRecordListFrontReq req) {
        VerificationRecord condition = EntityUtils.copyData(req, VerificationRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), VerificationRecord.class));

        List<VerificationRecord> verificationRecordList = verificationRecordMapper.selectByCondition(condition);
          // 转译UserId
          verificationRecordList.forEach(item -> {
              item.setUser(userService.selectSummaryInfo(item.getUserId()));
          });

        List< VerificationRecordListRes> resList = verificationRecordList.stream().map((entity) -> {
            VerificationRecordListRes res = new VerificationRecordListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

//    public static void main(String[] args) {
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATA_TIME_PATTERN_9);
//        LocalDate start = LocalDate.parse("2024-12-28", formatter);
//        LocalDate end = LocalDate.parse("2024-12-30", formatter);
//        List<String> dates = new ArrayList<>();
//
//        while (!start.isAfter(end)) {
//            dates.add(start.format(formatter));
//            start = start.plusDays(1);
//        }
//        String[] strings = dates.toArray(new String[0]);
//        System.out.println();
//    }
    public String[] getDatesBetween(String startDate, String endDate, DateTimeFormatter formatter) {
        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);
        List<String> dates = new ArrayList<>();

        while (!start.isAfter(end)) {
            dates.add(start.format(formatter));
            start = start.plusDays(1);
        }
        return dates.toArray(new String[0]);
    }


    public static void main(String[] args) {
        Date date = DateUtil.strToDate("2025-01-04", "yy-MM-dd");
        Date today = DateUtil.getTodayStart();
        String errorMessage = new String();
        if(today.compareTo(date)!=0){
            errorMessage ="非当天门票,请提示访客核对预约日期。";
        }
        System.out.println(errorMessage);
    }

}