package com.std.core.service.impl;

import com.std.core.config.WechatConfig;
import org.codehaus.jettison.json.JSONArray;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.json.JSONObject;

import javax.annotation.Resource;
import java.time.Instant;

@Service
public class WeChatService {

    @Resource
    private WechatConfig wechatConfig;


    public String getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token";
        
        // 构建请求URL
        String requestUrl = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("grant_type", "client_credential")
                .queryParam("appid", wechatConfig.getAppid())
                .queryParam("secret", wechatConfig.getAppSecret())
                .toUriString();

        RestTemplate restTemplate = new RestTemplate();
        String response = restTemplate.getForObject(requestUrl, String.class);
        
        // 解析返回的JSON，获取 access_token
        JSONObject jsonResponse = new JSONObject(response);
        String accessToken = jsonResponse.getString("access_token");

        return accessToken;
    }






}
