package com.std.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.config.WechatConfig;
import com.std.core.config.WxOpenConfig;
import com.std.core.enums.*;
import com.std.core.mapper.PayRecordMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.PayRecordModifyReq;
import com.std.core.pojo.response.WechatAppPayInfo;
import com.std.core.pojo.response.WechatPayCodeInfo;
import com.std.core.pojo.response.WechatPrepayInfo;
import com.std.core.service.*;
import com.std.core.util.DateUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.SysConstants;
import com.std.core.util.wechat.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.transaction.Transaction;
import org.jdom2.JDOMException;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:01
 */
@Slf4j
@Service
public class WechatServiceImpl implements IWechatService {

    @Resource
    private IAccountService accountService;

    @Resource
    private IChargeService chargeService;
    @Resource
    private IUserService userService;
    @Resource
    private IActivityService activityService;

    @Resource
    private ICuserService cuserService;

    @Resource
    private IPayRecordService payRecordService;

    @Resource
    private PayRecordMapper payRecordMapper;

    @Resource
    private WechatConfig wechatConfig;

    @Resource
    private IBigActivityOrderService bigActivityOrderService;

    @Resource
    private IGoodsOrderService goodsOrderService;

    @Autowired
    private IConfigService configService;

    @Resource
    private WxOpenConfig wxOpenConfig;

    private static final String IP = "***********";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCallback(String result) {
        Map<String, String> map;
        try {
            map = XMLUtil.doXMLParse(result);
            String wechatOrderNo = map.get("transaction_id");
            String outTradeNo = map.get("out_trade_no");

            // 查询支付记录
            PayRecord payRecord = payRecordService.detail(Long.valueOf(outTradeNo));
            if (!EPayRecordStatus.PAY_RECORD_STATUS_0.getCode().equals(payRecord.getStatus())) {
                log.error("支付记录不处于待回调状态:{}", outTradeNo);
                return;
            }

            // 此处调用订单查询接口验证是否交易成功
            boolean isSucc = reqOrderquery(map);

            PayRecordModifyReq payRecordModifyReq = new PayRecordModifyReq();
            payRecordModifyReq.setId(payRecord.getId());
            if (isSucc) {
                payRecordModifyReq.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_1.getCode());
                payRecordModifyReq.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_1.getCode());
                if (EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getCode().equals(payRecord.getBizType())) {
                    chargeService.doChargeCallback(isSucc, wechatOrderNo, payRecord.getBizCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_1.getCode().equals(payRecord.getBizType())) {
                    log.info("**** 预约回调开始****：");

                    bigActivityOrderService.serveDoCallback(payRecord.getBizCode().toString());
                    log.info("**** 预约回调结束 ****：");

                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getCode().equals(payRecord.getBizType())) {
                    goodsOrderService.serveDoCallback(payRecord.getBizCode(), wechatOrderNo);
                }
            } else {
                payRecordModifyReq.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_2.getCode());
                payRecordModifyReq.setBizStatus(EPayRecordBizStatus.PAY_RECORD_BIZSTATUS_1.getCode());
            }

//            payRecordMapper.updateByPrimaryKeySelective(payRecord);
            User user = new User();
            payRecordModifyReq.setResponse(result);
            payRecordService.modify(payRecordModifyReq, user);
        } catch (JDOMException | IOException e) {
            throw new BizException(EErrorCode.CORE00000.getCode(), "回调结果XML解析失败");
        }
    }

    @Override
    public <T> WechatPrepayInfo getAppPayInfo(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount) {

        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
        Long id = IdGeneratorUtil.generator();
        Cuser cuser = cuserService.detailByUserId(userId);

        // 获取微信APP支付
        String prepayId = wapperPrepay(cuser.getOpenid(), EWeChatType.JSAPI.getCode(), id.toString(),
                bizNote, amount).submitXmlGetPrepayId();


        WechatPrepayInfo prepayInfo = getPrepayInfo(id.toString(), prepayId);
        // 落地支付记录
        PayRecord payRecord = payRecordService
                .create(id, userId, EPayType.WECHAT.getCode(), EPayMethod.APP.getCode(), amount, bizType, bizCode, JSONObject.toJSONString(prepayInfo));
        return prepayInfo;


    }


    @Override
    public <T> WechatPrepayInfo getAppPayInfo(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount, String wxAppId
            , Object... args) {

        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        Cuser cuser = cuserService.detailByUserId(userId);


        String payType = EWeChatType.JSAPI.getCode();

        String openid = null;

        openid = cuser.getOpenid();
        PayRecord payRecord = payRecordService.detail(bizCode);
        System.out.println("1");
        if (payRecord != null) {
            payRecordService.remove(bizCode);
            // 落地支付记录
        }
        payRecord = payRecordService
                .create(userId, EPayType.WECHAT.getCode(), EPayMethod.H5.getCode(), amount, bizType, bizCode);

        // 获取微信APP支付
        String prepayId = wapperPrepay(openid, payType, payRecord.getId().toString(),
                bizNote, amount, wxAppId).submitXmlGetPrepayId();

        return getPrepayInfo(payRecord.toString(), prepayId, wxAppId);


    }

    @Override
    public <T> WechatPrepayInfo getPrepayIdH5(Long applyUser, String userKind, String openId,
                                              String toAccountNumber, T payGroup, String bizType, String bizNote,
                                              BigDecimal transAmount) {

        if (BigDecimal.ZERO.compareTo(transAmount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
        if (StringUtils.isBlank(openId)) {
            throw new BizException(EErrorCode.AIS00032);
        }

        // 获取收款方账户信息
        Account toAccount = accountService.getAccount(toAccountNumber);

        // 落地此次付款的订单信息
        Long orderCode = chargeService.applyOrderOnline(toAccount, userKind, payGroup,
                payGroup.toString(), bizType, bizNote, transAmount, EChannelType.WECHAT,
                applyUser);

        // 获取微信公众号支付prepayid
        String prepayId = wapperPrepay(openId, EWeChatType.JSAPI.getCode(), orderCode.toString(),
                bizNote, transAmount).submitXmlGetPrepayId();

        return getPrepayInfo(orderCode.toString(), prepayId);
    }

    @Override
    public <T> WechatPrepayInfo getPrepayIdH5(Long applyUser, String openId,
                                              Long bizCode, String bizType, String bizNote,
                                              BigDecimal transAmount, String payType, String timeStart, String timeExpire) {
        User user = userService.detailBrief(applyUser);
        if (99L == user.getCompanyId()) {
            throw new BizException(EErrorCode.CORE00580);
        }

        if (BigDecimal.ZERO.compareTo(transAmount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
        if (StringUtils.isBlank(payType)) {
            throw new BizException(EErrorCode.CORE00396);
        }

        if (EWeChatType.JSAPI.getCode().equals(payType) && StringUtils.isBlank(openId)) {
            throw new BizException(EErrorCode.CORE00397);
        }

        // 获取微信公众号支付prepayid
        String prepayId = wapperPrepay(openId, payType, bizCode.toString(),
                bizNote, transAmount, timeStart, timeExpire).submitXmlGetPrepayId();

        WechatPrepayInfo info = getPrepayInfoNew(bizCode.toString(), prepayId, payType);
        info.setMerchantId(wechatConfig.getMerchantId());
        payRecordService.create(bizCode, applyUser, EPayType.WECHAT.getCode(), payType, transAmount, bizType, bizCode, JSONObject.toJSONString(info));

        return info;
    }


    private WechatPrepayInfo getPrepayInfoNew(String bizCode, String prepayId, String payType) {

        if (EWeChatType.JSAPI.getCode().equals(payType)) {
            return getPrepayInfo(bizCode, prepayId);
        } else if (EWeChatType.APP.getCode().equals(payType)) {
            return getAppPayInfo(bizCode, prepayId);
        } else {
            throw new BizException(EErrorCode.CORE00398);
        }

    }


    private WXPrepay wapperPrepay(String openId, String payType, String chargeOrderCode,
                                  String bizNote, BigDecimal transAmount, String timeStart, String timeExpire) {
        WXPrepay prePay = new WXPrepay();

        // 微信支付分配的公众账号ID
//        prePay.setAppid(wechatConfig.getAppid());

        if (EWeChatType.JSAPI.getCode().equals(payType)) {
            prePay.setAppid(wxOpenConfig.getAppid());
            // 商户号
            prePay.setMch_id(wxOpenConfig.getMerchantId());
            // 商户秘钥
            prePay.setPartnerKey(wxOpenConfig.getMerchantPrivateKey());
        } else if (EWeChatType.APP.getCode().equals(payType)) {
            //APP 的
            prePay.setAppid(wechatConfig.getAppid());
            prePay.setMch_id(wechatConfig.getMerchantId());
            // 商户秘钥
            prePay.setPartnerKey(wechatConfig.getMerchantPrivateKey());
            openId = null;
        }

        // 商品描述
        prePay.setBody(wechatConfig.getMerchantName() + "-" + bizNote);

        // 订单号
        prePay.setOut_trade_no(chargeOrderCode);

        // 订单总金额，转化成分
        prePay.setTotal_fee(transAmount.multiply(BigDecimal.valueOf(100)).
                stripTrailingZeros().toPlainString());

        // 用户IP
        prePay.setSpbill_create_ip(IP);

        // 交易类型
        prePay.setTrade_type(payType);

        // 回调地址
        prePay.setNotify_url(wechatConfig.getBackurl());

        if (StringUtils.isNotBlank(openId)) {
            // 支付者openid
            prePay.setOpenid(openId);
        }
        prePay.setTime_start(timeStart);
        prePay.setTime_expire(timeExpire);
        // 附加字段，回调时返回
        prePay.setAttach(wechatConfig.getSystemCode());
        return prePay;
    }

    @Override
    public <T> WechatPayCodeInfo getPayCodeInfo(Long applyUser, String userKind,
                                                String toAccountNumber, T payGroup,
                                                String bizType, String bizNote, BigDecimal transAmount) {

        if (BigDecimal.ZERO.compareTo(transAmount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        // 获取收款方账户信息
        Account toAccount = accountService.getAccount(toAccountNumber);

        // 落地此次付款的订单信息
        Long orderCode = chargeService.applyOrderOnline(toAccount, userKind, payGroup,
                payGroup.toString(), bizType, bizNote, transAmount, EChannelType.WECHAT,
                applyUser);

        // 获取微信网站支付二维码
        String codeUrl = wapperPrepay(null, EWeChatType.NATIVE.getCode(), orderCode.toString(),
                bizNote, transAmount).submitXmlGetCodeUrl();

        return getPayCodeInfo(orderCode.toString(), codeUrl);
    }

    @Override
    public <T> String doRefund(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
                               Object... bizArgs) {

        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
//
//        Charge charge = chargeService.detail(payGroup.toString());
//        if (null == charge) {
//            throw new BizException(EErrorCode.CORE00020);
//        }
        BigDecimal refundAmountInCent = refundAmount.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN); // 转换为分并确保是整数
        String refundFee = refundAmountInCent.toString();
        String totalFee = refundAmountInCent.toString();
        BigActivityOrder condition = new BigActivityOrder();
        condition.setSerialNumber(payGroup.toString());
        List<BigActivityOrder> list = bigActivityOrderService.list(condition);
        BigActivityOrder bigActivityOrder = list.get(0);
        if (!EBigActivityOrderStatus.BIG_ACTIVITY_ORDER_STATUS_2.getCode().equals(bigActivityOrder.getStatus())) {
            throw new BizException(EErrorCode.CORE00020);
        }
        WXRefund refund = new WXRefund();
        refund.setWECHAT_REFUND_URL(wechatConfig.getRefundUrl());
        // 微信支付分配的公众账号ID
        refund.setAppid(wechatConfig.getAppid());
        // 商户号
        refund.setMch_id(wechatConfig.getMerchantId());
        String randomStr = MD5.GetMD5String(String.valueOf(new Random().nextInt(10000)));
        refund.setNonce_str(MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        refund.setOut_trade_no(payGroup.toString());
        refund.setOut_refund_no(payGroup.toString());
        refund.setRefund_fee(refundAmountInCent.toPlainString());
        refund.setTotal_fee(refundAmountInCent.toPlainString());
        refund.setPrivateKey(wechatConfig.getMerchantPrivateKey());

        refund.setBizType(bizType.getCode());
        refund.setBizNote(String.format(bizType.getValue(), bizArgs));

        WechatReturnCode wechatReturnCode = refund.submitXmlRefund();

        if ("SUCCESS".equals(Jsoup.parse(wechatReturnCode.getReturn_code()).text())) {
            log.info("微信退款请求成功,支付组号{}", payGroup.toString());
            if ("SUCCESS".equals(Jsoup.parse(wechatReturnCode.getResult_code()).text())) {
                log.info("微信退款成功,支付组号{}", payGroup.toString());
            } else {
                log.error("微信退款失败,支付组号{}", payGroup.toString());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "退款失败，请检查账户余额是否充足");
            }
            // 托管账户扣钱
//            Account fromAccount = accountService.getAccount(TRUST.WECHAT.getAccountNumber());
//            accountService.changeAmount(fromAccount, refundAmount.setScale(2, BigDecimal.ROUND_DOWN).negate(),
//                    EChannelType.WECHAT.getCode(), charge.getId().toString(), charge.getId(),
//                    bizType, bizType, bizType, bizArgs);

            log.info("微信退款成功,支付组号{}", bigActivityOrder.getId());
        } else {
            throw new BizException(EErrorCode.AIS00002);
        }

        return wechatReturnCode.getResult_code();
    }

    @Override
    public <T> String doRefund2(T payGroup, BigDecimal refundAmount, EJourCommon bizType,
                                Object... bizArgs) {

        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new BizException(EErrorCode.CORE00019);
        }
//
//        Charge charge = chargeService.detail(payGroup.toString());
//        if (null == charge) {
//            throw new BizException(EErrorCode.CORE00020);
//        }
        BigDecimal refundAmountInCent = refundAmount.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_DOWN); // 转换为分并确保是整数
        String refundFee = refundAmountInCent.toString();
        String totalFee = refundAmountInCent.toString();

        WXRefund refund = new WXRefund();
        refund.setWECHAT_REFUND_URL(wechatConfig.getRefundUrl());
        // 微信支付分配的公众账号ID
        refund.setAppid(wechatConfig.getAppid());
        // 商户号
        refund.setMch_id(wechatConfig.getMerchantId());
        String randomStr = MD5.GetMD5String(String.valueOf(new Random().nextInt(10000)));
        refund.setNonce_str(MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        refund.setOut_trade_no(payGroup.toString());
        refund.setOut_refund_no(payGroup.toString());
        refund.setRefund_fee(refundAmountInCent.toPlainString());
        refund.setTotal_fee(refundAmountInCent.toPlainString());
        refund.setPrivateKey(wechatConfig.getMerchantPrivateKey());

        refund.setBizType(bizType.getCode());
        refund.setBizNote(String.format(bizType.getValue(), bizArgs));

        WechatReturnCode wechatReturnCode = refund.submitXmlRefund();

        if ("SUCCESS".equals(Jsoup.parse(wechatReturnCode.getReturn_code()).text())) {
            log.info("微信退款请求成功,支付组号{}", payGroup.toString());
            if ("SUCCESS".equals(Jsoup.parse(wechatReturnCode.getResult_code()).text())) {
                log.info("微信退款成功,支付组号{}", payGroup.toString());
            } else {
                log.error("微信退款失败,支付组号{}", payGroup.toString());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "退款失败，请检查账户余额是否充足");
            }
            // 托管账户扣钱
//            Account fromAccount = accountService.getAccount(TRUST.WECHAT.getAccountNumber());
//            accountService.changeAmount(fromAccount, refundAmount.setScale(2, BigDecimal.ROUND_DOWN).negate(),
//                    EChannelType.WECHAT.getCode(), charge.getId().toString(), charge.getId(),
//                    bizType, bizType, bizType, bizArgs);


        } else {
            throw new BizException(EErrorCode.AIS00002);
        }

        return wechatReturnCode.getResult_code();
    }

    @Override
    public Transaction queryOrderByOutTradeNo(String outTradeNo) {
//        // 初始化商户配置
//        Config config =
//                new RSAAutoCertificateConfig.Builder()
//                        .merchantId(merchantId)
//                        // 使用 com.wechat.pay.java.core.util 中的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
//                        .privateKeyFromPath(privateKeyPath)
//                        .merchantSerialNumber(merchantSerialNumber)
//                        .apiV3Key(apiV3Key)
//                        .build();
//
//        // 初始化服务
//        appService = new AppService.Builder().config(config).build();
//        // ... 调用接口
//        try {
//            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
//            request.setMchid(merchantId);
//            request.setOutTradeNo(outTradeNo.toString());
//            // 调用request.setXxx(val)设置所需参数，具体参数可见Request定义
//            // 调用接口
//            return appService.queryOrderByOutTradeNo(request);
//        } catch (HttpException e) { // 发送HTTP请求失败
//            // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
//            log.error("调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义，原因={}", e.getMessage());
//        } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
//            // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
//            log.error("调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义，原因={}", e.getMessage());
//        } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
//            // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
//            log.error("调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义，原因={}", e.getMessage());
//        } catch (Exception e) {
//            log.error("微信转账用户零钱接口调用失败，原因={}", e.toString());
//        }
        return null;
    }


    private WXPrepay wapperPrepay(String openId, String payType, String chargeOrderCode,
                                  String bizNote, BigDecimal transAmount) {
        WXPrepay prePay = new WXPrepay();

        // 微信支付分配的公众账号ID
        prePay.setAppid(wechatConfig.getAppid());

        // 商户号
        prePay.setMch_id(wechatConfig.getMerchantId());

        // 商品描述
        prePay.setBody(wechatConfig.getMerchantName() + "-" + bizNote);

        // 订单号
        prePay.setOut_trade_no(chargeOrderCode);

        // 订单总金额，转化成分
        prePay.setTotal_fee(transAmount.multiply(BigDecimal.valueOf(100)).
                stripTrailingZeros().toPlainString());

        // 用户IP
        prePay.setSpbill_create_ip(IP);

        // 交易类型
        prePay.setTrade_type(payType);

        // 回调地址
        prePay.setNotify_url(wechatConfig.getBackurl());

        // 商户秘钥
        prePay.setPartnerKey(wechatConfig.getMerchantPrivateKey());

        if (StringUtils.isNotBlank(openId)) {
            // 支付者openid
            prePay.setOpenid(openId);
        }

        // 附加字段，回调时返回
        prePay.setAttach(wechatConfig.getSystemCode());
        return prePay;
    }


    private WXPrepay wapperPrepay(String openId, String payType, String chargeOrderCode,
                                  String bizNote, BigDecimal transAmount, String wxAppId) {
        WXPrepay prePay = new WXPrepay();

        // 微信支付分配的公众账号ID
        prePay.setAppid(StringUtils.isNotBlank(wxAppId) ? wxAppId : wechatConfig.getAppid());

        // 商户号
        prePay.setMch_id(wechatConfig.getMerchantId());

        // 商品描述
        prePay.setBody(wechatConfig.getMerchantName() + "-" + bizNote);

        // 订单号
        prePay.setOut_trade_no(chargeOrderCode);
//        if (payType.equals(EWeChatType.JSAPI.getCode())) {
//            //防止app下单，小程序支付订单号重复
//            prePay.setOut_trade_no(chargeOrderCode.concat("-"));
//        }

        // 订单总金额，转化成分
        prePay.setTotal_fee(transAmount.multiply(BigDecimal.valueOf(100)).
                stripTrailingZeros().toPlainString());

        // 用户IP
        prePay.setSpbill_create_ip(IP);

        // 交易类型
        prePay.setTrade_type(payType);

        // 回调地址
        prePay.setNotify_url(wechatConfig.getBackurl());

        // 商户秘钥
        prePay.setPartnerKey(wechatConfig.getMerchantPrivateKey());

        if (StringUtils.isNotBlank(openId)) {
            // 支付者openid
            prePay.setOpenid(openId);
        }
        String timeExpire = DateUtil.dateToStr(DateUtil.getRelativeDateOfMinute(new Date(), configService.getIntegerValue(SysConstants.ORDER_EXPIRE_TIME)), DateUtil.YYYYMMDDHHMMSS);
        prePay.setTime_expire(timeExpire);
        // 附加字段，回调时返回
        prePay.setAttach(wechatConfig.getSystemCode());
        return prePay;
    }

    private WechatPrepayInfo getPrepayInfo(String payCode, String prepayId) {
        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("appid", wechatConfig.getAppid());
        String timestamp = OrderUtil.GetTimestamp();
        nativeObj.put("timeStamp", timestamp);
        Random random = new Random();
        String randomStr = MD5.GetMD5String(String.valueOf(random.nextInt(10000)));
//        String randomStr = UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();

        nativeObj.put("nonceStr", MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
//        nativeObj.put("nonceStr", randomStr);
        nativeObj.put("package", "prepay_id=" + prepayId);
        nativeObj.put("signType", "MD5");
        nativeObj.put("paySign", createSign(nativeObj, wechatConfig.getMerchantPrivateKey()));

        WechatPrepayInfo res = new WechatPrepayInfo();
        res.setPrepayId(prepayId);
        res.setPayCode(payCode);
        res.setAppId(wechatConfig.getAppid());
        res.setTimeStamp(timestamp);
        res.setNonceStr(nativeObj.get("nonceStr"));
        res.setWechatPackage("prepay_id=" + prepayId);
        res.setSignType("MD5");
        res.setPaySign(nativeObj.get("paySign"));
        return res;
    }

    private WechatPrepayInfo getPrepayInfo(String payCode, String prepayId, String wxAppId) {
        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("appId", StringUtils.isNotBlank(wxAppId) ? wxAppId : wechatConfig.getAppid());
        nativeObj.put("timeStamp", OrderUtil.GetTimestamp());
        Random random = new Random();
        String randomStr = MD5.GetMD5String(String.valueOf(random.nextInt(10000)));
        nativeObj.put("nonceStr", MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        nativeObj.put("package", "prepay_id=" + prepayId);
        nativeObj.put("signType", "MD5");
        nativeObj.put("paySign", createSign(nativeObj, wechatConfig.getMerchantPrivateKey()));

        WechatPrepayInfo res = new WechatPrepayInfo();
        res.setPrepayId(prepayId);
        res.setPayCode(payCode);
        res.setAppId(StringUtils.isNotBlank(wxAppId) ? wxAppId : wechatConfig.getAppid());
        res.setTimeStamp(OrderUtil.GetTimestamp());
        res.setNonceStr(MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        res.setWechatPackage("prepay_id=" + prepayId);
        res.setSignType("MD5");
        res.setPaySign(nativeObj.get("paySign"));
        return res;
    }

    private WechatAppPayInfo getAppPayInfo1(String payCode, String prepayId) {
        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("appid", wechatConfig.getAppid());
        nativeObj.put("partnerid", wechatConfig.getMerchantId());
        nativeObj.put("prepayid", prepayId);
        nativeObj.put("package", "Sign=WXPay");
        Random random = new Random();
        String randomStr = MD5.GetMD5String(String.valueOf(random.nextInt(10000)));
        nativeObj.put("noncestr", MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        nativeObj.put("timestamp", OrderUtil.GetTimestamp());
//        nativeObj.put("signType", "MD5");
        nativeObj.put("paySign", createSign(nativeObj, wechatConfig.getMerchantPrivateKey()));

        WechatAppPayInfo res = new WechatAppPayInfo();
        res.setPrepayId(prepayId);
        res.setPayCode(payCode);
        res.setMerchantId(wechatConfig.getMerchantId());
        res.setAppId(wechatConfig.getAppid());
        res.setTimeStamp(nativeObj.get("timestamp"));
        res.setNonceStr(nativeObj.get("noncestr"));
        res.setWechatPackage("Sign=WXPay");
        res.setSignType("MD5");
        res.setPaySign(nativeObj.get("paySign"));
        return res;
    }


    private WechatPrepayInfo getAppPayInfo(String payCode, String prepayId) {
        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("appid", wechatConfig.getAppid());
        nativeObj.put("partnerid", wechatConfig.getMerchantId());
        nativeObj.put("prepayid", prepayId);
        nativeObj.put("package", "Sign=WXPay");
        Random random = new Random();
        String randomStr = MD5.GetMD5String(String.valueOf(random.nextInt(10000)));
        nativeObj.put("noncestr", MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        nativeObj.put("timestamp", OrderUtil.GetTimestamp());
        nativeObj.put("paySign", createSign(nativeObj, wechatConfig.getMerchantPrivateKey()));

        WechatPrepayInfo res = new WechatPrepayInfo();
        res.setPrepayId(prepayId);
        res.setPayCode(payCode);
        res.setMerchantId(wechatConfig.getMerchantId());
        res.setAppId(wechatConfig.getAppid());
        res.setTimeStamp(nativeObj.get("timestamp"));
        res.setNonceStr(nativeObj.get("noncestr"));
        res.setWechatPackage("Sign=WXPay");
        res.setSignType("MD5");
        res.setPaySign(nativeObj.get("paySign"));
        return res;
    }


    private WechatPayCodeInfo getPayCodeInfo(String payCode, String codeUrl) {
        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("appid", wechatConfig.getAppid());
        nativeObj.put("timeStamp", OrderUtil.GetTimestamp());
        Random random = new Random();
        String randomStr = MD5.GetMD5String(String.valueOf(random.nextInt(10000)));
        nativeObj.put("nonceStr", MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        nativeObj.put("package", "code_url=" + codeUrl);
        nativeObj.put("signType", "MD5");
        nativeObj.put("paySign", createSign(nativeObj, wechatConfig.getMerchantPrivateKey()));

        WechatPayCodeInfo res = new WechatPayCodeInfo();
        res.setCodeUrl(codeUrl);
        res.setPayCode(payCode);
        res.setAppId(wechatConfig.getAppid());
        res.setTimeStamp(OrderUtil.GetTimestamp());
        res.setNonceStr(MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        res.setWechatPackage("code_url=" + codeUrl);
        res.setSignType("MD5");
        res.setPaySign(nativeObj.get("paySign"));
        return res;
    }

    private String createSign(SortedMap<String, String> packageParams,
                              String AppKey) {
        StringBuffer sb = new StringBuffer();
        Set es = packageParams.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k)
                    && !"key".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("key=" + AppKey);
        String sign = MD5Util.MD5Encode(sb.toString(), "UTF-8").toUpperCase();
        return sign;
    }

    private boolean reqOrderquery(Map<String, String> map) {
        log.info("******* 开始订单查询 ******");
        WXOrderQuery orderQuery = new WXOrderQuery();
        orderQuery.setAppid(map.get("appid"));
        orderQuery.setMch_id(map.get("mch_id"));
        orderQuery.setTransaction_id(map.get("transaction_id"));
        System.out.println("transaction_id:" + map.get("transaction_id"));
        orderQuery.setOut_trade_no(map.get("out_trade_no"));
        orderQuery.setNonce_str(map.get("nonce_str"));

//        //微信单号赋值
//        try {
//            GoodsOrder goodsOrder = goodsOrderService.detailSimple(Long.valueOf(map.get("out_trade_no")));
//            goodsOrder.setTransactionId(map.get("transaction_id"));
//            goodsOrderService.modify(goodsOrder);
//        } catch (NumberFormatException e) {
//            throw new RuntimeException("商品订单查询失败");
//        }

        // 此处需要密钥PartnerKey，此处直接写死，自己的业务需要从持久化中获取此密钥，否则会报签名错误
        orderQuery.setPartnerKey(wechatConfig.getMerchantPrivateKey());

        Map<String, String> orderMap = orderQuery.reqOrderquery();
        // 此处添加支付成功后，支付金额和实际订单金额是否等价，防止钓鱼
        if (orderMap.get("return_code") != null
                && orderMap.get("return_code").equalsIgnoreCase("SUCCESS")) {
            if (orderMap.get("trade_state") != null && orderMap
                    .get("trade_state").equalsIgnoreCase("SUCCESS")) {
                String total_fee = map.get("total_fee");
                String order_total_fee = map.get("total_fee");
                if (Integer.parseInt(order_total_fee) >= Integer
                        .parseInt(total_fee)) {
                    log.info("******* 开订单查询结束，结果正确 ******");
                    return true;
                }
            }
        }
        log.info("******* 开订单查询结束，结果不正确 ******");
        return false;
    }


    @Override
    public void sendSubscribeMessage(String openId, String activityDetailId) {
        String accessToken = getAccessToken1();
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + accessToken;

        com.alibaba.fastjson.JSONObject data = new com.alibaba.fastjson.JSONObject();
        data.put("touser", openId);
        data.put("template_id", "FNxpr2MUquNxRrR3uVqlnkDvt4e0Z7zseIfQo17LrLk");
        data.put("page", "pagesActivity/pages/activityDetail/index?id=" + activityDetailId);
        data.put("miniprogram_state", "developer");
        data.put("lang", "zh_CN");

        Activity activity = activityService.detail(Long.valueOf(activityDetailId));

        com.alibaba.fastjson.JSONObject content = new com.alibaba.fastjson.JSONObject();

        // 订单信息
        com.alibaba.fastjson.JSONObject thing1 = new com.alibaba.fastjson.JSONObject();
        thing1.put("value", "杭州西溪湿地旅游发展有限公司西溪美术馆");
        content.put("thing1", thing1);

        // 开始时间（格式化为 yyyy-MM-dd）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = sdf.format(activity.getStartTime());  // 将 Date 格式化为 "yyyy-MM-dd"

        com.alibaba.fastjson.JSONObject date2 = new com.alibaba.fastjson.JSONObject();
        date2.put("value", startDate);  // 设置格式化后的开始时间
        content.put("date2", date2);

        // 活动地点
        com.alibaba.fastjson.JSONObject thing4 = new com.alibaba.fastjson.JSONObject();
        thing4.put("value", activity.getAddress());  // 确保有值
        content.put("thing4", thing4);

        // 报名开始时间
        String time22 = startDate + " 00:00:00";  // 设置报名开始时间为开始日期的 00:00:00
        com.alibaba.fastjson.JSONObject time22Json = new com.alibaba.fastjson.JSONObject();
        time22Json.put("value", time22);
        content.put("time22", time22Json);

        // 报名结束时间（假设报名结束时间是活动结束时间的 23:59:59）
        SimpleDateFormat sdfTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String endTime = sdfTime.format(activity.getEndTime());  // 格式化活动结束时间
        com.alibaba.fastjson.JSONObject time23Json = new com.alibaba.fastjson.JSONObject();
        time23Json.put("value", endTime);  // 设置报名结束时间
        content.put("time23", time23Json);

        data.put("data", content);

        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头，确保使用 UTF-8 编码
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));

        HttpEntity<String> request = new HttpEntity<>(data.toJSONString(), headers);

        // 发送请求并获取响应
        String response = restTemplate.postForObject(url, request, String.class);

        // 获取响应体内容并确保使用 UTF-8 解码
        if (response != null) {
            // 确保 UTF-8 解码响应内容，防止乱码
            response = new String(response.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        }

        System.out.println("微信返回：" + response);
    }


    public String getAccessToken1() {
        String appId = wechatConfig.getAppid();
        String appSecret = wechatConfig.getAppSecret();
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appId + "&secret=" + appSecret;

        RestTemplate restTemplate = new RestTemplate();
        String response = restTemplate.getForObject(url, String.class);

        org.json.JSONObject jsonObject = new org.json.JSONObject(response);

        // 检查是否有 "access_token"
        if (jsonObject.has("access_token")) {
            return jsonObject.getString("access_token");
        } else {
            // 打印错误信息，帮助排查问题
            System.err.println("获取 access_token 失败，微信返回：" + response);
            throw new RuntimeException("获取 access_token 失败：" + jsonObject.toString());
        }
    }


}
