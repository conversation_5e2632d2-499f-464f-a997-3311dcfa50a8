package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EUserKind;
import com.std.core.enums.EWithdrawFeeTakeLocation;
import com.std.core.enums.EWithdrawFeeType;
import com.std.core.mapper.WithdrawRuleMapper;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.WithdrawRule;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawNoteRes;
import com.std.core.service.IWithdrawRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 取现规则ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-03-11 10:35
 */
@Service
public class WithdrawRuleServiceImpl implements IWithdrawRuleService {

    @Resource
    private WithdrawRuleMapper withdrawRuleMapper;

    /**
     * 新增取现规则
     *
     * @param req      新增取现规则入参
     * @param operator 操作人
     */
    @Override
    public void create(WithdrawRuleCreateReq req, User operator) {
        WithdrawRule withdrawRule = EntityUtils.copyData(req, WithdrawRule.class);

        withdrawRuleMapper.insertSelective(withdrawRule);
    }

    /**
     * 删除取现规则
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Integer id) {
        withdrawRuleMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改取现规则
     *
     * @param req      修改取现规则入参
     * @param operator 操作人
     */
    @Override
    public void modify(WithdrawRuleModifyReq req, User operator) {
        WithdrawRule withdrawRule = EntityUtils.copyData(req, WithdrawRule.class);

        withdrawRuleMapper.updateByPrimaryKeySelective(withdrawRule);
    }

    /**
     * 详情查询取现规则
     *
     * @param id 主键ID
     * @return 取现规则对象
     */
    @Override
    public WithdrawRule detail(Integer id) {
        WithdrawRule withdrawRule = withdrawRuleMapper.selectByPrimaryKey(id);
        if (null == withdrawRule) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return withdrawRule;
    }

    @Override
    public WithdrawRule detail(String kind, String type, String symbol) {
        WithdrawRule condition = new WithdrawRule();
        condition.setKind(kind);
        condition.setType(type);
        condition.setSymbol(symbol);

        WithdrawRule withdrawRule = new WithdrawRule();
        List<WithdrawRule> withdrawRuleList = withdrawRuleMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(withdrawRuleList)) {
            withdrawRule = withdrawRuleList.get(0);
        }

        return withdrawRule;
    }

    /**
     * 分页查询取现规则
     *
     * @param req 分页查询取现规则入参
     * @return 分页取现规则对象
     */
    @Override
    public List<WithdrawRule> page(WithdrawRulePageReq req) {
        WithdrawRule condition = EntityUtils.copyData(req, WithdrawRule.class);

        return withdrawRuleMapper.selectByCondition(condition);
    }

    /**
     * 列表查询取现规则
     *
     * @param req 列表查询取现规则入参
     * @return 列表取现规则对象
     */
    @Override
    public List<WithdrawRule> list(WithdrawRuleListReq req) {
        WithdrawRule condition = EntityUtils.copyData(req, WithdrawRule.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), WithdrawRule.class));

        return withdrawRuleMapper.selectByCondition(condition);
    }

    /**
     * 提币手续费查询
     *
     * @return
     */
    @Override
    public WithdrawNoteRes detailFee(WithdrawNoteReq request) {
        WithdrawRule withdrawRule = detail(EUserKind.C.getCode(),
                request.getType(), request.getCurrency());
        String content = "";
        String location = "";
        //判断手续费扣除得位置
        if (EWithdrawFeeTakeLocation.WITHDRAW_AMOUNT.getCode().equals(withdrawRule.getWithdrawFeeTakeLocation())) {
            location = "手续费从取现金额中扣除";
        } else if (EWithdrawFeeTakeLocation.BALANCE.getCode().equals(withdrawRule.getWithdrawFeeTakeLocation())) {
            location = "手续费从余额中扣除";
        }
        // 判断手续费得扣除方式
        if (EWithdrawFeeType.VALUE.getCode().equals(withdrawRule.getWithdrawFeeType())) {
            content = "提币手续费为" + withdrawRule.getWithdrawFee().setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString() + "个NAT;" + location;
        } else if (EWithdrawFeeType.RATE.getCode().equals(withdrawRule.getWithdrawFeeType())) {
            content =
                    "提币手续费率为" + withdrawRule.getWithdrawFee().multiply(new BigDecimal(100)).setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString() + "%;" + location;
        }
        WithdrawNoteRes withdrawNoteRes = new WithdrawNoteRes();
        withdrawNoteRes.setContent(content);

        withdrawNoteRes.setWithdrawFee(withdrawRule.getWithdrawFee());
        withdrawNoteRes.setWithdrawFeeTakeLocation(withdrawRule.getWithdrawFeeTakeLocation());
        withdrawNoteRes.setWithdrawFeeType(withdrawRule.getWithdrawFeeType());
        withdrawNoteRes.setWithdrawLimit(withdrawRule.getWithdrawLimit());
        withdrawNoteRes.setWithdrawMin(withdrawRule.getWithdrawMin());
        withdrawNoteRes.setWithdrawRule(withdrawRule.getWithdrawRule());
        withdrawNoteRes.setWithdrawStep(withdrawRule.getWithdrawStep());
        return withdrawNoteRes;
    }

    /**
     * OSS：分页条件查询取现规则（除流通NAT）
     *
     * @param request
     * @return
     */
    @Override
    public List<WithdrawRule> pageRule(WithdrawRulePageReq request) {
        WithdrawRule condition = EntityUtils.copyData(request, WithdrawRule.class);

        return withdrawRuleMapper.selectRule(condition);
    }

}