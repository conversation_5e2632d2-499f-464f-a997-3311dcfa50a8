package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.WithdrawMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.WithdrawSuccessRes;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 取现订单ServiceImpl
 *
 * <AUTHOR> xiongk
 * @since : 2020-02-26 13:47
 */
@Service
public class WithdrawServiceImpl implements IWithdrawService {

    @Resource
    private WithdrawMapper withdrawMapper;

    @Resource
    private IAccountService accountService;

    @Resource
    private IUserService userService;

    @Resource
    private IWithdrawRuleService withdrawRuleService;

    @Resource
    private IBankcardService bankcardService;


    @Override
    public WithdrawSuccessRes create(WithdrawCreateReq request, User operator) {

        //支付密码
        userService.checkTradePwd(operator.getId(), request.getTradePwd());

        BigDecimal availableAmount = BigDecimal.ZERO;

        Account account = accountService.getAccount(request.getAccountNumber());

        WithdrawRule withdrawRule = withdrawRuleService.detail(operator.getKind(),
                EWithdrawBizType.withdraw.getCode(), request.getCurrency());


        if (BigDecimal.ZERO.compareTo(request.getAmount()) >= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "取现金额必须大于0");
        }

        Withdraw condition = new Withdraw();
        condition.setAccountNumber(request.getAccountNumber());
        condition.setStatusList(EWithdrawStatus.getWithdrawingStatusList());
        if (withdrawMapper.selectTotalCount(condition) > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您当前有一笔正在提现中的订单");
        }

        // 历史取现次数
        condition.setStatus(EWithdrawStatus.Pay_YES.getCode());
        condition.setStatusList(null);
        Long totalWithdrawCount = withdrawMapper.selectTotalCount(condition);

        if (null != withdrawRule.getWithdrawMin1() && totalWithdrawCount == 0) {
            if (request.getAmount().compareTo(withdrawRule.getWithdrawMin1()) < 0) {
                throw new BizException(EErrorCode.CORE00014, withdrawRule.getWithdrawMin1());
            }
        }

        if (null != withdrawRule.getWithdrawMin2() && totalWithdrawCount == 1) {
            if (request.getAmount().compareTo(withdrawRule.getWithdrawMin2()) < 0) {
                throw new BizException(EErrorCode.CORE00014, withdrawRule.getWithdrawMin2());
            }
        }

        if (null != withdrawRule.getWithdrawMin3() && totalWithdrawCount > 1) {
            if (request.getAmount().compareTo(withdrawRule.getWithdrawMin3()) < 0) {
                throw new BizException(EErrorCode.CORE00014, withdrawRule.getWithdrawMin3());
            }
        }

        //最小取现金额
        if (null != withdrawRule.getWithdrawMin()) {
            if (request.getAmount().compareTo(withdrawRule.getWithdrawMin()) < 0) {
                throw new BizException(EErrorCode.CORE00014, withdrawRule.getWithdrawMin());
            }
        }

        //最大取现金额
        if (null != withdrawRule.getWithdrawMax()) {
            if (request.getAmount().compareTo(withdrawRule.getWithdrawMax()) > 0) {
                throw new BizException(EErrorCode.CORE00015, withdrawRule.getWithdrawMax());
            }
        }

        // 取现步长
        if (null != withdrawRule.getWithdrawStep()
                && withdrawRule.getWithdrawStep().compareTo(BigDecimal.ZERO) > 0) {
            if (request.getAmount().remainder(withdrawRule.getWithdrawStep()).compareTo(BigDecimal.ZERO) != 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), String.format("取现金额须为%S的倍数", withdrawRule.getWithdrawStep()));
            }
        }

        //计算手续费
        BigDecimal fee = BigDecimal.ZERO;
        if (EWithdrawFeeType.VALUE.getCode().equals(withdrawRule.getWithdrawFeeType())) {
            fee = withdrawRule.getWithdrawFee().setScale(2, RoundingMode.UP);
        } else if (EWithdrawFeeType.RATE.getCode().equals(withdrawRule.getWithdrawFeeType())) {
            fee = request.getAmount().multiply(withdrawRule.getWithdrawFee()).setScale(2, RoundingMode.UP);
        }


        Boolean flag = false;
        //实际取现金额
        BigDecimal actualAmount = request.getAmount();
        if (EWithdrawFeeTakeLocation.WITHDRAW_AMOUNT.getCode()
                .equals(withdrawRule.getWithdrawFeeTakeLocation())) {
            //  取现金额金扣取
            if (availableAmount.compareTo(actualAmount) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
            actualAmount = actualAmount.subtract(fee);
            //同时取现金额要大于手续费。
            if (actualAmount.compareTo(fee) < 1) {
                throw new BizException(EErrorCode.CORE00002);
            }
            flag = true;
        } else {
            //余额中扣  （取现金额+手续费）
            // 账户可用余额是否充足
            if (availableAmount.compareTo(actualAmount.add(fee)) < 0) {
                throw new BizException(EErrorCode.CORE00001);
            }
        }

        //每人每日最大取现额
        if (null != withdrawRule.getWithdrawLimit()
                && withdrawRule.getWithdrawLimit().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal total = withdrawMapper.selectCurrDayWithdrawAmount(request.getAccountNumber());
            total = total.add(actualAmount);
            if (total.compareTo(withdrawRule.getWithdrawLimit()) > 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您今天的提现额度已上限。提现额度为" + withdrawRule.getWithdrawLimit());
            }
        }

        String    withdrawId = create(request, account, actualAmount, fee, operator);


        WithdrawSuccessRes withdrawSuccessRes = new WithdrawSuccessRes();
        withdrawSuccessRes.setAmount(actualAmount);
        withdrawSuccessRes.setFee(fee);
        withdrawSuccessRes.setId(Long.parseLong(withdrawId));

        return withdrawSuccessRes;
    }

    @Override
    public String create(WithdrawToBankcardReq req, User operator) {

        Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        Bankcard bankcard = bankcardService.detail(req.getBankcardId());

        return create(new WithdrawCreateReq(req, account.getAccountNumber(), bankcard), operator).getId().toString();
    }




    private String create(WithdrawCreateReq request, Account account, BigDecimal actualAmount,
                          BigDecimal fee, User operator) {
        Withdraw withdraw = EntityUtils.copyData(request, Withdraw.class);
        Long id = IdGeneratorUtil.generator();

        withdraw.setId(id);
        withdraw.setAccountType(account.getType());
        withdraw.setCurrency(account.getCurrency());
        withdraw.setBizType(EWithdrawBizType.withdraw.getCode());
        withdraw.setFee(fee);

        withdraw.setActualAmount(actualAmount);
        withdraw.setBalanceAmount(account.getAvailableAmount());
        withdraw.setChannelType(EChannelType.OFF_LINE.getCode());
        withdraw.setStatus(EWithdrawStatus.To_Approve.getCode());
        withdraw.setApplyUser(operator.getId());
        withdraw.setApplyUserKind(operator.getKind());
        withdraw.setApplyDatetime(new Date());

        withdrawMapper.insertSelective(withdraw);
        return id.toString();
    }


    @Override
    public void approve(WithdrawApproveReq request, User operator) {
        Withdraw withdraw = detail(request.getId());

        if (!EWithdrawStatus.To_Approve.getCode().equals(withdraw.getStatus())) {
            throw new BizException(EErrorCode.CORE00003);
        }

        String status = EWithdrawStatus.Approved_YES.getCode();


        if (EBoolean.NO.getCode().equals(request.getApproveResult())) {
            //审核不通过
            status = EWithdrawStatus.Approved_NO.getCode();

            //因为取现手续费分从余额中扣除和  取现金额中扣除
            //所以当再余额中扣除的时候，是冻结了 取现金额+手续费的   用户拿到的现钱是 等于 取现金额的
            //再取现金额中扣除时： 是直接冻结的取现金额  用户拿到的现钱是 减去了手续费的
            BigDecimal amount = withdraw.getAmount().compareTo(withdraw.getActualAmount()) == 0
                    ? withdraw.getAmount().add(withdraw.getFee()) : withdraw.getAmount();


            Account account = accountService.getAccount(withdraw.getAccountNumber());
            // 解冻
            accountService.unfrozenAmount(account, amount,
                    EJourBizTypeUser.Withdraw.Withdraw_UnForzen,
                    EJourBizTypeUser.Withdraw.Withdraw_UnForzen, withdraw.getId(),
                    EJourBizTypeUser.Withdraw.Withdraw_UnForzen,
                    withdraw.getChannelAccountInfo(), withdraw.getChannelAccountNumber());
        }

        withdraw.setStatus(status);
        withdraw.setApproveUser(operator.getId());
        withdraw.setApproveNote(request.getApproveNote());
        withdraw.setApproveDatetime(new Date());

        withdrawMapper.updateByPrimaryKeySelective(withdraw);
    }

    @Override
    public void approve(WithdrawBatchApproveNoReq request, User operator) {
        for (Long id : request.getIds()) {
            Withdraw withdraw = new Withdraw();
            withdraw.setId(id);
            withdraw.setStatus(EWithdrawStatus.To_Approve.getCode());
            withdraw.setApproveNote(request.getApproveNote());
            withdrawMapper.updateByPrimaryKeySelective(withdraw);
        }
    }

    @Override
    public void pay(WithdrawPayReq request, User operator) {
        Withdraw withdraw = detail(request.getId());
        if (!EWithdrawStatus.Approved_YES.getCode().equals(withdraw.getStatus())) {
            throw new BizException(EErrorCode.CORE00004);
        }

        String status = EWithdrawStatus.Pay_NO.getCode();
        if (EBoolean.YES.getCode().equals(request.getPayResult())) {
            status = EWithdrawStatus.Pay_YES.getCode();
            payYes(withdraw, request);
        } else if (EBoolean.NO.getCode().equals(request.getPayResult())) {
            payNo(withdraw);
        }

        pay(request, status, operator);
    }

    private void payYes(Withdraw withdraw, WithdrawPayReq request) {

        BigDecimal amount = withdraw.getAmount().compareTo(withdraw.getActualAmount()) == 0
                ? withdraw.getAmount().add(withdraw.getFee()) : withdraw.getAmount();

        Account userAccount = accountService.getAccount(withdraw.getAccountNumber());
        //解冻取现金额
        accountService.unfrozenAmount(userAccount, amount,
                EJourBizTypeUser.YxFruitBiz.Withdraw,
                EJourBizTypeUser.YxFruitBiz.Withdraw_Unfrozen, withdraw.getId(),
                EJourBizTypeUser.YxFruitBiz.Withdraw_Unfrozen,
                withdraw.getChannelAccountInfo(), withdraw.getChannelAccountNumber());

        //用户账户扣减取现金额
        accountService.changeAmount(userAccount, amount.setScale(2, BigDecimal.ROUND_DOWN).negate(),
                EChannelType.OFF_LINE.getCode(),
                request.getChannelOrder(), withdraw.getId(),
                EJourBizTypeUser.YxFruitBiz.Withdraw,
                EJourBizTypeUser.YxFruitBiz.Yxfruit_Withdraw_Out,
                EJourBizTypeUser.YxFruitBiz.Yxfruit_Withdraw_Out,
                withdraw.getChannelAccountInfo(), withdraw.getChannelAccountNumber());

    }

    private void payNo(Withdraw withdraw) {

        BigDecimal amount = withdraw.getAmount().compareTo(withdraw.getActualAmount()) == 0
                ? withdraw.getAmount().add(withdraw.getFee()) : withdraw.getAmount();

        Account account = accountService.getAccount(withdraw.getAccountNumber());

        //因为取现手续费分从余额中扣除和  取现金额中扣除
        //所以当再余额中扣除的时候，是冻结了 取现金额+手续费的   用户拿到的现钱是 等于 取现金额的
        //再取现金额中扣除时： 是直接冻结的取现金额  用户拿到的现钱是 减去了手续费的
        // 解冻
        accountService.unfrozenAmount(account, amount,
                EJourBizTypeUser.YxFruitBiz.Withdraw,
                EJourBizTypeUser.YxFruitBiz.Withdraw_Unfrozen, withdraw.getId(),
                EJourBizTypeUser.YxFruitBiz.Withdraw_Unfrozen,
                withdraw.getChannelAccountInfo(), withdraw.getChannelAccountNumber());

    }

    private void pay(WithdrawPayReq request, String status, User operator) {
        Withdraw withdraw = EntityUtils.copyData(request, Withdraw.class);

        if (StringUtils.isNotBlank(operator.getRealName())) {
            withdraw.setPayUser(operator.getRealName());
        } else if (StringUtils.isNotBlank(operator.getNickname())) {
            withdraw.setPayUser(operator.getNickname());
        } else {
            withdraw.setPayUser(operator.getLoginName());
        }
        withdraw.setStatus(status);
        withdraw.setPayDatetime(new Date());
        withdrawMapper.updateByPrimaryKeySelective(withdraw);
    }

    /**
     * 详情查询取现订单
     *
     * @param id 主键ID
     * @return 取现订单对象
     */
    @Override
    public Withdraw detail(Long id) {
        Withdraw withdraw = withdrawMapper.selectByPrimaryKey(id);
        if (null == withdraw) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        init(withdraw);
        return withdraw;
    }

    /**
     * 分页查询取现订单
     *
     * @param req 分页查询取现订单入参
     * @return 分页取现订单对象
     */
    @Override
    public List<Withdraw> page(WithdrawPageReq req, User operator) {
        Withdraw condition = EntityUtils.copyData(req, Withdraw.class);

        if (StringUtils.isNotBlank(req.getApplyDatetimeEnd())) {
            condition.setApplyDatetimeEnd(DateUtil.getEndDatetime(req.getApplyDatetimeEnd()));
        }

        List<Withdraw> withdrawList = withdrawMapper.selectByCondition(condition);

        for (Withdraw withdraw : withdrawList) {
            init(withdraw);
        }

        return withdrawList;
    }

    /**
     * 列表查询取现订单
     *
     * @param req 列表查询取现订单入参
     * @return 列表取现订单对象
     */
    @Override
    public List<Withdraw> list(WithdrawListReq req) {
        Withdraw condition = EntityUtils.copyData(req, Withdraw.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Withdraw.class));

        condition.setApplyDatetimeStart(
                DateUtil.strToDate(req.getApplyDatetimeStart(), DateUtil.DATA_TIME_PATTERN_9));
        condition.setApplyDatetimeEnd(DateUtil.getEndDatetime(req.getApplyDatetimeEnd()));

        return withdrawMapper.selectByCondition(condition);
    }

    @Override
    public List<Withdraw> myPage(WithdrawMyPageReq request, User operator) {
        Withdraw condition = EntityUtils.copyData(request, Withdraw.class);
        condition.setApplyUser(operator.getId());
        condition.setOrderBy("t.id desc");
        List<Withdraw> withdrawList = withdrawMapper.selectByCondition(condition);

        return withdrawList;
    }

    @Override
    public BigDecimal selectWithdrawingAmount(String accountNumber) {
        return withdrawMapper.selectWithdrawingAmount(accountNumber);
    }

    private void init(Withdraw withdraw) {
        User userInfo = userService.selectSummaryInfo(withdraw.getApplyUser());
        withdraw.setApplyUserInfo(userInfo);
        withdraw.setApplyUserName(userInfo.getNickname());

        if (null != withdraw.getApproveUser()) {
            User approveUserInfo = userService.selectSummaryInfo(withdraw.getApproveUser());
            withdraw.setApproveUserInfo(approveUserInfo);
        }
    }

}
