package com.std.core.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class AmountUtil {

    public static Long mul(Long amount, double rate) {
        BigDecimal a = new BigDecimal(Double.toString(amount));
        BigDecimal b = new BigDecimal(Double.toString(rate));
        return a.multiply(b).longValue();
    }

    public static BigDecimal mul(BigDecimal amount, double rate) {
        BigDecimal a = amount;
        BigDecimal b = new BigDecimal(Double.toString(rate));
        return a.multiply(b);
    }

    public static BigDecimal mul(String amount, Long radix) {
        BigDecimal a = new BigDecimal(amount);
        BigDecimal b = new BigDecimal(Double.toString(radix));
        return a.multiply(b);
    }

    public static BigDecimal mul(BigDecimal amount, Long radix) {
        BigDecimal a = amount;
        BigDecimal b = new BigDecimal(radix);
        return a.multiply(b);
    }

    public static BigDecimal mul(BigDecimal amount, BigDecimal rate) {
        BigDecimal a = amount;
        BigDecimal b = rate;
        return a.multiply(b);
    }

    public static double div(Double amount, Long number) {
        BigDecimal a = new BigDecimal(Double.toString(amount));
        BigDecimal b = new BigDecimal(Double.toString(number));
        return a.divide(b).doubleValue();
    }

    public static BigDecimal div(BigDecimal amount, Long number) {
        BigDecimal b = new BigDecimal(Double.toString(number));
        return amount.divide(b, 2, BigDecimal.ROUND_DOWN);
    }

    public static String toDisplayAmount(BigDecimal amount) {
        return amount.setScale(2, RoundingMode.DOWN).toPlainString();
    }

    public static BigDecimal fromOriginal(BigDecimal orgNum, Integer unit) {
        return orgNum.divide(BigDecimal.TEN.pow(unit));
    }

    public static BigDecimal toOriginal(BigDecimal orgNum, Integer unit) {
        return orgNum.multiply(BigDecimal.TEN.pow(unit));
    }

    public static void main(String[] args) {
        System.out.println(div(new BigDecimal(839), 1000L));
    }
}
