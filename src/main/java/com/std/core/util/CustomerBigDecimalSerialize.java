package com.std.core.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/9/16 21:33
 */
public class CustomerBigDecimalSerialize extends JsonSerializer<BigDecimal> {



    @Override
    public void serialize(BigDecimal arg0, JsonGenerator arg1, SerializerProvider serializers) throws IOException {

        String obj = arg1.getCurrentValue() != null ? arg1.getCurrentValue().toString() : "";

        System.out.println(obj);

        if (obj.contains("serviceChargePercentage,yxFruitRatio")) {
            //商家前端不格式化，
            arg1.writeString(arg0.stripTrailingZeros().toPlainString());
            return;
        }


        if (arg0 != null) {
            //先格式化成2位数，
//            arg0 = new BigDecimal(df.format(arg0));
            //去掉尾数为0的变为整数
            arg1.writeString(arg0.stripTrailingZeros().toPlainString());
        }
    }
}
