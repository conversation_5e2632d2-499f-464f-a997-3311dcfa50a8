package com.std.core.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2021/3/16 下午5:32
 */
public class CustomerStrToListSerialize extends JsonSerializer<String> {

    @Override
    public void serialize(String arg0, JsonGenerator arg1, SerializerProvider serializers) throws IOException {
        List<String> resultList = null;
        try {
            if (StringUtils.isNotBlank(arg0)){
                resultList =   Arrays.asList(arg0.split(","));
            }
//            resultList = JSONObject.parseArray(arg0, String.class);
        } catch (Exception e) {

        }
        if (arg0 != null) {
            arg1.writeObject(resultList);
        }
    }
}
