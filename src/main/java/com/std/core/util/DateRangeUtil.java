package com.std.core.util;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

public class DateRangeUtil {

    public enum RangeType {
        DAILY, WEEKLY, MONTHLY
    }

    public static DateRange getStatisticalRange(Date baseDate, RangeType type) {
        LocalDate baseLocalDate = convertToLocalDate(baseDate);

        switch (type) {
            case DAILY:
                LocalDate sevenDaysAgo = baseLocalDate.minusDays(6);
                return new DateRange(
                        toDate(sevenDaysAgo.atStartOfDay()),
                        toDate(baseLocalDate.atTime(LocalTime.MAX))
                );
            case WEEKLY:
                LocalDate endOfWeek = baseLocalDate.with(DayOfWeek.SUNDAY);
                LocalDate startOf8Weeks = baseLocalDate.minusWeeks(7).with(DayOfWeek.MONDAY);
                return new DateRange(
                        toDate(startOf8Weeks.atStartOfDay()),
                        toDate(endOfWeek.atTime(LocalTime.MAX))
                );
            case MONTHLY:
                LocalDate startOf6Months = baseLocalDate.minusMonths(5).with(TemporalAdjusters.firstDayOfMonth());
                LocalDate endOfMonth = baseLocalDate.with(TemporalAdjusters.lastDayOfMonth());
                return new DateRange(
                        toDate(startOf6Months.atStartOfDay()),
                        toDate(endOfMonth.atTime(LocalTime.MAX))
                );
            default:
                throw new IllegalArgumentException("Unsupported range type: " + type);
        }
    }

    // LocalDate 转 Date
    private static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    // Date 转 LocalDate
    private static LocalDate convertToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    // 时间范围封装类
    public static class DateRange {
        private Date startDate;
        private Date endDate;

        public DateRange(Date startDate, Date endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public Date getStartDate() {
            return startDate;
        }

        public Date getEndDate() {
            return endDate;
        }

        @Override
        public String toString() {
            return "DateRange{start=" + startDate + ", end=" + endDate + '}';
        }
    }

    // 示例用法
    public static void main(String[] args) {
        Date now = new Date();

        System.out.println("最近7天: " + getStatisticalRange(now, RangeType.DAILY));
        System.out.println("最近8周: " + getStatisticalRange(now, RangeType.WEEKLY));
        System.out.println("最近6个月: " + getStatisticalRange(now, RangeType.MONTHLY));
    }
}
