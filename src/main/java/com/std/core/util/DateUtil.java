package com.std.core.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtil extends com.std.common.utils.DateUtil {

    public static String YYYY = "yyyy";
    public static String YYYY_MM = "yyyy-MM";
    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYY_MM_DD_PATH = "yyyy/MM/dd";
    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String HH_MM_SS = "HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static String YYYY_MM_DD_HH_MM_SS_MILLIS = "yyyy-MM-dd HH:mm:ss.SSS";

    private static String[] parsePatterns = {
            "yyyy-MM-dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM",
            "yyyy/MM/dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM",
            "yyyy.MM.dd",
            "yyyy.MM.dd HH:mm:ss",
            "yyyy.MM.dd HH:mm",
            "yyyy.MM"
    };

    /**
     * 获取当前时间戳（单位秒）
     *
     * @return
     */
    public static Long getTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（单位毫秒）
     *
     * @return
     */
    public static Long getMillisTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    /**
     * 获取当前日期, 默认格式为 yyyy/MM/dd
     *
     * @return String
     */
    public static String getDatePath() {
        return dateTimeNow(YYYY_MM_DD_PATH);
    }

    /**
     * 根据时间戳获取年月日时分秒
     *
     * @param timestamp 秒级时间戳
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String getAllTime(Long timestamp) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS, new Date(timestamp * 1000));
    }

    /**
     * 根据时间戳获取年月日时分秒毫秒
     *
     * @param timestamp 毫秒级时间戳
     * @return yyyy-MM-dd HH:mm:ss.SSS
     */
    public static String getAllTimeOfMillis(Long timestamp) {
        return parseDateToStr(YYYY_MM_DD_HH_MM_SS_MILLIS, new Date(timestamp));
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将秒数格式化成时间字符串（HH:mm:ss）
     *
     * @param seconds
     * @return java.lang.String
     */
    public static String parseTime(Integer seconds) {
        return DateFormatUtils.format(seconds * 1000 - TimeZone.getDefault().getRawOffset(), "HH:mm:ss");
    }

    /**
     * 时分秒对比 s1晚于s2返回true
     *
     * @param s1
     * @param s2
     * @return
     */
    public static boolean compTime(String s1, String s2) {
        try {
            if (s1.indexOf(":") < 0 || s1.indexOf(":") < 0) {
                System.out.println("格式不正确");
            } else {
                String[] array1 = s1.split(":");
                int total1 = Integer.valueOf(array1[0]) * 3600 + Integer.valueOf(array1[1]) * 60 + Integer.valueOf(array1[2]);
                String[] array2 = s2.split(":");
                int total2 = Integer.valueOf(array2[0]) * 3600 + Integer.valueOf(array2[1]) * 60 + Integer.valueOf(array2[2]);
                return total1 - total2 > 0 ? true : false;
            }
        } catch (NumberFormatException e) {
            return true;
        }
        return false;
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }


    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static long getDateByDay(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天

        return diff / nd;
    }


    /**
     * 获取当前月的第一天 最早时间
     *
     * @return
     * <AUTHOR>
     * @date 2017-3-25 - 上午10:57:27
     */
    public static Date getCurrMonthFirstDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取当前月的最后一天 最晚时间
     *
     * @return
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:00:34
     */
    public static Date getCurrMonthLastDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }


    /**
     * 获取当天的最早时间
     *
     * @return
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getCurrDayFirstTime() {
        Calendar c = Calendar.getInstance();
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        // 将分钟至0
        c.set(Calendar.MINUTE, 0);
        // 将秒至0
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    /**
     * 获取当天的最晚时间
     *
     * @return
     * <AUTHOR>
     * @date 2017-3-25 - 上午11:35:28
     */
    public static Date getCurrDayLastTime() {
        Calendar c = Calendar.getInstance();
        // 将小时至0
        c.set(Calendar.HOUR_OF_DAY, 23);
        // 将分钟至0
        c.set(Calendar.MINUTE, 59);
        // 将秒至0
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    public static Long getUnixTimestap() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        return calendar.getTimeInMillis() / 1000;
    }

}
