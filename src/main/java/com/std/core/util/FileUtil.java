package com.std.core.util;
import java.io.BufferedReader;

import java.io.BufferedWriter;

import java.io.File;

import java.io.FileReader;

import java.io.FileWriter;

import java.io.IOException;

import java.util.ArrayList;

import java.util.List;
/**
 * <AUTHOR> ycj 没用
 * @since : 2020/10/19 18:50
 */
public class FileUtil {
    public static void rewrite(File file, String data) {

        BufferedWriter bw = null;

        try {

            bw = new BufferedWriter(new FileWriter(file));

            bw.write(data);

        } catch (IOException e) {

            e.printStackTrace();

        } finally {

            if (bw != null) {

                try {

                    bw.close();

                } catch (IOException e) {

                    e.printStackTrace();

                }

            }

        }

    }

    public static List<String> readList(File file) {

        BufferedReader br = null;

        List<String> data = new ArrayList<String>();

        try {

            br = new BufferedReader(new FileReader(file));

            String str = null;

            while ((str = br.readLine()) != null) {

                data.add(str);

            }

        } catch (IOException e) {

            e.printStackTrace();

        } finally {

            if (br != null) {

                try {

                    br.close();

                } catch (IOException e) {

                    e.printStackTrace();

                }

            }

        }

        return data;

    }
}
