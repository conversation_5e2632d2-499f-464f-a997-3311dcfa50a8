package com.std.core.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EErrorCode;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class GaoDeUtil {
    @Value("${gaode.key}")
    private String key;

    public String geocode(String address, String city) {
        String url = "https://restapi.amap.com/v3/geocode/geo?key=%s&address=%s&city=%s";
        String format = String.format(url, key, address, city);
        System.out.println(format);
        String result = OkHttpUtil.get(format);

        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
        System.out.println(object);
        String status = object.getString("status");
        if (StringUtils.isBlank(status) || !EBoolean.YES.getCode().equals(status)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }
        JSONArray geocodes = object.getJSONArray("geocodes");
        System.out.println(geocodes);
        JSONObject firstGeocode = geocodes.getJSONObject(0);
        String location = firstGeocode.getString("location");
        System.out.println(location);

        return location;
    }

    public String geocodeRegeo(String location) {
        String url = "https://restapi.amap.com/v3/geocode/regeo?location=%s&key=%s&radius=1000&extensions=all";
        String format = String.format(url, location, key);
        System.out.println(format);
        String result = OkHttpUtil.get(format);

        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
        System.out.println(object);
        JSONObject regeocode = object.getJSONObject("regeocode");
        String formattedAddress = regeocode.getString("formatted_address");
        System.out.println("formattedAddress:" + formattedAddress);
        JSONObject addressComponent = regeocode.getJSONObject("addressComponent");
        String country = addressComponent.getString("country");
        String province = addressComponent.getString("province");
        String city = addressComponent.getString("city");
        String district = addressComponent.getString("district");

        System.out.println();
        String districtContent = "";
        int districtIndex = formattedAddress.indexOf(district);

        if (districtIndex != -1) {
            // 区名存在，获取区名后的子串
            districtContent = formattedAddress.substring(districtIndex + district.length());
        }

        System.out.println("District content: " + districtContent);
        System.out.println("address:" + country.concat(province).concat(city).concat(district).concat(districtContent));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("formatted_address", formattedAddress);
        jsonObject.put("address", country.concat(province).concat(city).concat(district).concat(districtContent));
        return jsonObject.toJSONString();
    }


    public String directionDriving(String origin, String destination) {
        String url = "https://restapi.amap.com/v3/direction/driving?origin=%s&destination=%s&key=%s";
        String format = String.format(url, origin, destination, key);
//        System.out.println(format);
        String result = OkHttpUtil.get(format);

//        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
//        System.out.println(object);
        String status = object.getString("status");
        if (StringUtils.isBlank(status) || !EBoolean.YES.getCode().equals(status)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }
        JSONObject route = object.getJSONObject("route");
        JSONArray paths = route.getJSONArray("paths");
        JSONObject jsonObject = paths.getJSONObject(0);
        if (null == jsonObject) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }
        String distance = jsonObject.getString("distance");

//        System.out.println(distance);

        return distance;
    }

    public String distanceParameters(String origin, String destination) {


        String url = "https://restapi.amap.com/v3/distance?key=%s&origins=%s&destination=%s&type=3";
        String format = String.format(url, key, origin, destination);
//        System.out.println(format);
        String result = OkHttpUtil.get(format);

//        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
//        System.out.println(object);
        String status = object.getString("status");
        if (StringUtils.isBlank(status) || !EBoolean.YES.getCode().equals(status)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }

        JSONArray results = object.getJSONArray("results");
        JSONObject jsonObject = results.getJSONObject(0);
        if (null == jsonObject) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }
        String distance = jsonObject.getString("distance");

//        System.out.println(distance);

        return distance;
    }

    @Test
    public void distanceParametersTest() {

        String origin = "119.99598,30.284609";
        String destination = "120.000297,30.282562";
        String url = "https://restapi.amap.com/v3/distance?key=%s&origins=%s&destination=%s&type=3";
        String format = String.format(url, "e79d9eae1a4433848ddb7d06b10b034f", origin, destination);
        String result = OkHttpUtil.get(format);

        JSONObject object = JSONObject.parseObject(result);
        String status = object.getString("status");
        if (StringUtils.isBlank(status) || !EBoolean.YES.getCode().equals(status)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }

        JSONArray results = object.getJSONArray("results");
        JSONObject jsonObject = results.getJSONObject(0);
        if (null == jsonObject) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),"获取路线规划失败，origin:%s,destination:%s");
        }
        String distance = jsonObject.getString("distance");

        System.out.println(distance);

//        return distance;
    }

}
