package com.std.core.util;

import com.alibaba.fastjson.JSON;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Map;

/**
 * <AUTHOR> z<PERSON><PERSON>
 * @since : 2020/8/28 16:07
 */
public class HttpPostUtil {

    public static String post(String url, Map map) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;");
            StringEntity entity1 = new StringEntity(JSON.toJSON(map).toString(), "utf-8");
            post.setEntity(entity1);

            CloseableHttpResponse res = httpClient.execute(post);

            int retCode = res.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = res.getEntity();
            String resStr = entity != null ? EntityUtils.toString(entity, "utf-8") : null;
            return resStr;
        } catch (Exception e) {
            return null;
        }
    }

}
