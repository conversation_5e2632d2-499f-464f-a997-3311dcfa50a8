package com.std.core.util;

import com.alibaba.fastjson.JSON;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.Map;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/8/28 16:07
 */
public class HttpUtil {

    static OkHttpClient okHttpClient = new OkHttpClient();

    public static String post(String url, Map map) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;");
            StringEntity entity1 = new StringEntity(JSON.toJSON(map).toString(), "utf-8");
            post.setEntity(entity1);

            CloseableHttpResponse res = httpClient.execute(post);

            int retCode = res.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = res.getEntity();
            String resStr = entity != null ? EntityUtils.toString(entity, "utf-8") : null;
            return resStr;
        } catch (Exception e) {
            return null;
        }
    }

    public static String postByHead(String url, Map map, Map<String, String> headMap) {
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json;");
            if (headMap != null) {
                for (String key : headMap.keySet()) {
                    post.setHeader(key, headMap.get(key));
                }
            }
            if (map != null) {
                StringEntity entity1 = new StringEntity(JSON.toJSON(map).toString(), "utf-8");
                post.setEntity(entity1);
            }
            CloseableHttpResponse res = httpClient.execute(post);

            int retCode = res.getStatusLine().getStatusCode();
            if (retCode != 200) {
                System.out.println("连接不成功！状态码 ：" + retCode);
                return "";
            }
            HttpEntity entity = res.getEntity();
            return entity != null ? EntityUtils.toString(entity, "utf-8") : null;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getMethod(String urll, HttpClientContext context) throws Exception {
        CloseableHttpClient loginHttpClient = HttpClients.createDefault();
        URL url = new URL(urll);
        URI uri = new URI(url.getProtocol(), null, url.getHost(), url.getPort(), url.getPath(), url.getQuery(), null);
        HttpGet httpGet = new HttpGet(uri);
        CloseableHttpResponse res = null;
        if (context == null) {
            res = loginHttpClient.execute(httpGet);
        } else {
            res = loginHttpClient.execute(httpGet, context);
        }
        HttpEntity entity = res.getEntity();
        return entity != null ? EntityUtils.toString(entity, "UTF-8") : null;
    }


    public static String execute(Request executeRequest) {
        Response response = null;
        String str = null;
        try {
            response = okHttpClient.newCall(executeRequest).execute();
            str = response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return str;
    }

}
