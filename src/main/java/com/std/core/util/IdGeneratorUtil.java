package com.std.core.util;

import com.std.common.utils.IdWorker;

import java.util.UUID;

/**
 * <AUTHOR> qian<PERSON>ei
 * @since : 2019-01-23 19:56
 */
public class IdGeneratorUtil {

    private static IdWorker idWorker = null;

    public static synchronized Long generator() {
        if (idWorker == null) {
            idWorker = new IdWorker(2, 2);
        }
        return idWorker.nextId();
    }

    public static String[] chars = new String[]{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
            "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8",
            "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"};

    /**
     * 生成16位短UUID
     *
     * @return
     * <AUTHOR>
     * @date 2020-08-10 - 下午2:03:27
     */
    public static String generateShortUuid() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 16; i++) {
            String str = uuid.substring(i * 2, i * 2 + 2);
            int x = Integer.parseInt(str, 32);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }

    public static String generateShortUuid(int len) {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < len; i++) {
            String str = uuid.substring(i * 2, i * 2 + 2);
            int x = Integer.parseInt(str, 32);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }


    /**
     * 生成YYYYMMDDHHMMSS+6位随机数
     *
     * @return
     * <AUTHOR>
     * @date 2020-08-10 - 下午2:03:27
     */
    public static String generateTimeMatch() {
        StringBuffer shortBuffer = new StringBuffer();
        shortBuffer.append(DateUtil.dateTimeNow()).append((int) Math.floor((Math.random() * 9 + 1) * 1000));
        return shortBuffer.toString();
    }

}
