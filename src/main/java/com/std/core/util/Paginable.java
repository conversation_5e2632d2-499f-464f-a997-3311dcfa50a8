package com.std.core.util;

import java.util.List;

/**
 * 分页接口
 *
 * <AUTHOR>
 */
public interface Paginable<T> {

    int DEFAULT_PAGE_SIZE = 20;

    /**
     * 总记录数
     */
    public long getTotalCount();

    /**
     * 总页数
     */
    public long getTotalPage();

    /**
     * 每页记录数
     */
    public int getPageSize();

    /**
     * 当前页号
     */
    public int getPageNo();

    /**
     * 是否第一页
     */
    public boolean isFirstPage();

    /**
     * 是否最后一页
     */
    public boolean isLastPage();

    /**
     * 返回下页的页号
     */
    public int getNextPage();

    /**
     * 返回上页的页号
     */
    public int getPrePage();

    /**
     * 返回起始条数
     */
    public int getStart();

    /**
     * 返回数据列表
     */
    public List<T> getList();

    /**
     * 设置数据列表
     */
    public void setList(List<T> list);
}
