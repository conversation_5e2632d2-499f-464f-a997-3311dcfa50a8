package com.std.core.util;


import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> zhoudong
 * @since : 2020/7/30 10:00
 */
@Component
public class RowLockUtil {

    private ConcurrentHashMap map = new ConcurrentHashMap();

    /**
     * 行锁的加锁方法
     */
    public synchronized void lock(String id) {
//        System.out.println("当前线程 " + Thread.currentThread().getName());
        while (map.containsKey(id)) {
            try {
                wait();
            } catch (InterruptedException e) {
                map.remove(id);
            }
        }
        map.put(id, "");
//        System.out.println("线程 " + Thread.currentThread().getName() + "拿到锁 ， 锁定资源" + id);
    }

    /**
     * 行锁的解锁方法
     */
    public synchronized void unlock(String id) {
        while (map.containsKey(id)) {
            map.remove(id);
//            System.out.println("线程 " + Thread.currentThread().getName() + "释放锁 ， 释放资源" + id);
            notifyAll();
        }
    }
}