package com.std.core.util;

import com.alibaba.fastjson.JSON;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.enums.EBizErrorCode;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * RSA 工具方法
 */
public class RsaUtil {

    private static Logger log = LoggerFactory.getLogger(RsaUtil.class);

    public static final String RSA_ALGORITHM = "RSA";

    public static final String SHA256WITHRSA_ALGORITHM = "SHA256WithRSA";

    public static final String publicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMlI1Ph+YhCEQz6ZoD4L2JGMD5xF5q7uO5CPTxw/qNYDF4oHWeQJNRRUSlfZVl5sR2XmF6H6GZHdi+fJqsvqxP8CAwEAAQ==";

    /**
     * 公钥字符串转成PublicKey
     *
     * @param algorithm 算法
     * @param publicKey
     * @return
     * @throws Exception
     */
    public static PublicKey convertPublicKey(String algorithm, String publicKey)
            throws NoSuchAlgorithmException, IOException,
            InvalidKeySpecException {
        KeyFactory factory = KeyFactory.getInstance(algorithm);
//        byte[] bytes = (new BASE64Decoder()).decodeBuffer(publicKey);
        byte[] bytes =  Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
        return factory.generatePublic(keySpec);
    }

    /**
     * 私钥字符串转成PrivateKey
     *
     * @param algorithm
     * @param privateKey
     * @return
     * @throws Exception
     */
    public static PrivateKey convertPrivateKey(String algorithm,
                                               String privateKey) throws NoSuchAlgorithmException,
            InvalidKeySpecException {
        KeyFactory factory = KeyFactory.getInstance(algorithm);
        byte[] bytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(bytes);
        return factory.generatePrivate(keySpec);
    }

    /**
     * 签名
     *
     * @param data
     * @param privateKey
     * @return
     * @throws RuntimeException
     */
    public static String sign(String data, String privateKey)
            throws RuntimeException {
        try {
            PrivateKey pk = convertPrivateKey(RSA_ALGORITHM, privateKey);
            Signature signature = Signature
                    .getInstance(SHA256WITHRSA_ALGORITHM);
            signature.initSign(pk);
            signature.update(data.getBytes());
            byte[] res = signature.sign();
            return new String(Base64.getEncoder().encode(res));
        } catch (NoSuchAlgorithmException ignored) {
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("RSA私钥格式不正确", e);
        } catch (Exception e) {
            throw new RuntimeException("签名失败content=[" + data + "]", e);
        }
        return null;
    }

    public static String sign(Map<String, Object> map, String privateKey) {
        Map<String, Object> sorted = new TreeMap<>(map);
        String data = convertToChars(sorted);
        return sign(data, privateKey);
    }

    /**
     * 验签
     *
     * @param data
     * @param sign
     * @param publicKey
     * @return
     * @throws RuntimeException
     */
    public static boolean verify(String data, String sign, String publicKey)
            throws RuntimeException {
        try {
            PublicKey pk = convertPublicKey(RSA_ALGORITHM, publicKey);
            Signature verifier = Signature.getInstance(SHA256WITHRSA_ALGORITHM);
            verifier.initVerify(pk);
            verifier.update(data.getBytes());
            byte[] bytes = Base64.getDecoder().decode(sign.getBytes());
            return verifier.verify(bytes);
        } catch (IOException e) {
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("RSA公钥格式不正确", e);
        } catch (Exception e) {
            throw new RuntimeException("验签失败content=[" + data + "], sign=["
                    + sign + "]", e);
        }
        return false;
    }

    public static void checkSign(String data, String sign) {
        if (verify(data, sign, publicKey)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "sign error");
        }
    }

    public static boolean verify(Map<String, Object> map, String sign,
                                 String publicKey) {
        Map<String, Object> sorted = new TreeMap<>(map);
        String data = convertToChars(sorted);
        return verify(data, sign, publicKey);
    }

    protected static String convertToChars(Map<String, Object> map) {
        StringBuffer sb = new StringBuffer(map.size());
        int i = 1;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey()).append("=");

            Object value = entry.getValue();
            if (isBasicType(value.getClass())) {
                sb.append(value.toString());
            } else {
                sb.append(JSON.toJSONString(value));
            }
            sb.append(entry.getValue()).append(i == map.size() ? "" : "&");
            ++i;
        }
        return sb.toString();
    }

    /**
     * bean转Map类型转换支持的类型
     *
     * @param type
     * @param <T>
     * @return
     */
    public static <T> boolean isBasicType(Class<T> type) {
        return (type == String.class
                || type == Integer.class
                || type == BigDecimal.class // 注意有精度问题
                || type == Long.class || type == Double.class
                || type == Boolean.class || type == Float.class || type == Short.class);
    }

    public static void main(String[] args) {
    String priKey =
            "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMvKDdRFhkcvA+PhqtMZ4A1Wch9pBFt/nOu+yt7YrWJpNkH9rb3MDZ3etowM9jHVNHA53Tb1XffS7/ZrWjFOg3kvCqVWSMi7Sckw0IcARpUTSL0M7pYk62emZQRavhM4+BjzoJ2iFHfoOyj25+CTpDhWdx/9CbvXHdz+8/KszqLZAgMBAAECgYEAvEnLzT0Pe+Wsqnhu5O0bXjdo6iyVohSeYBLATnXUfQDI6OACD0LhseN8tpmjPxfGg4pzIbxDPh7fJ8a4ZqYsQbxnjOJdkWZrayo36s8CBAmBhtPDE6yBYRXyHsHgOO/KkEhhUN3sqsdVGbosSjxvndLo6NXkTKSgL5Wbi2VcwSECQQD0wrFVp7HgnbGVvBW/ye7DqJDyKx/I9k6qpH/82KbbVDKprtK4d93y2qCjPYMi3uecBmI+YG2L94PcZu+iGEyTAkEA1SW4OwefRqVjg0GKN5roWdEClRcbYRxgFJkG1fgddWcE8/7gZJDUDE8cEKwSQDG9WrqgdbgTTcR7m+zku7yiYwJAb4KsDFwkXDM8Dv3r4r77OsT3lH01M/eTi4b6kJWcH1zS4B+3/TcPNW+JtBV4Q1DOsHTwGX8RKa2Qs79p+a2cZQJAInzT7lkxYFDqZRZ5sr2R0taKwGzYtUHOGgcYoA5hVdmxZ3lZ/Wuho8w5EkmFOnXR2ZhQ1jtOhErCZVxTNj0zWQJBAJuBbpvSAdepfMTAGyKMaXrteYJ+YW7qw3T0D0uXIUgV1WfUJE57FqsxSmV8h4a3EC5LNwGFbu1b1Tde3kO84hM=";
        String data = "address=mt6uMz7ZgxEKLZvrMfddGHuH6NW8H1AVJe&companyCode=C100000000001&symbol=BTC&timestamp=1544012876";
    String pubKey =
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLyg3URYZHLwPj4arTGeANVnIfaQRbf5zrvsre2K1iaTZB/a29zA2d3raMDPYx1TRwOd029V330u/2a1oxToN5LwqlVkjIu0nJMNCHAEaVE0i9DO6WJOtnpmUEWr4TOPgY86CdohR36Dso9ufgk6Q4Vncf/Qm71x3c/vPyrM6i2QIDAQAB";
        String sign = sign(data, priKey);
        System.out.println(sign);
        boolean verify = verify(data, sign, pubKey);
        System.out.println(verify);
//        byte[] bt = null;
//        try {
//            String priKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCh5Nk2GLiyQFMIU+h3OEA4UeFbu3dCH5sjd/sLTxxvwjXq7JLqJbt2rCIdzpAXOi4jL+FRGQnHaxUlHUBZsojnCcHvhrz2knV6rXNogt0emL7f7ZMRo8IsQGV8mlKIC9xLnlOQQdRNUssmrROrCG99wpTRRNZjOmLvkcoXdeuaCQIDAQAB";
//            BASE64Decoder decoder = new BASE64Decoder();
//            bt = decoder.decodeBuffer(priKey);
//        } catch (IOException e) {
//        }
//        System.out.println(bytesToHex(bt));
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    /**
     * 反射拼接类的属性值
     *
     * @param object
     * @return
     */
    public static String getAscOrderString(Object object) {
        String rawString = "";
        Map<String, String> map = new HashMap<String, String>();
        Class clazz = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);
            String valString;
            try {
                if (field.get(object) == null) {
                    continue;
                } else if (field.get(object) instanceof List) {
                    continue;
                } else {
                    valString = field.get(object).toString();
                }
                map.put(field.getName(), valString);
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
                log.error(ECommonErrorCode.BIZ_DEFAULT.getCode(), e.getMessage());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), e.getMessage());
            } catch (IllegalAccessException e) {
                log.error(ECommonErrorCode.BIZ_DEFAULT.getCode(), e.getMessage());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), e.getMessage());
            }
        }
        Collection<String> keyset = map.keySet();
        List list = new ArrayList<String>(keyset);
        Collections.sort(list);
        for (int i = 0; i < list.size(); i++) {
            if (i == (list.size() - 1)) {
                if (!StringUtils.isEmpty(map.get(list.get(i)))) {
                    rawString += list.get(i) + "=" + map.get(list.get(i));
                }
            } else {
                if (!StringUtils.isEmpty(map.get(list.get(i)))) {
                    rawString += list.get(i) + "=" + map.get(list.get(i)) + "&";
                }
            }
        }
        return rawString;
    }
}
