package com.std.core.util;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * Copyright 2010-2019 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * This file is licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License. A copy of
 * the License is located at
 *
 * http://aws.amazon.com/apache2.0/
 *
 * This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

// snippet-sourcedescription:[pinpoint_send_email_smtp demonstrates how to send a transactional email by using the Amazon Pinpoint SMTP interface.]
// snippet-service:[Amazon Pinpoint]
// snippet-keyword:[Java]
// snippet-sourcesyntax:[java]
// snippet-keyword:[Amazon Pinpoint]
// snippet-keyword:[Code Sample]
// snippet-sourcetype:[snippet]
// snippet-sourcedate:[2019-01-20]
// snippet-sourceauthor:[AWS]
// snippet-start:[pinpoint.java.pinpoint_send_email_smtp.complete]

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

    public class SendEmail1 {

        public static void main(String[] args) {
            // 发件人邮箱
            String fromEmail = "<EMAIL>";
            // 发件人邮箱密码（注意：这里是邮箱的授权码而不是登录密码）
            String password = "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";
            // 收件人邮箱
//        String toEmail = "<EMAIL>";
            String toEmail = "<EMAIL>";


            // 设置邮件的属性
            Properties props = new Properties();
            props.put("mail.smtp.host", "smtp-relay.brevo.com"); // SMTP服务器地址
            props.put("mail.smtp.port", "587"); // SMTP服务器端口
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true"); // 启用 STARTTLS

            props.put("mail.smtp.socketFactory.class",
                    "javax.net.ssl.SSLSocketFactory");

            // 创建身份验证器
            Authenticator authenticator = new Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(fromEmail, password);
                }
            };

            // 创建会话
            Session session = Session.getInstance(props, authenticator);

            try {
                // 创建消息
                Message message = new MimeMessage(session);
                // 设置发件人
                message.setFrom(new InternetAddress(fromEmail));
                // 设置收件人
                message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
                // 设置邮件主题
                message.setSubject("Test Email");
                // 设置邮件正文
                message.setText("This is a test email sent from Java.");

                // 发送邮件
                Transport.send(message);

                System.out.println("Email sent successfully!");

            } catch (MessagingException e) {
                e.printStackTrace();
            }
        }
    }

