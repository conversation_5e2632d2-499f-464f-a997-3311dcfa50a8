package com.std.core.util;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2020/5/26 3:39 下午
 */
public class SysConstants {

    /**
     * 【全局配置】默认在线人数
     */
    public static final String ONLINE_COUNT = "online_count";

    /**
     * 【全局配置】在线人数放大倍数
     */
    public static final String ONLINE_COUNT_MULTIPLE = "online_count_multiple";


    /**
     * 【全局配置】系统编号
     */
    public static final String SYSTEM_CODE = "system_code";

    /**
     * 【全局配置】云wallet对接签名私钥
     */
    public static final String CLOUD_WALLET_PRIVATE_KEY = "cloud_wallet_private_key";

    /**
     * 【全局配置】ZG交易所API链接
     */
    public static final String ZG_API = "zg_api";

    /**
     * 【全局配置】怀南庄园康养卡每天产出x%的银杏叶
     */

    public static final String KYK_PROPORTION_OF_WELFARE = "kyk_proportion_of_welfare";

    /**
     * 订单超时时间（min）
     */
    public static String ORDER_EXPIRE_TIME = "order_expire_time";

    /**
     * 包邮金额
     */
    public static String SEND_AMOUNT = "send_amount";

    /**
     * 包邮说明
     */
    public static String SEND_MESSAGE = "send_message";

    public static final String USER_PHOTO = "user_photo";
    public static final String EXPIRE_DAY = "expire_day";

    public static final String SYS_PHOTO = "sys_photo";
    public static final String MAX_ORDER = "max_order";
    /**
     * 预约最多可过期次数
     */
    public static final String RESERVATION_EXPIRED_MAX_NUMBER = "reservation_expired_max_number";

    /**
     * 预约过期次数过多限制时间(天)
     */
    public static final String RESERVATION_EXPIRED_UNSEAL_TIME = "reservation_expired_unseal_time";

}


