package com.std.core.util;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-03-31 00:27
 */
@Component
@Data
public class SysProperties {

    /**
     * C端
     */
    @Value("${default.role.c}")
    private Long defaultRoleC;

    /**
     * P端
     */
    @Value("${default.role.p}")
    private Long defaultRoleP;


//    /**
//     * 商家
//     */
//    @Value("${default.role.mer}")
//    private Long defaultRoleMer;
}
