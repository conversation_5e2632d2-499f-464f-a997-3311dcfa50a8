package com.std.core.util;

import com.alibaba.fastjson.JSONObject;
import com.std.common.utils.JWTUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class TokenUtil {


    @Resource
    private RedisUtil redisUtil;

    public JSONObject generalToken(Long userId, Long expireTime) {

        JSONObject jsonObject = JWTUtil.generalToken(userId.toString(), expireTime);

        String token = jsonObject.getString("token");

        redisUtil.set(userId.toString(), token, expireTime/1000);

        return jsonObject;

    }
}
