package com.std.core.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class WxAppUtil {
    /**
     * 获取openid和session_key
     */
    public static JSONObject getAppOauth2AccessKey(String code, String appId, String secret) {
        JSONObject res = new JSONObject();
        //拼接url
        StringBuilder url = new StringBuilder("https://api.weixin.qq.com/sns/oauth2/access_token?");
        // appid设置
        url.append("appid=");
        url.append(appId);
        // secret设置
        url.append("&secret=");
        url.append(secret);
        // code设置
        url.append("&code=");
        url.append(code);
        url.append("&grant_type=authorization_code");
        try {
            // 构建一个Client
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url.toString());
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
            log.info("wxLogin:" + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 根据 appid 和 secret获取access_token
     * @param appId
     * @param secret
     * @return
     */
    public static JSONObject getAppOauth2AccessKey( String appId, String secret) {
        JSONObject res = new JSONObject();
        //拼接url
        String getTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
        String url = String.format(getTokenUrl, appId, secret);

        try {
            // 构建一个Client
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url);
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
            log.info("wxLogin:" + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 获取openid和session_key
     */
    public static JSONObject getAppUserInfo(String access_token, String openid) {
        JSONObject res = new JSONObject();
        //拼接url
        StringBuilder url = new StringBuilder("https://api.weixin.qq.com/sns/userinfo?");
        // access_token
        url.append("access_token=");
        url.append(access_token);
        // openid
        url.append("&openid=");
        url.append(openid);
        // lang
        url.append("&lang=");
        url.append("zh_CN");
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url.toString());
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
            log.info("wxUserInfo:" + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }
    public static JSONObject getAppUserInfo2(String access_token, String openid) {
        JSONObject res = new JSONObject();
        //拼接url
        StringBuilder url = new StringBuilder("https://api.weixin.qq.com/cgi-bin/user/info?");
        // access_token
        url.append("access_token=");
        url.append(access_token);
        // openid
        url.append("&openid=");
        url.append(openid);
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url.toString());
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
            log.info("wxUserInfo:" + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String s = "\"country\":\"\",\"unionid\":\"o7L5R5-8A6krnZORNLbTgUZjWrsw\",\"province\":\"\",\"city\":\"\",\"openid\":\"ohN3654nlPwGGnHbpiP1dpKpF6Ng\",\"sex\":0,\"nickname\":\"é\\u009D\\u0092é\\u009D\\u0092å\u00AD\\u0090è¡¿\",\"headimgurl\":\"https://thirdwx.qlogo.cn/mmopen/vi_32/NZvVnbgUJjmujSUhZuxOBic9Ff4SvwdnF73mAc5KkLLMypublC7pHfxaCCmPkyeghQwoeLDphqliaH8TVJqdhibKQ/132\",\"language\":\"\",\"privilege\":[]}";
        System.out.println(new String(s.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
    }
}
