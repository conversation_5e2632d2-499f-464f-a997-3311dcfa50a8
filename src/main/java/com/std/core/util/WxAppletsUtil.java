package com.std.core.util;

import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.sun.jersey.core.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Arrays;

@Slf4j
public class WxAppletsUtil {

    /**
     * 获取用户信息
     *
     * @param encryptedData
     * @param sessionKey
     * @param iv
     * @return
     */
    public static JSONObject getUserInfo(String encryptedData, String sessionKey, String iv) {
        // 被加密的数据
        byte[] dataByte = Base64.decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = Base64.decode(sessionKey);
        // 偏移量
        byte[] ivByte = Base64.decode(iv);

        try {
            // 如果密钥不足16位,那么就补足
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + (keyByte.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            // 初始化
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                String result = new String(resultByte, "UTF-8");
                return JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取openid和session_key
     *
     * @param code
     * @param APPID
     * @param APPSecret
     * @return
     */
    public static JSONObject getAccessKey(String code, String APPID, String APPSecret) {
        JSONObject res = new JSONObject();
        //拼接url
        StringBuilder url = new StringBuilder("https://api.weixin.qq.com/sns/jscode2session?");
        // appid设置
        url.append("appid=");
        url.append(APPID);
        // secret设置
        url.append("&secret=");
        url.append(APPSecret);
        // code设置
        url.append("&js_code=");
        url.append(code);
        url.append("&grant_type=authorization_code");
        try {
            // 构建一个Client
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url.toString());
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * Sha1
     *
     * @param str
     * @return
     */
    public static String getSha1(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取openid和session_key
     */
    public static JSONObject getAppOauth2AccessKey(String code, String appId, String secret) {
        JSONObject res = new JSONObject();
        //拼接url
        StringBuilder url = new StringBuilder("https://api.weixin.qq.com/sns/oauth2/access_token?");
        // appid设置
        url.append("appid=");
        url.append(appId);
        // secret设置
        url.append("&secret=");
        url.append(secret);
        // code设置
        url.append("&code=");
        url.append(code);
        url.append("&grant_type=authorization_code");
        try {
            // 构建一个Client
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet get = new HttpGet(url.toString());
            // 提交GET请求
            HttpResponse response = client.execute(get);
            // 拿到返回的HttpResponse的"实体"
            HttpEntity result = response.getEntity();
            String content = EntityUtils.toString(result);
            // 把信息封装为json
            res = JSONObject.parseObject(content);
            log.info("wxLogin:" + content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    public static void main(String[] args) {
        JSONObject jsonObject = getAppOauth2AccessKey("0a3245200TuBrT13x4400AHAQ722452E", "wx0bf01798e93cb2f9", "e3529c4760f3eff944adf6a305132a5f");

        String openid = jsonObject.getString("openid");
        String access_token = jsonObject.getString("access_token");

        JSONObject appUserInfo = WxAppUtil.getAppUserInfo(access_token, openid);
        System.out.println(appUserInfo);
    }
}
