package com.std.core.util.wechat;

import java.io.*;
import java.security.KeyStore;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import javax.net.ssl.SSLContext;

import com.std.core.pojo.domain.WechatReturnCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;

/**
 * 微信退款
 *
 * @author: silver
 * @since: Dec 17, 2018 2:56:28 PM
 * @history:
 */
@Data
@Slf4j
public class WXRefund {

    private String WECHAT_REFUND_URL;

    private String appid;

    private String mch_id;

    private String nonce_str = OrderUtil.CreateNoncestr();

    private String out_refund_no;

    private String out_trade_no;

    private String refund_fee;

    private String total_fee;

    private String sign;

    private String privateKey;

    private String bizType;

    private String bizNote;

    /**
     * 退款
     *
     * @create: Dec 17, 2018 3:24:14 PM silver
     * @history:
     */
    public WechatReturnCode submitXmlRefund() {
        String return_code = null;
        WechatReturnCode res = new WechatReturnCode();

        KeyStore keyStore = null;

        FileInputStream instream = null;
        try {
            instream = new FileInputStream(new File("/mnt/www/czzmsg/apiclient_cert.p12"));
            log.info("加载证书");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        try {
            keyStore = KeyStore.getInstance("PKCS12");

            keyStore.load(instream, mch_id.toCharArray());


            SSLContext sslcontext = SSLContexts.custom()
                    .loadKeyMaterial(keyStore, mch_id.toCharArray()).build();

            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                    sslcontext, new String[]{"TLSv1.2"}, null,
                    SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            CloseableHttpClient httpclient = HttpClients.custom()
                    .setSSLSocketFactory(sslsf).build();

            HttpPost httpPost = new HttpPost(WECHAT_REFUND_URL);
            String xml = getPackage();
            StringEntity entity;

            entity = new StringEntity(xml, "utf-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse response = httpclient.execute(httpPost);

            HttpEntity resEntity = response.getEntity();
            String result = EntityUtils.toString(resEntity, "UTF-8");
            System.out.println(result);

            result = result.replaceAll("<![CDATA[|]]>", "");
            return_code = Jsoup.parse(result).select("return_code").html();
            res.setReturn_code(return_code);
            String result_code = Jsoup.parse(result).select("result_code").html();
            res.setResult_code(result_code);
        } catch (Exception e1) {
            e1.printStackTrace();
        } finally {
            try {
                instream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return res;
    }

    public static void main(String[] args) {
        String result="<xml>\n" +
                "<appid>wxc92b342ab22bb91a</appid>\n" +
                "<mch_id>1704951116</mch_id>\n" +
                "<nonce_str>c659c667921e8b039ae7c30348046445</nonce_str>\n" +
                "<out_refund_no>972589612080046080</out_refund_no>\n" +
                "<out_trade_no>972589612080046080</out_trade_no>\n" +
                "<refund_fee>300000</refund_fee>\n" +
                "<sign>3A908863B7160D37695438DB706E9381</sign>\n" +
                "<total_fee>300000</total_fee>\n" +
                "</xml>\n" +
                "<xml><return_code><![CDATA[SUCCESS]]></return_code>\n" +
                "<return_msg><![CDATA[OK]]></return_msg>\n" +
                "<appid><![CDATA[wxc92b342ab22bb91a]]></appid>\n" +
                "<mch_id><![CDATA[1704951116]]></mch_id>\n" +
                "<nonce_str><![CDATA[gjIXND0E4bkgho7S]]></nonce_str>\n" +
                "<sign><![CDATA[8E132E7A18F2E783F59A70C0EFC85EF2]]></sign>\n" +
                "<result_code><![CDATA[FAIL]]></result_code>\n" +
                "<err_code><![CDATA[NOTENOUGH]]></err_code>\n" +
                "<err_code_des><![CDATA[基本账户余额不足，请充值后重新发起]]></err_code_des>\n" +
                "</xml>";
        WechatReturnCode res = new WechatReturnCode();
        result = result.replaceAll("<![CDATA[|]]>", "");
        String return_code = Jsoup.parse(result).select("return_code").html();
        res.setReturn_code(return_code);
        String result_code = Jsoup.parse(result).select("result_code").html();
        res.setResult_code(result_code);
        System.out.println(res);

        System.out.println(Jsoup.parse(res.getReturn_code()).text());
        System.out.println(Jsoup.parse(res.getResult_code()).text());
    }

    public String getPackage() {
        SortedMap<Object, Object> treeMap = new TreeMap<Object, Object>();
        treeMap.put("appid", this.appid);
        treeMap.put("mch_id", this.mch_id);
        treeMap.put("nonce_str", this.nonce_str);
        treeMap.put("out_refund_no", this.out_refund_no);
        treeMap.put("out_trade_no", this.out_trade_no);
        treeMap.put("refund_fee", this.refund_fee);
        treeMap.put("total_fee", this.total_fee);

        sign = createSign("utf-8", privateKey, treeMap);
        treeMap.put("sign", sign);

        StringBuilder xml = new StringBuilder();
        xml.append("<xml>\n");
        for (Map.Entry<Object, Object> entry : treeMap.entrySet()) {
            xml.append("<" + entry.getKey() + ">").append(entry.getValue())
                    .append("</" + entry.getKey() + ">\n");
        }
        xml.append("</xml>");
        System.out.println(xml.toString());
        return xml.toString();
    }

    public String createSign(String characterEncoding, String secretKey,
                             SortedMap<Object, Object> parameters) {
        StringBuffer sb = new StringBuffer();
        Set<Map.Entry<Object, Object>> es = parameters.entrySet();
        Iterator<Map.Entry<Object, Object>> it = es.iterator();
        while (it.hasNext()) {
            Map.Entry<Object, Object> entry = (Map.Entry<Object, Object>) it
                    .next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k)
                    && !"key".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("key=" + secretKey);
        String sign = MD5Util.MD5Encode(sb.toString(), characterEncoding)
                .toUpperCase();
        return sign;
    }

}
