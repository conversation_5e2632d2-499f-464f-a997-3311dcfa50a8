package com.std.core.websocket;

import com.alibaba.fastjson.JSONObject;
import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-03-12 20:11
 */
public class ServerEncoder implements Encoder.Text<WebSocketMessage> {

    //代表websocket调用sendObject方法返回客户端的时候，必须返回的是WebSocketMessage对象
    @Override
    public String encode(WebSocketMessage webSocketMessage) {
        //将java对象转换为json字符串
        return JSONObject.toJSONString(webSocketMessage);
    }

    @Override
    public void init(EndpointConfig endpointConfig) {
    }

    @Override
    public void destroy() {
    }

}

    
    