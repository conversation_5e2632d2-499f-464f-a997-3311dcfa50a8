package com.std.core.websocket;

import com.std.core.enums.EBoolean;
import com.std.core.enums.EWebSocketSendWay;
import com.std.core.enums.EWebSocketType;
import com.std.core.pojo.response.ConnectionRes;
import com.std.core.service.IConfigService;
import com.std.core.util.SysConstants;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArraySet;
import javax.websocket.EncodeException;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> haiqingzheng
 * @since : 2019-03-02 16:49
 */
@ServerEndpoint(value = "/websocket/{userId}/{tagList}", encoders = {ServerEncoder.class})
@Component
@Slf4j
public class WebSocketServer {

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象
     */
    public static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户标识列表
     */
    private List<String> tagList;

    private static IConfigService configService;

    @Autowired
    public void setConfigService(IConfigService configService) {
        this.configService = configService;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Long userId,
            @PathParam("tagList") String tagList) {
        this.session = session;
        this.userId = userId;

        // 加入set中
        webSocketSet.add(this);

        log.info("有新窗口开始监听:" + userId + ",当前在线人数为" + webSocketSet.size());

        if (StringUtils.isNotBlank(tagList)) {
            String[] tagArray = tagList.split("_");
            this.tagList = new ArrayList<>(Arrays.asList(tagArray));
        }

        try {
            connectSuccess(userId);
            sendOnlineCount();
        } catch (Exception e) {
            log.error("websocket {}用户连接异常", userId);
        }
    }

    private void connectSuccess(Long userId) {
        WebSocketMessage webSocketMessage = new WebSocketMessage();
        webSocketMessage.setCh(EWebSocketType.CONNECT_SUCCESS.getCode().concat(".").concat(EWebSocketSendWay.NOTICE.getCode()));
        webSocketMessage.setStatus(EBoolean.YES.getCode());
        webSocketMessage.setData(null);

        sendInfoByUserId(webSocketMessage, userId);
    }

    public Integer getOnlineCount() {
        Integer baseCount = configService.getIntegerValue(SysConstants.ONLINE_COUNT);
        Integer beishu = configService.getIntegerValue(SysConstants.ONLINE_COUNT_MULTIPLE);
        return baseCount + webSocketSet.size() * beishu;
    }

    private void sendOnlineCount() {
        WebSocketMessage webSocketMessage = new WebSocketMessage();
        webSocketMessage.setCh(EWebSocketType.TODAY_SUMMARY.getCode().concat(".").concat(EWebSocketSendWay.NOTICE.getCode()));
        webSocketMessage.setStatus(EBoolean.YES.getCode());
        ConnectionRes result = new ConnectionRes();

        result.setOnlineCount(getOnlineCount());
        webSocketMessage.setData(result);

        sendInfoToAll(webSocketMessage);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 从set中删除
        webSocketSet.remove(this);

        //推送最新的在线数据
        sendOnlineCount();

        // 在线数减1
        log.info("有一连接关闭！当前在线人数为" + webSocketSet.size());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session 连接会话
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到来自窗口" + userId + "的信息:" + message);
    }

    /**
     * 发送错误时调用
     *
     * @param session 连接会话
     * @param error 错误信息
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误");
    }

    /**
     * 实现服务器主动推送
     *
     * @param message 消息内容
     * @throws IOException IO异常
     */
    private void sendMessage(WebSocketMessage message) {
        try {
            this.session.getBasicRemote().sendObject(message);
        } catch (Exception e) {
            log.error("websocket 推送消息[{}]异常", e.getMessage());
        }
    }

    /**
     * 实现服务器主动推送
     *
     * @param message 消息对象
     * @throws IOException IO异常
     */
    private void sendWebSocketMessage(WebSocketMessage message)
            throws IOException, EncodeException {
        this.session.getBasicRemote().sendObject(message);
    }

    /**
     * 私发自定义消息
     *
     * @param message 连接会话
     * @throws IOException IO异常
     */
    public static void sendInfoByUserId(WebSocketMessage message, Long userId) {
        try {
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(userId);
            sendInfoByUserIdList(message, userIdList);
        } catch (Exception e) {
            log.error("websocket发送给[{}]用户信息[{}]错误", userId, message);
        }
    }

    /**
     * 私发自定义消息
     *
     * @param webSocketMessage 连接会话
     * @throws Exception 异常
     */
    public static void sendInfoToAll(WebSocketMessage webSocketMessage) {
        for (WebSocketServer item : webSocketSet) {
            try {
                item.sendMessage(webSocketMessage);
            } catch (Exception e) {
                log.error("websocket发送给[{}]用户信息[{}]错误", item.userId, webSocketMessage);
            }
        }
    }

    /**
     * 根据多个用户标识组群发文本消息
     *
     * @param message 连接会话
     * @param tagList 用户标识List
     * @throws Exception 异常
     */
    public static void sendTextInfoByTagList(WebSocketMessage message, List<String> tagList)
            throws IOException, EncodeException {
        for (WebSocketServer item : webSocketSet) {
            try {
                if (CollectionUtils.isNotEmpty(tagList) && CollectionUtils
                        .isNotEmpty(item.tagList)) {
                    for (String tag : tagList) {
                        if (item.tagList.contains(tag)) {
                            item.sendMessage(message);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("websocket tag发送给[{}]用户信息[{}]错误", item.userId, message);
            }
        }
    }

    /**
     * 群发自定义消息
     *
     * @param message 连接会话
     * @param userIdList 用户IdList
     * @throws Exception 异常
     */
    public static void sendInfoByUserIdList(WebSocketMessage message, List<Long> userIdList) {
        for (WebSocketServer item : webSocketSet) {
            try {
                if (CollectionUtils.isNotEmpty(userIdList)) {
                    if (userIdList.contains(item.userId)) {
                        log.info("发送实时消息");
                        log.info("用户ID={}", item.userId);
                        log.info("内容={}", message);
                        item.sendWebSocketMessage(message);
                    }
                }
            } catch (Exception e) {
                log.error("websocket tag发送给[{}]用户信息[{}]错误", item.userId, message);
            }
        }
    }

    /**
     * 根据单个用户标识群发自定义消息
     *
     * @param message 连接会话
     * @param tag 用户标识
     * @throws IOException IO异常
     */
    public static void sendInfoByTag(WebSocketMessage message, String tag)
            throws IOException, EncodeException {
        List<String> tagList = new ArrayList<>();
        tagList.add(tag);
        sendInfoByTagList(message, tagList);
    }

    /**
     * 根据多个用户标识组群发自定义消息
     *
     * @param message 连接会话
     * @param tagList 用户标识List
     * @throws Exception 异常
     */
    public static void sendInfoByTagList(WebSocketMessage message, List<String> tagList) {
        for (WebSocketServer item : webSocketSet) {
            try {
                if (CollectionUtils.isNotEmpty(tagList) && CollectionUtils
                        .isNotEmpty(item.tagList)) {
                    for (String tag : tagList) {
                        if (item.tagList.contains(tag)) {
                            log.info("发送实时消息");
                            log.info("用户ID={}", item.userId);
                            log.info("内容={}", message);
                            item.sendWebSocketMessage(message);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("websocket tag发送给[{}]用户信息[{}]错误", item.userId, message);
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WebSocketServer that = (WebSocketServer) o;
        return Objects.equals(session, that.session) &&
                Objects.equals(userId, that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(session, userId);
    }
}


