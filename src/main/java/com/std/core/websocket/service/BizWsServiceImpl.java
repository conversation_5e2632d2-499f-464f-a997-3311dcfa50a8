package com.std.core.websocket.service;

import com.std.core.enums.EBoolean;
import com.std.core.enums.EWebSocketSendWay;
import com.std.core.enums.EWebSocketType;
import com.std.core.websocket.WebSocketMessage;
import com.std.core.websocket.WebSocketServer;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> xieyj
 * @since : 2020-06-06 15:27
 */
@Service
public class BizWsServiceImpl implements IBizWsService {

    @Async
    @Override
    public void sendDate(Long userId, EWebSocketType eWebSocketType, Object data) {
        WebSocketMessage webSocketMessage = initMessageData(eWebSocketType, data, EWebSocketSendWay.DATA);
        WebSocketServer.sendInfoByUserId(webSocketMessage, userId);
    }

    @Async
    @Override
    public void sendDate(EWebSocketType eWebSocketType, Object data) {
        WebSocketMessage webSocketMessage = initMessageData(eWebSocketType, data, EWebSocketSendWay.DATA);
        WebSocketServer.sendInfoToAll(webSocketMessage);
    }


    private WebSocketMessage initMessageData(EWebSocketType eWebSocketType, Object data, EWebSocketSendWay webSocketSendWay) {
        WebSocketMessage webSocketMessage = new WebSocketMessage();
        webSocketMessage.setCh(eWebSocketType.getCode().concat(".").concat(webSocketSendWay.getCode()));
        webSocketMessage.setStatus(EBoolean.YES.getCode());
        webSocketMessage.setData(data);
        return webSocketMessage;
    }

}


