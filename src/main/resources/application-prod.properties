####### server #######
server.port=6003

####### dev datasource #######
spring.datasource.url=***************************************************************************************************************************************************************
spring.datasource.username=czzmsg_prod
spring.datasource.password=W8rhMWx$s#zi
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#rm-bp1353v5v7g599oo3wo.mysql.rds.aliyuncs.com
####### eureka #######
eureka.client.serviceUrl.defaultZone=***********************************/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetchRegistry=true
eureka.client.server.waitTimeInMsWhenSyncEmpty=0


####### sms #######
sms_url=http://127.0.0.1:7902/std-sms/api
cloud_wallet_url=http://**************:2102/forward-service/api

###### logger #######
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.std.core.mapper=INFO

####### swagger #######

swagger.host:api.xfbdev.hichengdai.com
swagger.enable=false

wx.open.config.appid=wxc92b342ab22bb91a
wx.open.config.secret=24ec65ebaad4dd8c922df64302f0988f
wx.open.config.secret.templateId=8n0sI9vDL895_cRdGr9cuUv4fGnnGnvUvAZns2tCBSw
wx.open.config.token=RsbTCmYVGsq4Y8hl
wx.open.wechat.merchant.id=**********
wx.open.wechat.merchant.v2.privatekey=zu462gmZjt7gEy2OSJKs34Uf5xeOVfnD
wx.open.wechat.templateId=b0Cb5-74bcsZzQCKdQ9Do90V9_eLgw6TUHS9Z-SXGzA
wx.open.wechat.activity.templateId=7Gvb5WSWy_ThQ2I9fXoOj360jn5O5b1WLh10nUVvjMU

###### alipay #######
alipay.providerid=
alipay.appid=
alipay.privatekey=
alipay.publickey=
alipay.notifyurl=
alipay.returnurl=
alipay.signtype=RSA2
alipay.gateway=https://openapi.alipay.com/gateway.do
alipay.format=json
alipay.charset=UTF-8

wechat.appid=wxc92b342ab22bb91a
wechat.appSecret=24ec65ebaad4dd8c922df64302f0988f
wechat.backurl=https://m.xixiartmuseum.com/api/core/callback/public/wechat
wechat.merchant.id=**********
wechat.merchant.privatekey=zu462gmZjt7gEy2OSJKs34Uf5xeOVfnD
wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
##### AliOSS  #######
oss.endpoint=sts.cn-hangzhou.aliyuncs.com
oss.accessKeyId=LTAI5tRK89cN1W74rrxs3pBU
oss.accessKeySecret=******************************
oss.roleArn=acs:ram::1766537379472507:role/czzmsg-role
oss.bucket=czzmsg
oss.bucket.endpoint=http://oss-accelerate.aliyuncs.com
oss.bucket.ossEndpoint=oss-accelerate
oss.bucket.filePath=https://czzmsg.oss-cn-hangzhou.aliyuncs.com


spring.devtools.add-properties=false

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://127.0.0.1:6904/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=default_token

### xxl-job executor appname
xxl.job.executor.appname=czzmsg-job-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=9999
### xxl-job executor log-path
xxl.job.executor.logpath=/mnt/www/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=3

meta.lock.timeout=30000

gaode.key=e79d9eae1a4433848ddb7d06b10b034f

##### REDIS  #######
spring.redis.database=3
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=FTMMS93oqNr5
spring.redis.max-active=600
spring.redis.max-wait=60000
spring.redis.max-idle=300
spring.redis.min-idle=0
spring.redis.timeout=6000
