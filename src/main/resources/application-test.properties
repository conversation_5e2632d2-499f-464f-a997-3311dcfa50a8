####### server #######
server.port=6003

####### dev datasource #######
spring.datasource.url=*********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=tKPnC*d^E8h@GnzF
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

####### eureka #######
eureka.client.serviceUrl.defaultZone=***********************************/eureka/
eureka.client.register-with-eureka=false
eureka.client.fetchRegistry=false
eureka.client.server.waitTimeInMsWhenSyncEmpty=0


####### sms #######
sms_url=http://127.0.0.1:7902/std-sms/api
cloud_wallet_url=http://**************:2102/forward-service/api

###### logger #######
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.std.core.mapper=INFO

####### swagger #######

swagger.host:api.xfbdev.hichengdai.com
swagger.enable=true

wx.open.config.appid=wxc92b342ab22bb91a
wx.open.config.secret=b5464c7e678f6d55d034c22bef939669
wx.open.config.secret.templateId=8n0sI9vDL895_cRdGr9cuUv4fGnnGnvUvAZns2tCBSw
wx.open.config.token=RsbTCmYVGsq4Y8hl
wx.open.wechat.merchant.id=**********
wx.open.wechat.merchant.v2.privatekey=E3AclPvYcmcy7AGGB8kvTLql2acQnIm3
wx.open.wechat.templateId=
wx.open.wechat.activity.templateId=

###### alipay #######
alipay.providerid=2088331233959325
alipay.appid=2021001115682384
alipay.privatekey=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCI8UimmK7i3BLthJuMahjoQ87Mw+YpqjO+P8cxCdpSZ0SB0wsKVD1rLhfdSGR19lU5jBnBHkRR68v1IHW7OGgOrFOlI1ZMMSn8gvJal7qE822M0tJZNCk431B7q/qLve3+y7UBvxdpOsHZ2SQeGcUrvG8pU4lzhgAi9Jg1BvqVzt9dXw2Lpu8C9NR0DJLDTsTCuq5V0nf9PYBNB4Hza1e/fhdWQ14Yg/bPMsH/+zAnB326gVlao6g1CrKtHEmRLZFDDk4tlIXYB6eeZBxhK14AjxpySL7hHJ1E2eweIuc8X+ztkqOQWvTGiHnDDEZWaZgPwUZd1ztO6txtzebYR3ZzAgMBAAECggEAEQtfDIVCdzAHFxMDPUXTVc1ixu+3DQaHLC3KABuG1ezOhQ4ceKulfVqeU8d34YrIbsI2TmgS/tNHbnoczTGnGfRu8MXmLtOLs0clHQt3hqa5wuZWkg3A97grz0jHhW8dWTJHlDERm+JZUGELcseoV9o7R7yDBiLuj2s8KpV0yQJoywIYL2viaEAS7oD7lUMIOONMn0ABT7OMzEHhCa5ObvEdvWt1DihrVPx2EAPZ4CWyr3l4eN7+RgeITcdA4vW+C3g5gvutsACwJqbi5u6ViUXwhwwNTmrx4iKUhKFrybT9DsNGW2DVZ9OfZaz36ERycQA3O+ed60GaO7AU1rEiQQKBgQDOmOznUZrysdMUq4iNR6TlQmkxYUUf3ttHznekGBIrjMndy2oJGoBmy7E/j4ULPzeZVhn0BMH9ILcSoi0DzTUdlKhsm+foTWmQMroQ/QjrucsORPIeH7N2JgyzxwR9j5xgOgiSqZo9dqhdnC7m9vzajJyUGwEbx+vEE5lCGe/YkwKBgQCpsF0Vo8cSUBzI5dzN5OgPpZ4crtwZxIkO+rI5EqLj6afupKP20he6JllGCpdqK5VtyUyAWLEEti40csRhiKKFqF0gJHOh2eHIOlPInIxCQ2SNUrLazSP5C8SxykTYXM3h4rL8s28Pu01h4L3AH1vVWaIJeULX2Z1JdZgVS0H2oQKBgG+g+6iFVHa8FLR/drn3syZY+uO+jg4JaWxkKAsXIoI7gTuBVJr0odppRy1zy8xm7RDCECXkiDjQe3rq++2o7IE+OL7ugqDnaxmJ/zV8s1xDOG9OW5BhSqOVLZnZ+BgsB6+Ky+ULFRcOzSw/XqxXWgpwnY39BV1QaZi5ij7IKutXAoGASMexFQ/62TrtLamRa+nrMpLZXvoMwyyNkJQsDtiUbuaPtYZ+IOX5cUjzb1wpl0r8O2tkWYQw4JF2/d3fpumgs20EDD42LkwyT9vchaXHbsBTB15aAgiMjkvi4FKd9qLPVzwRgts/HmM6jGmFWTrZ+4cHPcL5zXSzxoceZLuzniECgYAin6HFcsFvxN8Ia5LjMVxvRMpU0qxayhel2ikwO+ARdXXYzi6AEqxl7APuEzmbhOyyXeZsU6JRhXjfDJFFCZMOMLgByCjkV44sFVmjWqX3TDKEF66MNuEEtVwbUzHut22+ElISutu3QoDNvN7aUKiwHoV7Tyjx1q+bPYauiZlfBg==
alipay.publickey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnMlTC/OUfGf1MK3jIRRvGTo8HBLavAxJSqZmDSVjwTOLf/L7+fqt2nJGtQ3dYsWA92tanWJI13H7CphOuMrTq4JD+TaYcj3zZ/B52zJ6hyqFIUTNjjvArqWiZDEVSf1Fn52vNbOvrpze2ILzzqrxuTCH5tDOf5D6u6OmYWlTJJrvHjGcZdIEfMjfqNeMzn5yeKd35B18W8q3G/TapT/7rAWz+icFhS0DAPfWdWhXzhkqkVrdAaac1yQXvo9BNtm01GHRRZloC6X9dZUYR8NA/BmbulMiSQTqjzvZYYD5djeHf+6P+ZAbU+y7uPkjcPuSHIW2CbVz0U+sTHu1xlD1+QIDAQAB
alipay.notifyurl=http://sys.xfbdev.hichengdai.com/api/core/callback/public/alipay
alipay.returnurl=http://www.aismiledev.hichengdai.com/home/<USER>
alipay.signtype=RSA2
alipay.gateway=https://openapi.alipay.com/gateway.do
alipay.format=json
alipay.charset=UTF-8

###### wechat #######
#wechat.appid=wx690e0544bdec7cdb
#wechat.backurl=http://sys.xfbdev.hichengdai.com/api/core/callback/public/wechat
#wechat.merchant.id=1556257351
#wechat.merchant.privatekey=22e9f06c4d41b387e28dd708ef0dc111
#wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund


wechat.appid=wxc92b342ab22bb91a
wechat.appSecret=b5464c7e678f6d55d034c22bef939669
wechat.backurl=https://m.xixiartmuseum.com/api/core/callback/public/wechat
wechat.merchant.id=**********
wechat.merchant.privatekey=KfrbgdUAmgmB5z9jK254VdnzzB6WIvsPh7Apg8kQKWI=
wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
##### AliOSS  #######
oss.endpoint=sts.cn-hangzhou.aliyuncs.com
oss.accessKeyId=LTAI4FqPPBPaEBWcJ4bHpYCE
oss.accessKeySecret=******************************
oss.roleArn=acs:ram::1403162818021033:role/oss-manger
oss.bucket=xfb-bucket
oss.bucket.endpoint=http://oss-accelerate.aliyuncs.com
oss.bucket.ossEndpoint=oss-accelerate
oss.bucket.filePath=https://xfb-bucket.oss-accelerate.aliyuncs.com


spring.devtools.add-properties=false



###NAT????URl###
nat.market=http://api.gtmswzc.com/v1/market/detail/merged?symbol=NATUSDT


### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://127.0.0.1:6904/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=

### xxl-job executor appname
xxl.job.executor.appname=czzmsg-job-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=0
### xxl-job executor log-path
xxl.job.executor.logpath=/mnt/www/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30


meta.lock.timeout=30000

gaode.key=e79d9eae1a4433848ddb7d06b10b034f




##### REDIS  #######
spring.redis.database=3
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=clkj2018
spring.redis.max-active=600
spring.redis.max-wait=60000
spring.redis.max-idle=300
spring.redis.min-idle=0
spring.redis.timeout=6000
