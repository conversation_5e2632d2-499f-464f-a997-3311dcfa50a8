{"Read Me": {"使用说明": "key为异常代码，value为异常提示", "使用规则": "key采用9位，前4位用于区分类型，后5位用于区分具体异常", "返回字段": "key:errorCode value:errorMsg"}, "##通用异常": "通用异常（errorCode ）", "500003": "%s's ID=%s 的记录不存在", "##系统权限业务异常": "系统基础功能业务异常(AUTH*****)", "300004": "您的账号已在别处登录", "AUTH00001": "用户密码错误", "AUTH00002": "登录账号已存在", "AUTH00003": "登录名不存在", "AUTH00004": "很抱歉，您已被注销，请联系管理员！", "AUTH00005": "手机号已存在", "AUTH00006": "邮箱已存在", "AUTH00007": "请先设置密码", "AUTH00008": "七牛云图片参数异常", "AUTH00010": "手机号不存在", "##系统核心业务异常": "系统基础功能业务异常(CORE*****)", "CORE00000": "【%s】", "CORE00001": "账户可用余额不足", "CORE00002": "取现金额需大于手续费", "CORE00003": "订单未处于可审核状态", "CORE00004": "订单未处于可支付状态", "CORE00005": "充值金额不能小于0", "CORE00006": "账户编号异常", "CORE00007": "本次解冻会使账户冻结金额小于0", "CORE00008": "请先设置支付密码", "CORE00009": "支付密码错误", "CORE00010": "手机号不存在", "CORE00011": "手机号码已存在", "CORE00012": "登录密码错误", "CORE00013": "很抱歉，您已被注销，请联系管理员！", "CORE00014": "最小取现金额为：%s", "CORE00015": "最大取现金额为：%s", "CORE00016": "取现步长为：%s", "CORE00017": "组件模块请求异常，请检查url配置：【%s】", "CORE00018": "银行卡类型错误", "CORE00019": "支付金额不能小于0", "CORE00020": "订单未支付，无法退款", "CORE00021": "微信支付回调异常，原因%s：", "CORE00031": "空投账户可用余额不足", "CORE00555": "您还未绑定手机微信", "CORE00579": "绑定微信失败，请联系客服", "CORE00090": "登录失败", "CORE00095": "授权码错误", "CORE00096": "您还未绑定手机号", "CORE00097": "您已绑定手机微信", "CORE00098": "当前微信已被绑定", "CORE00281": "手机号已被使用", "CORE00396": "微信支付类型必填", "CORE00397": "微信JSAPI支付openid必填!", "CORE00398": "不支持的支付方式", "CORE00580": "微信支付失败，请联系客服", "##业务核心业务异常": "业务基础功能业务异常(AIS*****)", "AIS00001": "当前数据未处于可操作状态", "AIS00002": "不支持的操作", "AIS00003": "接收文件批次错误", "AIS00004": "病例%s不存在收货地址，无法发货", "AIS00005": "操作失败，物料不存在", "AIS00006": "操作失败，操作数量错误", "AIS00007": "操作失败，操作金额错误", "AIS00008": "操作失败，降低授信金额大于已授信金额", "AIS00009": "操作失败，请输入正确的批次及每批次套数", "AIS00010": "操作失败，请完善所有项目后再提交", "AIS00011": "操作失败，已存在保持批次", "AIS00012": "操作失败，病例【%s】批次信息错误，当前批次为【%s】", "AIS00013": "操作失败，所有批次已发货完成", "AIS00014": "操作失败，治疗方案还未形成", "AIS00015": "操作失败，病例已全部佩戴完成", "AIS00016": "操作失败，牙套错误，当前应佩戴第【%s】套", "AIS00017": "操作失败，硅橡胶印膜请发货后再提交", "AIS00018": "操作失败，请填写资料问题列表", "AIS00019": "操作失败，该问题项已存在", "AIS00020": "操作失败，请填写完整地址信息", "AIS00021": "操作失败，请填写数字", "AIS00022": "操作失败，请填写条件金额", "AIS00023": "操作失败，有效期填写错误", "AIS00024": "操作失败，只能发放给患者", "AIS00025": "操作失败，无法与自己建立关系", "AIS00026": "操作失败，产品已下架", "AIS00027": "操作失败，优惠券无法使用", "AIS00028": "操作失败，优惠券未达满减金额", "AIS00029": "操作失败，该用户已存在订单", "AIS00030": "操作失败，只有患者能下单", "AIS00031": "操作失败，支付宝支付异常，请联系客服", "AIS00032": "操作失败，请先微信登录再支付", "AIS00033": "操作失败，牙套还未收货", "AIS00034": "操作失败，请先完成物流收货", "AIS00035": "操作失败，请完成所有牙套佩戴", "AIS00036": "操作失败，当前批次已佩戴完，请重新发货", "AIS00037": "操作失败，患者名称长度最长为%s"}