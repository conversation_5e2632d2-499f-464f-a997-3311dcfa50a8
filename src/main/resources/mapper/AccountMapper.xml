<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.AccountMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Account">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="available_amount" jdbcType="DECIMAL" property="availableAmount"/>
        <result column="frozen_amount" jdbcType="DECIMAL" property="frozenAmount"/>
        <result column="lock_amount" jdbcType="DECIMAL" property="lockAmount"/>
        <result column="in_amount" jdbcType="DECIMAL" property="inAmount"/>
        <result column="out_amount" jdbcType="DECIMAL" property="outAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="last_order" jdbcType="VARCHAR" property="lastOrder"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.account_number
        , t.user_id
        , t.currency
        , t.type
        , t.status
        , t.amount
        , t.available_amount
        , t.frozen_amount
        , t.lock_amount
        , t.in_amount
        , t.out_amount
        , t.create_datetime
        , t.last_order
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                AND t.account_number = #{accountNumber, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="currencyList != null and currencyList.size > 0 ">
                AND t.currency in
                <foreach item="item" index="index" collection="currencyList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="typeList != null and typeList.size > 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%',#{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Account" useGeneratedKeys="true">
        insert into tstd_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountNumber != null and accountNumber != '' ">
                account_number,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="availableAmount != null">
                available_amount,
            </if>
            <if test="frozenAmount != null ">
                frozen_amount,
            </if>
            <if test="lockAmount != null">
                lock_amount,
            </if>
            <if test="inAmount != null">
                in_amount,
            </if>
            <if test="outAmount != null">
                out_amount,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="lastOrder != null and lastOrder != '' ">
                last_order,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountNumber != null and accountNumber != '' ">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="userId != null ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="availableAmount != null">
                #{availableAmount,jdbcType=DECIMAL},
            </if>
            <if test="frozenAmount != null">
                #{frozenAmount,jdbcType=DECIMAL},
            </if>
            <if test="lockAmount != null">
                #{lockAmount,jdbcType=DECIMAL},
            </if>
            <if test="inAmount != null">
                #{inAmount,jdbcType=DECIMAL},
            </if>
            <if test="outAmount != null">
                #{outAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastOrder != null and lastOrder != '' ">
                #{lastOrder,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tstd_account
        where account_number = #{accountNumber,jdbcType=VARCHAR}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Account">
        update tstd_account
        <set>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="availableAmount != null">
                available_amount = #{availableAmount,jdbcType=DECIMAL},
            </if>
            <if test="frozenAmount != null">
                frozen_amount = #{frozenAmount,jdbcType=DECIMAL},
            </if>
            <if test="lockAmount != null">
                lock_amount = #{lockAmount,jdbcType=DECIMAL},
            </if>
            <if test="inAmount != null">
                in_amount = #{inAmount,jdbcType=DECIMAL},
            </if>
            <if test="outAmount != null">
                out_amount = #{outAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastOrder != null and lastOrder != '' ">
                last_order = #{lastOrder,jdbcType=VARCHAR},
            </if>
        </set>
        where account_number = #{accountNumber,jdbcType=VARCHAR}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.account_number = #{accountNumber,jdbcType=VARCHAR}
    </select>

    <select id="selectByUserId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.user_id = #{userId,jdbcType=VARCHAR}
    </select>


    <!-- 查询 -->
    <select id="selectByPrimaryForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.id = #{id} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Account"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="select" parameterType="com.std.core.pojo.domain.Account"
            resultMap="BaseResultMap">
        select
        t.id
        , t.account_number
        , t.user_id
        , t.currency
        , t.type
        , t.status
        , t.amount
        , t.available_amount
        , t.frozen_amount
        , t.lock_amount
        , t.in_amount
        , t.create_datetime
        , t.last_order
        from tstd_account t
        left join tsys_user tu on t.user_id=tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalAmount" parameterType="com.std.core.pojo.domain.Account" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(amount), 0) FROM tstd_account t
        <include refid="where_condition"/>
    </select>
    <select id="selectSubSellerAccountByCondition" parameterType="com.std.core.pojo.domain.Account"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.type = #{type,jdbcType=VARCHAR}
        AND t.user_id NOT IN (SELECT user_id AS uid FROM tgyl_seller)
        and t.user_id not in (select user_id as uid from tgyl_anchor )
        <if test="userId != null">
            and t.user_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="currency != null and currency != '' ">
            and t.currency = #{currency,jdbcType=VARCHAR}
        </if>
        ORDER BY t.user_id,t.currency
    </select>
    <select id="selectSellerAccountByCondition" parameterType="com.std.core.pojo.domain.Account"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.type = #{type,jdbcType=VARCHAR}
        and t.currency = #{currency,jdbcType=VARCHAR}
        and t.user_id in (select user_id as uid from tgyl_seller group by user_id)
        <if test="userId != null">
            and t.user_id = #{userId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="selectAnchorAccountByCondition" parameterType="com.std.core.pojo.domain.Account"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_account t
        where t.user_id in (select user_id as uid from tgyl_anchor group by user_id)
        <if test="userId != null">
            and t.user_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="currency != null">
            and t.currency = #{currency,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectamountSumList" resultType="com.std.core.pojo.response.AccountAmountSumListRes">
        SELECT COALESCE(SUM(amount), 0)           amount,
               COALESCE(SUM(available_amount), 0) availableAmount,
               COALESCE(SUM(frozen_amount), 0)    frozenAmount
        FROM tstd_account
        WHERE currency = #{currency}
          AND user_id in (select id from tsys_user where status in ('normal'))
          AND `type` = 'C'
    </select>
    <select id="selectamountSumListExcept" resultType="com.std.core.pojo.response.AccountAmountSumListRes">
        SELECT COALESCE(SUM(amount), 0)           amount,
               COALESCE(SUM(available_amount), 0) availableAmount,
               COALESCE(SUM(frozen_amount), 0)    frozenAmount
        FROM tstd_account
        WHERE currency = #{currency}
          AND `type` = 'C'
          AND user_id in (select id from tsys_user where status in ('normal'))
          AND user_id NOT in (SELECT user_id from tstd_statistics_except_user where type = '0')
    </select>
    <select id="selectamountSumListNotExcept" resultType="com.std.core.pojo.response.AccountAmountSumListRes">
        SELECT COALESCE(SUM(amount), 0)           amount,
               COALESCE(SUM(available_amount), 0) availableAmount,
               COALESCE(SUM(frozen_amount), 0)    frozenAmount
        FROM tstd_account
        WHERE currency = #{currency}
          AND `type` = 'C'
          AND user_id in (select id from tsys_user where status in ('normal'))
          AND user_id in (SELECT user_id from tstd_statistics_except_user where type = '0')
    </select>
    <select id="selectIntegralList" resultType="com.std.core.pojo.response.AccountIntegralListRes">
        select t.user_id userId,
               t.currency,
               t.amount
        FROM tstd_account t
        WHERE t.currency = 'INTEGRAL'
          AND t.`type` = 'C'
        order by t.amount desc limit #{limit}
    </select>

    <select id="selectAccountList" resultType="com.std.core.pojo.response.AccountDeductRes">
        select t.id,
               t.mobile,
               t.deduct_amount deductAmount
        FROM tstd_deduct_account t
    </select>

</mapper>