<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActivityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Activity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="visitor_information" jdbcType="VARCHAR" property="visitorInformation"/>
        <result column="notice" jdbcType="VARCHAR" property="notice"/>
        <result column="minimum_buy_number" jdbcType="INTEGER" property="minimumBuyNumber"/>
        <result column="maximum_buy_number" jdbcType="INTEGER" property="maximumBuyNumber"/>
        <result column="day_limit" jdbcType="INTEGER" property="dayLimit"/>
        <result column="limit" jdbcType="INTEGER" property="limit"/>
        <result column="longitude" jdbcType="VARCHAR" property="longitude"/>
        <result column="latitude" jdbcType="VARCHAR" property="latitude"/>
        <result column="start_time" jdbcType="DATE" property="startTime"/>
        <result column="end_time" jdbcType="DATE" property="endTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="buy_start_time" jdbcType="TIMESTAMP" property="buyStartTime"/>
        <result column="buy_end_time" jdbcType="TIMESTAMP" property="buyEndTime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="address_location" jdbcType="VARCHAR" property="addressLocation"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.title
        , t.location
        , t.pic
        , t.address
        , t.province
        , t.city
        , t.county
        , t.price
        , t.visitor_information
        , t.notice
        , t.minimum_buy_number
        , t.maximum_buy_number
        , t.day_limit
        , t.limit
        , t.longitude
        , t.latitude
        , t.start_time
        , t.end_time
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.buy_start_time
        , t.buy_end_time
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.address_location
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="title != null and title != '' ">
                AND t.title = #{title, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="address != null and address != '' ">
                AND t.address = #{address, jdbcType=VARCHAR}
            </if>
            <if test="province != null and province != '' ">
                AND t.province = #{province, jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != '' ">
                AND t.city = #{city, jdbcType=VARCHAR}
            </if>
            <if test="county != null and county != '' ">
                AND t.county = #{county, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="visitorInformation != null and visitorInformation != '' ">
                AND t.visitor_information = #{visitorInformation, jdbcType=VARCHAR}
            </if>
            <if test="notice != null and notice != '' ">
                AND t.notice = #{notice, jdbcType=VARCHAR}
            </if>
            <if test="minimumBuyNumber != null">
                AND t.minimum_buy_number = #{minimumBuyNumber, jdbcType=INTEGER}
            </if>
            <if test="maximumBuyNumber != null">
                AND t.maximum_buy_number = #{maximumBuyNumber, jdbcType=INTEGER}
            </if>
            <if test="dayLimit != null">
                AND t.day_limit = #{dayLimit, jdbcType=INTEGER}
            </if>
            <if test="limit != null">
                AND t.limit = #{limit, jdbcType=INTEGER}
            </if>
            <if test="longitude != null and longitude != '' ">
                AND t.longitude= #{longitude, jdbcType=VARCHAR}
            </if>
           <if test="latitude != null and latitude != '' ">
                AND t.latitude = #{latitude, jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND t.start_time = #{startTime, jdbcType=DATE}
            </if>
            <if test="buyStartTime != null">
                AND t.buy_start_time = #{buyStartTime, jdbcType= jdbcType=TIMESTAMP}
            </if>
            <if test="buyEndTime != null">
                AND t.buy_end_time = #{buyEndTime, jdbcType= jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND t.end_time = #{endTime, jdbcType=DATE}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="noStatusList != null and noStatusList.size() != 0 ">
                AND t.status not in
                <foreach item="item" index="index" collection="noStatusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="addressLocation != null and addressLocation != '' ">
                AND t.address_location = #{addressLocation, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Activity" useGeneratedKeys="true" keyProperty="id">
        insert into tbiz_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="title != null and title != '' ">
                title,
              </if>
              <if test="location != null and location != '' ">
                location,
              </if>
              <if test="pic != null and pic != '' ">
                pic,
              </if>
              <if test="address != null and address != '' ">
                address,
              </if>
              <if test="province != null and province != '' ">
                  province,
              </if>
              <if test="city != null and city != '' ">
                  city,
              </if>
              <if test="county != null and county != '' ">
                  county,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="visitorInformation != null and visitorInformation != '' ">
                visitor_information,
              </if>
              <if test="notice != null and notice != '' ">
                notice,
              </if>
              <if test="minimumBuyNumber != null ">
                minimum_buy_number,
              </if>
              <if test="maximumBuyNumber != null ">
                maximum_buy_number,
              </if>
              <if test="dayLimit != null ">
                day_limit,
              </if>
              <if test="limit != null ">
                `limit`,
              </if>
              <if test="longitude!= null and longitude != '' ">
                longitude,
              </if>
             <if test="latitude != null and latitude != '' ">
                latitude,
              </if>
              <if test="startTime != null ">
                start_time,
              </if>
              <if test="endTime != null ">
                end_time,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="buyStartTime != null ">
                buy_start_time,
              </if>
              <if test="buyEndTime != null ">
                buy_end_time,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
            <if test="addressLocation != null and addressLocation != '' ">
                address_location,
            </if>
              <if test="orderNo != null ">
                order_no,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null and county != '' ">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="visitorInformation != null and visitorInformation != '' ">
                #{visitorInformation,jdbcType=VARCHAR},
            </if>
            <if test="notice != null and notice != '' ">
                #{notice,jdbcType=VARCHAR},
            </if>
            <if test="minimumBuyNumber != null">
                #{minimumBuyNumber,jdbcType=INTEGER},
            </if>
            <if test="maximumBuyNumber != null">
                #{maximumBuyNumber,jdbcType=INTEGER},
            </if>
            <if test="dayLimit != null">
                #{dayLimit,jdbcType=INTEGER},
            </if>
            <if test="limit != null">
                #{limit,jdbcType=INTEGER},
            </if>
            <if test="longitude != null and longitude != '' ">
                #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null and latitude != '' ">
                #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=DATE},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=DATE},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyStartTime != null">
                #{buyStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyEndTime != null">
                #{buyEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="addressLocation != null and addressLocation != '' ">
                #{addressLocation,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tbiz_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Activity">
        update tbiz_activity
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="county != null and county != '' ">
                county = #{county,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="visitorInformation != null and visitorInformation != '' ">
                visitor_information = #{visitorInformation,jdbcType=VARCHAR},
            </if>
            <if test="notice != null and notice != '' ">
                notice = #{notice,jdbcType=VARCHAR},
            </if>
            <if test="minimumBuyNumber != null">
                minimum_buy_number = #{minimumBuyNumber,jdbcType=INTEGER},
            </if>
            <if test="maximumBuyNumber != null">
                maximum_buy_number = #{maximumBuyNumber,jdbcType=INTEGER},
            </if>
            <if test="dayLimit != null">
                day_limit = #{dayLimit,jdbcType=INTEGER},
            </if>
            <if test="limit != null">
                `limit` = #{limit,jdbcType=INTEGER},
            </if>
            <if test="longitude != null and longitude != '' ">
                longitude = #{longitude,jdbcType=VARCHAR},
            </if>
            <if test="latitude != null and latitude != '' ">
                latitude = #{latitude,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=DATE},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=DATE},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyStartTime != null">
                buy_start_time = #{buyStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyEndTime != null">
                buy_end_time = #{buyEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="addressLocation != null and addressLocation != '' ">
                address_location = #{addressLocation,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Activity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>