<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActivityOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ActivityOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="serial_number" jdbcType="VARCHAR" property="serialNumber"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="ticket_line_id" jdbcType="BIGINT" property="ticketLineId"/>
        <result column="ticket_line_name" jdbcType="VARCHAR" property="ticketLineName"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contact" jdbcType="VARCHAR" property="contact"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="is_modify" jdbcType="VARCHAR" property="isModify"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="old_date" jdbcType="DATE" property="oldDate"/>
        <result column="code_pic" jdbcType="VARCHAR" property="codePic"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="cancle_datetime" jdbcType="TIMESTAMP" property="cancleDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.order_number
        , t.serial_number
        , t.user_id
        , t.activity_id
        , t.ticket_line_id
        , t.ticket_line_name
        , t.pic
        , t.name
        , t.contact
        , t.price
        , t.number
        , t.is_modify
        , t.date
        , t.old_date
        , t.code_pic
        , t.pay_type
        , t.status
        , t.create_datetime
        , t.cancle_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id like concat ('%', #{id, jdbcType=BIGINT},'%')
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                AND t.order_number = #{orderNumber, jdbcType=VARCHAR}
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                AND t.serial_number = #{serialNumber, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="ticketLineId != null">
                AND t.ticket_line_id = #{ticketLineId, jdbcType=BIGINT}
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                AND t.ticket_line_name = #{ticketLineName, jdbcType=VARCHAR}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="contact != null and contact != '' ">
                AND t.contact = #{contact, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="isModify != null and isModify != '' ">
                AND t.is_modify = #{isModify, jdbcType=VARCHAR}
            </if>
            <if test="date != null">
                AND t.date = #{date, jdbcType=DATE}
            </if>
            <if test="oldDate != null">
                AND t.old_date = #{oldDate, jdbcType=DATE}
            </if>
            <if test="codePic != null and codePic != '' ">
                AND t.code_pic = #{codePic, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>

            <if test="cancleDatetime != null">
                AND t.cancle_datetime = #{cancleDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="expiredTime != null ">
                AND #{expiredTime, jdbcType=TIMESTAMP} > DATE_ADD(CAST(t.date AS DATETIME), INTERVAL 1 DAY) - INTERVAL 1 SECOND
             </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ActivityOrder">
        insert into tbiz_activity_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                order_number,
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                serial_number,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="ticketLineId != null ">
                ticket_line_id,
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="contact != null and contact != '' ">
                contact,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="number != null ">
                number,
            </if>
            <if test="isModify != null and isModify != '' ">
                is_modify,
            </if>
            <if test="date != null ">
                date,
            </if>
            <if test="oldDate != null ">
                old_date,
            </if>
            <if test="codePic != null and codePic != '' ">
                code_pic,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>

            <if test="cancleDatetime != null ">
                cancle_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contact != null and contact != '' ">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="isModify != null and isModify != '' ">
                #{isModify,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="oldDate != null">
                #{oldDate,jdbcType=DATE},
            </if>
            <if test="codePic != null and codePic != '' ">
                #{codePic,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>

            <if test="cancleDatetime != null">
                #{cancleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>

        </trim>
    </insert>
    <insert id="insertBatchSelective">
        insert into tbiz_activity_order
        (
        order_number, serial_number, user_id, activity_id, ticket_line_id,
        ticket_line_name, pic, name, contact, price, number, is_modify, date,
        old_date, code_pic, pay_type, status, create_datetime, cancle_datetime,
        updater, updater_name, update_datetime
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderNumber,jdbcType=VARCHAR},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.userId,jdbcType=BIGINT},
            #{item.activityId,jdbcType=BIGINT},
            #{item.ticketLineId,jdbcType=BIGINT},
            #{item.ticketLineName,jdbcType=VARCHAR},
            #{item.pic,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.contact,jdbcType=VARCHAR},
            #{item.price,jdbcType=DECIMAL},
            #{item.number,jdbcType=INTEGER},
            #{item.isModify,jdbcType=VARCHAR},
            #{item.date,jdbcType=DATE},
            #{item.oldDate,jdbcType=DATE},
            #{item.codePic,jdbcType=VARCHAR},
            #{item.payType,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.createDatetime,jdbcType=TIMESTAMP},
            #{item.cancleDatetime,jdbcType=TIMESTAMP},
            #{item.updater,jdbcType=BIGINT},
            #{item.updaterName,jdbcType=VARCHAR},
            #{item.updateDatetime,jdbcType=TIMESTAMP}
            )
        </foreach>

    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tbiz_activity_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ActivityOrder">
        update tbiz_activity_order
        <set>
            <if test="orderNumber != null and orderNumber != '' ">
                order_number = #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                serial_number = #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                ticket_line_id = #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name = #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contact != null and contact != '' ">
                contact = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="isModify != null and isModify != '' ">
                is_modify = #{isModify,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="oldDate != null">
                old_date = #{oldDate,jdbcType=DATE},
            </if>
            <if test="codePic != null and codePic != '' ">
                code_pic = #{codePic,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>

            <if test="cancleDatetime != null">
                cancle_datetime = #{cancleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>

        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ActivityOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN tbiz_activity ta on t.activity_id = ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSumNumber" resultType="java.lang.Integer">
        select COALESCE(sum(t.number),0) from tbiz_activity_order t
        <include refid="where_condition"/>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity_order t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>

    <select id="selectActivityIncomeGroupBy"
            parameterType="com.std.core.pojo.request.IncomeGroupReq"
            resultType="com.std.core.pojo.response.IncomeItemRes">
        SELECT
        <choose>
            <when test="period == 'day'">
                DATE_FORMAT(t.create_datetime, '%Y-%m-%d') AS timeGroup
            </when>
            <when test="period == 'week'">
                DATE_FORMAT(DATE_SUB(t.create_datetime, INTERVAL WEEKDAY(t.create_datetime) DAY), '%Y-%m-%d') AS timeGroup
            </when>
            <when test="period == 'month'">
                DATE_FORMAT(t.create_datetime, '%Y-%m') AS timeGroup
            </when>
            <otherwise>
                DATE_FORMAT(t.create_datetime, '%Y-%m-%d') AS timeGroup
            </otherwise>
        </choose>
        , COALESCE(SUM(t.price * t.number), 0) AS income
        FROM tbiz_activity_order t
        <where>
            t.status IN ('0', '1','2')
            <if test="startTime != null">
                AND t.create_datetime &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_datetime &lt;= #{endTime}
            </if>
        </where>
        GROUP BY timeGroup
        ORDER BY timeGroup ASC
    </select>

    <select id="selectMinMaxCreateTime" resultType="com.std.core.pojo.response.MinMaxTimeRes">
        SELECT
            MIN(DATE(t.create_datetime)) AS minTime,
            MAX(DATE(t.create_datetime)) AS maxTime
        FROM tbiz_activity_order t
        WHERE t.status IN ('0', '1','2')
    </select>

    <select id="selectActivityIncomeSum"
            parameterType="com.std.core.pojo.request.IncomeGroupReq"
            resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(CAST(t.price AS DECIMAL(18,2)) * t.number), 0)
        FROM tbiz_activity_order t
        <where>
            t.status IN ('0', '1','2')
            <if test="startTime != null ">
                AND t.create_datetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null ">
                AND t.create_datetime <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
    <select id="selectIncomeByDate" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(t.price * t.number), 0)
        FROM tbiz_activity_order t
        <where>
            t.status IN ('0', '1','2')
            <if test="startTime != null and startTime != ''">
                AND t.create_datetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND t.create_datetime <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>


</mapper>