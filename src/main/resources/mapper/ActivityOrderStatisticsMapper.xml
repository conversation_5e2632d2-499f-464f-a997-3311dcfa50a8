<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActivityOrderStatisticsMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ActivityOrderStatistics">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="order_date" jdbcType="DATE" property="orderDate"/>
        <result column="total_tickets" jdbcType="INTEGER" property="totalTickets"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.activity_id
        , t.order_date
        , t.total_tickets
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="orderDate != null">
                AND t.order_date = #{orderDate, jdbcType=DATE}
            </if>
            <if test="totalTickets != null">
                AND t.total_tickets = #{totalTickets, jdbcType=INTEGER}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ActivityOrderStatistics">
        insert into tbiz_activity_order_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="orderDate != null ">
                order_date,
            </if>
            <if test="totalTickets != null ">
                total_tickets,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="orderDate != null">
                #{orderDate,jdbcType=DATE},
            </if>
            <if test="totalTickets != null">
                #{totalTickets,jdbcType=INTEGER},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tbiz_activity_order_statistics
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ActivityOrderStatistics">
        update tbiz_activity_order_statistics
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate,jdbcType=DATE},
            </if>
            <if test="totalTickets != null">
                total_tickets = #{totalTickets,jdbcType=INTEGER},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateTotalTickets">
        update tbiz_activity_order_statistics
        set total_tickets   = total_tickets + #{totalTickets},
            update_datetime = #{updateDatetime,jdbcType=TIMESTAMP}
        where activity_id = #{activityId}
          and date (order_date) = date (#{orderDate})
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity_order_statistics t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ActivityOrderStatistics"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_activity_order_statistics t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectFullyBookedDates" resultType="java.util.Date">
        select DATE_FORMAT(order_date, '%Y-%m-%d') orderDate
        from tbiz_activity_order_statistics
        where activity_id = #{activityId}
          and total_tickets <![CDATA[>=]]> #{dayLimit}
          and DATE_FORMAT(order_date, '%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(now(), '%Y-%m-%d')
    </select>
</mapper>