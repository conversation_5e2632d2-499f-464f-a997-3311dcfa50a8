<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.AreaMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Area">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="deep" jdbcType="TINYINT" property="deep"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="enabled" jdbcType="TINYINT" property="enabled"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.pid
        , t.deep
        , t.short_name
        , t.enabled
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="pid != null">
                AND t.pid = #{pid, jdbcType=BIGINT}
            </if>
            <if test="deep != null">
                AND t.deep = #{deep, jdbcType=TINYINT}
            </if>
            <if test="shortName != null and shortName != '' ">
                AND t.short_name = #{shortName, jdbcType=VARCHAR}
            </if>
            <if test="enabled != null">
                AND t.enabled = #{enabled, jdbcType=TINYINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Area">
        insert into tstd_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="pid != null ">
                pid,
              </if>
              <if test="deep != null ">
                deep,
              </if>
              <if test="shortName != null and shortName != '' ">
                short_name,
              </if>
              <if test="enabled != null ">
                enabled,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                #{pid,jdbcType=BIGINT},
            </if>
            <if test="deep != null">
                #{deep,jdbcType=TINYINT},
            </if>
            <if test="shortName != null and shortName != '' ">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                #{enabled,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_area
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Area">
        update tstd_area
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="pid != null">
                pid = #{pid,jdbcType=BIGINT},
            </if>
            <if test="deep != null">
                deep = #{deep,jdbcType=TINYINT},
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null">
                enabled = #{enabled,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_area t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Area"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_area t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>