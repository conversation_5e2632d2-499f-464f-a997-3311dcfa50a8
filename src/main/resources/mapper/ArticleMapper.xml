<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ArticleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Article">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="readNumber" jdbcType="INTEGER" property="readNumber"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type_id
        , t.type
        , t.pic
        , t.title
        , t.content
        , t.content_type
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>
    <sql id="Base_Column_PC_List">
        t.id
        , t.type_id
        , t.type
        , t.pic
        , t.title
        , t.content
        , t.content_type
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="typeId != null ">
                AND t.type_id = #{typeId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title like concat(concat('%',#{title}),'%')
            </if>
            <if test="frontFlag != null and frontFlag != ''">
                AND tat.status = '1'
            </if>
        </trim>
    </sql>

    <sql id="join_condition">
        <if test="frontFlag != null and frontFlag != ''">
            left join tsys_article_type tat on t.type_id = tat.id
        </if>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Article">
        insert into tsys_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="typeId != null ">
                type_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="title != null and title != '' ">
                title,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="typeId != null ">
                #{typeId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_article
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Article">
        update tsys_article
        <set>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type = #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_PC_List"/>
        from tsys_article t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectTitleByCondition" parameterType="com.std.core.pojo.domain.Article"
      resultMap="BaseResultMap">
        select
        t.id
        , t.type_id
        , t.type
        , t.pic
        , t.title
        , t.content
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , tr.readNumber
        from tsys_article t
        <include refid="join_condition"/>
        left join (select ref_id,number as readNumber from tyx_browse_records tbr where ref_type="7" and action_type='0' group by ref_id) tr on t.id=tr.ref_id
        <include refid="where_condition"/>
        ORDER BY  tr.readNumber DESC
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Article"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_article t
        <include refid="join_condition"/>
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <!-- 组合条件查询 -->
    <select id="selectPCPage" parameterType="com.std.core.pojo.domain.Article"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_PC_List"/>
        from tsys_article t
        <include refid="join_condition"/>
        left join (select ref_id,number as readNumber from tyx_browse_records tbr where ref_type="7" and action_type='0' group by ref_id) tr on t.id=tr.ref_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>