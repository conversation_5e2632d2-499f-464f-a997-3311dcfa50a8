<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.BigActivityOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.BigActivityOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="serial_number" jdbcType="VARCHAR" property="serialNumber"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="ticket_line_id" jdbcType="BIGINT" property="ticketLineId"/>
        <result column="ticket_line_name" jdbcType="VARCHAR" property="ticketLineName"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contact" jdbcType="VARCHAR" property="contact"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.serial_number
        , t.user_id
        , t.activity_id
        , t.ticket_line_id
        , t.ticket_line_name
        , t.pic
        , t.name
        , t.contact
        , t.total_price
        , t.number
        , t.pay_type
        , t.status
        , t.create_datetime
        , t.pay_datetime
        , t.date
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                AND t.serial_number = #{serialNumber, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="ticketLineId != null">
                AND t.ticket_line_id = #{ticketLineId, jdbcType=BIGINT}
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                AND t.ticket_line_name = #{ticketLineName, jdbcType=VARCHAR}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name = #{name, jdbcType=VARCHAR}
            </if>
            <if test="contact != null and contact != '' ">
                AND t.contact = #{contact, jdbcType=VARCHAR}
            </if>
            <if test="totalPrice != null">
                AND t.total_price = #{totalPrice, jdbcType=DECIMAL}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="date != null">
                AND t.date = #{date, jdbcType=DATE}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.BigActivityOrder" useGeneratedKeys="true" keyProperty="id">
        insert into tbiz_big_activity_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="serialNumber != null and serialNumber != '' ">
                serial_number,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
              <if test="ticketLineId != null ">
                ticket_line_id,
              </if>
              <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name,
              </if>
              <if test="pic != null and pic != '' ">
                pic,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="contact != null and contact != '' ">
                contact,
              </if>
              <if test="totalPrice != null ">
                total_price,
              </if>
              <if test="number != null ">
                number,
              </if>
              <if test="payType != null and payType != '' ">
                pay_type,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="payDatetime != null ">
                pay_datetime,
              </if>
              <if test="date != null ">
                date,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="serialNumber != null and serialNumber != '' ">
                #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contact != null and contact != '' ">
                #{contact,jdbcType=VARCHAR},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tbiz_big_activity_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.BigActivityOrder">
        update tbiz_big_activity_order
        <set>
            <if test="serialNumber != null and serialNumber != '' ">
                serial_number = #{serialNumber,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                ticket_line_id = #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name = #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contact != null and contact != '' ">
                contact = #{contact,jdbcType=VARCHAR},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_big_activity_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.BigActivityOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_big_activity_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectBySerialNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_big_activity_order t
        where t.serial_number = #{serialNumber,jdbcType=VARCHAR}
        for update
    </select>
    <select id="selectTimeOutOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_big_activity_order t
        where t.status='0' and DATE_ADD(t.create_datetime, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{date}
    </select>
</mapper>