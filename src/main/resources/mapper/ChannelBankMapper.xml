<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelBankMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelBank">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="channel_bank" jdbcType="VARCHAR" property="channelBank"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.bank_code
        , t.bank_name
        , t.channel_type
        , t.channel_bank
        , t.logo
        , t.status
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code = #{bankCode, jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name = #{bankName, jdbcType=VARCHAR}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="channelBank != null and channelBank != '' ">
                AND t.channel_bank = #{channelBank, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelBank">
        insert into tstd_channel_bank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
              </if>
              <if test="bankCode != null and bankCode != '' ">
                bank_code,
              </if>
              <if test="bankName != null and bankName != '' ">
                bank_name,
              </if>
              <if test="channelType != null and channelType != '' ">
                channel_type,
              </if>
              <if test="channelBank != null and channelBank != '' ">
                channel_bank,
              </if>
              <if test="logo != null and logo != '' ">
                logo,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelBank != null and channelBank != '' ">
                #{channelBank,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_channel_bank
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelBank">
        update tstd_channel_bank
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelBank != null and channelBank != '' ">
                channel_bank = #{channelBank,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_channel_bank t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelBank"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_channel_bank t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>