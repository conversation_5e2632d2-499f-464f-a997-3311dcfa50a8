<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChargeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Charge">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_kind" jdbcType="VARCHAR" property="userKind"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_note" jdbcType="VARCHAR" property="bizNote"/>
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo"/>
        <result column="bill_flag" jdbcType="VARCHAR" property="billFlag"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="apply_user" jdbcType="BIGINT" property="applyUser"/>
        <result column="apply_note" jdbcType="VARCHAR" property="applyNote"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="channel_bank" jdbcType="VARCHAR" property="channelBank"/>
        <result column="channel_account_info" jdbcType="VARCHAR" property="channelAccountInfo"/>
        <result column="channel_account_number" jdbcType="VARCHAR" property="channelAccountNumber"/>
        <result column="channel_order" jdbcType="VARCHAR" property="channelOrder"/>
        <result column="pay_group" jdbcType="VARCHAR" property="payGroup"/>
        <result column="pay_user" jdbcType="VARCHAR" property="payUser"/>
        <result column="pay_note" jdbcType="VARCHAR" property="payNote"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.account_number
        , t.account_type
        , t.user_id
        , t.user_kind
        , t.amount
        , t.currency
        , t.biz_type
        , t.biz_note
        , t.biz_no
        , t.bill_flag
        , t.status
        , t.apply_user
        , t.apply_note
        , t.apply_datetime
        , t.channel_type
        , t.channel_bank
        , t.channel_account_info
        , t.channel_account_number
        , t.channel_order
        , t.pay_group
        , t.pay_user
        , t.pay_note
        , t.pay_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                AND t.account_number = #{accountNumber, jdbcType=VARCHAR}
            </if>
            <if test="accountType != null and accountType != '' ">
                AND t.account_type = #{accountType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userList != null and userList.size() != 0 ">
                AND t.user_id in
                <foreach item="item" index="index" collection="userList" open="(" separator=","
                        close=")">
                    #{item.id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userKind != null and userKind != '' ">
                AND t.user_kind = #{userKind, jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizNo != null and bizNo != '' ">
                AND t.biz_no = #{bizNo, jdbcType=VARCHAR}
            </if>
            <if test="billFlag != null and billFlag != '' ">
                AND t.bill_flag = #{billFlag, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="applyUser != null  ">
                AND t.apply_user = #{applyUser, jdbcType=BIGINT}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                AND t.channel_order = #{channelOrder, jdbcType=VARCHAR}
            </if>
            <if test="payGroup != null and payGroup != '' ">
                AND t.pay_group = #{payGroup, jdbcType=VARCHAR}
            </if>
            <if test="payUser != null and payUser != '' ">
                AND t.pay_user = #{payUser, jdbcType=VARCHAR}
            </if>

            <if test="idForQuery != null ">
                AND id like concat('%',#{idForQuery},'%')
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="applyDatetimeStart != null">
                <![CDATA[AND t.apply_datetime >= #{applyDatetimeStart}]]>
            </if>

            <if test="applyDatetimeEnd != null">
                <![CDATA[AND t.apply_datetime <= #{applyDatetimeEnd}]]>
            </if>

            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>

            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>

            <if test="userKindList != null and userKindList.size() != 0 ">
                AND t.user_kind in
                <foreach item="item" index="index" collection="userKindList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="channelTypeList != null and channelTypeList.size() != 0 ">
                AND t.channel_type in
                <foreach item="item" index="index" collection="channelTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Charge">
        insert into tstd_charge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                account_number,
            </if>
            <if test="accountType != null and accountType != '' ">
                account_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="userKind != null and userKind != '' ">
                user_kind,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type,
            </if>
            <if test="bizNote != null and bizNote != '' ">
                biz_note,
            </if>
            <if test="bizNo != null and bizNo != '' ">
                biz_no,
            </if>
            <if test="billFlag != null and billFlag != '' ">
                bill_flag,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="applyUser != null ">
                apply_user,
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note,
            </if>
            <if test="applyDatetime != null ">
                apply_datetime,
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type,
            </if>
            <if test="channelBank != null and channelBank != '' ">
                channel_bank,
            </if>
            <if test="channelAccountInfo != null and channelAccountInfo != '' ">
                channel_account_info,
            </if>
            <if test="channelAccountNumber != null and channelAccountNumber != '' ">
                channel_account_number,
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                channel_order,
            </if>
            <if test="payGroup != null and payGroup != '' ">
                pay_group,
            </if>
            <if test="payUser != null and payUser != '' ">
                pay_user,
            </if>
            <if test="payNote != null and payNote != '' ">
                pay_note,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null and accountType != '' ">
                #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="userId != null  ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userKind != null and userKind != '' ">
                #{userKind,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizNote != null and bizNote != '' ">
                #{bizNote,jdbcType=VARCHAR},
            </if>
            <if test="bizNo != null and bizNo != '' ">
                #{bizNo,jdbcType=VARCHAR},
            </if>
            <if test="billFlag != null and billFlag != '' ">
                #{billFlag,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null  ">
                #{applyUser,jdbcType=BIGINT},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelBank != null and channelBank != '' ">
                #{channelBank,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountInfo != null and channelAccountInfo != '' ">
                #{channelAccountInfo,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountNumber != null and channelAccountNumber != '' ">
                #{channelAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                #{channelOrder,jdbcType=VARCHAR},
            </if>
            <if test="payGroup != null and payGroup != '' ">
                #{payGroup,jdbcType=VARCHAR},
            </if>
            <if test="payUser != null and payUser != '' ">
                #{payUser,jdbcType=VARCHAR},
            </if>
            <if test="payNote != null and payNote != '' ">
                #{payNote,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_charge
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Charge">
        update tstd_charge
        <set>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null ">
                apply_user = #{applyUser,jdbcType=BIGINT},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note = #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                apply_datetime = #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                channel_order = #{channelOrder,jdbcType=VARCHAR},
            </if>
            <if test="payGroup != null and payGroup != '' ">
                pay_group = #{payGroup,jdbcType=VARCHAR},
            </if>
            <if test="payUser != null and payUser != '' ">
                pay_user = #{payUser,jdbcType=VARCHAR},
            </if>
            <if test="payNote != null and payNote != '' ">
                pay_note = #{payNote,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateStatusByPryGroup" parameterType="com.std.core.pojo.domain.Charge">
    update tstd_charge set status = #{status,jdbcType=VARCHAR}
    where pay_group = #{payGroup,jdbcType=VARCHAR}
  </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_charge t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Charge"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_charge t
        left join tsys_user tu on t.user_id=tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalCountByCondition" parameterType="com.std.core.pojo.domain.Charge" resultType="java.lang.Long">
        select count(1)
        from tstd_charge t
        <include refid="where_condition"/>
    </select>

    <select id="selectEffectUserList" parameterType="com.std.core.pojo.domain.Charge" resultType="java.lang.Long">
        select distinct t.user_id as userId from tstd_charge t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalAmount" parameterType="com.std.core.pojo.domain.Charge" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(amount), 0) FROM tstd_charge t
        <include refid="where_condition"/>
    </select>
</mapper>