<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CnavigateMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Cnavigate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="action" jdbcType="VARCHAR" property="action"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.type
        , t.url
        , t.pic
        , t.status
        , t.location
        , t.action
        , t.group_name
        , t.order_no
        , t.parent_id
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="url != null and url != '' ">
                AND t.url = #{url, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="action != null and action != '' ">
                AND t.action = #{action, jdbcType=VARCHAR}
            </if>
            <if test="groupName != null and groupName != '' ">
                AND t.group_name = #{groupName, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="parentId != null ">
                AND t.parent_id = #{parentId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Cnavigate">
        insert into tsys_cnavigate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="type != null and type != ''">
                type,
            </if>
            <if test="url != null and url != ''">
                url,
            </if>
            <if test="pic != null and pic != ''">
                pic,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="location != null and location != ''">
                location,
            </if>
            <if test="action != null and action != ''">
                action,
            </if>
            <if test="groupName != null and groupName != ''">
                group_name,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != '' ">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="action != null and action != '' ">
                #{action,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null and groupName != '' ">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="parentId != null ">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_cnavigate
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Cnavigate">
        update tsys_cnavigate
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="url != null and url != '' ">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="action != null and action != '' ">
                action = #{action,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null and groupName != '' ">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="parentId != null ">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_cnavigate t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Cnavigate"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_cnavigate t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>