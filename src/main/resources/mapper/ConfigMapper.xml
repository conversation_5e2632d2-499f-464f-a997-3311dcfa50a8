<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.ConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Config">
        <id column="t_id" property="id" jdbcType="BIGINT"/>
        <result column="t_type" property="type" jdbcType="VARCHAR"/>
        <result column="t_key" property="key" jdbcType="VARCHAR"/>
        <result column="t_value" property="value" jdbcType="VARCHAR"/>
        <result column="t_updater" property="updater" jdbcType="VARCHAR"/>
        <result column="t_update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="t_remark" property="remark" jdbcType="VARCHAR"/>
        <result column="t_show_type" property="showType" jdbcType="VARCHAR"/>
        <result column="t_is_show" property="isShow" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.id as t_id
        , t.type as t_type
        , t.key as t_key
        , t.value as t_value
        , t.updater as t_updater
        , t.update_time as t_update_time
        , t.remark as t_remark
        , t.show_type as t_show_type
        , t.is_show as t_is_show
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type}
            </if>
            <if test="key != null and key != '' ">
                AND t.key = #{key}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark like CONCAT('%',#{remark,jdbcType=VARCHAR},'%')
            </if>
        </trim>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from tsys_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Config">
        insert into tsys_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="key != null">
                `key`,
            </if>
            <if test="value != null">
                `value`,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Config">
        update tsys_config
        <set>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                `value` = #{value,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Config"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>
