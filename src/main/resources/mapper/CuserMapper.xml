<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CuserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Cuser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="willing_value" jdbcType="DECIMAL" property="willingValue"/>
        <result column="member_flag" jdbcType="VARCHAR" property="memberFlag"/>
        <result column="vip_flag" jdbcType="VARCHAR" property="vipFlag"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="agent_status" jdbcType="VARCHAR" property="agentStatus"/>
        <result column="agent_join_datetime" jdbcType="TIMESTAMP" property="agentJoinDatetime"/>
        <result column="agent_quit_datetime" jdbcType="TIMESTAMP" property="agentQuitDatetime"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="recovery_time" jdbcType="TIMESTAMP" property="recoveryTime"/>
        <result column="negation_comment_decrease_status" jdbcType="VARCHAR" property="negationCommentDecreaseStatus"/>
        <result column="authentication_status" jdbcType="VARCHAR" property="authenticationStatus"/>
    </resultMap>

    <resultMap id="RichResultMap" type="com.std.core.pojo.domain.Cuser" extends="BaseResultMap">
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="front_img" jdbcType="VARCHAR" property="frontImg"/>
        <result column="back_img" jdbcType="VARCHAR" property="backImg"/>
        <result column="recommenderName" jdbcType="VARCHAR" property="recommenderName"/>
        <result column="register_datetime" jdbcType="TIMESTAMP" property="registerDatetime"/>
        <result column="register_ip" jdbcType="VARCHAR" property="registerIp"/>
        <result column="invite_no" jdbcType="BIGINT" property="inviteNo"/>
        <result column="user_referee" jdbcType="BIGINT" property="userReferee"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="authentication_status" jdbcType="VARCHAR" property="authenticationStatus"/>
        <result column="last_login_datetime" jdbcType="TIMESTAMP" property="lastLoginDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.willing_value
        , t.member_flag
        , t.level
        , t.openid
        , t.agent_status
        , t.agent_join_datetime
        , t.agent_quit_datetime
        , t.create_datetime
        , t.recovery_time
        , t.negation_comment_decrease_status
        , t.authentication_status
    </sql>

    <sql id="Rich_Column_List">
        <include refid="Base_Column_List"/>
        , tu.login_name
        , tu.nickname
        , tu.mobile
        , tu.email
        , tu.photo
        , tu.status
        , tu.real_name
        , tu.id_no
        , tu.register_datetime
        , tu.register_ip
        , tu.invite_no
        , tu.user_referee
        , tu.remark
        , tu.last_login_datetime
        , CONCAT(ifnull(ta.real_name,ta.nickname) ,'(',ta.mobile,')') as recommenderName
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="willingValue != null">
                AND t.willing_value = #{willingValue, jdbcType=DECIMAL}
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                AND t.member_flag = #{memberFlag, jdbcType=VARCHAR}
            </if>
            <if test="vipFlag != null and vipFlag != '' ">
                AND t.vip_flag = #{vipFlag, jdbcType=VARCHAR}
            </if>
            <if test="level != null and level != '' ">
                AND t.level = #{level, jdbcType=VARCHAR}
            </if>
            <if test="openid != null and openid != '' ">
                AND t.openid = #{openid, jdbcType=VARCHAR}
            </if>
            <if test="haveOpenid != null and haveOpenid != '' ">
                AND t.openid is not null
            </if>
            <if test="agentStatus != null and agentStatus != '' ">
                AND t.agent_status = #{agentStatus, jdbcType=VARCHAR}
            </if>
            <if test="authenticationStatus != null and authenticationStatus != '' ">
                AND t.authentication_status = #{authenticationStatus, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND tu.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="userReferee != null and userReferee != '' ">
                AND tu.user_referee = #{userReferee, jdbcType=VARCHAR}
            </if>

            <if test="userName != null and userName != '' ">
                AND tu.nickname LIKE concat('%',#{userName, jdbcType=VARCHAR},'%')
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Cuser" useGeneratedKeys="true" keyProperty="id">
        insert into tsys_cuser
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
            <if test="willingValue != null">
               willing_value ,
            </if>
              <if test="memberFlag != null and memberFlag != '' ">
                member_flag,
              </if>
              <if test="vipFlag != null and vipFlag != '' ">
                vip_flag,
              </if>
              <if test="level != null and level != '' ">
                level,
              </if>
              <if test="openid != null and openid != '' ">
                  openid,
              </if>
              <if test="agentStatus != null and agentStatus != '' ">
                agent_status,
              </if>
              <if test="agentJoinDatetime != null ">
                agent_join_datetime,
              </if>
              <if test="agentQuitDatetime != null ">
                agent_quit_datetime,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="recoveryTime != null ">
                recovery_time,
              </if>
            <if test="negationCommentDecreaseStatus != null and negationCommentDecreaseStatus !='' ">
                negation_comment_decrease_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="willingValue != null">
                #{willingValue, jdbcType=DECIMAL},
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                #{memberFlag,jdbcType=VARCHAR},
            </if>
            <if test="vipFlag != null and vipFlag != '' ">
                #{vipFlag,jdbcType=VARCHAR},
            </if>
            <if test="level != null and level != '' ">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="openid != null and openid != '' ">
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="agentStatus != null and agentStatus != '' ">
                #{agentStatus,jdbcType=VARCHAR},
            </if>
            <if test="agentJoinDatetime != null ">
                #{agentJoinDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="agentQuitDatetime != null ">
                #{agentQuitDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="recoveryTime != null">
                #{recoveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="negationCommentDecreaseStatus != null and negationCommentDecreaseStatus != '' ">
                #{negationCommentDecreaseStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_cuser
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Cuser">
        update tsys_cuser
        <set>
            <if test="willingValue != null">
                willing_value = #{willingValue, jdbcType=DECIMAL},
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                member_flag = #{memberFlag,jdbcType=VARCHAR},
            </if>
            <if test="vipFlag != null and vipFlag != '' ">
                vip_flag = #{vipFlag,jdbcType=VARCHAR},
            </if>
            <if test="level != null and level != '' ">
                level = #{level,jdbcType=VARCHAR},
            </if>
            <if test="openid != null and openid != '' ">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="agentStatus != null and agentStatus != '' ">
                agent_status = #{agentStatus,jdbcType=VARCHAR},
            </if>
            <if test="agentJoinDatetime != null ">
                agent_join_datetime = #{agentJoinDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="agentQuitDatetime != null ">
                agent_quit_datetime = #{agentQuitDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="recoveryTime != null">
                recovery_time = #{recoveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="negationCommentDecreaseStatus != null and negationCommentDecreaseStatus !='' ">
                negation_comment_decrease_status = #{negationCommentDecreaseStatus,jdbcType=VARCHAR},
            </if>
            <if test="authenticationStatus != null and authenticationStatus !='' ">
                authentication_status = #{authenticationStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="RichResultMap">
        select
        <include refid="Rich_Column_List"/>
        from tsys_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        left join tsys_user ta on tu.user_referee=ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByUserId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        *
        from tsys_cuser t
        where t.user_id = #{userId,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_cuser t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectRichByCondition" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="RichResultMap">
        select
        <include refid="Rich_Column_List"/>
        from tsys_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="select" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="RichResultMap">
        select
        t.id
        , tu.id as user_id
        , tu.login_name
        , tu.nickname
        , tu.mobile
        , tu.email
        , tu.photo
        , tu.status
        , tu.register_datetime
        , tu.register_ip
        , tu.invite_no
        , tu.user_referee
        , tu.remark
        , tu.last_login_datetime
        , tu.real_name
        , t.level
        , t.openid
        , t.willing_value
        , t.authentication_status
        from tsys_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
        <if test="loginName != null and loginName != '' ">
            AND tu.login_name = #{loginName, jdbcType=VARCHAR}
        </if>
        <if test="realName != null and realName != '' ">
            AND tu.real_name = #{realName, jdbcType=VARCHAR}
        </if>
        <if test="mobile != null and mobile != '' ">
            AND tu.mobile = #{mobile, jdbcType=VARCHAR}
        </if>
            <if test="status != null and status != '' ">
                AND tu.status = #{status, jdbcType=VARCHAR}
            </if>
        <if test="level != null and level != '' ">
            AND t.level = #{level, jdbcType=VARCHAR}
        </if>
            <if test="openid != null and openid != '' ">
            AND t.openid = #{openid, jdbcType=VARCHAR}
        </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null ">
                AND tu.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
        <if test="keywords != null and keywords != '' ">
            AND (tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
            or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
            or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
            )
        </if>
            <if test="registerTimeStart != null">
                <![CDATA[AND tu.register_datetime >= #{registerTimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="registerTimeEnd != null">
                <![CDATA[AND tu.register_datetime <= #{registerTimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectVague" resultMap="RichResultMap" parameterType="com.std.core.pojo.domain.Cuser">
         select tu.id as user_id,CONCAT(tu.nickname,'-',tu.mobile) as userName from tsys_cuser t
         left join tsys_user tu on t.user_id=tu.id
         <include refid="where_condition"/>

    </select>

    <delete id="deleteByUser">
        delete
        from tsys_cuser
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>

    <update id="updateUntieWx">
        update tsys_cuser
        set union_id=null,
            openid = null,
            app_openid = null,
            union_id =null,
            wx_nickname =null
        where user_id = #{userId,jdbcType=BIGINT}
    </update>
</mapper>