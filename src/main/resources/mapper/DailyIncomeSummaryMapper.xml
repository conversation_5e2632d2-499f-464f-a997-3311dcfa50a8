<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.DailyIncomeSummaryMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.DailyIncomeSummary">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_amount" jdbcType="DECIMAL" property="activityAmount"/>
        <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount"/>
        <result column="order_count" jdbcType="INTEGER" property="orderCount"/>
        <result column="income_date" jdbcType="DATE" property="incomeDate"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.activity_amount
        , t.goods_amount
        , t.order_count
        , t.income_date
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityAmount != null">
                AND t.activity_amount = #{activityAmount, jdbcType=DECIMAL}
            </if>
            <if test="goodsAmount != null">
                AND t.goods_amount = #{goodsAmount, jdbcType=DECIMAL}
            </if>
            <if test="orderCount != null">
                AND t.order_count = #{orderCount, jdbcType=INTEGER}
            </if>
            <if test="incomeDate != null">
                AND t.income_date = #{incomeDate, jdbcType=DATE}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.DailyIncomeSummary">
        insert into tbiz_daily_income_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="activityAmount != null ">
                activity_amount,
            </if>
            <if test="goodsAmount != null ">
                goods_amount,
            </if>
            <if test="orderCount != null ">
                order_count,
            </if>
            <if test="incomeDate != null ">
                income_date,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityAmount != null">
                #{activityAmount,jdbcType=DECIMAL},
            </if>
            <if test="goodsAmount != null">
                #{goodsAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderCount != null">
                #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="incomeDate != null">
                #{incomeDate,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tbiz_daily_income_summary
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.DailyIncomeSummary">
        update tbiz_daily_income_summary
        <set>
            <if test="activityAmount != null">
                activity_amount = #{activityAmount,jdbcType=DECIMAL},
            </if>
            <if test="goodsAmount != null">
                goods_amount = #{goodsAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderCount != null">
                order_count = #{orderCount,jdbcType=INTEGER},
            </if>
            <if test="incomeDate != null">
                income_date = #{incomeDate,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateActivityIncomeSummary">
        update tbiz_daily_income_summary
        set activity_amount = activity_amount + #{activityAmount,jdbcType=DECIMAL}
        where income_date = #{incomeDate}
    </update>
    <update id="updateGoodsIncomeSummary">
        update tbiz_daily_income_summary
        set goods_amount = goods_amount + #{goodsAmount,jdbcType=DECIMAL}
        where income_date = #{incomeDate}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_daily_income_summary t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.DailyIncomeSummary"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_daily_income_summary t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_daily_income_summary t
        where t.income_date = #{incomeDate}
    </select>
    <select id="selectByDay" resultType="com.std.core.pojo.response.DailyIncomeSummaryListRes">
        SELECT income_date        AS timeGroup,
               ${type} AS income
        FROM tbiz_daily_income_summary t
        WHERE income_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY income_date ASC;
    </select>
    <select id="selectByWeek" resultType="com.std.core.pojo.response.DailyIncomeSummaryListRes">
        SELECT DATE_FORMAT(DATE_SUB(t.income_date, INTERVAL WEEKDAY(t.income_date) DAY), '%Y-%m-%d') AS timeGroup,
               COALESCE(SUM(${type}), 0)                                                                     AS income
        FROM tbiz_daily_income_summary t
        WHERE income_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY timeGroup
        ORDER BY timeGroup ASC;
    </select>
    <select id="selectByMonth" resultType="com.std.core.pojo.response.DailyIncomeSummaryListRes">
        SELECT DATE_FORMAT(income_date, '%Y-%m') AS timeGroup,
               COALESCE(SUM(${type}), 0)         AS income
        FROM tbiz_daily_income_summary t
        WHERE income_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY timeGroup
        ORDER BY timeGroup ASC;
    </select>
    <select id="selectIncomeTotal" resultType="com.std.core.pojo.response.DailyIncomeSummaryDetailRes">
        SELECT COALESCE(SUM(t.activity_amount), 0) activityIncome,
        COALESCE(SUM(t.goods_amount), 0) goodsIncome
        FROM tbiz_daily_income_summary t
        <if test="startDate != null">
            WHERE income_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>
</mapper>