<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsOrderDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="norms_id" jdbcType="BIGINT" property="normsId"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="norms_name" jdbcType="VARCHAR" property="normsName"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.order_id
        , t.user_id
        , t.order_number
        , t.goods_id
        , t.norms_id
        , t.number
        , t.name
        , t.price
        , t.pic
        , t.norms_name
        , t.content
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                AND t.order_number = #{orderNumber, jdbcType=VARCHAR}
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="normsId != null">
                AND t.norms_id = #{normsId, jdbcType=BIGINT}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="normsName != null and normsName != '' ">
                AND t.norms_name = #{normsName, jdbcType=VARCHAR}
            </if>
            <if test="content != null">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsOrderDetail">
        insert into tbiz_goods_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                order_number,
            </if>
            <if test="goodsId != null ">
                goods_id,
            </if>
            <if test="normsId != null ">
                norms_id,
            </if>
            <if test="number != null ">
                number,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="normsName != null and normsName != '' ">
                norms_name,
            </if>
            <if test="content != null ">
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="normsId != null">
                #{normsId,jdbcType=BIGINT},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="normsName != null and normsName != '' ">
                #{normsName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        insert into tbiz_goods_order_detail
        ( order_id,user_id,order_number,goods_id,norms_id,number,name,price,pic,norms_name,content
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId,jdbcType=BIGINT},
            #{item.userId,jdbcType=BIGINT},
            #{item.orderNumber,jdbcType=VARCHAR},
            #{item.goodsId,jdbcType=BIGINT},
            #{item.normsId,jdbcType=BIGINT},
            #{item.number,jdbcType=INTEGER},
            #{item.name,jdbcType=VARCHAR},
            #{item.price,jdbcType=DECIMAL},
            #{item.pic,jdbcType=VARCHAR},
            #{item.normsName,jdbcType=VARCHAR},
            #{item.content,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tbiz_goods_order_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsOrderDetail">
        update tbiz_goods_order_detail
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                order_number = #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="normsId != null">
                norms_id = #{normsId,jdbcType=BIGINT},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="normsName != null and normsName != '' ">
                norms_name = #{normsName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>