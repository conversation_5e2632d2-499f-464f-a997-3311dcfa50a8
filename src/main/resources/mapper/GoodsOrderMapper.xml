<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="courier_number" jdbcType="VARCHAR" property="courierNumber"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="receive_way" jdbcType="VARCHAR" property="receiveWay"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="cancle_datetime" jdbcType="TIMESTAMP" property="cancleDatetime"/>
        <result column="auto_receive_datetime" jdbcType="TIMESTAMP" property="autoReceiveDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="finish_datetime" jdbcType="TIMESTAMP" property="finishDatetime"/>
        <result column="transaction_id" jdbcType="VARCHAR" property="transactionId"/>

    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.order_number
        , t.courier_number
        , t.number
        , t.total_price
        , t.status
        , t.receive_way
        , t.pay_type
        , t.address
        , t.user_name
        , t.user_mobile
        , t.company
        , t.remark
        , t.create_datetime
        , t.pay_datetime
        , t.cancle_datetime
        , t.auto_receive_datetime
        , t.updater
        , t.user_id
        , t.updater_name
        , t.update_datetime
        , t.finish_datetime
        , t.transaction_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id like concat('%',#{id, jdbcType=BIGINT},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                AND t.order_number = #{orderNumber, jdbcType=VARCHAR}
            </if>
            <if test="courierNumber != null and courierNumber != '' ">
                AND t.courier_number = #{courierNumber, jdbcType=VARCHAR}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="totalPrice != null">
                AND t.total_price = #{totalPrice, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                AND t.receive_way = #{receiveWay, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="address != null and address != '' ">
                AND t.address = #{address, jdbcType=VARCHAR}
            </if>
            <if test="userName != null and userName != '' ">
                AND t.user_name = #{userName, jdbcType=VARCHAR}
            </if>
            <if test="userMobile != null and userMobile != '' ">
                AND t.user_mobile = #{userMobile, jdbcType=VARCHAR}
            </if>
            <if test="company != null and company != '' ">
                AND t.company = #{company, jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createStartTime != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createStartTime, jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND t.create_datetime <![CDATA[ <=]]> #{createEndTime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="cancleDatetime != null">
                AND t.cancle_datetime = #{cancleDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="autoReceiveDatetime != null">
                AND t.auto_receive_datetime <![CDATA[ <=]]> #{autoReceiveDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>

            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="finishDatetime != null">
                AND t.finish_datetime = #{finishDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsOrder" useGeneratedKeys="true" keyProperty="id">
        insert into tbiz_goods_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="orderNumber != null and orderNumber != '' ">
                order_number,
              </if>
              <if test="courierNumber != null and courierNumber != '' ">
                courier_number,
              </if>
              <if test="number != null ">
                number,
              </if>
              <if test="totalPrice != null ">
                total_price,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="receiveWay != null and receiveWay != '' ">
                receive_way,
              </if>
              <if test="payType != null and payType != '' ">
                pay_type,
              </if>
              <if test="address != null and address != '' ">
                address,
              </if>
              <if test="userName != null and userName != '' ">
                user_name,
              </if>
              <if test="userMobile != null and userMobile != '' ">
                user_mobile,
              </if>
              <if test="company != null and company != '' ">
                company,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="payDatetime != null ">
                pay_datetime,
              </if>
              <if test="cancleDatetime != null ">
                cancle_datetime,
              </if>
              <if test="autoReceiveDatetime != null ">
                  auto_receive_datetime,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
              <if test="finishDatetime != null ">
                finish_datetime,
              </if>
              <if test="transactionId != null ">
                  transaction_id,
              </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="courierNumber != null and courierNumber != '' ">
                #{courierNumber,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                #{receiveWay,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != '' ">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userMobile != null and userMobile != '' ">
                #{userMobile,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancleDatetime != null">
                #{cancleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="autoReceiveDatetime != null">
                #{autoReceiveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishDatetime != null">
                #{finishDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="transactionId != null">
                #{transactionId,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tbiz_goods_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsOrder">
        update tbiz_goods_order
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null and orderNumber != '' ">
                order_number = #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="courierNumber != null and courierNumber != '' ">
                courier_number = #{courierNumber,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                receive_way = #{receiveWay,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != '' ">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userMobile != null and userMobile != '' ">
                user_mobile = #{userMobile,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != '' ">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="cancleDatetime != null">
                cancle_datetime = #{cancleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="autoReceiveDatetime != null">
                auto_receive_datetime = #{autoReceiveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishDatetime != null">
                finish_datetime = #{finishDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="transactionId != null">
                transaction_id = #{transactionId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectTimeOutOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_order t
        where t.status='0' and DATE_ADD(t.create_datetime, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{date}
    </select>


    <select id="selectMinMaxCreateTime" resultType="com.std.core.pojo.response.MinMaxTimeRes">
        SELECT
            MIN(DATE(t.create_datetime)) AS minTime,
            MAX(DATE(t.create_datetime)) AS maxTime
        FROM tbiz_goods_order t
        WHERE t.status IN ('0', '1')
    </select>

    <select id="selectActivityIncomeGroupBy"
            parameterType="com.std.core.pojo.request.IncomeGroupReq"
            resultType="com.std.core.pojo.response.IncomeItemRes">
        SELECT
        <choose>
            <when test="period == 'day'">
                DATE_FORMAT(t.create_datetime, '%Y-%m-%d') AS timeGroup
            </when>
            <when test="period == 'week'">
                DATE_FORMAT(DATE_SUB(t.create_datetime, INTERVAL WEEKDAY(t.create_datetime) DAY), '%Y-%m-%d') AS timeGroup
            </when>
            <when test="period == 'month'">
                DATE_FORMAT(t.create_datetime, '%Y-%m') AS timeGroup
            </when>
            <otherwise>
                DATE_FORMAT(t.create_datetime, '%Y-%m-%d') AS timeGroup
            </otherwise>
        </choose>
        , COALESCE(SUM(t.total_price), 0) AS income
        FROM tbiz_goods_order t
        <where>
            t.status IN ('1', '2', '3')
            <if test="startTime != null">
                AND t.create_datetime &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_datetime &lt;= #{endTime}
            </if>
        </where>
        GROUP BY timeGroup
        ORDER BY timeGroup ASC
    </select>


    <select id="selectActivityIncomeSum"
            parameterType="com.std.core.pojo.request.IncomeGroupReq"
            resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(t.total_price), 0)
        FROM tbiz_goods_order t
        <where>
            t.status IN ('1', '2', '3')
            <if test="startTime != null">
                AND t.create_datetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND t.create_datetime <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
    <select id="selectIncomeByDate" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(t.total_price), 0)
        FROM tbiz_goods_order t
        <where>
            t.status IN ('1', '2', '3')
            <if test="startTime != null and startTime != ''">
                AND t.create_datetime <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null and endTime != '' ">
                AND t.create_datetime <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>
</mapper>