<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsTrolleyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsTrolley">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="norms_id" jdbcType="BIGINT" property="normsId"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.order_id
        , t.goods_id
        , t.norms_id
        , t.number
        , t.user_id
        , t.status
        , t.create_datetime
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="normsId != null">
                AND t.norms_id = #{normsId, jdbcType=BIGINT}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsTrolley">
        insert into tbiz_goods_trolley
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="orderId != null ">
                order_id,
              </if>
              <if test="goodsId != null ">
                goods_id,
              </if>
              <if test="normsId != null ">
                norms_id,
              </if>
              <if test="number != null ">
                number,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="normsId != null">
                #{normsId,jdbcType=BIGINT},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tbiz_goods_trolley
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsTrolley">
        update tbiz_goods_trolley
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="normsId != null">
                norms_id = #{normsId,jdbcType=BIGINT},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_trolley t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsTrolley"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_trolley t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_goods_trolley t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>