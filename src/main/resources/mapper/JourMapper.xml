<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.JourMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Jour">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="biz_category" jdbcType="VARCHAR" property="bizCategory"/>
        <result column="biz_category_note" jdbcType="VARCHAR" property="bizCategoryNote"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_note" jdbcType="VARCHAR" property="bizNote"/>
        <result column="ref_no" jdbcType="VARCHAR" property="refNo"/>
        <result column="ref_user_id" jdbcType="BIGINT" property="refUserId"/>
        <result column="trans_amount" jdbcType="DECIMAL" property="transAmount"/>
        <result column="pre_amount" jdbcType="DECIMAL" property="preAmount"/>
        <result column="post_amount" jdbcType="DECIMAL" property="postAmount"/>
        <result column="prev_jour_code" jdbcType="VARCHAR" property="prevJourCode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>

    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.user_id
        , t.account_number
        , t.account_type
        , t.currency
        , t.biz_category
        , t.biz_category_note
        , t.biz_type
        , t.biz_note
        , t.ref_no
        , t.ref_user_id
        , t.trans_amount
        , t.pre_amount
        , t.post_amount
        , t.prev_jour_code
        , t.status
        , t.remark
        , t.create_datetime

    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                AND t.account_number = #{accountNumber, jdbcType=VARCHAR}
            </if>
            <if test="accountType != null and accountType != '' ">
                AND t.account_type = #{accountType, jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                AND t.biz_category = #{bizCategory, jdbcType=VARCHAR}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizNote != null and bizNote != '' ">
                AND t.biz_note = #{bizNote, jdbcType=VARCHAR}
            </if>
            <if test="refNo != null and refNo != '' ">
                AND t.ref_no = #{refNo, jdbcType=VARCHAR}
            </if>
            <if test="refUserId != null ">
                AND t.ref_user_id = #{refUserId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="arriveAccountType != null and arriveAccountType != '' ">
                AND t.arrive_account_type = #{arriveAccountType, jdbcType=VARCHAR}
            </if>
            <if test="income != null and income != '' ">
                AND t.trans_amount <![CDATA[ >= ]]> 0
            </if>
            <if test="expenditure != null and expenditure != '' ">
                AND t.trans_amount <![CDATA[ < ]]> 0
            </if>
            <if test="bizTypeList != null and bizTypeList.size() != 0 ">
                AND t.biz_type in
                <foreach item="item" index="index" collection="bizTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="accountNumberList != null and accountNumberList.size() != 0 ">
                AND t.account_number in
                <foreach item="item" index="index" collection="accountNumberList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="bizCategoryList != null and bizCategoryList.size() != 0 ">
                AND t.biz_category in
                <foreach item="item" index="index" collection="bizCategoryList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''  ">
                AND   DATE_FORMAT(t.create_datetime,'%Y-%m-%d')
                BETWEEN  #{startTime, jdbcType=VARCHAR} AND #{endTime, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Jour">
        insert into tstd_jour
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                account_number,
            </if>
            <if test="accountType != null and accountType != '' ">
                account_type,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                biz_category,
            </if>
            <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                biz_category_note,
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type,
            </if>
            <if test="bizNote != null and bizNote != '' ">
                biz_note,
            </if>
            <if test="refNo != null and refNo != '' ">
                ref_no,
            </if>
            <if test="refUserId != null">
                ref_user_id,
            </if>
            <if test="transAmount != null">
                trans_amount,
            </if>
            <if test="preAmount != null ">
                pre_amount,
            </if>
            <if test="postAmount != null">
                post_amount,
            </if>
            <if test="prevJourCode != null and prevJourCode != '' ">
                prev_jour_code,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="userId != null ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null and accountType != '' ">
                #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                #{bizCategoryNote,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizNote != null and bizNote != '' ">
                #{bizNote,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                #{refNo,jdbcType=VARCHAR},
            </if>
            <if test="refUserId != null ">
                #{refUserId,jdbcType=BIGINT},
            </if>
            <if test="transAmount != null">
                #{transAmount,jdbcType=DECIMAL},
            </if>
            <if test="preAmount != null">
                #{preAmount,jdbcType=DECIMAL},
            </if>
            <if test="postAmount != null">
                #{postAmount,jdbcType=DECIMAL},
            </if>
            <if test="prevJourCode != null and prevJourCode != '' ">
                #{prevJourCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_jour
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Jour">
        update tstd_jour
        <set>
            <if test="transAmount != null">
                trans_amount = #{transAmount,jdbcType=DECIMAL},
            </if>
            <if test="preAmount != null">
                pre_amount = #{preAmount,jdbcType=DECIMAL},
            </if>
            <if test="postAmount != null">
                post_amount = #{postAmount,jdbcType=DECIMAL},
            </if>
            <if test="prevJourCode != null and prevJourCode != '' ">
                prev_jour_code = #{prevJourCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
      where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_jour t
      where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Jour"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_jour t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

  <select id="selectTotalAmount" parameterType="com.std.core.pojo.domain.Jour"
    resultType="java.math.BigDecimal">
    select sum(t.trans_amount)
    from tstd_jour t
    <include refid="where_condition"/>
  </select>

    <select id="selectTransAmount" parameterType="com.std.core.pojo.domain.Jour"
            resultType="java.math.BigDecimal">
        SELECT
            t.trans_amount
        FROM
            tstd_jour t
        WHERE
            user_id = ( SELECT t.id FROM tsys_user t LEFT JOIN tgyl_anchor ta ON t.id = ta.user_id WHERE ta.id = #{id,jdbcType=BIGINT})
            AND ref_no= #{refNo,jdbcType=VARCHAR}
    </select>
</mapper>