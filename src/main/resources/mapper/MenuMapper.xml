<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.MenuMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Menu">
        <id column="t_id" property="id" jdbcType="BIGINT"/>
        <result column="t_parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="t_type" property="type" jdbcType="VARCHAR"/>
        <result column="t_kind" property="kind" jdbcType="VARCHAR"/>
        <result column="t_name" property="name" jdbcType="VARCHAR"/>
        <result column="t_logo" property="logo" jdbcType="VARCHAR"/>
        <result column="t_url" property="url" jdbcType="VARCHAR"/>

        <result column="t_order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="t_location" property="location" jdbcType="VARCHAR"/>
        <result column="t_remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id as t_id,
        t.parent_id as t_parent_id,
        t.type as t_type,
        t.kind as t_kind,
        t.name as t_name,
        t.logo as t_logo,
        t.url as t_url,

        t.order_no as t_order_no,
        t.location as t_location,
        t.remark as t_remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="parentId != null and parentId != '' ">
                AND t.parent_id = #{parentId,jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="typeList != null and typeList.size() != 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind,jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location,jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat(concat('%',#{name}),'%')
            </if>
            <if test="url != null and url != '' ">
                AND t.url like concat(concat('%',#{url}),'%')
            </if>
        </trim>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from tsys_menu t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <resultMap id="TopMenuResultMap" type="com.std.core.pojo.domain.Menu">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="kind" property="kind" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="logo" property="logo" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>

        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="location" property="location" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="depth" property="depth" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Menu"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_menu t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByConditionByRoleType" parameterType="com.std.core.pojo.domain.Menu"
      resultMap="TopMenuResultMap">
        SELECT t.id,t.parent_id,t.type,t.kind,t.name,t.logo,t.url,t.order_no,t.location,t.remark
        FROM tsys_menu t, tsys_permission_role tpr
        WHERE t.id = tpr.resource_id AND tpr.resource_type != 'action'
        <if test="typeList != null and typeList.size() != 0 ">
            AND t.type in
            <foreach item="item" index="index" collection="typeList" open="(" separator=","
              close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="roleList != null and roleList.size() != 0 ">
            AND tpr.role_id in
            <foreach item="item" index="index" collection="roleList" open="(" separator=","
              close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_menu
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Menu"
            useGeneratedKeys="true" keyProperty="id">

        insert into tsys_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="kind != null">
                kind,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="logo != null">
                logo,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="location != null">
                location,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Menu">
        update tsys_menu
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                kind = #{kind,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
