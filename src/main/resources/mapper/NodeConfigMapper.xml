<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.NodeConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.NodeConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="team_performance" jdbcType="DECIMAL" property="teamPerformance"/>
        <result column="first_team_number" jdbcType="INTEGER" property="firstTeamNumber"/>
        <result column="first_team_performance" jdbcType="DECIMAL" property="firstTeamPerformance"/>
        <result column="first_person_level" jdbcType="INTEGER" property="firstPersonLevel"/>
        <result column="first_person_level_number" jdbcType="INTEGER" property="firstPersonLevelNumber"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="rate1" jdbcType="DECIMAL" property="rate1"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.level
        , t.team_performance
        , t.first_team_number
        , t.first_team_performance
        , t.first_person_level
        , t.first_person_level_number
        , t.rate
        , t.rate1
        , t.creator
        , t.creator_name
        , t.create_time
        , t.updater
        , t.updater_name
        , t.update_time
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="level != null">
                AND t.level = #{level, jdbcType=INTEGER}
            </if>
            <if test="teamPerformance != null">
                AND t.team_performance = #{teamPerformance, jdbcType=DECIMAL}
            </if>
            <if test="firstTeamNumber != null">
                AND t.first_team_number = #{firstTeamNumber, jdbcType=INTEGER}
            </if>
            <if test="firstTeamPerformance != null">
                AND t.first_team_performance = #{firstTeamPerformance, jdbcType=DECIMAL}
            </if>
            <if test="firstPersonLevel != null">
                AND t.first_person_level = #{firstPersonLevel, jdbcType=INTEGER}
            </if>
            <if test="firstPersonLevelNumber != null">
                AND t.first_person_level_number = #{firstPersonLevelNumber, jdbcType=INTEGER}
            </if>
            <if test="rate != null">
                AND t.rate = #{rate, jdbcType=DECIMAL}
            </if>
            <if test="creator != null and creator != '' ">
                AND t.creator = #{creator, jdbcType=VARCHAR}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.NodeConfig">
        insert into tsys_node_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="level != null ">
                level,
            </if>
            <if test="teamPerformance != null ">
                team_performance,
            </if>
            <if test="firstTeamNumber != null ">
                first_team_number,
            </if>
            <if test="firstTeamPerformance != null ">
                first_team_performance,
            </if>
            <if test="firstPersonLevel != null ">
                first_person_level,
            </if>
            <if test="firstPersonLevelNumber != null ">
                first_person_level_number,
            </if>
            <if test="rate != null ">
                rate,
            </if>
            <if test="rate1 != null ">
                rate1,
            </if>
            <if test="creator != null and creator != '' ">
                creator,
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updater != null and updater != '' ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=INTEGER},
            </if>
            <if test="teamPerformance != null">
                #{teamPerformance,jdbcType=DECIMAL},
            </if>
            <if test="firstTeamNumber != null">
                #{firstTeamNumber,jdbcType=INTEGER},
            </if>
            <if test="firstTeamPerformance != null">
                #{firstTeamPerformance,jdbcType=DECIMAL},
            </if>
            <if test="firstPersonLevel != null">
                #{firstPersonLevel,jdbcType=INTEGER},
            </if>
            <if test="firstPersonLevelNumber != null">
                #{firstPersonLevelNumber,jdbcType=INTEGER},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="rate1 != null">
                #{rate1,jdbcType=DECIMAL},
            </if>
            <if test="creator != null and creator != '' ">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_node_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.NodeConfig">
        update tsys_node_config
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="teamPerformance != null">
                team_performance = #{teamPerformance,jdbcType=DECIMAL},
            </if>
            <if test="firstTeamNumber != null">
                first_team_number = #{firstTeamNumber,jdbcType=INTEGER},
            </if>
            <if test="firstTeamPerformance != null">
                first_team_performance = #{firstTeamPerformance,jdbcType=DECIMAL},
            </if>
            <if test="firstPersonLevel != null">
                first_person_level = #{firstPersonLevel,jdbcType=INTEGER},
            </if>
            <if test="firstPersonLevelNumber != null">
                first_person_level_number = #{firstPersonLevelNumber,jdbcType=INTEGER},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="rate1 != null">
                rate1 = #{rate1,jdbcType=DECIMAL},
            </if>
            <if test="creator != null and creator != '' ">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_node_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.NodeConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_node_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>