<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.NotifierMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Notifier">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="start_date" jdbcType="TINYINT" property="startDate"/>
        <result column="end_date" jdbcType="TINYINT" property="endDate"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.start_date
        , t.end_date
        , t.name
        , t.phone
        , t.email
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="startDate != null">
                AND t.start_date = #{startDate, jdbcType=TINYINT}
            </if>
            <if test="endDate != null">
                AND t.end_date = #{endDate, jdbcType=TINYINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="phone != null and phone != '' ">
                AND t.phone = #{phone, jdbcType=VARCHAR}
            </if>
            <if test="email != null and email != '' ">
                AND t.email = #{email, jdbcType=VARCHAR}
            </if>
            <if test="hour != null">
                AND hour BETWEEN  t.start_date AND t.end_date
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Notifier">
        insert into tsys_notifier
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="startDate != null ">
                start_date,
              </if>
              <if test="endDate != null ">
                end_date,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="phone != null and phone != '' ">
                phone,
              </if>
              <if test="email != null and email != '' ">
                email,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=TINYINT},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=TINYINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != '' ">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                #{email,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tsys_notifier
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Notifier">
        update tsys_notifier
        <set>
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                start_date = #{startDate,jdbcType=TINYINT},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=TINYINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != '' ">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                email = #{email,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_notifier t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Notifier"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_notifier t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>