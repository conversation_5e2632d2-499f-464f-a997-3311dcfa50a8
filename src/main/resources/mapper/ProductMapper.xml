<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ProductMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Product">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="initial_capital" jdbcType="DECIMAL" property="initialCapital"/>
        <result column="step" jdbcType="DECIMAL" property="step"/>
        <result column="hourly_interest" jdbcType="DECIMAL" property="hourlyInterest"/>
        <result column="year_interest" jdbcType="DECIMAL" property="yearInterest"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order" jdbcType="INTEGER" property="order"/>
        <result column="intro" jdbcType="VARCHAR" property="intro"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
        <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.lock_time
        , t.type
        , t.initial_capital
        , t.step
        , t.hourly_interest
        , t.year_interest
        , t.status
        , t.order
        , t.intro
        , t.updater
        , t.update_name
        , t.updatetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="lockTime != null">
                AND t.lock_time = #{lockTime, jdbcType=INTEGER}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="initialCapital != null">
                AND t.initial_capital = #{initialCapital, jdbcType=DECIMAL}
            </if>
            <if test="step != null">
                AND t.step = #{step, jdbcType=DECIMAL}
            </if>
            <if test="hourlyInterest != null">
                AND t.hourly_interest = #{hourlyInterest, jdbcType=DECIMAL}
            </if>
            <if test="yearInterest != null">
                AND t.year_interest = #{yearInterest, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="order != null">
                AND t.order = #{order, jdbcType=INTEGER}
            </if>
            <if test="intro != null and intro != '' ">
                AND t.intro = #{intro, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateName != null and updateName != '' ">
                AND t.update_name = #{updateName, jdbcType=VARCHAR}
            </if>
            <if test="updatetime != null">
                AND t.updatetime = #{updatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Product">
        insert into tvop_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="lockTime != null ">
                lock_time,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="initialCapital != null ">
                initial_capital,
              </if>
              <if test="step != null ">
                step,
              </if>
              <if test="hourlyInterest != null ">
                hourly_interest,
              </if>
              <if test="yearInterest != null ">
                year_interest,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="order != null ">
                order,
              </if>
              <if test="intro != null and intro != '' ">
                intro,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updateName != null and updateName != '' ">
                update_name,
              </if>
              <if test="updatetime != null ">
                updatetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="initialCapital != null">
                #{initialCapital,jdbcType=DECIMAL},
            </if>
            <if test="step != null">
                #{step,jdbcType=DECIMAL},
            </if>
            <if test="hourlyInterest != null">
                #{hourlyInterest,jdbcType=DECIMAL},
            </if>
            <if test="yearInterest != null">
                #{yearInterest,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="order != null">
                #{order,jdbcType=INTEGER},
            </if>
            <if test="intro != null and intro != '' ">
                #{intro,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateName != null and updateName != '' ">
                #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="updatetime != null">
                #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tvop_product
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Product">
        update tvop_product
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="initialCapital != null">
                initial_capital = #{initialCapital,jdbcType=DECIMAL},
            </if>
            <if test="step != null">
                step = #{step,jdbcType=DECIMAL},
            </if>
            <if test="hourlyInterest != null">
                hourly_interest = #{hourlyInterest,jdbcType=DECIMAL},
            </if>
            <if test="yearInterest != null">
                year_interest = #{yearInterest,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="order != null">
                order = #{order,jdbcType=INTEGER},
            </if>
            <if test="intro != null and intro != '' ">
                intro = #{intro,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateName != null and updateName != '' ">
                update_name = #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="updatetime != null">
                updatetime = #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tvop_product t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Product"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tvop_product t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>