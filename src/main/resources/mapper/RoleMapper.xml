<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Role">
        <id column="t_id" jdbcType="BIGINT" property="id"/>
        <result column="t_name" jdbcType="VARCHAR" property="name"/>
        <result column="t_kind" jdbcType="VARCHAR" property="kind"/>
        <result column="t_creator" jdbcType="VARCHAR" property="creator"/>
        <result column="t_create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="t_updater" jdbcType="VARCHAR" property="updater"/>
        <result column="t_update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="t_remark" jdbcType="VARCHAR" property="remark"/>
        <result column="t_company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="t_alias" jdbcType="VARCHAR" property="alias"/>
        <result column="t_is_default" jdbcType="VARCHAR" property="isDefault"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id as t_id,
        t.name as t_name,
        t.kind as t_kind,
        t.creator as t_creator,
        t.create_time as t_create_time,
        t.updater as t_updater,
        t.update_time as t_update_time,
        t.remark as t_remark,
        t.company_id as t_company_id,
        t.alias as t_alias,
        t.is_default as t_is_default
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="name != null and name != '' ">
                AND t.name like concat(concat('%',#{name}),'%')
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind,jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId,jdbcType=BIGINT}
            </if>
            <if test="companyIdAndDefault != null">
                AND (t.company_id = #{companyIdAndDefault,jdbcType=BIGINT} OR t.is_default =
                '1')
            </if>
            <if test="isDefault != null and isDefault != '' ">
                AND t.is_default = #{isDefault,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_role t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Role"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_role t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByConditionByUser" parameterType="java.lang.Long" resultType="java.lang.Long">
        select distinct id
        from (select t.id
              from tsys_role t,
                   tsys_user_role tur
              where t.id = tur.role_id
                and tur.user_id = #{userId}
              union
              select t.id
              from tsys_role t,
                   tsys_group_role tgr,
                   tsys_user_group tug
              where t.id = tgr.role_id
                and tgr.group_id = tug.group_id
                and tug.user_id = #{userId}) t
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_role
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Role">
        insert into tsys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="kind != null">
                kind,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Role">
        update tsys_role
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="kind != null">
                kind = #{kind,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
