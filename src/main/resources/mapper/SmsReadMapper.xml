<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.SmsReadMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.SmsRead">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="sms_code" jdbcType="BIGINT" property="smsCode"/>
        <result column="receive_way" jdbcType="VARCHAR" property="receiveWay"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="read_datetime" jdbcType="TIMESTAMP" property="readDatetime"/>
        <result column="delete_datetime" jdbcType="TIMESTAMP" property="deleteDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.sms_code
        , t.receive_way
        , t.status
        , t.create_datetime
        , t.read_datetime
        , t.delete_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null and userId != '' ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="smsCode != null and smsCode != '' ">
                AND t.sms_code = #{smsCode, jdbcType=BIGINT}
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                AND t.receive_way = #{receiveWay, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="readDatetime != null">
                AND t.read_datetime = #{readDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="deleteDatetime != null">
                AND t.delete_datetime = #{deleteDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.SmsRead" useGeneratedKeys="true" keyProperty="id">
        insert into tstd_sms_read
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null and userId != '' ">
                user_id,
              </if>
              <if test="smsCode != null and smsCode != '' ">
                sms_code,
              </if>
              <if test="receiveWay != null and receiveWay != '' ">
                receive_way,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="readDatetime != null ">
                read_datetime,
              </if>
              <if test="deleteDatetime != null ">
                delete_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null and userId != '' ">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null and smsCode != '' ">
                #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                #{receiveWay,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="readDatetime != null">
                #{readDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteDatetime != null">
                #{deleteDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_sms_read
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteBySmsCode" parameterType="java.lang.Long">
        delete from tstd_sms_read
        where sms_code = #{smsCode,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.SmsRead">
        update tstd_sms_read
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null and userId != '' ">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="smsCode != null and smsCode != '' ">
                sms_code = #{smsCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveWay != null and receiveWay != '' ">
                receive_way = #{receiveWay,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="readDatetime != null">
                read_datetime = #{readDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteDatetime != null">
                delete_datetime = #{deleteDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_read t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.SmsRead"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms_read t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCount" parameterType="com.std.core.pojo.domain.SmsRead" resultType="java.lang.Integer">
        select count(1)
        from tstd_sms_read t
        <include refid="where_condition"/>
    </select>

    <sql id="My_Column_List">
        t.id
        , t.target
        , t.type
        , t.lang
        , t.title
        , t.content
        , t.status
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.ref_type
        , t.ref_no
        , ifnull((select 1 from tstd_sms_read tr where t.id = tr.sms_code and tr.user_id=#{userId} limit 1),0) is_read
    </sql>

    <sql id="where_my_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="target != null and target != '' ">
                AND t.target = #{target, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="lang != null and lang != '' ">
                AND t.lang = #{lang, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title = #{title, jdbcType=VARCHAR}
            </if>
            <choose>
                <when test='type != "1"'>
                    AND t.user_id = #{userId, jdbcType=BIGINT}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <resultMap id="MyResultMap" type="com.std.core.pojo.domain.Sms">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="target" jdbcType="VARCHAR" property="target"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="lang" jdbcType="VARCHAR" property="lang"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_no" jdbcType="VARCHAR" property="refNo"/>
        <result column="is_read" jdbcType="VARCHAR" property="isRead"/>
    </resultMap>

    <sql id="where_read_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="isRead != null and isRead != '' ">
                AND t.is_read = #{isRead, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 组合条件查询 -->
    <select id="selectMySmsByCondition" parameterType="com.std.core.pojo.domain.Sms"
      resultMap="MyResultMap">
        select * from (select
        <include refid="My_Column_List"/>
        from tstd_sms t
        <include refid="where_my_condition"/>
        ) t
        <include refid="where_read_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 查询我的公告未阅读数量 -->
    <select id="selectMyUnreadCount" parameterType="com.std.core.pojo.domain.Sms" resultType="java.lang.Integer">
        select count(*) from tstd_sms t
        where t.target= #{target} and t.status = #{status} and not exists (select * from tstd_sms_read tr where tr.user_id = #{userId} and tr.sms_code = t.id)
    </select>

    <!-- 查询我的消息未阅读数量 -->
    <select id="selectMsgMyUnreadCount" parameterType="com.std.core.pojo.domain.Sms"
            resultType="java.lang.Integer">
        select count(*) from tstd_sms t
        where t.target= #{target} and t.status = #{status} and t.user_id = #{userId} and not exists (select * from tstd_sms_read tr where tr.user_id = #{userId} and tr.sms_code = t.id)
    </select>

</mapper>