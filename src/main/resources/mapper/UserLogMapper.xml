<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserLogMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserLog">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_note" jdbcType="VARCHAR" property="typeNote"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="client" jdbcType="VARCHAR" property="client"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.user_name
        , t.content
        , t.type
        , t.type_note
        , t.ip
        , t.client
        , t.location
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="userId != null and userId != '' ">
                AND t.user_id = #{userId, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="typeNote != null and typeNote != '' ">
                AND t.type_note = #{typeNote, jdbcType=VARCHAR}
            </if>
            <if test="ip != null and ip != '' ">
                AND t.ip = #{ip, jdbcType=VARCHAR}
            </if>
            <if test="client != null and client != '' ">
                AND t.client = #{client, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

  <!--    批量插入 sql-->
  <sql id="userInfoColumnsList">
    <trim suffixOverrides=",">
      <if test="item.userId != null and item.userId != '' ">
        user_id,
      </if>
      <if test="item.userName != null and item.userName != '' ">
        user_name,
      </if>
      <if test="item.content != null and item.content != '' ">
        content,
      </if>
      <if test="item.type != null and item.type != '' ">
        type,
      </if>
        <if test="item.typeNote != null and item.typeNote != '' ">
            type_note,
        </if>
      <if test="item.ip != null and item.ip != '' ">
        ip,
      </if>
      <if test="item.client != null and item.client != '' ">
        client,
      </if>
      <if test="item.location != null and item.location != '' ">
        location,
      </if>
      <if test="item.createDatetime != null">
        create_datetime,
      </if>
    </trim>
  </sql>
  <sql id="userInfoValuesList">
    <trim suffixOverrides=",">
      <if test="item.userId != null and item.userId != '' ">
        #{item.userId,jdbcType=VARCHAR},
      </if>
      <if test="item.userName != null and item.userName != '' ">
        #{item.userName,jdbcType=VARCHAR},
      </if>
      <if test="item.content != null and item.content != '' ">
        #{item.content,jdbcType=VARCHAR},
      </if>
      <if test="item.type != null and item.type != '' ">
        #{item.type,jdbcType=VARCHAR},
      </if>
        <if test="item.typeNote != null and item.typeNote != '' ">
            #{item.typeNote,jdbcType=VARCHAR},
        </if>
      <if test="item.ip != null and item.ip != '' ">
        #{item.ip,jdbcType=VARCHAR},
      </if>
      <if test="item.client != null and item.client != '' ">
        #{item.client,jdbcType=VARCHAR},
      </if>
      <if test="item.location != null and item.location != '' ">
        #{item.location,jdbcType=VARCHAR},
      </if>
      <if test="item.createDatetime != null">
        #{item.createDatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </sql>

  <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserLog">
        insert into tsys_user_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != '' ">
                user_id,
            </if>
            <if test="userName != null and userName != '' ">
                user_name,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="typeNote != null and typeNote != '' ">
                type_note,
            </if>
            <if test="ip != null and ip != '' ">
                ip,
            </if>
            <if test="client != null and client != '' ">
                client,
            </if>
            <if test="location != null and location != '' ">
                location,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != '' ">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != '' ">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="typeNote != null and typeNote != '' ">
                #{typeNote,jdbcType=VARCHAR},
            </if>
            <if test="ip != null and ip != '' ">
                #{ip,jdbcType=VARCHAR},
            </if>
            <if test="client != null and client != '' ">
                #{client,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id" parameterType="com.std.core.pojo.domain.UserLog">
    <foreach collection="list" item="item" index="index" separator=";">
      INSERT INTO tsys_user_log
      (<include refid="userInfoColumnsList"/>)
      VALUES
      (<include refid="userInfoValuesList"/>)
    </foreach>
    
  </insert>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_user_log
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserLog">
        update tsys_user_log
        <set>
            <if test="userId != null and userId != '' ">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != '' ">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="typeNote != null and typeNote != '' ">
                type_note = #{typeNote,jdbcType=VARCHAR},
            </if>
            <if test="ip != null and ip != '' ">
                ip = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="client != null and client != '' ">
                client = #{client,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user_log t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserLog"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user_log t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>