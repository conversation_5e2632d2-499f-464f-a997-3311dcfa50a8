<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.User">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="kind" jdbcType="VARCHAR" property="kind"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="nickname_change_flag" jdbcType="VARCHAR" property="nicknameChangeFlag"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="age" jdbcType="INTEGER" property="age"/>
        <result column="id_kind" jdbcType="VARCHAR" property="idKind"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="willing_value" jdbcType="DECIMAL" property="willingValue"/>
        <result column="login_pwd" jdbcType="VARCHAR" property="loginPwd"/>
        <result column="login_pwd_strength" jdbcType="VARCHAR" property="loginPwdStrength"/>
        <result column="trade_pwd" jdbcType="VARCHAR" property="tradePwd"/>
        <result column="trade_pwd_strength" jdbcType="VARCHAR" property="tradePwdStrength"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="register_datetime" jdbcType="TIMESTAMP" property="registerDatetime"/>
        <result column="register_ip" jdbcType="VARCHAR" property="registerIp"/>
        <result column="invite_no" jdbcType="BIGINT" property="inviteNo"/>
        <result column="user_referee" jdbcType="BIGINT" property="userReferee"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="last_login_datetime" jdbcType="TIMESTAMP" property="lastLoginDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="refer_user_count" jdbcType="BIGINT" property="referUserCount"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="channel_grade" jdbcType="INTEGER" property="channelGrade"/>
        <result column="reservation_expired_number" jdbcType="INTEGER" property="reservationExpiredNumber"/>
        <result column="reservation_status" jdbcType="VARCHAR" property="reservationStatus"/>
        <result column="reservation_unseal_time" jdbcType="TIMESTAMP" property="reservationUnsealTime"/>
    </resultMap>

    <resultMap id="UserResultMap" type="com.std.core.pojo.response.UserDetailRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="nickname_change_flag" jdbcType="VARCHAR" property="nicknameChangeFlag"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="credit_score" jdbcType="DECIMAL" property="creditScore"/>
        <result column="available_amount" jdbcType="DECIMAL" property="availableAmount"/>
        <result column="frozen_amount" jdbcType="DECIMAL" property="frozenAmount"/>
    </resultMap>

    <resultMap id="UserTeamDetailMap" type="com.std.core.pojo.response.TeamUserOssRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cuserId" jdbcType="VARCHAR" property="cuserId"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>


    <sql id="Base_Column_List">
        t.id
        , t.kind
        , t.login_name
        , t.nickname
        , t.nickname_change_flag
        , t.mobile
        , t.email
        , t.photo
        , t.sex
        , t.age
        , t.id_kind
        , t.id_no
        , t.willing_value
        , t.login_pwd
        , t.login_pwd_strength
        , t.trade_pwd
        , t.trade_pwd_strength
        , t.real_name
        , t.province
        , t.city
        , t.area
        , t.address
        , t.status
        , t.register_datetime
        , t.register_ip
        , t.invite_no
        , t.user_referee
        , t.remark
        , t.last_login_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.company_id
        , t.channel_id
        , t.channel_grade
        , t.reservation_expired_number
        , t.reservation_status
        , t.reservation_unseal_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind, jdbcType=VARCHAR}
            </if>
            <if test="loginName != null and loginName != '' ">
                AND t.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="idKind != null and idKind != '' ">
                AND t.id_kind = #{idKind, jdbcType=VARCHAR}
            </if>
            <if test="idNo != null and idNo != '' ">
                AND t.id_no = #{idNo, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND t.real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="mobileForQuery != null and mobileForQuery != '' ">
                AND t.mobile like concat('%',#{mobileForQuery},'%')
            </if>
            <if test="email != null and email != '' ">
                AND t.email = #{email, jdbcType=VARCHAR}
            </if>
            <if test="sex != null and sex != '' ">
                AND t.sex = #{sex, jdbcType=VARCHAR}
            </if>
            <if test="willingValue != null">
                AND t.willing_value = #{willingValue, jdbcType=DECIMAL}
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                AND t.login_pwd = #{loginPwd, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                AND t.trade_pwd = #{tradePwd, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="inviteNo != null">
                AND t.invite_no = #{inviteNo, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null">
                AND t.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="registerTimeStart != null">
                <![CDATA[AND t.register_datetime >= #{registerTimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="registerTimeEnd != null">
                <![CDATA[AND t.register_datetime <= #{registerTimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeStart != null">
                <![CDATA[AND t.last_login_datetime >= #{lastLoginDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeEnd != null">
                <![CDATA[AND t.last_login_datetime <= #{lastLoginDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="channelGrade != null">
                AND t.channel_grade = #{channelGrade, jdbcType=INTEGER}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                t.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.nickname like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="gradeList != null and gradeList.size > 0 ">
                AND t.channel_grade in
                <foreach item="item" index="index" collection="gradeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reservationExpiredNumber != null">
                AND t.reservation_expired_number = #{reservationExpiredNumber, jdbcType=INTEGER}
            </if>
            <if test="reservationStatus != null and reservationStatus != ''">
                AND t.reservation_status = #{reservationStatus, jdbcType=VARCHAR}
            </if>
            <if test="reservationUnsealTime != null">
                AND t.reservation_unseal_time = #{reservationUnsealTime, jdbcType=TIMESTAMP}
            </if>
            <if test="reservationUnsealTimeEnd != null">
                <![CDATA[AND t.reservation_unseal_time <= #{reservationUnsealTimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.User">
        insert into tsys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="kind != null and kind != '' ">
                kind,
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name,
            </if>
            <if test="nickname != null and nickname != '' ">
                nickname,
            </if>
            <if test="nicknameChangeFlag != null and nicknameChangeFlag != '' ">
                nickname_change_flag,
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile,
            </if>
            <if test="email != null and email != '' ">
                email,
            </if>
            <if test="photo != null and photo != '' ">
                photo,
            </if>
            <if test="sex != null and sex != '' ">
                sex,
            </if>
            <if test="age != null and age != '' ">
                age,
            </if>
            <if test="idKind != null and idKind != '' ">
                id_kind,
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no,
            </if>
            <if test="realName != null and realName != '' ">
                real_name,
            </if>
            <if test="province != null and province != '' ">
                province,
            </if>
            <if test="city != null and city != '' ">
                city,
            </if>
            <if test="area != null and area != '' ">
                area,
            </if>
            <if test="address != null and address != '' ">
                address,
            </if>
            <if test="willingValue != null ">
                willing_value,
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                login_pwd,
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                login_pwd_strength,
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                trade_pwd,
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                trade_pwd_strength,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="registerDatetime != null ">
                register_datetime,
            </if>
            <if test="registerIp != null and registerIp != '' ">
                register_ip,
            </if>
            <if test="inviteNo != null ">
                invite_no,
            </if>
            <if test="userReferee != null ">
                user_referee,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="lastLoginDatetime != null  ">
                last_login_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="channelId != null">
                channel_id
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null  ">
                update_datetime,
            </if>
            <if test="reservationExpiredNumber != null">
                reservation_expired_number,
            </if>
            <if test="reservationStatus != null and reservationStatus != ''">
                reservation_status,
            </if>
            <if test="reservationUnsealTime != null">
                reservation_unseal_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="kind != null and kind != '' ">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="loginName != null and loginName != '' ">
                #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null and nickname != '' ">
                #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="nicknameChangeFlag != null and nicknameChangeFlag != '' ">
                #{nicknameChangeFlag,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="photo != null and photo != '' ">
                #{photo,jdbcType=VARCHAR},
            </if>
            <if test="sex != null and sex != '' ">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="age != null ">
                #{age,jdbcType=INTEGER},
            </if>
            <if test="idKind != null and idKind != '' ">
                #{idKind,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != '' ">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="willingValue != null">
                #{willingValue,jdbcType=DECIMAL},
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                #{loginPwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                #{tradePwd,jdbcType=VARCHAR},
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                #{tradePwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="registerDatetime != null">
                #{registerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="registerIp != null and registerIp != '' ">
                #{registerIp,jdbcType=VARCHAR},
            </if>
            <if test="inviteNo != null and inviteNo != '' ">
                #{inviteNo,jdbcType=BIGINT},
            </if>
            <if test="userReferee != null and userReferee != '' ">
                #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDatetime != null">
                #{lastLoginDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="reservationExpiredNumber != null">
                #{reservationExpiredNumber, jdbcType=INTEGER},
            </if>
            <if test="reservationStatus != null and reservationStatus != ''">
                #{reservationStatus, jdbcType=VARCHAR},
            </if>
            <if test="reservationUnsealTime != null">
                #{reservationUnsealTime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into tsys_user
        (id
        ,kind
        ,login_name
        ,nickname
        ,mobile
        ,email
        ,photo
        ,sex
        ,age
        ,id_kind
        ,id_no
        ,real_name
        ,province
        ,city
        ,area
        ,address
        ,willing_value
        ,login_pwd
        ,login_pwd_strength
        ,status
        ,register_datetime
        ,register_ip
        ,remark
        ,last_login_datetime
        ,company_id)
        values
        <foreach item="user" index="index" collection="list" separator=",">
            (
            #{user.id,jdbcType=BIGINT},
            #{user.kind,jdbcType=VARCHAR},
            #{user.loginName,jdbcType=VARCHAR},
            #{user.nickname,jdbcType=VARCHAR},
            #{user.mobile,jdbcType=VARCHAR},
            #{user.email,jdbcType=VARCHAR},
            #{user.photo,jdbcType=VARCHAR},
            #{user.sex,jdbcType=VARCHAR},
            #{user.age,jdbcType=INTEGER},
            #{user.idKind,jdbcType=VARCHAR},
            #{user.idNo,jdbcType=VARCHAR},
            #{user.realName,jdbcType=VARCHAR},
            #{user.province,jdbcType=VARCHAR},
            #{user.city,jdbcType=VARCHAR},
            #{user.area,jdbcType=VARCHAR},
            #{user.address,jdbcType=VARCHAR},
            #{user.willingValue,jdbcType=DECIMAL},
            #{user.loginPwd,jdbcType=VARCHAR},
            #{user.loginPwdStrength,jdbcType=VARCHAR},
            #{user.status,jdbcType=VARCHAR},
            #{user.registerDatetime,jdbcType=TIMESTAMP},
            #{user.registerIp,jdbcType=VARCHAR},
            #{user.remark,jdbcType=VARCHAR},
            #{user.lastLoginDatetime,jdbcType=TIMESTAMP},
            #{user.companyId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.User">
        update tsys_user
        <set>
            <if test="loginName != null and loginName != '' ">
                login_name = #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null and nickname != '' ">
                nickname = #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="nicknameChangeFlag != null and nicknameChangeFlag != '' ">
                nickname_change_flag = #{nicknameChangeFlag,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="photo != null and photo != '' ">
                photo = #{photo,jdbcType=VARCHAR},
            </if>
            <if test="idKind != null and idKind != '' ">
                id_kind = #{idKind,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null and area != '' ">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="willingValue != null">
                willing_Value = #{willingValue,jdbcType=DECIMAL},
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                login_pwd = #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                login_pwd_strength = #{loginPwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                trade_pwd = #{tradePwd,jdbcType=VARCHAR},
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                trade_pwd_strength = #{tradePwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="sex != null and sex != '' ">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="age != null and age != '' ">
                age = #{age,jdbcType=INTEGER},
            </if>
            <if test="userReferee != null">
                user_referee = #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDatetime != null">
                last_login_datetime = #{lastLoginDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId,jdbcType=BIGINT},
            </if>
            <if test="channelGrade != null">
                channel_grade = #{channelGrade,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="reservationExpiredNumber != null">
                reservation_expired_number = #{reservationExpiredNumber, jdbcType=INTEGER},
            </if>
            <if test="reservationStatus != null and reservationStatus != ''">
                reservation_status = #{reservationStatus, jdbcType=VARCHAR},
            </if>
            <if test="reservationUnsealTime != null">
                reservation_unseal_time = #{reservationUnsealTime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdate">

        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update tsys_user
            <set>
                <choose>
                    <when test="item.channelId != null and item.channelId == -1 ">
                        channel_id = null,
                    </when>
                    <when test="item.channelId != null ">
                        channel_id = #{item.channelId},
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>

                <if test="item.channelGrade != null  ">
                    channel_grade = #{item.channelGrade},
                </if>
            </set>
            where id = #{item.id}
        </foreach>

    </update>

    <update id="batchModifyChannelFlag">
        <if test="userList != null and userList.size > 0 ">
            update tsys_user
            channel_id =  #{channelId,jdbcType=BIGINT}
            where id in
            <foreach item="item" index="index" collection="userList" open="(" separator=","
                     close=")">
                #{item.id}
            </foreach>
        </if>

    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="UserRef_Column_List">
        t.id
        , t.kind
        , t.login_name
        , t.nickname
        , t.mobile
        , t.email
        , t.photo
        , t.sex
        , t.age
        , t.id_kind
        , t.id_no
        , t.trade_pwd_strength
        , t.real_name
        , t.province
        , t.city
        , t.area
        , t.address
        , t.willing_value
        , t.status
        , t.register_datetime
        , t.register_ip
        , t.invite_no
        , t.user_referee
        , t.remark
        , t.last_login_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.company_id
        ,(select count(1) from tsys_user tu where tu.user_referee = t.id) as refer_user_count
    </sql>

    <!-- 组合条件查询数量 -->
    <select id="selectCount" parameterType="com.std.core.pojo.domain.User" resultType="java.lang.Integer">
        SELECT count(1) FROM tsys_user t
        <include refid="where_condition"/>
    </select>

    <select id="selectUserRefereeByCondition" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
      <select id="getAllCUser" resultType="com.std.core.pojo.domain.User"
                resultMap="BaseResultMap">
          select
          <include refid="UserRef_Column_List"/>
          from tsys_user t
          left join ttask_cuser tt on t.id = tt.user_id
          where t.kind = #{userKind,jdbcType=VARCHAR} and t.status = #{status,jdbcType=VARCHAR}
          and t.id not in (select user_id as uid from tgyl_seller group by user_id)
      </select>

    <select id="selectMineDetail" resultMap="UserResultMap">
        select
        t.id
        , t.nickname
        , t.photo
        , tc.level
        , tc.credit_score
        from tsys_user t
        left join ttask_cuser tc on t.id = tc.user_id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="cUserlist" resultType="com.std.core.pojo.domain.User">
         select * from tsys_user t
         where t.kind='C' AND
         t.id not in (select c_user_id as id from tgyl_seller)
    </select>

    <select id="selectSubUserList" parameterType="java.lang.Long" resultType="com.std.core.pojo.response.SubUserRes">
         select t.id,t.nickname,t.mobile,t.photo,tc.level from tsys_user t
         left join tsys_cuser tc on t.id=tc.user_id where t.user_referee =#{id,jdbcType=BIGINT}
    </select>

    <select id="ossTeamDeatil" resultType="com.std.core.pojo.domain.User" resultMap="UserTeamDetailMap">
         select t.id,tc.id as cuserId,t.nickname,t.real_name,tc.level,t.mobile,t.status from tsys_user t
         left join tsys_cuser tc on t.id=tu.user_id
         where t.user_referee=#{userReferee,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectIdList" resultType="java.lang.Long">
        select
        t.id
        from tsys_user t
        where t.kind='C' and t.status='normal'
    </select>


    <select id="dissociateChain"   resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        where t.channel_grade in (1,2,3)
        and channel_id  is null
    </select>

    <select id="getUserByOpenid" resultType="com.std.core.pojo.domain.User">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        left join tsys_cuser tc on t.id = tc.user_id
        where tc.openid = #{openid,jdbcType=VARCHAR}
    </select>

    <select id="getUserByUnionId" resultType="com.std.core.pojo.domain.User">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        left join tsys_cuser tc on t.id = tc.user_id
        where tc.union_id = #{unionId,jdbcType=VARCHAR}
    </select>
    <select id="getUserByAppOpenid" resultType="com.std.core.pojo.domain.User">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        left join tsys_cuser tc on t.id = tc.user_id
        where tc.app_openid = #{openid,jdbcType=VARCHAR}
    </select>

</mapper>
