<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserNodeLevelMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserNodeLevel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="way" jdbcType="VARCHAR" property="way"/>
        <result column="node_level_auto" jdbcType="INTEGER" property="nodeLevelAuto"/>
        <result column="node_level_manual" jdbcType="INTEGER" property="nodeLevelManual"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.way
        , t.node_level_auto
        , t.node_level_manual
        , t.create_time
        , t.update_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="way != null and way != '' ">
                AND t.way = #{way, jdbcType=VARCHAR}
            </if>
            <if test="nodeLevelAuto != null">
                AND t.node_level_auto = #{nodeLevelAuto, jdbcType=INTEGER}
            </if>
            <if test="nodeLevelManual != null">
                AND t.node_level_manual = #{nodeLevelManual, jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserNodeLevel">
        insert into tstd_user_node_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="way != null and way != '' ">
                way,
              </if>
              <if test="nodeLevelAuto != null ">
                node_level_auto,
              </if>
              <if test="nodeLevelManual != null ">
                node_level_manual,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
              <if test="updateTime != null ">
                update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="way != null and way != '' ">
                #{way,jdbcType=VARCHAR},
            </if>
            <if test="nodeLevelAuto != null">
                #{nodeLevelAuto,jdbcType=INTEGER},
            </if>
            <if test="nodeLevelManual != null">
                #{nodeLevelManual,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_user_node_level
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserNodeLevel">
        update tstd_user_node_level
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="way != null and way != '' ">
                way = #{way,jdbcType=VARCHAR},
            </if>
            <if test="nodeLevelAuto != null">
                node_level_auto = #{nodeLevelAuto,jdbcType=INTEGER},
            </if>
            <if test="nodeLevelManual != null">
                node_level_manual = #{nodeLevelManual,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_node_level t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserNodeLevel"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_node_level t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectMySubUserCount" parameterType="com.std.core.pojo.domain.UserNodeLevel" resultType="java.lang.Long" >
        select count(1) from tstd_user_node_level tunl, tsys_user tu
        where tunl.user_id = tu.id and
              tu.user_referee = #{userId} and
            case
                when tunl.way = 0 then
                    tunl.node_level_auto = #{nodeLevel}
                when tunl.way = 1 then
                    tunl.node_level_manual = #{nodeLevel}
            end;
    </select>

</mapper>