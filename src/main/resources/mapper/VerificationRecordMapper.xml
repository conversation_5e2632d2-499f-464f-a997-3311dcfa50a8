<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.VerificationRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.VerificationRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="ticket_line_id" jdbcType="BIGINT" property="ticketLineId"/>
        <result column="activity_order_id" jdbcType="BIGINT" property="activityOrderId"/>
        <result column="date" jdbcType="TIMESTAMP" property="date"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="ticket_line_name" jdbcType="VARCHAR" property="ticketLineName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contract" jdbcType="VARCHAR" property="contract"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.activity_id
        , t.ticket_line_id
        , t.activity_order_id
        , t.date
        , t.status
        , t.user_id
        , t.price
        , t.code
        , t.pic
        , t.ticket_line_name
        , t.name
        , t.contract
        , t.creater
        , t.create_time
        , t.creater_name
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="ticketLineId != null">
                AND t.ticket_line_id = #{ticketLineId, jdbcType=BIGINT}
            </if>
            <if test="activityOrderId != null">
                AND t.activity_order_id = #{activityOrderId, jdbcType=BIGINT}
            </if>
            <if test="date != null">
                AND t.date = #{date, jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.nickname like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="activityName != null and activityName != '' ">
                AND (
                ta.name like concat('%', #{activityName, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="code != null and code != '' ">
                AND t.code = #{code, jdbcType=VARCHAR}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                AND t.ticket_line_name = #{ticketLineName, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="contract != null and contract != '' ">
                AND t.contract = #{contract, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND t.date <![CDATA[ <=]]> #{endDate, jdbcType=TIMESTAMP}
            </if>
            <if test="startDate != null">
                AND t.date <![CDATA[ >=]]> #{startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.VerificationRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tbiz_verification_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
              <if test="ticketLineId != null ">
                ticket_line_id,
              </if>
              <if test="activityOrderId != null ">
                activity_order_id,
              </if>
              <if test="date != null ">
                date,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="code != null and code != '' ">
                code,
              </if>
              <if test="pic != null and pic != '' ">
                pic,
              </if>
              <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="contract != null and contract != '' ">
                contract,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="activityOrderId != null">
                #{activityOrderId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="code != null and code != '' ">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contract != null and contract != '' ">
                #{contract,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tbiz_verification_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.VerificationRecord">
        update tbiz_verification_record
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="ticketLineId != null">
                ticket_line_id = #{ticketLineId,jdbcType=BIGINT},
            </if>
            <if test="activityOrderId != null">
                activity_order_id = #{activityOrderId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="code != null and code != '' ">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="ticketLineName != null and ticketLineName != '' ">
                ticket_line_name = #{ticketLineName,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contract != null and contract != '' ">
                contract = #{contract,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_verification_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.VerificationRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tbiz_verification_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN tbiz_activity ta on t.activity_id = ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>