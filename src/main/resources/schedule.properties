#å¦æè¦åç¨ï¼åå°cronè®¾ç½®ä¸º - å³å¯

doStatisticUserTrigger.name=ãç»è®¡ç¨æ·ãæ¯æ¥ç¨æ·ä¿¡æ¯åºå
doStatisticUserTrigger.desc=æ¯æ¥ç»è®¡ç¨æ·ä¿¡æ¯
doStatisticUserTrigger.cron=0 0 0 * * ?

doStatisticPlatUserTrigger.name=ãç»è®¡ç¨æ·ãå¹³å°æ¯æ¥ç¨æ·æåµç»è®¡
doStatisticPlatUserTrigger.desc=å¹³å°æ¯æ¥ç¨æ·æåµç»è®¡
doStatisticPlatUserTrigger.cron=0 0 0 * * ?

doStatisticPlatAccountTrigger.name=ãç»è®¡å¹³å°è´¦æ·ãå¹³å°è´¦æ·æ¯æ¥æåµç»è®¡
doStatisticPlatAccountTrigger.desc=å¹³å°è´¦æ·æ¯æ¥æåµç»è®¡
doStatisticPlatAccountTrigger.cron=0 0 0 * * ?

doStatisticsAssignmentShareTrigger.name=ãè¥éæ¨¡åãå®æ¶ç»è®¡ä»»å¡ä»½é¢å¼ééåååé
doStatisticsAssignmentShareTrigger.desc=å®æ¶ç»è®¡ä»»å¡ä»½é¢å¼ééåååé
doStatisticsAssignmentShareTrigger.cron=0 0 0 * * ?


doCheckOrderTime.name=ãè®¢åãæ£æ¥è®¢åæ¯å¦è¿æ
doCheckOrderTime.desc=æ£æ¥è®¢åæ¯å¦è¿æ
doCheckOrderTime.cron=1 * * * * ?

doCheckOrderDeliverTime.name=ãè®¢åãè®¢åå·²åè´§15å¤©åæªæ¶è´§èªå¨æ¶è´§
doCheckOrderDeliverTime.desc=è®¢åå·²åè´§15å¤©åæªæ¶è´§èªå¨æ¶è´§
doCheckOrderDeliverTime.cron=0 0 1 * * ?

doCheckUnlockOpen.name=ãè§£éåãè§£éåæ¶é´å°èªå¨å¼å¯
doCheckUnlockOpen.desc=è§£éåæ¶é´å°èªå¨å¼å¯
doCheckUnlockOpen.cron=0 0 1 * * ?

doCheckJSBPlan.name=ãè§£éåãè§£éè®¡å
doCheckJSBPlan.desc=åè§£éè®¡åå¨ç¸åºæ¶é´è§£éNAT
doCheckJSBPlan.cron=0 0 2 * * ?
#doCheckJSBPlan.cron=*/10 * * * * ?

doCheckYkt.name=ãä¸å¡éãä¸å¡éæ¯æ¥äº§çé¶ææåé¶æå¶
doCheckYkt.desc=ä¸å¡éæ¯æ¥äº§çé¶ææåé¶æå¶
doCheckYkt.cron=0 0 2 * * ?
#doCheckYkt.cron=*/30 * * * * ?

doCheckactivityInvestmentPlan.name=ãæ´»å¨ãæ´»å¨èªå¨æå¥
doCheckactivityInvestmentPlan.desc=æ£æ¥æ¯å¦å°è¾¾æ´»å¨æå¥çæ¶é´ï¼èªå¨æå¥
doCheckactivityInvestmentPlan.cron=0 0 1 * * ?


doCheckactivityProfitPlan.name=ãæ´»å¨ãæ´»å¨æ¯ææ¶ç
doCheckactivityProfitPlan.desc=æ£æ¥æ¯å¦å°è¾¾æ´»å¨æ¶ççæ¶é´
doCheckactivityProfitPlan.cron=0 0 1 * * ?


doCheckactivityJoinRecord.name=ãæ´»å¨ãæ´»å¨åä¸è®°å½ç¶æåæ´
doCheckactivityJoinRecord.desc=æ£æ¥æ´»å¨åä¸è®°å½ç¶æ
doCheckactivityJoinRecord.cron=0 0 2 * * ?


doCheckActivityEnd.name=ãæ´»å¨ãæ£æ¥æ´»å¨æ¯å¦æ¶çä¸­æå®ç»
doCheckActivityEnd.desc=æ£æ¥æ´»å¨æ¯å¦æ¶çä¸­æå®ç»
doCheckActivityEnd.cron=0 0 4 * * ?

doCheckNatZYTransferTime.name=ãè´¨æ¼NATãæ£æ¥è´¨æ¼NATæ¯å¦å¯è½¬åº
doCheckNatZYTransferTime.desc=æ£æ¥è´¨æ¼NATæ¯å¦å¯è½¬åº
doCheckNatZYTransferTime.cron=0 0 1 * * ?

doGetLatestNatPrice.name=ãNATè¡æãè·åNATå®æ¶ä»·æ ¼
doGetLatestNatPrice.desc=è·åNATå®æ¶ä»·æ ¼
doGetLatestNatPrice.cron=0/30 * * * * ?

doCheckAgentActivityStartTrigger.name=ãä¸åºæ´»å¨ãä¸åºæ´»å¨æ¯å¦å¼å§
doCheckAgentActivityStartTrigger.desc=ä¸åºæ´»å¨æ¯å¦å¼å§
doCheckAgentActivityStartTrigger.cron=0 */1 * * * ?

doCheckAgentActivityOrderTrigger.name=ãä¸åºæ´»å¨ãä¸åºæ¢è´­è®¢åæ¯å¦è¶æ¶
doCheckAgentActivityOrderTrigger.desc=ä¸åºæ¢è´­è®¢åæ¯å¦è¶æ¶
doCheckAgentActivityOrderTrigger.cron=0 */1 * * * ?

doCheckAgentActivityRenewOrderTrigger.name=ãä¸åºæ´»å¨ãä¸åºèµäº§ç»­è´¹è®¢åæ¯å¦è¶æ¶
doCheckAgentActivityRenewOrderTrigger.desc=ä¸åºèµäº§ç»­è´¹è®¢åæ¯å¦è¶æ¶
doCheckAgentActivityRenewOrderTrigger.cron=0 */1 * * * ?

doCheckAgentAssetsExpireTrigger.name=ãä¸åºæ´»å¨ãä¸åºèµäº§æ¯å¦å°æ
doCheckAgentAssetsExpireTrigger.desc=ä¸åºèµäº§æ¯å¦å°æ
doCheckAgentAssetsExpireTrigger.cron=0 0 1 * * ?

doCheckMemorialGoodsExpireTrigger.name=ãä¸åºæ´»å¨ãä¸åºèµäº§æ¯å¦å°æ
doCheckMemorialGoodsExpireTrigger.desc=ä¸åºèµäº§æ¯å¦å°æ
doCheckMemorialGoodsExpireTrigger.cron=0 0 1 * * ?

doCheckWorshipBuyOrderExpireTrigger.name=ãé¾ä¸ç¥­ç¥ãç¥­ç¥è®¢åæ¯å¦å°æ
doCheckWorshipBuyOrderExpireTrigger.desc=ç¥­ç¥è®¢åæ¯å¦å°æ
doCheckWorshipBuyOrderExpireTrigger.cron=0 0/1 * * * ?


doHealthyCardStartExpireTrigger.name=ãåº·å»å¡ãåº·å»å¡å¼å§èµ·æ¯
doHealthyCardStartExpireTrigger.desc=æ£æ¥åº·å»å¡å°èµ·æ¥å¼å§èµ·æ¯
#doHealthyCardStartExpireTrigger.cron=0 0 0 * * ?
doHealthyCardStartExpireTrigger.cron=0 */1 * * * ?

doHealthyCardDayIncomeExpireTrigger.name=ãåº·å»å¡ãåº·å»å¡æ¯æ¥æ¶ç
doHealthyCardDayIncomeExpireTrigger.desc=åº·å»å¡æ¯æ¥æ¶ç
#doHealthyCardDayIncomeExpireTrigger.cron=0 1 0 * * ?
doHealthyCardDayIncomeExpireTrigger.cron=0 */1 * * * ?