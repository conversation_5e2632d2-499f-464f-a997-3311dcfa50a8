package com.std.core;

import com.alibaba.fastjson.JSON;
import com.std.core.service.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CoreApplicationTests {

    @Resource
    private IUserService userService;


    @Test
    public void test() {


    }

}
